<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.WecomConfigMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.WecomConfig" id="WecomConfigResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="corpSecret" column="corp_secret"/>
        <result property="agentId" column="agent_id"/>
        <result property="apiBaseUrl" column="api_base_url"/>
        <result property="redirectUri" column="redirect_uri"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWecomConfigVo">
        select id, corp_id, corp_secret, agent_id, api_base_url, redirect_uri, status, create_time, update_time
        from wecom_config
    </sql>

    <select id="selectWecomConfigById" parameterType="Long" resultMap="WecomConfigResult">
        <include refid="selectWecomConfigVo"/>
        where id = #{id}
    </select>
    
    <select id="selectEnabledWecomConfig" resultMap="WecomConfigResult">
        <include refid="selectWecomConfigVo"/>
        where status = '0'
        limit 1
    </select>

    <insert id="insertWecomConfig" parameterType="com.ruoyi.common.domain.entity.WecomConfig">
        insert into wecom_config (
            corp_id,
            corp_secret,
            agent_id,
            api_base_url,
            redirect_uri,
            status
        ) values (
            #{corpId},
            #{corpSecret},
            #{agentId},
            #{apiBaseUrl},
            #{redirectUri},
            #{status}
        )
    </insert>

    <update id="updateWecomConfig" parameterType="com.ruoyi.common.domain.entity.WecomConfig">
        update wecom_config
        <set>
            <if test="corpId != null">corp_id = #{corpId},</if>
            <if test="corpSecret != null">corp_secret = #{corpSecret},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="apiBaseUrl != null">api_base_url = #{apiBaseUrl},</if>
            <if test="redirectUri != null">redirect_uri = #{redirectUri},</if>
            <if test="status != null">status = #{status},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteWecomConfigById" parameterType="Long">
        delete from wecom_config where id = #{id}
    </delete>

</mapper> 