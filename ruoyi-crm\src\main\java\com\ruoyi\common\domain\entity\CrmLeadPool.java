package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 线索池对象 crm_lead_pool
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmLeadPool extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 线索ID，关联crm_business_leads表 */
    @Excel(name = "线索ID")
    private Long leadId;

    /** 池状态：available-可用，assigned-已分配，locked-锁定 */
    @Excel(name = "池状态", readConverterExp = "available=可用,assigned=已分配,locked=锁定")
    private String poolStatus;

    /** 质量等级：A-优质，B-良好，C-一般，D-较差 */
    @Excel(name = "质量等级", readConverterExp = "A=优质,B=良好,C=一般,D=较差")
    private String qualityLevel;

    /** 优先级：1-10，数字越大优先级越高 */
    @Excel(name = "优先级")
    private Integer priority;

    /** 来源类型：new-新增，recycled-回收，imported-导入 */
    @Excel(name = "来源类型", readConverterExp = "new=新增,recycled=回收,imported=导入")
    private String sourceType;

    /** 进入池时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进入池时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterPoolTime;

    /** 最后分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后分配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastAssignTime;

    /** 分配次数 */
    @Excel(name = "分配次数")
    private Integer assignCount;

    /** 地区，用于地区优先分配 */
    @Excel(name = "地区")
    private String region;

    /** 行业，用于行业专业分配 */
    @Excel(name = "行业")
    private String industry;

    /** 预估价值 */
    @Excel(name = "预估价值")
    private BigDecimal estimatedValue;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    // 扩展字段，用于关联查询
    /** 线索名称（来自crm_business_leads） */
    private String leadName;
    
    /** 客户名称（来自crm_business_leads） */
    private String customerName;
    
    /** 手机号码（来自crm_business_leads） */
    private String mobile;
    
    /** 邮箱（来自crm_business_leads） */
    private String email;
    
    /** 线索来源（来自crm_business_leads） */
    private String leadSource;

    // 构造函数
    public CrmLeadPool() {
        this.poolStatus = "available";
        this.qualityLevel = "C";
        this.priority = 5;
        this.sourceType = "new";
        this.assignCount = 0;
        this.enterPoolTime = new Date();
    }

    public CrmLeadPool(Long leadId, String sourceType) {
        this();
        this.leadId = leadId;
        this.sourceType = sourceType;
    }

    /**
     * 检查是否可以分配
     */
    public boolean isAvailable() {
        return "available".equals(this.poolStatus);
    }

    /**
     * 检查是否已分配
     */
    public boolean isAssigned() {
        return "assigned".equals(this.poolStatus);
    }

    /**
     * 检查是否被锁定
     */
    public boolean isLocked() {
        return "locked".equals(this.poolStatus);
    }

    /**
     * 设置为已分配状态
     */
    public void setAssigned() {
        this.poolStatus = "assigned";
        this.lastAssignTime = new Date();
        this.assignCount = (this.assignCount == null ? 0 : this.assignCount) + 1;
    }

    /**
     * 设置为可用状态（回收时使用）
     */
    public void setAvailable() {
        this.poolStatus = "available";
    }

    /**
     * 设置为锁定状态
     */
    public void setLocked() {
        this.poolStatus = "locked";
    }
}
