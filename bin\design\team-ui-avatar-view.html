<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团队成员展示 - 头像墙视图</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .team-container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        .team-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        .team-header h4 {
            margin: 0;
            color: #2c3e50;
        }
        .avatar-wall {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .avatar-item {
            position: relative;
            cursor: pointer;
        }
        .avatar-img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .avatar-item:hover .avatar-img {
            transform: scale(1.1);
            z-index: 2;
        }
        .avatar-role-badge {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            border: 2px solid white;
        }
        .leader-badge { background-color: #ffc107; }
        .admin-badge { background-color: #0d6efd; }
        .member-badge { background-color: #6c757d; }
        
        .add-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px dashed #adb5bd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #adb5bd;
            font-size: 20px;
            transition: all 0.3s ease;
        }
        .add-avatar:hover {
            background-color: #e9ecef;
            color: #495057;
            border-color: #495057;
        }
        /* Tooltip styles */
        [data-bs-toggle="tooltip"] {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="team-container">
            <div class="team-header">
                <h4>团队成员 (头像墙视图)</h4>
            </div>
            <div class="avatar-wall">
                <!-- Avatar 1: Leader -->
                <div class="avatar-item" data-bs-toggle="tooltip" data-bs-placement="top" 
                     title="王丽 (负责人) - 产品部">
                    <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop" alt="Avatar" class="avatar-img">
                    <span class="avatar-role-badge leader-badge" title="负责人"><i class="bi bi-star-fill"></i></span>
                </div>
                <!-- Avatar 2: Admin -->
                <div class="avatar-item" data-bs-toggle="tooltip" data-bs-placement="top" 
                     title="李强 (管理员) - 技术部">
                    <img src="https://images.unsplash.com/photo-1556157382-97eda2d62296?w=100&h=100&fit=crop" alt="Avatar" class="avatar-img">
                    <span class="avatar-role-badge admin-badge" title="管理员"><i class="bi bi-shield-lock-fill"></i></span>
                </div>
                <!-- Avatar 3: Member -->
                <div class="avatar-item" data-bs-toggle="tooltip" data-bs-placement="top" 
                     title="张伟 (成员) - 市场部">
                    <img src="https://images.unsplash.com/photo-1542744095-291d1f67b221?w=100&h=100&fit=crop" alt="Avatar" class="avatar-img">
                    <span class="avatar-role-badge member-badge" title="成员"><i class="bi bi-person-fill"></i></span>
                </div>
                <!-- Avatar 4: Member -->
                <div class="avatar-item" data-bs-toggle="tooltip" data-bs-placement="top" 
                     title="刘芳 (成员) - 销售部">
                    <img src="https://images.unsplash.com/photo-1589156280159-27698a70f29e?w=100&h=100&fit=crop" alt="Avatar" class="avatar-img">
                    <span class="avatar-role-badge member-badge" title="成员"><i class="bi bi-person-fill"></i></span>
                </div>
                <!-- Add new member -->
                <a href="#" class="add-avatar" data-bs-toggle="tooltip" data-bs-placement="top" title="添加新成员">
                    <i class="bi bi-plus"></i>
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.7/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    </script>
</body>
</html>
