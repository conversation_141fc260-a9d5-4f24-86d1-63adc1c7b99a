import type { CommonFilterConfig } from '@/types';

// 筛选类型选项
const filterOptions = [
  { label: '全部线索', value: 'all' },
  { label: '我负责的', value: 'mine' },
  { label: '下属负责的', value: 'subordinate' },
  { label: '我关注的线索', value: 'following' } ,
  { label: '以转化的', value: 'following' }
];

// 线索筛选配置
export const leadsFilterConfig: CommonFilterConfig = {
  search: {
    placeholder: '线索名称/手机/电话',
    width: '240px',
    icon: 'Search',
    debounceTime: 300
  },
  filter: {
    label: '显示：',
    options: filterOptions,
    buttonStyle: true,
    size: 'default'
  }
}; 