<template>
    <div class="payment-attachments-tab">
        <div class="attachments-header">
            <h3>附件管理</h3>
            <el-upload
                class="upload-demo"
                action="#"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :limit="10"
                :on-exceed="handleExceed"
                :file-list="fileList"
                :auto-upload="false"
            >
                <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传附件
                </el-button>
                <template #tip>
                    <div class="el-upload__tip">
                        支持jpg/png/pdf/doc/docx/xls/xlsx文件，且不超过10MB
                    </div>
                </template>
            </el-upload>
        </div>

        <div class="attachments-list">
            <el-table :data="attachments" style="width: 100%" border>
                <el-table-column prop="name" label="文件名" min-width="200">
                    <template #default="scope">
                        <div class="file-info">
                            <el-icon class="file-icon">
                                <component :is="getFileIcon(scope.row.type)" />
                            </el-icon>
                            <span class="file-name">{{ scope.row.name }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="size" label="文件大小" width="120">
                    <template #default="scope">
                        {{ formatFileSize(scope.row.size) }}
                    </template>
                </el-table-column>
                <el-table-column prop="uploadTime" label="上传时间" width="180" />
                <el-table-column prop="uploader" label="上传人" width="120" />
                <el-table-column label="操作" width="150">
                    <template #default="scope">
                        <el-button link type="primary" size="small" @click="downloadFile(scope.row)">
                            <el-icon><Download /></el-icon>
                            下载
                        </el-button>
                        <el-button link type="danger" size="small" @click="deleteFile(scope.row)">
                            <el-icon><Delete /></el-icon>
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 预览对话框 -->
        <el-dialog v-model="previewVisible" title="文件预览" width="80%">
            <div class="preview-content">
                <img v-if="isImage(previewFile?.type)" :src="previewFile?.url" style="max-width: 100%; max-height: 500px;" />
                <iframe v-else-if="isPdf(previewFile?.type)" :src="previewFile?.url" style="width: 100%; height: 500px; border: none;"></iframe>
                <div v-else class="unsupported-preview">
                    <el-icon size="48"><Document /></el-icon>
                    <p>此文件类型不支持预览，请下载后查看</p>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Upload, Download, Delete, Document, Picture, Folder } from '@element-plus/icons-vue';

interface Props {
    entityData: any;
}

defineProps<Props>();

const previewVisible = ref(false);
const previewFile = ref<any>(null);

// 模拟文件列表
const fileList = ref([]);
const attachments = ref([
    {
        id: 1,
        name: '回款凭证.pdf',
        type: 'pdf',
        size: 1024000,
        uploadTime: '2024-02-21 10:30',
        uploader: '张三',
        url: '#'
    },
    {
        id: 2,
        name: '银行转账截图.jpg',
        type: 'jpg',
        size: 512000,
        uploadTime: '2024-02-21 14:20',
        uploader: '李四',
        url: '#'
    },
    {
        id: 3,
        name: '合同扫描件.pdf',
        type: 'pdf',
        size: 2048000,
        uploadTime: '2024-02-21 16:45',
        uploader: '王五',
        url: '#'
    }
]);

// 获取文件图标
const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return Picture;
        case 'pdf':
        case 'doc':
        case 'docx':
        case 'xls':
        case 'xlsx':
            return Document;
        default:
            return Folder;
    }
};

// 格式化文件大小
const formatFileSize = (size: number) => {
    if (size < 1024) {
        return size + ' B';
    } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB';
    } else {
        return (size / (1024 * 1024)).toFixed(1) + ' MB';
    }
};

// 判断是否为图片
const isImage = (type: string) => {
    return ['jpg', 'jpeg', 'png', 'gif'].includes(type?.toLowerCase());
};

// 判断是否为PDF
const isPdf = (type: string) => {
    return type?.toLowerCase() === 'pdf';
};

// 文件预览
const handlePreview = (file: any) => {
    previewFile.value = file;
    previewVisible.value = true;
};

// 文件移除
const handleRemove = (file: any, fileList: any[]) => {
    console.log('移除文件:', file, fileList);
};

// 移除前确认
const beforeRemove = (file: any) => {
    return ElMessageBox.confirm(`确定移除文件 ${file.name}？`);
};

// 文件数量超出限制
const handleExceed = (files: any[], fileList: any[]) => {
    ElMessage.warning(`当前限制选择 10 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
};

// 下载文件
const downloadFile = (file: any) => {
    // 实际项目中应该调用下载API
    ElMessage.success(`开始下载文件: ${file.name}`);
};

// 删除文件
const deleteFile = (file: any) => {
    ElMessageBox.confirm(`确定删除文件 "${file.name}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        const index = attachments.value.findIndex(item => item.id === file.id);
        if (index > -1) {
            attachments.value.splice(index, 1);
            ElMessage.success('删除成功');
        }
    }).catch(() => {
        ElMessage.info('已取消删除');
    });
};
</script>

<style scoped>
.payment-attachments-tab {
    padding: 20px;
}

.attachments-header {
    margin-bottom: 20px;
}

.attachments-header h3 {
    margin: 0 0 16px 0;
    color: #303133;
}

.attachments-list {
    margin-top: 20px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-icon {
    color: #409EFF;
}

.file-name {
    color: #303133;
}

.preview-content {
    text-align: center;
}

.unsupported-preview {
    padding: 40px;
    color: #909399;
}

.unsupported-preview p {
    margin-top: 16px;
    font-size: 14px;
}

.upload-demo {
    margin-bottom: 20px;
}
</style>