<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票和报价单模块数据库设计详细方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #e74c3c;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #c0392b;
            margin-top: 25px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #e74c3c;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 13px;
        }
        .sql-block {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 5px 0;
        }
        .relationship {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .relationship h3 {
            color: white;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 发票和报价单模块数据库设计详细方案</h1>
        
        <div class="highlight">
            <strong>📋 设计原则：</strong>基于现有CRM系统的客户、联系人关系模型，确保数据一致性和业务逻辑的完整性。
        </div>

        <h2>📊 一、发票模块数据库设计</h2>
        
        <h3>1.1 发票主表 (crm_invoices)</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>长度</th>
                <th>说明</th>
                <th>约束</th>
                <th>默认值</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
                <td>-</td>
            </tr>
            <tr>
                <td>invoice_no</td>
                <td>VARCHAR</td>
                <td>50</td>
                <td>发票编号</td>
                <td>NOT NULL, UNIQUE</td>
                <td>-</td>
            </tr>
            <tr>
                <td>invoice_title</td>
                <td>VARCHAR</td>
                <td>200</td>
                <td>发票抬头</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>invoice_type</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>发票类型</td>
                <td>NOT NULL</td>
                <td>'special'</td>
            </tr>
            <tr>
                <td>customer_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>客户ID</td>
                <td>NOT NULL, FK</td>
                <td>-</td>
            </tr>
            <tr>
                <td>customer_name</td>
                <td>VARCHAR</td>
                <td>100</td>
                <td>客户名称（冗余）</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>contact_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>联系人ID</td>
                <td>NULL, FK</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>contact_name</td>
                <td>VARCHAR</td>
                <td>100</td>
                <td>联系人姓名（冗余）</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>quotation_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>关联报价单ID</td>
                <td>NULL, FK</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>contract_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>关联合同ID</td>
                <td>NULL, FK</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>contract_no</td>
                <td>VARCHAR</td>
                <td>50</td>
                <td>合同编号（冗余）</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>responsible_person_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>负责人ID</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>tax_rate</td>
                <td>DECIMAL</td>
                <td>5,4</td>
                <td>税率</td>
                <td>NOT NULL</td>
                <td>0.1300</td>
            </tr>
            <tr>
                <td>amount_excluding_tax</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>不含税金额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>tax_amount</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>税额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>amount_including_tax</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>含税金额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>发票状态</td>
                <td>NOT NULL</td>
                <td>'draft'</td>
            </tr>
            <tr>
                <td>approval_status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>审批状态</td>
                <td>NOT NULL</td>
                <td>'pending'</td>
            </tr>
            <tr>
                <td>process_instance_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>流程实例ID</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>process_status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>流程状态</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>current_task_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>当前任务ID</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>current_assignee</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>当前处理人</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>issue_date</td>
                <td>DATE</td>
                <td>-</td>
                <td>开票日期</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>due_date</td>
                <td>DATE</td>
                <td>-</td>
                <td>到期日期</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>remarks</td>
                <td>TEXT</td>
                <td>-</td>
                <td>备注</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>del_flag</td>
                <td>CHAR</td>
                <td>1</td>
                <td>删除标志</td>
                <td>NOT NULL</td>
                <td>'0'</td>
            </tr>
            <tr>
                <td>create_by</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>创建者</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>create_time</td>
                <td>DATETIME</td>
                <td>-</td>
                <td>创建时间</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>update_by</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>更新者</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>update_time</td>
                <td>DATETIME</td>
                <td>-</td>
                <td>更新时间</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
        </table>

        <h3>1.2 发票明细表 (crm_invoice_items)</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>长度</th>
                <th>说明</th>
                <th>约束</th>
                <th>默认值</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
                <td>-</td>
            </tr>
            <tr>
                <td>invoice_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>发票ID</td>
                <td>NOT NULL, FK</td>
                <td>-</td>
            </tr>
            <tr>
                <td>item_name</td>
                <td>VARCHAR</td>
                <td>200</td>
                <td>项目名称</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>specification</td>
                <td>VARCHAR</td>
                <td>500</td>
                <td>规格型号</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>unit</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>单位</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>quantity</td>
                <td>DECIMAL</td>
                <td>10,2</td>
                <td>数量</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>unit_price</td>
                <td>DECIMAL</td>
                <td>10,2</td>
                <td>单价</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>amount</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>金额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>tax_rate</td>
                <td>DECIMAL</td>
                <td>5,4</td>
                <td>税率</td>
                <td>NOT NULL</td>
                <td>0.1300</td>
            </tr>
            <tr>
                <td>tax_amount</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>税额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>sort_order</td>
                <td>INT</td>
                <td>11</td>
                <td>排序</td>
                <td>NOT NULL</td>
                <td>0</td>
            </tr>
        </table>

        <h2>📋 二、报价单模块数据库设计（完整版）</h2>
        
        <h3>2.1 报价单主表 (crm_quotations) - 完整字段</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>长度</th>
                <th>说明</th>
                <th>约束</th>
                <th>默认值</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
                <td>-</td>
            </tr>
            <tr>
                <td>quotation_no</td>
                <td>VARCHAR</td>
                <td>50</td>
                <td>报价单编号</td>
                <td>NOT NULL, UNIQUE</td>
                <td>-</td>
            </tr>
            <tr>
                <td>quotation_name</td>
                <td>VARCHAR</td>
                <td>200</td>
                <td>报价单名称</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>customer_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>客户ID</td>
                <td>NOT NULL, FK</td>
                <td>-</td>
            </tr>
            <tr>
                <td>customer_name</td>
                <td>VARCHAR</td>
                <td>100</td>
                <td>客户名称（冗余）</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>contact_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>联系人ID</td>
                <td>NOT NULL, FK</td>
                <td>-</td>
            </tr>
            <tr>
                <td>contact_name</td>
                <td>VARCHAR</td>
                <td>100</td>
                <td>联系人姓名（冗余）</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>responsible_person_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>负责人ID</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>total_amount</td>
                <td>DECIMAL</td>
                <td>15,2</td>
                <td>报价总金额</td>
                <td>NOT NULL</td>
                <td>0.00</td>
            </tr>
            <tr>
                <td>currency</td>
                <td>VARCHAR</td>
                <td>10</td>
                <td>币种</td>
                <td>NOT NULL</td>
                <td>'CNY'</td>
            </tr>
            <tr>
                <td>status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>状态</td>
                <td>NOT NULL</td>
                <td>'draft'</td>
            </tr>
            <tr>
                <td>approval_status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>审批状态</td>
                <td>NOT NULL</td>
                <td>'pending'</td>
            </tr>
            <tr>
                <td>process_instance_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>流程实例ID</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>process_status</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>流程状态</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>current_task_id</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>当前任务ID</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>current_assignee</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>当前处理人</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>valid_until</td>
                <td>DATE</td>
                <td>-</td>
                <td>有效期至</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>quotation_date</td>
                <td>DATE</td>
                <td>-</td>
                <td>报价日期</td>
                <td>NOT NULL</td>
                <td>CURRENT_DATE</td>
            </tr>
            <tr>
                <td>delivery_terms</td>
                <td>VARCHAR</td>
                <td>200</td>
                <td>交货条款</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>payment_terms</td>
                <td>VARCHAR</td>
                <td>200</td>
                <td>付款条款</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>remarks</td>
                <td>TEXT</td>
                <td>-</td>
                <td>备注</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>del_flag</td>
                <td>CHAR</td>
                <td>1</td>
                <td>删除标志</td>
                <td>NOT NULL</td>
                <td>'0'</td>
            </tr>
            <tr>
                <td>create_by</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>创建者</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>create_time</td>
                <td>DATETIME</td>
                <td>-</td>
                <td>创建时间</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>update_by</td>
                <td>VARCHAR</td>
                <td>64</td>
                <td>更新者</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>update_time</td>
                <td>DATETIME</td>
                <td>-</td>
                <td>更新时间</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
        </table>

        <div class="info">
            <strong>💡 状态说明：</strong>
            <ul>
                <li><strong>status:</strong> draft(草稿), submitted(已提交), approved(已审批), rejected(已驳回), cancelled(已取消)</li>
                <li><strong>approval_status:</strong> pending(待审批), approved(审批通过), rejected(审批驳回)</li>
                <li><strong>process_status:</strong> running(流程运行中), completed(流程完成), terminated(流程终止)</li>
            </ul>
        </div>

        <h3>2.2 报价单明细表 (crm_quotation_items) - 完整字段</h3>
        <div class="info">
            <strong>📝 说明：</strong>报价单明细表字段设计与发票明细表类似，主要用于存储报价单的具体项目信息。
        </div>

        <h2>🔗 三、关联关系设计</h2>
        
        <div class="relationship">
            <h3>3.1 外键关系</h3>
            <ul>
                <li><strong>crm_quotations.customer_id</strong> → crm_business_customers.id</li>
                <li><strong>crm_quotations.contact_id</strong> → crm_business_contacts.id</li>
                <li><strong>crm_invoices.customer_id</strong> → crm_business_customers.id</li>
                <li><strong>crm_invoices.contact_id</strong> → crm_business_contacts.id</li>
                <li><strong>crm_invoices.quotation_id</strong> → crm_quotations.id</li>
                <li><strong>crm_quotation_items.quotation_id</strong> → crm_quotations.id</li>
                <li><strong>crm_invoice_items.invoice_id</strong> → crm_invoices.id</li>
            </ul>
        </div>

        <h3>3.2 索引设计</h3>
        <div class="code">
-- 报价单表索引
CREATE INDEX idx_quotations_customer ON crm_quotations(customer_id);
CREATE INDEX idx_quotations_contact ON crm_quotations(contact_id);
CREATE INDEX idx_quotations_responsible ON crm_quotations(responsible_person_id);
CREATE INDEX idx_quotations_status ON crm_quotations(status);
CREATE INDEX idx_quotations_date ON crm_quotations(quotation_date);
CREATE INDEX idx_quotations_process ON crm_quotations(process_instance_id);

-- 发票表索引
CREATE INDEX idx_invoices_customer ON crm_invoices(customer_id);
CREATE INDEX idx_invoices_contact ON crm_invoices(contact_id);
CREATE INDEX idx_invoices_quotation ON crm_invoices(quotation_id);
CREATE INDEX idx_invoices_responsible ON crm_invoices(responsible_person_id);
CREATE INDEX idx_invoices_status ON crm_invoices(status);
CREATE INDEX idx_invoices_issue_date ON crm_invoices(issue_date);

-- 明细表索引
CREATE INDEX idx_quotation_items_quotation ON crm_quotation_items(quotation_id);
CREATE INDEX idx_invoice_items_invoice ON crm_invoice_items(invoice_id);
        </div>

        <h2>📊 四、数据字典</h2>
        
        <h3>4.1 发票类型 (invoice_type)</h3>
        <table>
            <tr>
                <th>值</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>special</td>
                <td>增值税专用发票</td>
            </tr>
            <tr>
                <td>ordinary</td>
                <td>增值税普通发票</td>
            </tr>
            <tr>
                <td>electronic</td>
                <td>电子发票</td>
            </tr>
        </table>

        <h3>4.2 发票状态 (status)</h3>
        <table>
            <tr>
                <th>值</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>pending</td>
                <td>待开票</td>
            </tr>
            <tr>
                <td>issued</td>
                <td>已开票</td>
            </tr>
            <tr>
                <td>cancelled</td>
                <td>已作废</td>
            </tr>
            <tr>
                <td>returned</td>
                <td>已退票</td>
            </tr>
        </table>

        <div class="success">
            <strong>✅ 设计优势：</strong>
            <ul>
                <li>充分利用现有客户、联系人关系模型</li>
                <li>支持报价单到发票的完整业务流程</li>
                <li>集成Activiti工作流，支持审批流程</li>
                <li>数据冗余设计，提高查询性能</li>
                <li>完善的索引设计，优化查询效率</li>
            </ul>
        </div>

        <h2>📎 五、发票附件表设计</h2>

        <h3>5.1 发票附件表 (crm_invoice_attachments)</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>长度</th>
                <th>说明</th>
                <th>约束</th>
                <th>默认值</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
                <td>-</td>
            </tr>
            <tr>
                <td>invoice_id</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>发票ID</td>
                <td>NOT NULL, FK</td>
                <td>-</td>
            </tr>
            <tr>
                <td>file_name</td>
                <td>VARCHAR</td>
                <td>255</td>
                <td>文件名称</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>file_original_name</td>
                <td>VARCHAR</td>
                <td>255</td>
                <td>原始文件名</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>file_path</td>
                <td>VARCHAR</td>
                <td>500</td>
                <td>文件存储路径</td>
                <td>NOT NULL</td>
                <td>-</td>
            </tr>
            <tr>
                <td>file_url</td>
                <td>VARCHAR</td>
                <td>500</td>
                <td>文件访问URL</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>file_size</td>
                <td>BIGINT</td>
                <td>20</td>
                <td>文件大小（字节）</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>file_type</td>
                <td>VARCHAR</td>
                <td>50</td>
                <td>文件类型（MIME）</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>attachment_type</td>
                <td>VARCHAR</td>
                <td>20</td>
                <td>附件类型</td>
                <td>NOT NULL</td>
                <td>'invoice_image'</td>
            </tr>
            <tr>
                <td>is_main</td>
                <td>CHAR</td>
                <td>1</td>
                <td>是否主要附件</td>
                <td>NULL</td>
                <td>'0'</td>
            </tr>
            <tr>
                <td>description</td>
                <td>VARCHAR</td>
                <td>500</td>
                <td>附件描述</td>
                <td>NULL</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>sort_order</td>
                <td>INT</td>
                <td>11</td>
                <td>排序</td>
                <td>NOT NULL</td>
                <td>0</td>
            </tr>
        </table>

        <h3>5.2 附件类型说明</h3>
        <table>
            <tr>
                <th>值</th>
                <th>说明</th>
                <th>用途</th>
            </tr>
            <tr>
                <td>invoice_image</td>
                <td>发票图片</td>
                <td>发票扫描件或照片</td>
            </tr>
            <tr>
                <td>contract_scan</td>
                <td>合同扫描件</td>
                <td>相关合同文件</td>
            </tr>
            <tr>
                <td>other</td>
                <td>其他附件</td>
                <td>其他相关文件</td>
            </tr>
        </table>

        <h2>🔗 六、关联关系更新</h2>

        <div class="relationship">
            <h3>6.1 新增外键关系</h3>
            <ul>
                <li><strong>crm_invoices.contract_id</strong> → crm_contracts.id</li>
                <li><strong>crm_invoice_attachments.invoice_id</strong> → crm_invoices.id</li>
            </ul>
        </div>

        <h3>6.2 更新索引设计</h3>
        <div class="code">
-- 发票表新增索引
CREATE INDEX idx_invoices_contract ON crm_invoices(contract_id);
CREATE INDEX idx_invoices_approval_status ON crm_invoices(approval_status);
CREATE INDEX idx_invoices_process ON crm_invoices(process_instance_id);

-- 附件表索引
CREATE INDEX idx_invoice_attachments_invoice ON crm_invoice_attachments(invoice_id);
CREATE INDEX idx_invoice_attachments_type ON crm_invoice_attachments(attachment_type);
CREATE INDEX idx_invoice_attachments_main ON crm_invoice_attachments(is_main);
        </div>

        <h2>📊 七、更新数据字典</h2>

        <h3>7.1 发票状态更新</h3>
        <table>
            <tr>
                <th>值</th>
                <th>说明</th>
                <th>变更</th>
            </tr>
            <tr>
                <td>draft</td>
                <td>草稿</td>
                <td>新增</td>
            </tr>
            <tr>
                <td>submitted</td>
                <td>已提交</td>
                <td>新增</td>
            </tr>
            <tr>
                <td>approved</td>
                <td>已审批</td>
                <td>新增</td>
            </tr>
            <tr>
                <td>rejected</td>
                <td>已驳回</td>
                <td>新增</td>
            </tr>
            <tr>
                <td>issued</td>
                <td>已开票</td>
                <td>保留</td>
            </tr>
            <tr>
                <td>cancelled</td>
                <td>已作废</td>
                <td>保留</td>
            </tr>
        </table>

        <div class="success">
            <strong>✅ 完善后的设计优势：</strong>
            <ul>
                <li>发票支持完整的审批流程，状态管理更清晰</li>
                <li>合同关联功能，业务流程更完整</li>
                <li>完善的附件管理系统，支持多种文件类型</li>
                <li>工作流字段与Activiti引擎深度集成</li>
                <li>灵活的附件分类和排序机制</li>
            </ul>
        </div>

        <div class="warning">
            <strong>⚠️ 注意事项：</strong>
            <ul>
                <li>客户和联系人必须通过现有的关联关系表验证</li>
                <li>报价单的客户和联系人必须匹配</li>
                <li>金额计算需要考虑税率和精度问题</li>
                <li>工作流字段需要与Activiti引擎保持同步</li>
                <li>合同关联需要验证合同的有效性和状态</li>
                <li>附件上传需要配置存储路径和安全策略</li>
                <li>文件类型和大小需要进行限制和验证</li>
            </ul>
        </div>
    </div>
</body>
</html>
