import request from '@/utils/request'

// 启动审批流程
export function startApproval(planId, variables) {
  return request({
    url: '/crm/paymentPlan/workflow/start/' + planId,
    method: 'post',
    data: variables
  })
}

// 审批通过
export function approveTask(data) {
  return request({
    url: '/crm/paymentPlan/workflow/approve',
    method: 'post',
    params: data
  })
}

// 审批拒绝
export function rejectTask(data) {
  return request({
    url: '/crm/paymentPlan/workflow/reject',
    method: 'post',
    params: data
  })
}

// 获取我的待办任务
export function getPendingTasks(query) {
  return request({
    url: '/crm/paymentPlan/workflow/pending',
    method: 'get',
    params: query
  })
}

// 获取流程任务历史
export function getTaskHistory(processInstanceId) {
  return request({
    url: '/crm/paymentPlan/workflow/history/' + processInstanceId,
    method: 'get'
  })
}

// 获取当前任务
export function getCurrentTask(processInstanceId) {
  return request({
    url: '/crm/paymentPlan/workflow/current/' + processInstanceId,
    method: 'get'
  })
}

// 撤销流程
export function cancelProcess(data) {
  return request({
    url: '/crm/paymentPlan/workflow/cancel',
    method: 'post',
    params: data
  })
}

// 根据计划ID获取流程信息
export function getProcessByPlanId(planId) {
  return request({
    url: '/crm/paymentPlan/workflow/process/' + planId,
    method: 'get'
  })
}

// 检查计划审批状态
export function getApprovalStatus(planId) {
  return request({
    url: '/crm/paymentPlan/workflow/status/' + planId,
    method: 'get'
  })
}

// 获取流程定义列表
export function getProcessDefinitions() {
  return request({
    url: '/crm/paymentPlan/workflow/definitions',
    method: 'get'
  })
}

// 完成任务 (通用接口)
export function completeTask(taskId, variables, comment) {
  return request({
    url: '/crm/paymentPlan/workflow/complete',
    method: 'post',
    params: {
      taskId: taskId,
      comment: comment
    },
    data: variables
  })
}

// 获取流程变量
export function getProcessVariables(processInstanceId) {
  return request({
    url: '/crm/paymentPlan/workflow/variables/' + processInstanceId,
    method: 'get'
  })
}

// 设置流程变量
export function setProcessVariables(processInstanceId, variables) {
  return request({
    url: '/crm/paymentPlan/workflow/variables/' + processInstanceId,
    method: 'post',
    data: variables
  })
}