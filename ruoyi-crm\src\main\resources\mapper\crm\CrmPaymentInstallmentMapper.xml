<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPaymentInstallmentMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmPaymentInstallment" id="CrmPaymentInstallmentResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="installmentNumber"    column="installment_number"    />
        <result property="installmentName"    column="installment_name"    />
        <result property="installmentAmount"    column="installment_amount"    />
        <result property="installmentPercentage"    column="installment_percentage"    />
        <result property="plannedDate"    column="planned_date"    />
        <result property="actualDate"    column="actual_date"    />
        <result property="actualAmount"    column="actual_amount"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="penaltyAmount"    column="penalty_amount"    />
        <result property="paymentVoucher"    column="payment_voucher"    />
        <result property="installmentStatus"    column="installment_status"    />
        <result property="remark"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCrmPaymentInstallmentVo">
        select id, plan_id, installment_number, installment_name, installment_amount, installment_percentage, 
               planned_date, actual_date, actual_amount, overdue_days, penalty_amount, payment_voucher, 
               installment_status, remarks, del_flag, create_by, create_time, update_by, update_time 
        from crm_payment_installment
    </sql>

    <select id="selectCrmPaymentInstallmentList" parameterType="com.ruoyi.common.domain.entity.CrmPaymentInstallment" resultMap="CrmPaymentInstallmentResult">
        <include refid="selectCrmPaymentInstallmentVo"/>
        <where>
            del_flag = '0'
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="installmentName != null  and installmentName != ''"> and installment_name like concat('%', #{installmentName}, '%')</if>
            <if test="installmentStatus != null  and installmentStatus != ''"> and installment_status = #{installmentStatus}</if>
            <if test="plannedDate != null "> and planned_date = #{plannedDate}</if>
        </where>
        order by installment_number
    </select>
    
    <select id="selectCrmPaymentInstallmentById" parameterType="Long" resultMap="CrmPaymentInstallmentResult">
        <include refid="selectCrmPaymentInstallmentVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectInstallmentsByPlanId" parameterType="Long" resultMap="CrmPaymentInstallmentResult">
        <include refid="selectCrmPaymentInstallmentVo"/>
        where plan_id = #{planId} and del_flag = '0'
        order by installment_number
    </select>
        
    <insert id="insertCrmPaymentInstallment" parameterType="com.ruoyi.common.domain.entity.CrmPaymentInstallment" useGeneratedKeys="true" keyProperty="id">
        insert into crm_payment_installment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="installmentNumber != null">installment_number,</if>
            <if test="installmentName != null">installment_name,</if>
            <if test="installmentAmount != null">installment_amount,</if>
            <if test="installmentPercentage != null">installment_percentage,</if>
            <if test="plannedDate != null">planned_date,</if>
            <if test="actualDate != null">actual_date,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="penaltyAmount != null">penalty_amount,</if>
            <if test="paymentVoucher != null">payment_voucher,</if>
            <if test="installmentStatus != null">installment_status,</if>
            <if test="remark != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="installmentNumber != null">#{installmentNumber},</if>
            <if test="installmentName != null">#{installmentName},</if>
            <if test="installmentAmount != null">#{installmentAmount},</if>
            <if test="installmentPercentage != null">#{installmentPercentage},</if>
            <if test="plannedDate != null">#{plannedDate},</if>
            <if test="actualDate != null">#{actualDate},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="penaltyAmount != null">#{penaltyAmount},</if>
            <if test="paymentVoucher != null">#{paymentVoucher},</if>
            <if test="installmentStatus != null">#{installmentStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <insert id="batchInsertInstallments" parameterType="java.util.List">
        insert into crm_payment_installment(plan_id, installment_number, installment_name, installment_amount, 
                                          installment_percentage, planned_date, installment_status, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.installmentNumber}, #{item.installmentName}, #{item.installmentAmount}, 
             #{item.installmentPercentage}, #{item.plannedDate}, #{item.installmentStatus}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateCrmPaymentInstallment" parameterType="com.ruoyi.common.domain.entity.CrmPaymentInstallment">
        update crm_payment_installment
        <trim prefix="SET" suffixOverrides=",">
            <if test="installmentName != null">installment_name = #{installmentName},</if>
            <if test="installmentAmount != null">installment_amount = #{installmentAmount},</if>
            <if test="installmentPercentage != null">installment_percentage = #{installmentPercentage},</if>
            <if test="plannedDate != null">planned_date = #{plannedDate},</if>
            <if test="actualDate != null">actual_date = #{actualDate},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="penaltyAmount != null">penalty_amount = #{penaltyAmount},</if>
            <if test="paymentVoucher != null">payment_voucher = #{paymentVoucher},</if>
            <if test="installmentStatus != null">installment_status = #{installmentStatus},</if>
            <if test="remark != null">remarks = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateInstallmentStatus">
        update crm_payment_installment
        set installment_status = #{status},
            update_time = sysdate()
        where id = #{id}
    </update>

    <update id="updateOverdueDays">
        update crm_payment_installment
        set overdue_days = datediff(curdate(), planned_date)
        where installment_status in ('待回款', '部分回款')
        and planned_date &lt; curdate()
        and del_flag = '0'
    </update>

    <delete id="deleteCrmPaymentInstallmentById" parameterType="Long">
        update crm_payment_installment set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmPaymentInstallmentByIds" parameterType="String">
        update crm_payment_installment set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInstallmentsByPlanId" parameterType="Long">
        update crm_payment_installment set del_flag = '2' where plan_id = #{planId}
    </delete>
</mapper>