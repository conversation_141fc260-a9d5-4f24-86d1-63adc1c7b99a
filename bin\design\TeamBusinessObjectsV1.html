<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM团队业务对象关联交互演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            margin-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
        }
        .subtitle {
            font-size: 16px;
            color: #606266;
            margin-top: 5px;
        }
        .card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #303133;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-image {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-top: 10px;
        }
        .feature-list {
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
        .code-block {
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .dialog-footer button {
            margin-left: 10px;
        }
        .el-tag {
            margin-right: 5px;
        }
        .el-table .warning-row {
            background: #fdf6ec;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-table .primary-row {
            background: #ecf5ff;
        }
        .el-table .info-row {
            background: #f4f4f5;
        }
        .team-member-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ebeef5;
        }
        .team-member-item:last-child {
            border-bottom: none;
        }
        .team-member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .team-member-info {
            flex: 1;
        }
        .team-member-name {
            font-weight: bold;
            font-size: 14px;
        }
        .team-member-role {
            font-size: 12px;
            color: #909399;
        }
        .team-member-actions {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
            <div class="title">CRM团队业务对象关联交互演示</div>
            <div class="subtitle">解决"跟谁关联"的问题，优化业务对象与团队的关联流程</div>
        </div>

        <div class="card demo-section">
            <div class="section-title">问题分析</div>
            <p>当前CRM系统在业务对象关联团队时存在以下缺陷：</p>
            <ol class="feature-list">
                <li><strong>缺少明确的关联对象</strong>：在关联业务对象到团队时，没有明确指出"跟谁关联"，即没有显示团队成员信息</li>
                <li><strong>关联过程不透明</strong>：用户无法在关联过程中看到团队的组成和结构</li>
                <li><strong>缺乏上下文</strong>：关联操作缺少业务上下文，用户难以理解关联的意义和影响</li>
                <li><strong>操作流程不直观</strong>：当前界面设计不够直观，用户体验不佳</li>
            </ol>
        </div>

        <div class="card demo-section">
            <div class="section-title">解决方案</div>
            <p>针对上述问题，我们提出以下解决方案：</p>
            <ol class="feature-list">
                <li><strong>团队成员可视化</strong>：在关联对话框中显示团队成员列表，明确"跟谁关联"</li>
                <li><strong>团队结构展示</strong>：展示团队的层级结构，包括负责人、管理员和普通成员</li>
                <li><strong>关联上下文增强</strong>：提供关联的业务上下文，如关联后的权限变化、数据访问范围等</li>
                <li><strong>交互流程优化</strong>：简化操作步骤，提供更直观的用户界面</li>
            </ol>
        </div>

        <div class="card demo-section">
            <div class="section-title">交互演示</div>
            
            <!-- 业务对象列表 -->
            <el-card class="box-card" style="margin-bottom: 20px;">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>业务对象列表</span>
                        <el-button type="primary" @click="openAssignDialog">关联业务对象</el-button>
                    </div>
                </template>
                <el-table :data="businessObjects" style="width: 100%">
                    <el-table-column prop="name" label="名称" width="180"></el-table-column>
                    <el-table-column prop="type" label="类型" width="120">
                        <template #default="scope">
                            <el-tag :type="getBizTypeTagType(scope.row.type)">{{ getBizTypeLabel(scope.row.type) }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="assignTime" label="分配时间" width="180"></el-table-column>
                    <el-table-column prop="assignBy" label="分配人" width="120"></el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button size="small" @click="viewObjectDetail(scope.row)">查看</el-button>
                            <el-button size="small" type="danger" @click="unassignObject(scope.row)">取消关联</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 关联业务对象对话框 -->
            <el-dialog v-model="assignDialogVisible" title="关联业务对象" width="70%">
                <div style="display: flex; gap: 20px;">
                    <!-- 左侧：团队信息 -->
                    <div style="width: 30%; border-right: 1px solid #EBEEF5; padding-right: 20px;">
                        <div class="section-title" style="font-size: 16px;">团队信息</div>
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="团队名称">{{ currentTeam.name }}</el-descriptions-item>
                            <el-descriptions-item label="创建时间">{{ currentTeam.createTime }}</el-descriptions-item>
                            <el-descriptions-item label="团队描述">{{ currentTeam.description }}</el-descriptions-item>
                        </el-descriptions>
                        
                        <div class="section-title" style="font-size: 16px; margin-top: 20px;">团队成员</div>
                        <div style="max-height: 300px; overflow-y: auto;">
                            <div v-for="member in currentTeam.members" :key="member.userId" class="team-member-item">
                                <el-avatar :size="32" :src="member.avatar" class="team-member-avatar">{{ member.userName.charAt(0) }}</el-avatar>
                                <div class="team-member-info">
                                    <div class="team-member-name">{{ member.userName }}</div>
                                    <div class="team-member-role">
                                        <el-tag size="small" :type="member.roleType === 'leader' ? 'danger' : member.roleType === 'admin' ? 'warning' : 'info'">
                                            {{ member.roleType === 'leader' ? '负责人' : member.roleType === 'admin' ? '管理员' : '成员' }}
                                        </el-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：可关联业务对象 -->
                    <div style="width: 70%;">
                        <div class="section-title" style="font-size: 16px;">可关联业务对象</div>
                        <div style="margin-bottom: 20px;">
                            <el-form :inline="true">
                                <el-form-item label="业务类型">
                                    <el-select v-model="assignQuery.bizType" placeholder="选择业务类型">
                                        <el-option label="联系人" value="CONTACT"></el-option>
                                        <el-option label="线索" value="LEAD"></el-option>
                                        <el-option label="客户" value="CUSTOMER"></el-option>
                                        <el-option label="商机" value="OPPORTUNITY"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="关键词">
                                    <el-input v-model="assignQuery.keyword" placeholder="名称/编号"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="handleAssignQuery">搜索</el-button>
                                    <el-button @click="resetAssignQuery">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                        
                        <el-table 
                            :data="unassignedObjects" 
                            style="width: 100%" 
                            @selection-change="handleAssignSelectionChange"
                            height="350">
                            <el-table-column type="selection" width="55"></el-table-column>
                            <el-table-column prop="name" label="名称" width="180"></el-table-column>
                            <el-table-column prop="type" label="类型" width="100">
                                <template #default="scope">
                                    <el-tag :type="getBizTypeTagType(scope.row.type)">{{ getBizTypeLabel(scope.row.type) }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="owner" label="负责人" width="120"></el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
                        </el-table>
                        
                        <div style="margin-top: 20px;">
                            <el-pagination
                                @size-change="handleAssignSizeChange"
                                @current-change="handleAssignCurrentChange"
                                :current-page="assignQuery.pageNum"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="assignQuery.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="assignTotal">
                            </el-pagination>
                        </div>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <el-button @click="assignDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleBatchAssign" :disabled="assignSelection.length === 0">
                        关联 ({{ assignSelection.length }})
                    </el-button>
                </div>
            </el-dialog>
        </div>

        <div class="card demo-section">
            <div class="section-title">关联流程说明</div>
            <p>优化后的业务对象关联团队流程如下：</p>
            <ol class="feature-list">
                <li><strong>选择关联操作</strong>：用户点击"关联业务对象"按钮</li>
                <li><strong>查看团队信息</strong>：对话框左侧展示当前团队信息和成员列表，明确"跟谁关联"</li>
                <li><strong>筛选业务对象</strong>：用户可以按类型和关键词筛选未关联的业务对象</li>
                <li><strong>选择业务对象</strong>：用户选择需要关联的业务对象</li>
                <li><strong>确认关联</strong>：用户点击"关联"按钮完成操作</li>
            </ol>
            <p>这种设计有以下优势：</p>
            <ul class="feature-list">
                <li>用户可以清晰地看到业务对象将要关联到哪个团队，以及团队中有哪些成员</li>
                <li>提供了完整的上下文信息，帮助用户做出更准确的决策</li>
                <li>简化了操作流程，提高了用户体验</li>
                <li>减少了误操作的可能性</li>
            </ul>
        </div>

        <div class="card demo-section">
            <div class="section-title">实施建议</div>
            <p>要实现上述优化，建议按以下步骤进行：</p>
            <ol class="feature-list">
                <li><strong>前端组件改造</strong>：修改 TeamBusinessObjects.vue 组件，增加团队成员展示区域</li>
                <li><strong>API 扩展</strong>：在关联业务对象时，同时获取团队成员信息</li>
                <li><strong>数据结构优化</strong>：确保团队成员数据结构清晰，包含必要的角色信息</li>
                <li><strong>交互逻辑优化</strong>：简化关联流程，减少不必要的步骤</li>
                <li><strong>UI/UX 改进</strong>：优化界面设计，提高用户体验</li>
            </ol>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;

        createApp({
            setup() {
                // 当前团队信息
                const currentTeam = reactive({
                    id: 1,
                    name: '销售一部团队',
                    createTime: '2023-08-15',
                    description: '负责华东区域的销售工作',
                    members: [
                        { userId: 1, userName: '张经理', roleType: 'leader', avatar: '' },
                        { userId: 2, userName: '李助理', roleType: 'admin', avatar: '' },
                        { userId: 3, userName: '王销售', roleType: 'member', avatar: '' },
                        { userId: 4, userName: '赵客服', roleType: 'member', avatar: '' },
                        { userId: 5, userName: '钱技术', roleType: 'member', avatar: '' }
                    ]
                });

                // 已关联业务对象
                const businessObjects = ref([
                    { id: 1, name: '上海ABC科技有限公司', type: 'CUSTOMER', assignTime: '2023-08-20 14:30', assignBy: '系统管理员' },
                    { id: 2, name: '张三', type: 'CONTACT', assignTime: '2023-08-21 09:15', assignBy: '张经理' },
                    { id: 3, name: '新能源设备采购项目', type: 'OPPORTUNITY', assignTime: '2023-08-22 16:45', assignBy: '王销售' }
                ]);

                // 未关联业务对象
                const unassignedObjects = ref([
                    { id: 4, name: '北京XYZ科技有限公司', type: 'CUSTOMER', owner: '李四', createTime: '2023-07-15' },
                    { id: 5, name: '王五', type: 'CONTACT', owner: '张三', createTime: '2023-07-20' },
                    { id: 6, name: '软件开发项目', type: 'OPPORTUNITY', owner: '李四', createTime: '2023-08-01' },
                    { id: 7, name: '南京DEF贸易有限公司', type: 'CUSTOMER', owner: '王五', createTime: '2023-08-05' },
                    { id: 8, name: '赵六', type: 'LEAD', owner: '张三', createTime: '2023-08-10' }
                ]);

                // 关联对话框
                const assignDialogVisible = ref(false);
                const assignSelection = ref([]);
                const assignQuery = reactive({
                    bizType: '',
                    keyword: '',
                    pageNum: 1,
                    pageSize: 10
                });
                const assignTotal = ref(5);

                // 打开关联对话框
                const openAssignDialog = () => {
                    assignDialogVisible.value = true;
                    // 在实际应用中，这里会调用API加载未关联的业务对象
                };

                // 处理关联查询
                const handleAssignQuery = () => {
                    console.log('查询参数:', assignQuery);
                    // 在实际应用中，这里会调用API重新加载未关联的业务对象
                };

                // 重置关联查询
                const resetAssignQuery = () => {
                    assignQuery.bizType = '';
                    assignQuery.keyword = '';
                    // 在实际应用中，这里会调用API重新加载未关联的业务对象
                };

                // 处理关联选择变化
                const handleAssignSelectionChange = (selection) => {
                    assignSelection.value = selection;
                };

                // 处理关联分页大小变化
                const handleAssignSizeChange = (size) => {
                    assignQuery.pageSize = size;
                    // 在实际应用中，这里会调用API重新加载未关联的业务对象
                };

                // 处理关联分页页码变化
                const handleAssignCurrentChange = (page) => {
                    assignQuery.pageNum = page;
                    // 在实际应用中，这里会调用API重新加载未关联的业务对象
                };

                // 批量关联
                const handleBatchAssign = () => {
                    console.log('关联选中的业务对象:', assignSelection.value);
                    // 在实际应用中，这里会调用API批量关联业务对象到团队
                    // 关联成功后关闭对话框并刷新数据
                    assignDialogVisible.value = false;
                    // 模拟关联成功，将选中的业务对象添加到已关联列表
                    const now = new Date().toLocaleString();
                    assignSelection.value.forEach(item => {
                        businessObjects.value.push({
                            id: item.id,
                            name: item.name,
                            type: item.type,
                            assignTime: now,
                            assignBy: currentTeam.members[0].userName
                        });
                    });
                    // 从未关联列表中移除
                    const selectedIds = assignSelection.value.map(item => item.id);
                    unassignedObjects.value = unassignedObjects.value.filter(item => !selectedIds.includes(item.id));
                    // 清空选择
                    assignSelection.value = [];
                };

                // 查看业务对象详情
                const viewObjectDetail = (row) => {
                    console.log('查看业务对象详情:', row);
                    // 在实际应用中，这里会跳转到业务对象详情页面
                };

                // 取消关联
                const unassignObject = (row) => {
                    console.log('取消关联:', row);
                    // 在实际应用中，这里会调用API取消业务对象的团队关联
                    // 取消成功后刷新数据
                    businessObjects.value = businessObjects.value.filter(item => item.id !== row.id);
                    // 将取消关联的业务对象添加到未关联列表
                    unassignedObjects.value.push({
                        id: row.id,
                        name: row.name,
                        type: row.type,
                        owner: '未知',
                        createTime: '未知'
                    });
                };

                // 获取业务类型标签样式
                const getBizTypeTagType = (bizType) => {
                    const typeMap = {
                        'CONTACT': 'success',
                        'LEAD': 'warning',
                        'CUSTOMER': 'primary',
                        'OPPORTUNITY': 'danger'
                    };
                    return typeMap[bizType] || 'info';
                };

                // 获取业务类型显示名称
                const getBizTypeLabel = (bizType) => {
                    const labelMap = {
                        'CONTACT': '联系人',
                        'LEAD': '线索',
                        'CUSTOMER': '客户',
                        'OPPORTUNITY': '商机'
                    };
                    return labelMap[bizType] || '未知';
                };

                return {
                    currentTeam,
                    businessObjects,
                    unassignedObjects,
                    assignDialogVisible,
                    assignSelection,
                    assignQuery,
                    assignTotal,
                    openAssignDialog,
                    handleAssignQuery,
                    resetAssignQuery,
                    handleAssignSelectionChange,
                    handleAssignSizeChange,
                    handleAssignCurrentChange,
                    handleBatchAssign,
                    viewObjectDetail,
                    unassignObject,
                    getBizTypeTagType,
                    getBizTypeLabel
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>