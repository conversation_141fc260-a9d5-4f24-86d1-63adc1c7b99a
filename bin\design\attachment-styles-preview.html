<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>附件组件样式设计预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .style-section {
            background: white;
            margin-bottom: 30px;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .style-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        /* 样式1：卡片网格布局 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }

        .attachment-card {
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 16px;
            background: white;
            transition: all 0.3s ease;
            position: relative;
        }

        .attachment-card:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: white;
        }

        .file-icon.pdf { background: #e74c3c; }
        .file-icon.image { background: #2ecc71; }
        .file-icon.document { background: #3498db; }
        .file-icon.excel { background: #27ae60; }

        .card-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .card-meta {
            font-size: 12px;
            color: #7f8c8d;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; }

        /* 样式2：列表布局 */
        .list-layout {
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #ecf0f1;
            transition: background 0.2s;
        }

        .list-item:hover {
            background: #f8f9fa;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
            color: white;
        }

        .list-info {
            flex: 1;
        }

        .list-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .list-meta {
            font-size: 12px;
            color: #7f8c8d;
            display: flex;
            gap: 16px;
        }

        .list-actions {
            display: flex;
            gap: 8px;
        }

        /* 样式3：紧凑型布局 */
        .compact-layout {
            background: white;
            border-radius: 8px;
            padding: 16px;
        }

        .compact-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .compact-item:last-child {
            border-bottom: none;
        }

        .compact-icon {
            width: 24px;
            height: 24px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 12px;
            color: white;
        }

        .compact-info {
            flex: 1;
            min-width: 0;
        }

        .compact-name {
            font-size: 13px;
            font-weight: 500;
            color: #2c3e50;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .compact-meta {
            font-size: 11px;
            color: #95a5a6;
        }

        .compact-actions {
            display: flex;
            gap: 4px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }

        /* 样式4：现代化设计 */
        .modern-layout {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .modern-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .modern-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .modern-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .modern-info h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .modern-meta {
            font-size: 13px;
            color: #7f8c8d;
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .modern-actions {
            display: flex;
            gap: 8px;
        }

        .btn-modern {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-modern.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-modern.secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-modern:hover {
            transform: translateY(-1px);
        }

        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed #bdc3c7;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #3498db;
            background: #ebf3fd;
        }

        .upload-icon {
            font-size: 48px;
            color: #95a5a6;
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: #7f8c8d;
        }

        .feature-tag {
            display: inline-block;
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px; font-size: 32px;">附件组件样式设计预览</h1>
        
        <!-- 上传区域 -->
        <div class="style-section">
            <h2 class="style-title">📤 上传区域设计</h2>
            <div class="upload-area">
                <div class="upload-icon">📁</div>
                <div class="upload-text">将文件拖拽到此处，或点击上传</div>
                <div class="upload-hint">支持 PDF、图片、Word、Excel 等格式，单个文件不超过 10MB</div>
            </div>
            <div style="margin-top: 15px;">
                <span class="feature-tag">拖拽上传</span>
                <span class="feature-tag">批量上传</span>
                <span class="feature-tag">格式限制</span>
                <span class="feature-tag">大小限制</span>
            </div>
        </div>

        <!-- 样式1：卡片网格布局 -->
        <div class="style-section">
            <h2 class="style-title">🎨 样式1：卡片网格布局（推荐）</h2>
            <div class="card-grid">
                <div class="attachment-card">
                    <div class="card-header">
                        <div class="file-icon pdf">📄</div>
                        <div class="card-info">
                            <h4>合同文件.pdf</h4>
                            <div class="card-meta">2.5MB • 2小时前</div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
                <div class="attachment-card">
                    <div class="card-header">
                        <div class="file-icon image">🖼️</div>
                        <div class="card-info">
                            <h4>产品图片.jpg</h4>
                            <div class="card-meta">1.2MB • 1天前</div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
                <div class="attachment-card">
                    <div class="card-header">
                        <div class="file-icon document">📝</div>
                        <div class="card-info">
                            <h4>需求文档.docx</h4>
                            <div class="card-meta">856KB • 3天前</div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <span class="feature-tag">视觉美观</span>
                <span class="feature-tag">响应式</span>
                <span class="feature-tag">悬浮效果</span>
                <span class="feature-tag">易于浏览</span>
            </div>
        </div>

        <!-- 样式2：列表布局 -->
        <div class="style-section">
            <h2 class="style-title">📋 样式2：列表布局</h2>
            <div class="list-layout">
                <div class="list-item">
                    <div class="list-icon pdf">📄</div>
                    <div class="list-info">
                        <h4>合同文件.pdf</h4>
                        <div class="list-meta">
                            <span>2.5MB</span>
                            <span>2小时前</span>
                            <span>张三上传</span>
                        </div>
                    </div>
                    <div class="list-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
                <div class="list-item">
                    <div class="list-icon image">🖼️</div>
                    <div class="list-info">
                        <h4>产品图片.jpg</h4>
                        <div class="list-meta">
                            <span>1.2MB</span>
                            <span>1天前</span>
                            <span>李四上传</span>
                        </div>
                    </div>
                    <div class="list-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
                <div class="list-item">
                    <div class="list-icon excel">📊</div>
                    <div class="list-info">
                        <h4>数据统计表.xlsx</h4>
                        <div class="list-meta">
                            <span>3.8MB</span>
                            <span>3天前</span>
                            <span>王五上传</span>
                        </div>
                    </div>
                    <div class="list-actions">
                        <button class="btn btn-primary">下载</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <span class="feature-tag">信息详细</span>
                <span class="feature-tag">易于排序</span>
                <span class="feature-tag">快速操作</span>
                <span class="feature-tag">传统布局</span>
            </div>
        </div>

        <!-- 样式3：紧凑型布局 -->
        <div class="style-section">
            <h2 class="style-title">🗂️ 样式3：紧凑型布局</h2>
            <div class="compact-layout">
                <div class="compact-item">
                    <div class="compact-icon pdf">📄</div>
                    <div class="compact-info">
                        <div class="compact-name">合同文件.pdf</div>
                        <div class="compact-meta">2.5MB • 2小时前</div>
                    </div>
                    <div class="compact-actions">
                        <button class="btn btn-primary btn-sm">下载</button>
                        <button class="btn btn-danger btn-sm">删除</button>
                    </div>
                </div>
                <div class="compact-item">
                    <div class="compact-icon image">🖼️</div>
                    <div class="compact-info">
                        <div class="compact-name">产品图片.jpg</div>
                        <div class="compact-meta">1.2MB • 1天前</div>
                    </div>
                    <div class="compact-actions">
                        <button class="btn btn-primary btn-sm">下载</button>
                        <button class="btn btn-danger btn-sm">删除</button>
                    </div>
                </div>
                <div class="compact-item">
                    <div class="compact-icon document">📝</div>
                    <div class="compact-info">
                        <div class="compact-name">需求文档.docx</div>
                        <div class="compact-meta">856KB • 3天前</div>
                    </div>
                    <div class="compact-actions">
                        <button class="btn btn-primary btn-sm">下载</button>
                        <button class="btn btn-danger btn-sm">删除</button>
                    </div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <span class="feature-tag">节省空间</span>
                <span class="feature-tag">高密度</span>
                <span class="feature-tag">快速浏览</span>
                <span class="feature-tag">移动友好</span>
            </div>
        </div>

        <!-- 样式4：现代化设计 -->
        <div class="style-section">
            <h2 class="style-title">✨ 样式4：现代化设计</h2>
            <div class="modern-layout">
                <div class="modern-card">
                    <div class="modern-header">
                        <div class="modern-icon">📄</div>
                        <div class="modern-info">
                            <h4>合同文件.pdf</h4>
                        </div>
                    </div>
                    <div class="modern-meta">
                        <span>2.5MB</span>
                        <span>2小时前</span>
                        <span>张三上传</span>
                    </div>
                    <div class="modern-actions">
                        <button class="btn-modern primary">下载</button>
                        <button class="btn-modern secondary">删除</button>
                    </div>
                </div>
                <div class="modern-card">
                    <div class="modern-header">
                        <div class="modern-icon">🖼️</div>
                        <div class="modern-info">
                            <h4>产品图片.jpg</h4>
                        </div>
                    </div>
                    <div class="modern-meta">
                        <span>1.2MB</span>
                        <span>1天前</span>
                        <span>李四上传</span>
                    </div>
                    <div class="modern-actions">
                        <button class="btn-modern primary">下载</button>
                        <button class="btn-modern secondary">删除</button>
                    </div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <span class="feature-tag">现代设计</span>
                <span class="feature-tag">渐变效果</span>
                <span class="feature-tag">动画交互</span>
                <span class="feature-tag">视觉冲击</span>
            </div>
        </div>

        <!-- 推荐总结 -->
        <div class="style-section" style="border-left: 4px solid #3498db; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
            <h2 class="style-title">💡 推荐方案</h2>
            <div style="font-size: 16px; line-height: 1.6; color: #2c3e50;">
                <p><strong>推荐使用样式1：卡片网格布局</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>✅ 视觉效果最佳，符合现代设计趋势</li>
                    <li>✅ 响应式设计，适配各种屏幕尺寸</li>
                    <li>✅ 悬浮效果提升用户体验</li>
                    <li>✅ 易于扩展，支持更多文件信息展示</li>
                    <li>✅ 与Element Plus设计语言保持一致</li>
                </ul>
                <p style="margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    备选方案：对于需要展示大量附件的场景，可以考虑样式2的列表布局，信息密度更高。
                </p>
            </div>
        </div>
    </div>
</body>
</html>
