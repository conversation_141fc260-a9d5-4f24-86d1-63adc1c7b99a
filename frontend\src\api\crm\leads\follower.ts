import request from '@/utils/request';

// 检查是否关注了线索
export function checkLeadFollower(leadId: number) {
    return request({
        url: `/crm/leadFollower/check/${leadId}`,
        method: 'get'
    });
}

// 关注线索
export function followLead(leadId: number) {
    return request({
        url: '/crm/leadFollower/follow',
        method: 'post',
        data: { leadId }
    });
}

// 取消关注线索
export function unfollowLead(leadId: number) {
    return request({
        url: '/crm/leadFollower/unfollow',
        method: 'post',
        data: { leadId }
    });
}

// 获取线索的关注者列表
export function getLeadFollowers(leadId: number) {
    return request({
        url: `/crm/leadFollower/list/${leadId}`,
        method: 'get'
    });
} 