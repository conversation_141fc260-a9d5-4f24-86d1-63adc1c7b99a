package com.ruoyi.crm.controller;

import java.util.List;
import java.util.Map;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;

/**
 * 线索分配记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/front/crm/assignmentRecords")
public class CrmLeadAssignmentRecordController extends BaseController {
    
    @Autowired
    private ICrmLeadAssignmentRecordService crmLeadAssignmentRecordService;

    /**
     * 查询线索分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        startPage();
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.selectCrmLeadAssignmentRecordList(crmLeadAssignmentRecord);
        return getDataTable(list);
    }

    /**
     * 导出线索分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:export')")
    @Log(title = "线索分配记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.selectCrmLeadAssignmentRecordList(crmLeadAssignmentRecord);
        ExcelUtil<CrmLeadAssignmentRecord> util = new ExcelUtil<CrmLeadAssignmentRecord>(CrmLeadAssignmentRecord.class);
        util.exportExcel(response, list, "线索分配记录数据");
    }

    /**
     * 获取线索分配记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmLeadAssignmentRecordService.selectCrmLeadAssignmentRecordById(id));
    }

    /**
     * 新增线索分配记录
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:add')")
    @Log(title = "线索分配记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        return toAjax(crmLeadAssignmentRecordService.insertCrmLeadAssignmentRecord(crmLeadAssignmentRecord));
    }

    /**
     * 修改线索分配记录
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:edit')")
    @Log(title = "线索分配记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        return toAjax(crmLeadAssignmentRecordService.updateCrmLeadAssignmentRecord(crmLeadAssignmentRecord));
    }

    /**
     * 删除线索分配记录
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:remove')")
    @Log(title = "线索分配记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmLeadAssignmentRecordService.deleteCrmLeadAssignmentRecordByIds(ids));
    }

    /**
     * 根据线索ID查询分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/lead/{leadId}")
    public AjaxResult getRecordsByLeadId(@PathVariable Long leadId) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByLeadId(leadId);
        return success(list);
    }

    /**
     * 根据用户ID查询分配记录列表（作为分配对象）
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/toUser/{userId}")
    public AjaxResult getRecordsByToUserId(@PathVariable Long userId) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByToUserId(userId);
        return success(list);
    }

    /**
     * 根据用户ID查询分配记录列表（作为原负责人）
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/fromUser/{userId}")
    public AjaxResult getRecordsByFromUserId(@PathVariable Long userId) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByFromUserId(userId);
        return success(list);
    }

    /**
     * 根据操作人ID查询分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/operator/{operatorId}")
    public AjaxResult getRecordsByOperatorId(@PathVariable Long operatorId) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByOperatorId(operatorId);
        return success(list);
    }

    /**
     * 根据分配类型查询分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/type/{assignmentType}")
    public AjaxResult getRecordsByAssignmentType(@PathVariable String assignmentType) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByAssignmentType(assignmentType);
        return success(list);
    }

    /**
     * 根据时间范围查询分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/timeRange")
    public AjaxResult getRecordsByTimeRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        List<CrmLeadAssignmentRecord> list = crmLeadAssignmentRecordService.getRecordsByTimeRange(startTime, endTime);
        return success(list);
    }

    /**
     * 获取分配记录统计信息
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/stats")
    public AjaxResult getAssignmentRecordStats() {
        Map<String, Object> stats = crmLeadAssignmentRecordService.getAssignmentRecordStats();
        return success(stats);
    }

    /**
     * 统计各分配类型的记录数量
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/stats/type")
    public AjaxResult countRecordsByAssignmentType() {
        List<CrmLeadAssignmentRecord> stats = crmLeadAssignmentRecordService.countRecordsByAssignmentType();
        return success(stats);
    }

    /**
     * 统计各用户的分配数量（作为分配对象）
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/stats/user")
    public AjaxResult countRecordsByToUser() {
        List<CrmLeadAssignmentRecord> stats = crmLeadAssignmentRecordService.countRecordsByToUser();
        return success(stats);
    }

    /**
     * 统计各操作人的操作数量
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/stats/operator")
    public AjaxResult countRecordsByOperator() {
        List<CrmLeadAssignmentRecord> stats = crmLeadAssignmentRecordService.countRecordsByOperator();
        return success(stats);
    }

    /**
     * 统计指定时间范围内的分配数量
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:list')")
    @GetMapping("/stats/timeRange")
    public AjaxResult countRecordsByTimeRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        int count = crmLeadAssignmentRecordService.countRecordsByTimeRange(startTime, endTime);
        return success(count);
    }

    /**
     * 获取最近的分配记录
     */
    @PreAuthorize("@ss.hasPermi('crm:assignmentRecords:query')")
    @GetMapping("/latest/{leadId}")
    public AjaxResult getLatestRecordByLeadId(@PathVariable Long leadId) {
        CrmLeadAssignmentRecord record = crmLeadAssignmentRecordService.getLatestRecordByLeadId(leadId);
        return success(record);
    }
}
