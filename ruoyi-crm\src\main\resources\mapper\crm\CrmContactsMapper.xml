<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmContactsMapper">
    
    <resultMap type="CrmContacts" id="CrmContactsResult">
        <result property="id" column="id"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="phone" column="phone"/>
        <result property="telephone" column="telephone"/>
        <result property="email" column="email"/>
        <result property="position" column="position"/>
        <result property="department" column="department"/>
        <result property="decisionRole" column="decision_role"/>
        <result property="contactLevel" column="contact_level"/>
        <result property="isKeyDecisionMaker" column="is_key_decision_maker"/>
        <result property="directSuperior" column="direct_superior"/>
        <result property="address" column="address"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="nextContactTime" column="next_contact_time"/>
        <result property="selectedDate" column="selected_date"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isFollowing" column="is_following"/>
    </resultMap>

    <sql id="selectCrmContactsVo">
        select c.id, c.responsible_person_id, c.name, c.mobile, c.phone, c.telephone, c.email, c.position, 
        c.department, c.decision_role, c.contact_level, c.is_key_decision_maker, c.direct_superior, c.address, 
        c.detailed_address, c.next_contact_time, c.selected_date, c.gender, c.birthday, c.status, c.remarks, 
        c.del_flag, c.create_by, c.create_time, c.update_by, c.update_time
        from crm_business_contacts c
    </sql>

    <select id="selectCrmContactsList" parameterType="CrmContacts" resultMap="CrmContactsResult">
        select c.id, c.responsible_person_id, c.name, c.mobile, c.phone, c.email, c.position, 
        c.department, c.decision_role, c.contact_level, c.is_key_decision_maker, c.direct_superior, c.address, 
        c.detailed_address, c.next_contact_time, c.selected_date, c.gender, c.birthday, c.status, c.remarks, 
        c.del_flag, c.create_by, c.create_time, c.update_by, c.update_time,
        case when cf.id is not null then 1 else 0 end as is_following
        from crm_business_contacts c
        <if test="followerId != null">
            inner join crm_contact_followers cf on c.id = cf.contact_id and cf.follower_id = #{followerId} and cf.is_active = 1
        </if>
        <if test="followerId == null">
            left join crm_contact_followers cf on c.id = cf.contact_id and cf.is_active = 1
        </if>
        <where>
            c.del_flag = '0'
            <if test="responsiblePersonId != null and responsiblePersonId != ''"> and c.responsible_person_id = #{responsiblePersonId}</if>
            <if test="subordinateIds != null and subordinateIds.size() > 0">
                and c.responsible_person_id in
                <foreach collection="subordinateIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="name != null and name != ''"> and c.name like concat('%', #{name}, '%')</if>
            <if test="mobile != null and mobile != ''"> and c.mobile = #{mobile}</if>
            <if test="phone != null and phone != ''"> and c.phone = #{phone}</if>
            <if test="telephone != null and telephone != ''"> and c.telephone = #{telephone}</if>
            <if test="email != null and email != ''"> and c.email = #{email}</if>
            <if test="position != null and position != ''"> and c.position = #{position}</if>
            <if test="department != null and department != ''"> and c.department = #{department}</if>
            <if test="decisionRole != null and decisionRole != ''"> and c.decision_role = #{decisionRole}</if>
            <if test="contactLevel != null and contactLevel != ''"> and c.contact_level = #{contactLevel}</if>
            <if test="status != null and status != ''"> and c.status = #{status}</if>
            <if test="isKeyDecisionMaker != null and isKeyDecisionMaker != ''"> and c.is_key_decision_maker = #{isKeyDecisionMaker}</if>
            <if test="directSuperior != null and directSuperior != ''"> and c.direct_superior = #{directSuperior}</if>
            <if test="address != null and address != ''"> and c.address = #{address}</if>
            <if test="detailedAddress != null and detailedAddress != ''"> and c.detailed_address = #{detailedAddress}</if>
            <if test="gender != null and gender != ''"> and c.gender = #{gender}</if>
        </where>
        order by c.create_time desc
    </select>
    
    <select id="selectCrmContactsById" parameterType="Long" resultMap="CrmContactsResult">
        <include refid="selectCrmContactsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCrmContacts" parameterType="CrmContacts" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_contacts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="responsiblePersonId != null">responsible_person_id,</if>
            <if test="name != null">name,</if>
            <if test="mobile != null">mobile,</if>
            <if test="phone != null">phone,</if>
            <if test="telephone != null">telephone,</if>
            <if test="email != null">email,</if>
            <if test="position != null">position,</if>
            <if test="department != null">department,</if>
            <if test="decisionRole != null">decision_role,</if>
            <if test="contactLevel != null">contact_level,</if>
            <if test="isKeyDecisionMaker != null">is_key_decision_maker,</if>
            <if test="directSuperior != null">direct_superior,</if>
            <if test="address != null">address,</if>
            <if test="detailedAddress != null">detailed_address,</if>
            <if test="nextContactTime != null">next_contact_time,</if>
            <if test="selectedDate != null">selected_date,</if>
            <if test="gender != null">gender,</if>
            <if test="birthday != null">birthday,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="responsiblePersonId != null">#{responsiblePersonId},</if>
            <if test="name != null">#{name},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="phone != null">#{phone},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="email != null">#{email},</if>
            <if test="position != null">#{position},</if>
            <if test="department != null">#{department},</if>
            <if test="decisionRole != null">#{decisionRole},</if>
            <if test="contactLevel != null">#{contactLevel},</if>
            <if test="isKeyDecisionMaker != null">#{isKeyDecisionMaker},</if>
            <if test="directSuperior != null">#{directSuperior},</if>
            <if test="address != null">#{address},</if>
            <if test="detailedAddress != null">#{detailedAddress},</if>
            <if test="nextContactTime != null">#{nextContactTime},</if>
            <if test="selectedDate != null">#{selectedDate},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmContacts" parameterType="CrmContacts">
        update crm_business_contacts
        <trim prefix="SET" suffixOverrides=",">
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="position != null">position = #{position},</if>
            <if test="department != null">department = #{department},</if>
            <if test="decisionRole != null">decision_role = #{decisionRole},</if>
            <if test="contactLevel != null">contact_level = #{contactLevel},</if>
            <if test="isKeyDecisionMaker != null">is_key_decision_maker = #{isKeyDecisionMaker},</if>
            <if test="directSuperior != null">direct_superior = #{directSuperior},</if>
            <if test="address != null">address = #{address},</if>
            <if test="detailedAddress != null">detailed_address = #{detailedAddress},</if>
            <if test="nextContactTime != null">next_contact_time = #{nextContactTime},</if>
            <if test="selectedDate != null">selected_date = #{selectedDate},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmContactsById" parameterType="Long">
        update crm_business_contacts set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmContactsByIds" parameterType="String">
        update crm_business_contacts set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>