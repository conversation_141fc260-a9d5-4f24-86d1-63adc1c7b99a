# CRM分表系统架构与工作原理

## 系统概述

本CRM系统的分表功能基于MyBatis-Plus动态表名插件实现，通过在SQL执行前动态替换表名的方式，实现数据的自动分表存储和查询。系统采用时间维度分表策略，支持按月、按年等多种分表粒度。

## 核心架构

```mermaid
graph TB
    A[业务层 Service] --> B[MyBatis-Plus 动态表名插件]
    B --> C[TableShardingManager 分表管理器]
    C --> D[数据库分表]
    
    E[分表配置 application-sharding.yml] --> C
    F[定时任务] --> G[TableShardingService 分表服务]
    G --> C
    
    H[分表管理API] --> G
    
    subgraph "分表策略"
        I[按月分表<br/>crm_business_leads_202412]
        J[按年分表<br/>crm_business_customers_2024]
        K[按月分表<br/>crm_lead_operation_log_202412]
    end
    
    C --> I
    C --> J
    C --> K
```

## 核心组件详解

### 1. MyBatis-Plus 动态表名插件 (MybatisPlusConfig)

**作用**：SQL执行前的表名动态替换

**工作流程**：
```java
// 1. 拦截SQL执行
@Bean
public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    
    // 2. 注册动态表名插件
    DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
    dynamicTableNameInnerInterceptor.setTableNameHandler(this::handleTableName);
    
    return interceptor;
}

// 3. 表名处理逻辑
private String handleTableName(String sql, String tableName) {
    switch (tableName) {
        case "crm_business_leads":
            return tableShardingManager.getLeadsTableName(LocalDate.now());
        // ... 其他表的处理
    }
}
```

**执行时机**：
1. 业务代码调用Mapper方法
2. MyBatis-Plus准备执行SQL
3. 动态表名插件拦截SQL
4. 调用表名处理器替换表名
5. 执行修改后的SQL

### 2. 分表管理器 (TableShardingManager)

**核心职责**：
- 根据日期生成分表名
- 自动创建不存在的分表
- 管理分表的生命周期

**关键方法解析**：

#### 获取线索表名
```java
public String getLeadsTableName(LocalDate date) {
    // 1. 根据日期生成表名后缀
    String suffix = date.format(DateTimeFormatter.ofPattern("yyyyMM"));
    String tableName = "crm_business_leads_" + suffix;
    
    // 2. 确保表存在（不存在则创建）
    ensureTableExists(tableName, "crm_business_leads");
    
    return tableName;
}
```

#### 自动创建分表
```java
private void ensureTableExists(String targetTableName, String templateTableName) {
    // 1. 检查缓存
    if (createdTables.containsKey(targetTableName)) {
        return;
    }
    
    // 2. 查询数据库确认表是否存在
    String checkSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?";
    Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, targetTableName);
    
    // 3. 表不存在则创建
    if (count == null || count == 0) {
        createTableFromTemplate(targetTableName, templateTableName);
    }
    
    // 4. 缓存已创建的表
    createdTables.put(targetTableName, true);
}
```

#### 从模板表创建新表
```java
private void createTableFromTemplate(String targetTableName, String templateTableName) {
    // 1. 获取模板表的创建语句
    String showCreateSql = "SHOW CREATE TABLE " + templateTableName;
    Map<String, Object> result = jdbcTemplate.queryForMap(showCreateSql);
    String createSql = (String) result.get("Create Table");
    
    // 2. 替换表名
    createSql = createSql.replaceFirst("CREATE TABLE `" + templateTableName + "`", 
                                     "CREATE TABLE `" + targetTableName + "`");
    
    // 3. 执行创建语句
    jdbcTemplate.execute(createSql);
}
```

### 3. 分表服务 (TableShardingService)

**核心功能**：
- 跨表查询
- 数据迁移
- 表统计信息
- 定时清理

#### 跨表查询实现
```java
public List<Map<String, Object>> queryLeadsAcrossTables(LocalDate startDate, LocalDate endDate, Map<String, Object> conditions) {
    // 1. 获取时间范围内的所有分表名
    List<String> tableNames = tableShardingManager.getTablesInRange("crm_business_leads", startDate, endDate, "yyyyMM");
    
    // 2. 遍历每个表进行查询
    List<Map<String, Object>> results = new ArrayList<>();
    for (String tableName : tableNames) {
        // 检查表是否存在
        if (!tableExists(tableName)) continue;
        
        // 构建查询SQL
        StringBuilder sql = new StringBuilder("SELECT * FROM " + tableName + " WHERE 1=1");
        // 添加查询条件...
        
        // 执行查询并合并结果
        List<Map<String, Object>> tableResults = jdbcTemplate.queryForList(sql.toString(), params.toArray());
        results.addAll(tableResults);
    }
    
    // 3. 排序返回
    return results;
}
```

### 4. 配置管理 (ShardingConfiguration)

**配置结构**：
```yaml
crm:
  sharding:
    enabled: true                    # 启用分表
    data-retention-months: 36        # 数据保留36个月
    pre-create-months: 3            # 预创建3个月
    cleanup-cron: "0 0 2 1 * ?"     # 清理任务时间
    strategies:                      # 分表策略配置
      crm_business_leads:
        type: MONTH                  # 按月分表
        sharding-column: create_time # 分表字段
        enabled: true
```

## 完整工作流程

### 1. 数据插入流程

```mermaid
sequenceDiagram
    participant Service as 业务服务
    participant MP as MyBatis-Plus
    participant Handler as 动态表名处理器
    participant Manager as 分表管理器
    participant DB as 数据库

    Service->>MP: leadsMapper.insert(lead)
    MP->>Handler: 拦截SQL，调用handleTableName()
    Handler->>Manager: getLeadsTableName(now())
    Manager->>Manager: 生成表名 crm_business_leads_202412
    Manager->>DB: 检查表是否存在
    alt 表不存在
        Manager->>DB: 创建分表
    end
    Manager-->>Handler: 返回实际表名
    Handler-->>MP: 替换SQL中的表名
    MP->>DB: 执行 INSERT INTO crm_business_leads_202412
    DB-->>Service: 返回结果
```

### 2. 数据查询流程

```mermaid
sequenceDiagram
    participant Service as 业务服务
    participant MP as MyBatis-Plus
    participant Handler as 动态表名处理器
    participant Manager as 分表管理器
    participant DB as 数据库

    Service->>MP: leadsMapper.selectList(query)
    MP->>Handler: 拦截SQL，调用handleTableName()
    Handler->>Manager: getLeadsTableName(now())
    Manager-->>Handler: 返回当前月份表名
    Handler-->>MP: 替换SQL中的表名
    MP->>DB: 执行 SELECT FROM crm_business_leads_202412
    DB-->>Service: 返回查询结果
```

### 3. 跨表查询流程

```mermaid
sequenceDiagram
    participant Service as 业务服务
    participant ShardingService as 分表服务
    participant Manager as 分表管理器
    participant DB as 数据库

    Service->>ShardingService: queryLeadsAcrossTables(start, end, conditions)
    ShardingService->>Manager: getTablesInRange(start, end)
    Manager-->>ShardingService: 返回表名列表
    loop 遍历每个表
        ShardingService->>DB: 检查表是否存在
        alt 表存在
            ShardingService->>DB: 执行查询
            DB-->>ShardingService: 返回表数据
        end
    end
    ShardingService->>ShardingService: 合并和排序结果
    ShardingService-->>Service: 返回最终结果
```

## 关键技术点

### 1. 动态表名替换机制

**实现原理**：
- 利用MyBatis-Plus的`DynamicTableNameInnerInterceptor`拦截器
- 在SQL执行前通过正则表达式匹配表名
- 调用自定义的表名处理器进行替换

**优势**：
- 对业务代码完全透明
- 支持所有类型的SQL操作
- 性能开销极小

### 2. 分表自动创建机制

**设计思路**：
- 延迟创建：只在需要时创建分表
- 模板复制：基于原表结构创建分表
- 缓存机制：避免重复检查表存在性

**安全保障**：
- 双重检查：缓存+数据库查询
- 异常处理：创建失败时的降级策略
- 并发安全：使用ConcurrentHashMap

### 3. 跨表查询优化

**查询策略**：
- 时间范围过滤：只查询相关时间段的表
- 并行查询：可扩展为并行查询多个表
- 结果合并：统一排序和分页

**性能优化**：
- 索引策略：为分表创建合适索引
- 查询条件下推：将过滤条件应用到每个表
- 数据量控制：避免返回过多数据

### 4. 定时清理机制

**清理策略**：
```java
@Scheduled(cron = "#{@shardingConfiguration.cleanupCron}")
public void scheduledCleanupExpiredTables() {
    // 1. 计算截止日期
    LocalDate cutoffDate = LocalDate.now().minusMonths(retentionMonths);
    
    // 2. 查询过期表
    String querySql = "SELECT table_name FROM information_schema.tables WHERE ...";
    
    // 3. 删除过期表
    for (String tableName : expiredTables) {
        jdbcTemplate.execute("DROP TABLE IF EXISTS " + tableName);
    }
}
```

## 性能特点

### 1. 查询性能
- **单表查询**：性能与原表相同，表数据量减少
- **跨表查询**：线性增长，但每个表数据量小
- **索引效果**：分表后索引更小，查询更快

### 2. 存储优化
- **数据分散**：避免单表过大
- **索引优化**：每个分表的索引更小更高效
- **清理机制**：自动删除过期数据

### 3. 维护成本
- **自动化**：分表创建、清理全自动
- **透明化**：业务代码无需修改
- **监控友好**：提供完整的管理接口

## 扩展性设计

### 1. 分表策略扩展
- 支持自定义分表算法
- 支持多维度分表（时间+业务）
- 支持动态调整分表策略

### 2. 存储引擎适配
- 支持MySQL、PostgreSQL等数据库
- 支持分布式数据库
- 支持云数据库服务

### 3. 监控和运维
- 分表状态监控
- 性能指标收集
- 自动化运维工具

## 注意事项

### 1. 事务限制
- 跨表操作不支持数据库事务
- 需要应用层实现分布式事务
- 建议避免跨表的复杂业务操作

### 2. 数据一致性
- 分表间的数据一致性需要应用层保证
- 外键约束无法跨表使用
- 需要设计合适的数据校验机制

### 3. 运维考虑
- 备份策略需要适应分表结构
- 数据迁移需要考虑分表逻辑
- 监控告警需要覆盖所有分表

---

这套分表系统通过合理的架构设计和技术选型，在保证系统性能的同时，最大化地降低了对现有业务的影响，是一个实用且可靠的解决方案。 