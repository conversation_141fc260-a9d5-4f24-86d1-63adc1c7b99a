import request from '@/utils/request'

// 查询回款分期列表
export function listInstallments(query) {
  return request({
    url: '/crm/paymentInstallment/list',
    method: 'get',
    params: query
  })
}

// 根据回款计划ID查询分期列表
export function getInstallmentsByPlan(planId) {
  return request({
    url: '/crm/paymentInstallment/plan/' + planId,
    method: 'get'
  })
}

// 查询回款分期详细
export function getInstallment(installmentId) {
  return request({
    url: '/crm/paymentInstallment/' + installmentId,
    method: 'get'
  })
}

// 新增回款分期
export function addInstallment(data) {
  return request({
    url: '/crm/paymentInstallment',
    method: 'post',
    data: data
  })
}

// 批量新增回款分期
export function batchAddInstallments(data) {
  return request({
    url: '/crm/paymentInstallment/batch',
    method: 'post',
    data: data
  })
}

// 修改回款分期
export function updateInstallment(data) {
  return request({
    url: '/crm/paymentInstallment',
    method: 'put',
    data: data
  })
}

// 删除回款分期
export function delInstallment(installmentId) {
  return request({
    url: '/crm/paymentInstallment/' + installmentId,
    method: 'delete'
  })
}

// 记录实际回款
export function recordPayment(data) {
  return request({
    url: '/crm/paymentInstallment/recordPayment',
    method: 'post',
    params: data
  })
}

// 更新分期状态
export function updateStatus(installmentId, status) {
  return request({
    url: '/crm/paymentInstallment/updateStatus',
    method: 'post',
    params: {
      installmentId: installmentId,
      status: status
    }
  })
}

// 更新逾期天数
export function updateOverdue() {
  return request({
    url: '/crm/paymentInstallment/updateOverdue',
    method: 'post'
  })
}

// 创建分期模板
export function createTemplate(data) {
  return request({
    url: '/crm/paymentInstallment/createTemplate',
    method: 'post',
    data: data
  })
}

// 获取分期统计信息
export function getStatistics(planId) {
  return request({
    url: '/crm/paymentInstallment/statistics/' + planId,
    method: 'get'
  })
}

// 查询逾期分期
export function getOverdueInstallments(query) {
  return request({
    url: '/crm/paymentInstallment/overdue',
    method: 'get',
    params: query
  })
}

// 查询即将到期分期
export function getUpcomingInstallments(query) {
  return request({
    url: '/crm/paymentInstallment/upcoming',
    method: 'get',
    params: query
  })
}

// 导出回款分期
export function exportInstallments(query) {
  return request({
    url: '/crm/paymentInstallment/export',
    method: 'post',
    params: query
  })
}