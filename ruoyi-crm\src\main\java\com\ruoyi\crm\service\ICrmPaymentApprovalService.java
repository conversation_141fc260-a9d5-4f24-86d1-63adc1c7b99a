package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;

/**
 * 回款审批Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface ICrmPaymentApprovalService 
{
    /**
     * 查询回款审批
     * 
     * @param id 回款审批主键
     * @return 回款审批
     */
    public CrmPaymentApproval selectCrmPaymentApprovalById(Long id);

    /**
     * 查询回款审批列表
     * 
     * @param crmPaymentApproval 回款审批
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectCrmPaymentApprovalList(CrmPaymentApproval crmPaymentApproval);

    /**
     * 根据计划ID查询审批列表
     * 
     * @param planId 计划ID
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectApprovalsByPlanId(Long planId);

    /**
     * 新增回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    public int insertCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval);

    /**
     * 批量新增回款审批
     * 
     * @param approvals 回款审批列表
     * @return 结果
     */
    public int batchInsertApprovals(List<CrmPaymentApproval> approvals);

    /**
     * 修改回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    public int updateCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval);

    /**
     * 批量删除回款审批
     * 
     * @param ids 需要删除的回款审批主键集合
     * @return 结果
     */
    public int deleteCrmPaymentApprovalByIds(Long[] ids);

    /**
     * 删除回款审批信息
     * 
     * @param id 回款审批主键
     * @return 结果
     */
    public int deleteCrmPaymentApprovalById(Long id);

    /**
     * 根据计划ID删除审批
     * 
     * @param planId 计划ID
     * @return 结果
     */
    public int deleteApprovalsByPlanId(Long planId);

    /**
     * 查询待审批的记录
     * 
     * @param approverId 审批人ID
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectPendingApprovals(Long approverId);

    /**
     * 创建审批流程
     * 
     * @param planId 计划ID
     * @param approverIds 审批人ID列表
     * @return 结果
     */
    public int createApprovalFlow(Long planId, List<Long> approverIds);

    /**
     * 获取当前审批级别
     * 
     * @param planId 计划ID
     * @return 当前审批级别
     */
    public Integer getCurrentApprovalLevel(Long planId);

    /**
     * 获取下一个待审批人
     * 
     * @param planId 计划ID
     * @return 下一个审批记录
     */
    public CrmPaymentApproval getNextApprover(Long planId);
}