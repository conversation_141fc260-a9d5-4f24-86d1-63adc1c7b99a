# CRM无入侵式无密码认证系统详细设计

## 🎯 设计理念：完全无入侵

### 核心原则
- **不修改现有表结构**：sys_user表保持完全不变
- **密码保留机制**：后台管理员仍可使用密码登录
- **用户无感知**：前端用户体验完全无密码，但系统内部自动管理
- **渐进式部署**：可以与现有系统并行运行

## 📊 表结构关系和协作机制

### 表关系图
```
现有系统 (不变)                    新增无密码系统 (扩展)
┌─────────────────┐              ┌─────────────────────────┐
│   sys_user      │              │  crm_user_registration  │
│  ┌─────────────┐│              │ ┌─────────────────────┐ │
│  │user_id (PK) ││              │ │id (PK)              │ │
│  │user_name    ││              │ │phone_number         │ │
│  │password     ││              │ │registration_type    │ │
│  │phonenumber  ││              │ │verification_code    │ │
│  │...          ││              │ │user_id (FK)         │ │
│  └─────────────┘│              │ │status               │ │
└─────────────────┘              │ └─────────────────────┘ │
         │                       └─────────────────────────┘
         │ (外键关联，无入侵)                  │
         │                                    │
         ▼                                    ▼
┌─────────────────────────┐      ┌─────────────────────────┐
│ crm_user_auth_methods   │      │  crm_customer_leads     │
│ ┌─────────────────────┐ │      │ ┌─────────────────────┐ │
│ │id (PK)              │ │      │ │id (PK)              │ │
│ │user_id (FK)         │ │      │ │user_id (FK)         │ │
│ │auth_type            │ │      │ │phone_number         │ │
│ │auth_identifier      │ │      │ │source_type          │ │
│ │is_primary           │ │      │ │status               │ │
│ │last_used_time       │ │      │ │assigned_to (FK)     │ │
│ └─────────────────────┘ │      │ │access_token         │ │
└─────────────────────────┘      │ └─────────────────────┘ │
                                 └─────────────────────────┘
```

## 🔄 表协作流程详解

### 流程1：客户首次无密码注册
```
1. 客户输入手机号 → crm_user_registration (创建注册记录)
   ├─ phone_number: "13800138000"
   ├─ registration_type: "sms"
   ├─ status: "pending"
   └─ verification_code: "123456" (加密存储)

2. 验证码验证成功 → 自动创建sys_user记录
   ├─ user_name: 自动生成 (如: "user_13800138000")
   ├─ password: 自动生成强密码 (用户不知道)
   ├─ phonenumber: "13800138000"
   └─ nick_name: "客户138****8000"

3. 更新注册记录 → crm_user_registration
   ├─ user_id: 关联新创建的用户ID
   ├─ status: "completed"
   └─ is_verified: true

4. 创建认证方式 → crm_user_auth_methods
   ├─ user_id: 关联用户ID
   ├─ auth_type: "phone"
   ├─ auth_identifier: "13800138000"
   ├─ is_primary: true
   └─ last_used_time: 当前时间

5. 生成客户线索 → crm_customer_leads
   ├─ user_id: 关联用户ID
   ├─ phone_number: "13800138000"
   ├─ source_type: "website"
   ├─ status: "new"
   └─ access_token: 生成专用访问令牌
```

### 流程2：企业微信员工无密码登录
```
1. 企业微信扫码 → 获取微信用户信息
   ├─ union_id: "wx_union_123456"
   ├─ name: "张三"
   └─ mobile: "13900139000"

2. 查找现有用户 → crm_user_auth_methods
   └─ 通过auth_identifier查找是否已绑定

3. 如果是新用户 → 创建完整用户记录
   ├─ sys_user: 创建用户(含自动生成密码)
   ├─ crm_user_registration: 记录注册信息
   └─ crm_user_auth_methods: 绑定企业微信认证

4. 如果是已有用户 → 直接登录
   └─ 更新last_used_time，生成JWT令牌
```

### 流程3：客户专区访问
```
1. 客户点击专属链接 → 通过access_token访问
   └─ URL: /customer/zone?token=abc123...

2. 验证令牌 → crm_customer_leads
   ├─ 通过access_token查找线索记录
   └─ 获取关联的user_id

3. 加载客户数据 → 多表联查
   ├─ crm_customer_leads: 线索基本信息
   ├─ crm_lead_files: 相关文件
   ├─ crm_lead_communications: 沟通记录
   └─ sys_user: 分配的销售人员信息

4. 返回专区页面 → 展示项目进度和沟通记录
```

## 🔐 密码管理策略：用户无感知

### 自动密码生成机制
```java
/**
 * 无感知密码生成器
 * 用户永远不知道这个密码，但系统内部需要
 */
public class PasswordlessPasswordGenerator {
    
    public String generateSecurePassword(String phoneNumber) {
        // 生成复杂密码：手机号+时间戳+随机字符串的MD5
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomStr = UUID.randomUUID().toString().substring(0, 8);
        String rawPassword = phoneNumber + timestamp + randomStr;
        
        // 使用BCrypt加密，符合系统安全要求
        return BCrypt.hashpw(rawPassword, BCrypt.gensalt());
    }
    
    public void createUserWithAutoPassword(String phoneNumber, String userType) {
        SysUser user = new SysUser();
        user.setUserName(generateUsername(phoneNumber));
        user.setPhonenumber(phoneNumber);
        
        // 自动生成安全密码，用户永远不知道
        user.setPassword(generateSecurePassword(phoneNumber));
        
        // 设置用户类型和默认信息
        user.setUserType(userType);
        user.setStatus("0"); // 正常状态
        user.setNickName("客户" + phoneNumber.substring(7)); // 客户138****8000
        
        // 保存到数据库
        userService.insertUser(user);
    }
}
```

### 双重认证机制
```java
/**
 * 混合认证服务：传统密码 + 无密码并存
 */
@Service
public class HybridAuthService {
    
    /**
     * 管理员仍然可以使用密码登录
     */
    public LoginResult adminPasswordLogin(String username, String password) {
        // 走原有的密码验证逻辑
        return traditionalLoginService.login(username, password);
    }
    
    /**
     * 前端用户使用无密码登录
     */
    public LoginResult passwordlessLogin(String phoneNumber, String verificationCode) {
        // 1. 验证短信验证码
        if (!smsService.verifyCode(phoneNumber, verificationCode)) {
            return LoginResult.failure("验证码错误");
        }
        
        // 2. 查找用户认证记录
        CrmUserAuthMethods authMethod = authMethodsService.findByPhone(phoneNumber);
        if (authMethod == null) {
            // 新用户，自动创建
            SysUser newUser = createUserWithAutoPassword(phoneNumber, "customer");
            authMethod = createAuthMethod(newUser.getUserId(), phoneNumber);
        }
        
        // 3. 生成JWT令牌（不包含密码信息）
        String token = jwtService.generatePasswordlessToken(authMethod.getUserId());
        
        // 4. 更新最后使用时间
        authMethod.setLastUsedTime(LocalDateTime.now());
        authMethodsService.updateById(authMethod);
        
        return LoginResult.success(token);
    }
}
```

## 📋 无入侵实施方案

### 阶段1：新表创建（完全独立）
```sql
-- 完全独立的新表，不影响现有系统
CREATE TABLE `crm_user_registration` (...);
CREATE TABLE `crm_user_auth_methods` (...);
CREATE TABLE `crm_customer_leads` (...);
-- 只有外键关联到sys_user，但不修改sys_user结构
```

### 阶段2：服务层扩展（并行开发）
```java
// 新增认证服务，与现有LoginService并行
@Service
public class PasswordlessAuthService {
    // 无密码认证逻辑
}

// 现有服务保持不变
@Service 
public class SysLoginService {
    // 原有密码登录逻辑保持完全不变
}
```

### 阶段3：前端路由分离
```typescript
// 新增无密码登录路由
const passwordlessRoutes = [
  { path: '/passwordless-login', component: PasswordlessLogin },
  { path: '/customer-zone', component: CustomerZone }
];

// 原有管理后台路由保持不变
const adminRoutes = [
  { path: '/login', component: AdminLogin }, // 仍然使用密码
  { path: '/dashboard', component: Dashboard }
];
```

## 🔄 系统兼容性保证

### 登录入口分离
```
管理后台 (原有) → /admin/login → 密码登录 → 管理系统
客户前台 (新增) → /login → 无密码登录 → CRM功能
员工企微 (新增) → /wechat/login → 企微扫码 → CRM功能
```

### 权限体系兼容
```java
@PreAuthorize("hasRole('ADMIN')") // 原有管理员权限
public void adminFunction() {}

@PreAuthorize("hasRole('EMPLOYEE')") // 新增员工权限
public void employeeFunction() {}

@PreAuthorize("hasRole('CUSTOMER')") // 新增客户权限  
public void customerFunction() {}
```

## 🛠️ 实施建议

### 1. 渐进式部署
- 第一周：创建新表，不影响现有功能
- 第二周：开发无密码认证API，与现有系统并行
- 第三周：前端页面开发，独立入口
- 第四周：内测和逐步开放

### 2. 回滚保障
- 新功能完全独立，随时可以关闭
- 原有系统功能完全不受影响
- 数据库变更可逆，支持快速回滚

### 3. 监控和观察
- 新旧系统使用情况对比
- 用户体验反馈收集
- 系统性能影响评估

这样的设计确保了：
✅ **完全无入侵**：不修改任何现有表和功能
✅ **密码保留**：管理员仍可正常使用密码登录
✅ **用户无感**：前端用户完全无密码体验
✅ **渐进部署**：可以分阶段实施，风险可控
✅ **完全可逆**：任何时候都可以回到原有状态
