package com.ruoyi.common.mapper;

import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;

/**
 * 线索分配记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface CrmLeadAssignmentRecordMapper {
    
    /**
     * 查询线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 线索分配记录
     */
    CrmLeadAssignmentRecord selectCrmLeadAssignmentRecordById(Long id);

    /**
     * 查询线索分配记录列表
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 线索分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectCrmLeadAssignmentRecordList(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 根据线索ID查询分配记录列表
     * 
     * @param leadId 线索ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据用户ID查询分配记录列表（作为分配对象）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByToUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询分配记录列表（作为原负责人）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByFromUserId(@Param("userId") Long userId);

    /**
     * 根据操作人ID查询分配记录列表
     * 
     * @param operatorId 操作人ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByOperatorId(@Param("operatorId") Long operatorId);

    /**
     * 根据分配类型查询分配记录列表
     * 
     * @param assignmentType 分配类型
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByAssignmentType(@Param("assignmentType") String assignmentType);

    /**
     * 根据时间范围查询分配记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectRecordsByTimeRange(@Param("startTime") Date startTime, 
                                                          @Param("endTime") Date endTime);

    /**
     * 新增线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    int insertCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 修改线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    int updateCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 删除线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 结果
     */
    int deleteCrmLeadAssignmentRecordById(Long id);

    /**
     * 批量删除线索分配记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCrmLeadAssignmentRecordByIds(Long[] ids);

    /**
     * 统计分配记录数量
     * 
     * @param crmLeadAssignmentRecord 查询条件
     * @return 数量
     */
    int countAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 统计各分配类型的记录数量
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByAssignmentType();

    /**
     * 统计各用户的分配数量（作为分配对象）
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByToUser();

    /**
     * 统计各操作人的操作数量
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByOperator();

    /**
     * 统计指定时间范围内的分配数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配数量
     */
    int countRecordsByTimeRange(@Param("startTime") Date startTime, 
                               @Param("endTime") Date endTime);

    /**
     * 获取最近的分配记录
     * 
     * @param leadId 线索ID
     * @return 最近的分配记录
     */
    CrmLeadAssignmentRecord selectLatestRecordByLeadId(@Param("leadId") Long leadId);
}
