<template>
  <div class="assignment-records-container">
    <!-- 头部统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ stats.totalCount || 0 }}</div>
              <div class="stats-label">总记录数</div>
            </div>
            <el-icon class="stats-icon"><Document /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card manual">
            <div class="stats-content">
              <div class="stats-number">{{ stats.manualCount || 0 }}</div>
              <div class="stats-label">手动分配</div>
            </div>
            <el-icon class="stats-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card grab">
            <div class="stats-content">
              <div class="stats-number">{{ stats.grabCount || 0 }}</div>
              <div class="stats-label">抢单获取</div>
            </div>
            <el-icon class="stats-icon"><Download /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card recycle">
            <div class="stats-content">
              <div class="stats-number">{{ stats.recycleCount || 0 }}</div>
              <div class="stats-label">回收记录</div>
            </div>
            <el-icon class="stats-icon"><RefreshRight /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
        <el-form-item label="线索ID" prop="leadId">
          <el-input v-model="queryParams.leadId" placeholder="请输入线索ID" clearable />
        </el-form-item>
        <el-form-item label="分配类型" prop="assignmentType">
          <el-select v-model="queryParams.assignmentType" placeholder="请选择分配类型" clearable>
            <el-option label="手动分配" value="manual" />
            <el-option label="抢单" value="grab" />
            <el-option label="回收" value="recycle" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人" prop="operatorName">
          <el-input v-model="queryParams.operatorName" placeholder="请输入操作人姓名" clearable />
        </el-form-item>
        <el-form-item label="分配时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operation-card">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="!multiple"
            @click="handleDelete"
            v-hasPermi="['crm:assignmentRecords:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['crm:assignmentRecords:export']"
          >导出</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="recordsList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="线索名称" align="center" prop="leadName" min-width="120">
          <template #default="scope">
            <el-link type="primary" @click="handleLeadDetail(scope.row)">
              {{ scope.row.leadName || '未关联线索' }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" prop="customerName" min-width="120" />
        <el-table-column label="分配类型" align="center" prop="assignmentType" width="100">
          <template #default="scope">
            <el-tag
              :type="getAssignmentTypeTagType(scope.row.assignmentType)"
              disable-transitions
            >
              {{ getAssignmentTypeText(scope.row.assignmentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="原负责人" align="center" prop="fromUserName" width="120">
          <template #default="scope">
            <span>{{ scope.row.fromUserName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="新负责人" align="center" prop="toUserName" width="120">
          <template #default="scope">
            <span>{{ scope.row.toUserName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分配原因" align="center" prop="assignmentReason" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作人" align="center" prop="operatorName" width="120" />
        <el-table-column label="分配时间" align="center" prop="assignmentTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.assignmentTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remarks" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleDetail(scope.row)"
              v-hasPermi="['crm:assignmentRecords:query']"
            >详情</el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['crm:assignmentRecords:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="分配记录详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
        <el-descriptions-item label="线索名称">{{ currentRecord.leadName || '未关联线索' }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ currentRecord.customerName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="分配类型">
          <el-tag :type="getAssignmentTypeTagType(currentRecord.assignmentType)">
            {{ getAssignmentTypeText(currentRecord.assignmentType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="原负责人">{{ currentRecord.fromUserName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="新负责人">{{ currentRecord.toUserName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="分配原因" :span="2">{{ currentRecord.assignmentReason || '-' }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ currentRecord.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="分配时间">
          {{ parseTime(currentRecord.assignmentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentRecord.remarks || '-' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, User, Download, RefreshRight, Search, Refresh, Delete, View } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import {
  listAssignmentRecords,
  getAssignmentRecord,
  deleteAssignmentRecord,
  getAssignmentRecordStats,
  exportAssignmentRecords,
  type AssignmentRecord,
  type AssignmentRecordQuery
} from '../api/assignmentRecords';

// 响应式数据
const loading = ref(true);
const ids = ref<number[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const recordsList = ref<AssignmentRecord[]>([]);
const detailOpen = ref(false);
const currentRecord = ref<AssignmentRecord>({} as AssignmentRecord);
const stats = ref<any>({});
const dateRange = ref<string[]>([]);

// 查询参数
const queryParams = reactive<AssignmentRecordQuery>({
  pageNum: 1,
  pageSize: 10,
  leadId: undefined,
  assignmentType: undefined,
  operatorName: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 获取分配类型标签类型
const getAssignmentTypeTagType = (type: string) => {
  switch (type) {
    case 'manual': return 'primary';
    case 'grab': return 'success';
    case 'recycle': return 'warning';
    default: return 'info';
  }
};

// 获取分配类型文本
const getAssignmentTypeText = (type: string) => {
  switch (type) {
    case 'manual': return '手动分配';
    case 'grab': return '抢单';
    case 'recycle': return '回收';
    default: return '未知';
  }
};

// 查询分配记录列表
const getList = async () => {
  loading.value = true;
  try {
    const response = await listAssignmentRecords(queryParams);
    recordsList.value = response.rows || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取分配记录列表失败:', error);
    ElMessage.error('获取分配记录列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取统计信息
const getStats = async () => {
  try {
    const response = await getAssignmentRecordStats();
    stats.value = response.data || {};
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
};

// 日期范围变化处理
const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    queryParams.beginTime = dates[0];
    queryParams.endTime = dates[1];
  } else {
    queryParams.beginTime = undefined;
    queryParams.endTime = undefined;
  }
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.leadId = undefined;
  queryParams.assignmentType = undefined;
  queryParams.operatorName = undefined;
  queryParams.beginTime = undefined;
  queryParams.endTime = undefined;
  dateRange.value = [];
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: AssignmentRecord[]) => {
  ids.value = selection.map(item => item.id!);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 行点击事件
const handleRowClick = (row: AssignmentRecord) => {
  // 可以在这里添加行点击逻辑
};

// 详情按钮操作
const handleDetail = async (row: AssignmentRecord) => {
  try {
    const response = await getAssignmentRecord(row.id!);
    currentRecord.value = response.data;
    detailOpen.value = true;
  } catch (error) {
    ElMessage.error('获取记录详情失败');
  }
};

// 线索详情操作
const handleLeadDetail = (row: AssignmentRecord) => {
  ElMessage.success('线索详情功能开发中...');
};

// 删除按钮操作
const handleDelete = (row?: AssignmentRecord) => {
  ElMessage.success('删除功能开发中...');
};

// 导出按钮操作
const handleExport = () => {
  ElMessage.success('导出功能开发中...');
};

// 初始化
onMounted(() => {
  getList();
  getStats();
});
</script>

<style scoped>
.assignment-records-container {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card.manual {
  border-left: 4px solid #409eff;
}

.stats-card.grab {
  border-left: 4px solid #67c23a;
}

.stats-card.recycle {
  border-left: 4px solid #e6a23c;
}

.stats-content {
  padding: 20px;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #e4e7ed;
}

.filter-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
