# BPMN 文件质量检查脚本
# 用于检查 BPMN 文件是否包含必要的显示元素

param(
    [string]$Path = "src\main\resources\processes",
    [switch]$Verbose
)

Write-Host "🔍 BPMN 文件质量检查器" -ForegroundColor Green
Write-Host "检查路径: $Path" -ForegroundColor Gray
Write-Host ""

# 检查路径是否存在
if (-not (Test-Path $Path)) {
    Write-Host "❌ 路径不存在: $Path" -ForegroundColor Red
    exit 1
}

# 获取所有 BPMN 文件
$bpmnFiles = Get-ChildItem -Path $Path -Filter "*.bpmn" -Recurse

if ($bpmnFiles.Count -eq 0) {
    Write-Host "⚠️  未找到 BPMN 文件" -ForegroundColor Yellow
    exit 0
}

Write-Host "找到 $($bpmnFiles.Count) 个 BPMN 文件" -ForegroundColor Blue
Write-Host ""

$totalFiles = 0
$healthyFiles = 0
$issueFiles = 0

foreach ($file in $bpmnFiles) {
    $totalFiles++
    $fileName = $file.Name
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart("\")
    
    Write-Host "📄 检查文件: $fileName" -ForegroundColor Cyan
    if ($Verbose) {
        Write-Host "   路径: $relativePath" -ForegroundColor Gray
    }
    
    $content = Get-Content $file.FullName -Raw
    $issues = @()
    
    # 检查基本结构
    if ($content -notmatch "bpmn:process") {
        $issues += "缺少 bpmn:process 定义"
    }
    
    if ($content -notmatch "bpmndi:BPMNDiagram") {
        $issues += "缺少 bpmndi:BPMNDiagram 定义"
    }
    
    if ($content -notmatch "bpmndi:BPMNShape") {
        $issues += "缺少 bpmndi:BPMNShape 定义"
    }
    
    # 关键检查：BPMNEdge
    if ($content -notmatch "bpmndi:BPMNEdge") {
        $issues += "⚠️  缺少 bpmndi:BPMNEdge 定义 - 可能无法正常显示"
    }
    
    # 检查 sequenceFlow 和 BPMNEdge 的对应关系
    $sequenceFlowCount = ([regex]::Matches($content, "bpmn:sequenceFlow")).Count
    $bpmnEdgeCount = ([regex]::Matches($content, "bpmndi:BPMNEdge")).Count
    
    if ($sequenceFlowCount -gt 0 -and $bpmnEdgeCount -eq 0) {
        $issues += "有 $sequenceFlowCount 个连接流但没有对应的 BPMNEdge"
    } elseif ($sequenceFlowCount -ne $bpmnEdgeCount -and $bpmnEdgeCount -gt 0) {
        $issues += "连接流数量($sequenceFlowCount)与BPMNEdge数量($bpmnEdgeCount)不匹配"
    }
    
    # 显示结果
    if ($issues.Count -eq 0) {
        Write-Host "   ✅ 文件格式正常" -ForegroundColor Green
        $healthyFiles++
    } else {
        Write-Host "   ❌ 发现 $($issues.Count) 个问题:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "      - $issue" -ForegroundColor Yellow
        }
        $issueFiles++
    }
    
    if ($Verbose) {
        # 显示详细统计
        Write-Host "   📊 统计信息:" -ForegroundColor Gray
        Write-Host "      - sequenceFlow: $sequenceFlowCount" -ForegroundColor Gray
        Write-Host "      - BPMNEdge: $bpmnEdgeCount" -ForegroundColor Gray
        Write-Host "      - BPMNShape: $(([regex]::Matches($content, 'bpmndi:BPMNShape')).Count)" -ForegroundColor Gray
    }
    
    Write-Host ""
}

# 显示总结
Write-Host "📋 检查总结" -ForegroundColor Blue
Write-Host "总文件数: $totalFiles" -ForegroundColor White
Write-Host "正常文件: $healthyFiles" -ForegroundColor Green
Write-Host "问题文件: $issueFiles" -ForegroundColor $(if ($issueFiles -gt 0) { "Red" } else { "Green" })

if ($issueFiles -gt 0) {
    Write-Host ""
    Write-Host "💡 修复建议:" -ForegroundColor Yellow
    Write-Host "1. 检查缺少 BPMNEdge 的文件，这是导致 BPMN 无法显示的主要原因" -ForegroundColor White
    Write-Host "2. 使用标准 BPMN 编辑器（如 Camunda Modeler）重新保存文件" -ForegroundColor White
    Write-Host "3. 参考文档: ruoyi-crm/doc/bpmn-format-rules.md" -ForegroundColor White
    
    exit 1
} else {
    Write-Host ""
    Write-Host "🎉 所有文件都符合质量标准！" -ForegroundColor Green
    exit 0
} 