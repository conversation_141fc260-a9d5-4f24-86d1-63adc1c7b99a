<template>
    <el-dialog v-model="dialogVisible" title="转化线索" width="30%" append-to-body>
        <div class="convert-content">
            <p>请选择要将线索"{{ leadName }}"转化为:</p>
            <el-radio-group v-model="convertType">
                <el-radio :value="'customer'">客户</el-radio>
                <el-radio :value="'opportunity'">商机</el-radio>
            </el-radio-group>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认转化</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
    modelValue: boolean;
    leadId: number;
    leadName: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const dialogVisible = ref(props.modelValue);
const convertType = ref('customer');

// 监听对话框可见性
watch(() => props.modelValue, (newVal: boolean) => {
    dialogVisible.value = newVal;
});

watch(() => dialogVisible.value, (newVal: boolean) => {
    emit('update:modelValue', newVal);
});

const handleConfirm = () => {
    emit('confirm', {
        leadId: props.leadId,
        status: 'converted',
        convertType: convertType.value
    });
};

const handleCancel = () => {
    convertType.value = 'customer';
    emit('cancel');
};
</script>

<style scoped>
.convert-content {
    padding: 20px 0;
    text-align: center;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style> 