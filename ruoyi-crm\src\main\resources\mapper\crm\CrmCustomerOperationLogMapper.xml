<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerOperationLogMapper">

    <resultMap type="CrmCustomerOperationLog" id="CrmCustomerOperationLogResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="businessType" column="business_type"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationContent" column="operation_content"/>
        <result property="operationDetails" column="operation_details"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operationTime" column="operation_time"/>
        <result property="extraData" column="extra_data"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCrmCustomerOperationLogVo">
        select id, customer_id, business_type, operation_type, operation_content, operation_details, operator_id, 
        operator_name, operation_time, extra_data, create_time, update_time
        from crm_customer_operation_log
    </sql>

    <select id="selectCrmCustomerOperationLogList" parameterType="CrmCustomerOperationLog" resultMap="CrmCustomerOperationLogResult">
        <include refid="selectCrmCustomerOperationLogVo"/>
        <where>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="operationContent != null  and operationContent != ''"> and operation_content = #{operationContent}</if>
            <if test="operationDetails != null  and operationDetails != ''"> and operation_details = #{operationDetails}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="extraData != null  and extraData != ''"> and extra_data = #{extraData}</if>
        </where>
        order by operation_time desc
    </select>
    
    <select id="selectCrmCustomerOperationLogById" parameterType="Long" resultMap="CrmCustomerOperationLogResult">
        <include refid="selectCrmCustomerOperationLogVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByCustomerId" parameterType="Long" resultMap="CrmCustomerOperationLogResult">
        <include refid="selectCrmCustomerOperationLogVo"/>
        where customer_id = #{customerId}
        order by operation_time desc
    </select>
    
    <select id="selectByTimeRange" resultMap="CrmCustomerOperationLogResult">
        <include refid="selectCrmCustomerOperationLogVo"/>
        <where>
            <if test="startTime != null and startTime != ''">
                AND operation_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND operation_time &lt;= #{endTime}
            </if>
        </where>
        order by operation_time desc
    </select>
        
    <insert id="insertCrmCustomerOperationLog" parameterType="CrmCustomerOperationLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_customer_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="operationContent != null">operation_content,</if>
            <if test="operationDetails != null">operation_details,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="extraData != null">extra_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="operationContent != null">#{operationContent},</if>
            <if test="operationDetails != null">#{operationDetails},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="extraData != null">#{extraData},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmCustomerOperationLog" parameterType="CrmCustomerOperationLog">
        update crm_customer_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="operationContent != null">operation_content = #{operationContent},</if>
            <if test="operationDetails != null">operation_details = #{operationDetails},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="extraData != null">extra_data = #{extraData},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmCustomerOperationLogById" parameterType="Long">
        delete from crm_customer_operation_log where id = #{id}
    </delete>

    <delete id="deleteCrmCustomerOperationLogByIds" parameterType="String">
        delete from crm_customer_operation_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
