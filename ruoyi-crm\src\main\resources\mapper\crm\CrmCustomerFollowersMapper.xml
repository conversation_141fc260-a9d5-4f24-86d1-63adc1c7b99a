<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerFollowersMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmCustomerFollowers" id="CrmCustomerFollowersResult">
        <result property="id"             column="id" />
        <result property="customerId"     column="customer_id" />
        <result property="followerId"     column="follower_id" />
        <result property="followTime"     column="follow_time" />
        <result property="isActive"       column="is_active" />
        <result property="createBy"       column="create_by" />
        <result property="createTime"     column="create_time" />
        <result property="updateBy"       column="update_by" />
        <result property="updateTime"     column="update_time" />
        <result property="followerName"   column="follower_name" />
        <result property="customerName"   column="customer_name" />
    </resultMap>

    <sql id="selectCrmCustomerFollowersVo">
        select id, customer_id, follower_id, follow_time, is_active, create_by, create_time, update_by, update_time from crm_customer_followers
    </sql>

    <select id="selectCrmCustomerFollowersList" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowers" resultMap="CrmCustomerFollowersResult">
        <include refid="selectCrmCustomerFollowersVo"/>
        <where>  
            <if test="customerId != null"> and customer_id = #{customerId}</if>
            <if test="followerId != null"> and follower_id = #{followerId}</if>
            <if test="isActive != null"> and is_active = #{isActive}</if>
        </where>
        order by follow_time desc
    </select>

    <insert id="insertCrmCustomerFollowers" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowers" useGeneratedKeys="true" keyProperty="id">
        insert into crm_customer_followers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="followerId != null">follower_id,</if>
            <if test="followTime != null">follow_time,</if>
            <if test="isActive != null">is_active,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="followerId != null">#{followerId},</if>
            <if test="followTime != null">#{followTime},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCrmCustomerFollowers" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowers">
        update crm_customer_followers
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="followerId != null">follower_id = #{followerId},</if>
            <if test="followTime != null">follow_time = #{followTime},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmCustomerFollowersById" parameterType="Long">
        delete from crm_customer_followers where id = #{id}
    </delete>

    <!-- 关注客户 -->
    <insert id="followCustomer">
        INSERT INTO crm_customer_followers (customer_id, follower_id, follow_time, is_active, create_by, create_time)
        VALUES (#{customerId}, #{followerId}, NOW(), 1, #{followerName}, NOW())
        ON DUPLICATE KEY UPDATE
        follow_time = NOW(),
        is_active = 1,
        update_by = #{followerName},
        update_time = NOW()
    </insert>

    <!-- 取消关注客户 -->
    <update id="unfollowCustomer">
        UPDATE crm_customer_followers 
        SET is_active = 0, update_time = NOW()
        WHERE customer_id = #{customerId} AND follower_id = #{followerId}
    </update>

    <!-- 查询关注关系 -->
    <select id="selectFollowRelation" resultMap="CrmCustomerFollowersResult">
        <include refid="selectCrmCustomerFollowersVo"/>
        WHERE customer_id = #{customerId} AND follower_id = #{followerId}
    </select>

    <!-- 批量关注客户 -->
    <insert id="batchFollowCustomers">
        INSERT INTO crm_customer_followers (customer_id, follower_id, follow_time, is_active, create_by, create_time)
        VALUES
        <foreach collection="customerIds" item="customerId" separator=",">
            (#{customerId}, #{followerId}, NOW(), 1, #{followerName}, NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        follow_time = NOW(),
        is_active = 1,
        update_by = #{followerName},
        update_time = NOW()
    </insert>

    <!-- 批量取消关注客户 -->
    <update id="batchUnfollowCustomers">
        UPDATE crm_customer_followers 
        SET is_active = 0, update_time = NOW()
        WHERE follower_id = #{followerId} AND customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </update>

    <!-- 统计用户关注的客户数量 -->
    <select id="countFollowedCustomers" resultType="int">
        SELECT COUNT(*) FROM crm_customer_followers 
        WHERE follower_id = #{followerId} AND is_active = 1
    </select>

    <!-- 统计客户被关注的数量 -->
    <select id="countCustomerFollowers" resultType="int">
        SELECT COUNT(*) FROM crm_customer_followers 
        WHERE customer_id = #{customerId} AND is_active = 1
    </select>

    <!-- 查询热门客户排行 -->
    <select id="selectPopularCustomers" resultMap="CrmCustomerFollowersResult">
        SELECT 
            cf.customer_id,
            COUNT(*) as follower_count,
            c.customer_name
        FROM crm_customer_followers cf
        LEFT JOIN crm_business_customers c ON cf.customer_id = c.id
        WHERE cf.is_active = 1 AND c.del_flag = '0'
        GROUP BY cf.customer_id, c.customer_name
        ORDER BY follower_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>