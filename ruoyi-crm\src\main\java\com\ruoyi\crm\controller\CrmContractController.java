package com.ruoyi.crm.controller;

import java.util.List;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.OperationLog;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmContract;
import com.ruoyi.crm.service.ICrmContractService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@RestController
@RequestMapping("/front/crm/contracts")
@OperationLog(
    businessType = "合同",
    entityClass = CrmContract.class,
    serviceClass = ICrmContractService.class
)
public class CrmContractController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(CrmContractController.class);
    
    @Autowired
    private ICrmContractService contractService;

    /**
     * 查询合同列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CrmContract contract) {
        startPage();
        List<CrmContract> list = contractService.selectContractList(contract);
        return getDataTable(list);
    }

    /**
     * 导出合同列表
     */
    @PreAuthorize("@ss.hasPermi('crm:contract:export')")
    @Log(title = "合同管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CrmContract contract) {
        List<CrmContract> list = contractService.selectContractList(contract);
        ExcelUtil<CrmContract> util = new ExcelUtil<CrmContract>(CrmContract.class);
        return util.exportExcel(list, "contract");
    }

    /**
     * 获取合同详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:contract:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(contractService.selectContractById(id));
    }

    /**
     * 新增合同
     */
    @PreAuthorize("@ss.hasPermi('crm:contract:add')")
    @Log(title = "合同管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult addWithRecord(@RequestBody CrmContract contract) {
        int rows = contractService.insertContract(contract);
        if (rows > 0) {
            return AjaxResult.success(contract); // contract对象已包含ID
        }
        return AjaxResult.error();
    }

    /**
     * 修改合同
     */
    @PreAuthorize("@ss.hasPermi('crm:contract:edit')")
    @Log(title = "合同管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmContract contract) {
        return toAjax(contractService.updateContract(contract));
    }

    /**
     * 删除合同
     */
    @PreAuthorize("@ss.hasPermi('crm:contract:remove')")
    @Log(title = "合同管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(contractService.deleteContractByIds(ids));
    }

    /**
     * 根据客户ID查询合同
     */
    @GetMapping("/customer/{customerId}")
    public TableDataInfo getContractByCustomer(@PathVariable("customerId") Long customerId) {
        try {
            // 暂时返回空列表，需要根据实际数据库设计调整
            // 可能需要先根据customerId查询客户名称，然后根据客户名称查询合同
            logger.info("根据客户ID查询合同，客户ID: {}", customerId);
            return new TableDataInfo(new ArrayList<>(), 0);
        } catch (Exception e) {
            logger.error("根据客户ID查询合同失败", e);
            return new TableDataInfo(new ArrayList<>(), 0);
        }
    }

    /**
     * 搜索合同
     */
    @GetMapping("/search")
    public TableDataInfo searchContract(@RequestParam(required = false) String keyword,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "100") Integer pageSize) {
        try {
            CrmContract searchCondition = new CrmContract();
            if (keyword != null && !keyword.trim().isEmpty()) {
                searchCondition.setContractNumber(keyword);
            }
            
            startPage();
            List<CrmContract> contracts = contractService.selectContractList(searchCondition);
            return getDataTable(contracts);
        } catch (Exception e) {
            logger.error("搜索合同失败", e);
            return new TableDataInfo(new ArrayList<>(), 0);
        }
    }
} 