package com.ruoyi.common.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 线索分配记录对象 crm_lead_assignment_records
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmLeadAssignmentRecord extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 线索ID */
    @Excel(name = "线索ID")
    private Long leadId;

    /** 线索池ID，关联crm_lead_pool表 */
    @Excel(name = "线索池ID")
    private Long poolId;

    /** 原负责人ID，可为空（从池中分配） */
    @Excel(name = "原负责人ID")
    private Long fromUserId;

    /** 新负责人ID */
    @Excel(name = "新负责人ID")
    private Long toUserId;

    /** 分配类型：manual-手动分配，grab-抢单，recycle-回收 */
    @Excel(name = "分配类型", readConverterExp = "manual=手动分配,grab=抢单,recycle=回收")
    private String assignmentType;

    /** 分配原因：手动分配/抢单/回收等 */
    @Excel(name = "分配原因")
    private String assignmentReason;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignmentTime;

    /** 操作人ID */
    @Excel(name = "操作人ID")
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人姓名")
    private String operatorName;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    // 扩展字段，用于关联查询
    /** 线索名称（来自crm_business_leads） */
    private String leadName;
    
    /** 客户名称（来自crm_business_leads） */
    private String customerName;
    
    /** 原负责人姓名（来自sys_user） */
    private String fromUserName;
    
    /** 新负责人姓名（来自sys_user） */
    private String toUserName;

    // 构造函数
    public CrmLeadAssignmentRecord() {
        this.assignmentTime = new Date();
    }

    public CrmLeadAssignmentRecord(Long leadId, Long poolId, Long fromUserId, Long toUserId, 
                                 String assignmentType, String assignmentReason, 
                                 Long operatorId, String operatorName) {
        this();
        this.leadId = leadId;
        this.poolId = poolId;
        this.fromUserId = fromUserId;
        this.toUserId = toUserId;
        this.assignmentType = assignmentType;
        this.assignmentReason = assignmentReason;
        this.operatorId = operatorId;
        this.operatorName = operatorName;
    }

    /**
     * 创建手动分配记录
     */
    public static CrmLeadAssignmentRecord createManualAssignment(Long leadId, Long poolId, 
                                                               Long toUserId, String reason,
                                                               Long operatorId, String operatorName) {
        return new CrmLeadAssignmentRecord(leadId, poolId, null, toUserId, 
                                         "manual", reason, operatorId, operatorName);
    }

    /**
     * 创建抢单记录
     */
    public static CrmLeadAssignmentRecord createGrabAssignment(Long leadId, Long poolId, 
                                                             Long toUserId, String reason,
                                                             Long operatorId, String operatorName) {
        return new CrmLeadAssignmentRecord(leadId, poolId, null, toUserId, 
                                         "grab", reason, operatorId, operatorName);
    }

    /**
     * 创建回收记录
     */
    public static CrmLeadAssignmentRecord createRecycleRecord(Long leadId, Long poolId, 
                                                            Long fromUserId, String reason,
                                                            Long operatorId, String operatorName) {
        return new CrmLeadAssignmentRecord(leadId, poolId, fromUserId, null, 
                                         "recycle", reason, operatorId, operatorName);
    }

    /**
     * 检查是否为手动分配
     */
    public boolean isManualAssignment() {
        return "manual".equals(this.assignmentType);
    }

    /**
     * 检查是否为抢单
     */
    public boolean isGrabAssignment() {
        return "grab".equals(this.assignmentType);
    }

    /**
     * 检查是否为回收
     */
    public boolean isRecycleRecord() {
        return "recycle".equals(this.assignmentType);
    }
}
