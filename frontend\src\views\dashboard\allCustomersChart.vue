<template>
    <div class="chart-container">
        <div class="charts-wrapper">
            <div ref="allCustomersChart" class="customer-chart"></div>
            <div ref="closedCustomersChart" class="customer-chart"></div>
        </div>

        <el-table :data="industryData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="industry" label="客户行业"></el-table-column>
            <el-table-column prop="allCustomers" label="所有客户"></el-table-column>
            <el-table-column prop="closedCustomers" label="成交客户"></el-table-column>
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    name: 'CustomerIndustryAnalysis',
    data() {
        return {
            allCustomersChartInstance: null,
            closedCustomersChartInstance: null,
            industryData: [
                { industry: 'IT', allCustomers: 200, closedCustomers: 150 },
                { industry: '金融', allCustomers: 180, closedCustomers: 120 },
                { industry: '制造', allCustomers: 160, closedCustomers: 90 },
                // 添加更多数据
            ],
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initCharts();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.allCustomersChartInstance) {
            this.allCustomersChartInstance.dispose();
            this.allCustomersChartInstance = null;
        }
        if (this.closedCustomersChartInstance) {
            this.closedCustomersChartInstance.dispose();
            this.closedCustomersChartInstance = null;
        }
    },
    methods: {
        handleResize() {
            if (this.allCustomersChartInstance) {
                this.allCustomersChartInstance.resize();
            }
            if (this.closedCustomersChartInstance) {
                this.closedCustomersChartInstance.resize();
            }
        },
        initCharts() {
            const allCustomersChartDom = this.$refs.allCustomersChart;
            const closedCustomersChartDom = this.$refs.closedCustomersChart;

            if (!allCustomersChartDom || !closedCustomersChartDom) return;

            if (this.allCustomersChartInstance) {
                this.allCustomersChartInstance.dispose();
            }
            if (this.closedCustomersChartInstance) {
                this.closedCustomersChartInstance.dispose();
            }

            this.allCustomersChartInstance = echarts.init(allCustomersChartDom);
            this.closedCustomersChartInstance = echarts.init(closedCustomersChartDom);

            const allCustomersOption = {
                title: { text: '全部客户', left: 'center' },
                tooltip: { trigger: 'item' },
                series: [
                    {
                        name: '客户行业分布',
                        type: 'pie',
                        radius: '50%',
                        data: this.industryData.map(item => ({
                            value: item.allCustomers,
                            name: item.industry,
                        })),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                            },
                        },
                    },
                ],
            };

            const closedCustomersOption = {
                title: { text: '成交客户', left: 'center' },
                tooltip: { trigger: 'item' },
                series: [
                    {
                        name: '客户行业分布',
                        type: 'pie',
                        radius: '50%',
                        data: this.industryData.map(item => ({
                            value: item.closedCustomers,
                            name: item.industry,
                        })),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                            },
                        },
                    },
                ],
            };

            this.allCustomersChartInstance.setOption(allCustomersOption);
            this.closedCustomersChartInstance.setOption(closedCustomersOption);
        },
    },
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.charts-wrapper {
    display: flex;
    justify-content: space-around;
    width: 100%;
}
.customer-chart {
    width: 45%;
    height: 400px;
}
</style>