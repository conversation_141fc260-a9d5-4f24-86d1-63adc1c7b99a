<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmVisitPlanLogMapper">
    
    <resultMap type="CrmVisitPlanLog" id="CrmVisitPlanLogResult">
        <result property="id"    column="id"    />
        <result property="visitPlanId"    column="visit_plan_id"    />
        <result property="fromStatus"    column="from_status"    />
        <result property="toStatus"    column="to_status"    />
        <result property="changeReason"    column="change_reason"    />
        <result property="changeRemark"    column="change_remark"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="operationType"    column="operation_type"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCrmVisitPlanLogVo">
        select id, visit_plan_id, from_status, to_status, change_reason, change_remark, 
               operator_id, operator_name, operation_type, create_time 
        from crm_visit_plan_logs
    </sql>

    <select id="selectCrmVisitPlanLogList" parameterType="CrmVisitPlanLog" resultMap="CrmVisitPlanLogResult">
        <include refid="selectCrmVisitPlanLogVo"/>
        <where>
            <if test="visitPlanId != null "> and visit_plan_id = #{visitPlanId}</if>
            <if test="fromStatus != null  and fromStatus != ''"> and from_status = #{fromStatus}</if>
            <if test="toStatus != null  and toStatus != ''"> and to_status = #{toStatus}</if>
            <if test="changeReason != null  and changeReason != ''"> and change_reason like concat('%', #{changeReason}, '%')</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCrmVisitPlanLogById" parameterType="Long" resultMap="CrmVisitPlanLogResult">
        <include refid="selectCrmVisitPlanLogVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByVisitPlanId" parameterType="Long" resultMap="CrmVisitPlanLogResult">
        <include refid="selectCrmVisitPlanLogVo"/>
        where visit_plan_id = #{visitPlanId}
        order by create_time desc
    </select>
    
    <select id="selectByOperatorId" parameterType="Long" resultMap="CrmVisitPlanLogResult">
        <include refid="selectCrmVisitPlanLogVo"/>
        where operator_id = #{operatorId}
        order by create_time desc
    </select>
    
    <select id="selectByOperationType" parameterType="String" resultMap="CrmVisitPlanLogResult">
        <include refid="selectCrmVisitPlanLogVo"/>
        where operation_type = #{operationType}
        order by create_time desc
    </select>
        
    <insert id="insertCrmVisitPlanLog" parameterType="CrmVisitPlanLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_visit_plan_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitPlanId != null">visit_plan_id,</if>
            <if test="fromStatus != null and fromStatus != ''">from_status,</if>
            <if test="toStatus != null and toStatus != ''">to_status,</if>
            <if test="changeReason != null">change_reason,</if>
            <if test="changeRemark != null">change_remark,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="createTime != null">create_time</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitPlanId != null">#{visitPlanId},</if>
            <if test="fromStatus != null and fromStatus != ''">#{fromStatus},</if>
            <if test="toStatus != null and toStatus != ''">#{toStatus},</if>
            <if test="changeReason != null">#{changeReason},</if>
            <if test="changeRemark != null">#{changeRemark},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="createTime != null">#{createTime}</if>
        </trim>
    </insert>
    
    <insert id="batchInsertCrmVisitPlanLog" parameterType="java.util.List">
        insert into crm_visit_plan_logs(visit_plan_id, from_status, to_status, change_reason, change_remark, 
                                       operator_id, operator_name, operation_type, create_time)
        values
        <foreach collection="logs" item="log" separator=",">
            (#{log.visitPlanId}, #{log.fromStatus}, #{log.toStatus}, #{log.changeReason}, #{log.changeRemark}, 
             #{log.operatorId}, #{log.operatorName}, #{log.operationType}, #{log.createTime})
        </foreach>
    </insert>

    <update id="updateCrmVisitPlanLog" parameterType="CrmVisitPlanLog">
        update crm_visit_plan_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitPlanId != null">visit_plan_id = #{visitPlanId},</if>
            <if test="fromStatus != null and fromStatus != ''">from_status = #{fromStatus},</if>
            <if test="toStatus != null and toStatus != ''">to_status = #{toStatus},</if>
            <if test="changeReason != null">change_reason = #{changeReason},</if>
            <if test="changeRemark != null">change_remark = #{changeRemark},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmVisitPlanLogById" parameterType="Long">
        delete from crm_visit_plan_logs where id = #{id}
    </delete>

    <delete id="deleteCrmVisitPlanLogByIds" parameterType="String">
        delete from crm_visit_plan_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
