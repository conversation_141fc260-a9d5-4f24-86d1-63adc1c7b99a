package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Calendar;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.RuoyiApplication;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.crm.service.ICrmPaymentPlanService;

@SpringBootTest(classes = RuoyiApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("回款分期Controller集成测试")
class CrmPaymentInstallmentControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    private CrmPaymentInstallment testInstallment;
    private CrmPaymentPlan testPaymentPlan;

    @BeforeEach
    void setUp() {
        // 创建测试回款计划
        testPaymentPlan = new CrmPaymentPlan();
        testPaymentPlan.setPlanNumber("PLN20240724001");
        testPaymentPlan.setCustomerId(1L);
        testPaymentPlan.setCustomerName("测试客户A");
        testPaymentPlan.setResponsibleUserId(1L);
        testPaymentPlan.setResponsibleUserName("张三");
        testPaymentPlan.setTotalAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setReceivedAmount(new BigDecimal("0.00"));
        testPaymentPlan.setRemainingAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setPlanStatus("执行中");
        testPaymentPlan.setApprovalStatus("已通过");
        testPaymentPlan.setPaymentMethod("银行转账");
        testPaymentPlan.setCurrency("CNY");
        testPaymentPlan.setPlanType("普通");
        testPaymentPlan.setRiskLevel("低");
        testPaymentPlan.setCreateTime(new Date());
        testPaymentPlan.setCreateBy("test");
        testPaymentPlan.setDelFlag("0");

        // 创建测试分期
        testInstallment = new CrmPaymentInstallment();
        testInstallment.setInstallmentName("首期款");
        testInstallment.setPlannedAmount(new BigDecimal("30000.00"));
        testInstallment.setActualAmount(new BigDecimal("0.00"));
        testInstallment.setInstallmentStatus("未回款");
        
        // 设置预期回款日期为30天后
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 30);
        testInstallment.setExpectedDate(cal.getTime());
        
        testInstallment.setInstallmentOrder(1);
        testInstallment.setPercentage(new BigDecimal("30.00"));
        testInstallment.setOverdueDays(0);
        testInstallment.setRemark("首期回款30%");
        testInstallment.setCreateTime(new Date());
        testInstallment.setCreateBy("test");
        testInstallment.setDelFlag("0");
    }

    @Test
    @DisplayName("测试分期列表查询")
    void testListInstallments() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/installments/{planId}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试新增分期")
    void testAddInstallment() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testInstallment.setPlanId(testPaymentPlan.getId());

        String jsonContent = objectMapper.writeValueAsString(testInstallment);

        MvcResult result = mockMvc.perform(post("/crm/paymentPlan/installments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("新增分期响应: " + response);
    }

    @Test
    @DisplayName("测试修改分期")
    void testUpdateInstallment() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testInstallment.setPlanId(testPaymentPlan.getId());
        testInstallment.setId(1L);

        // 修改数据
        testInstallment.setInstallmentName("修改后的分期名称");
        testInstallment.setPlannedAmount(new BigDecimal("40000.00"));
        testInstallment.setPercentage(new BigDecimal("40.00"));

        String jsonContent = objectMapper.writeValueAsString(testInstallment);

        mockMvc.perform(put("/crm/paymentPlan/installments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试删除分期")
    void testDeleteInstallment() throws Exception {
        mockMvc.perform(delete("/crm/paymentPlan/installments/{ids}", "1"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试记录实际回款")
    void testRecordPayment() throws Exception {
        mockMvc.perform(post("/crm/paymentPlan/installments/payment")
                .param("installmentId", "1")
                .param("actualAmount", "30000.00")
                .param("paymentDate", "2024-07-24")
                .param("paymentMethod", "银行转账")
                .param("remark", "实际收款"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试更新逾期天数")
    void testUpdateOverdueDays() throws Exception {
        mockMvc.perform(post("/crm/paymentPlan/installments/updateOverdue"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试创建标准分期模板")
    void testCreateStandardInstallments() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        String requestBody = """
            {
                "planId": %d,
                "template": "3_phase",
                "phases": [
                    {"name": "首期款", "percentage": 30, "daysOffset": 0},
                    {"name": "中期款", "percentage": 40, "daysOffset": 30},
                    {"name": "尾期款", "percentage": 30, "daysOffset": 60}
                ]
            }
            """.formatted(planId);

        mockMvc.perform(post("/crm/paymentPlan/installments/createTemplate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试分期状态统计")
    void testInstallmentStatistics() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/installments/statistics/{planId}", planId))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").isMap())
                .andExpected(jsonPath("$.data.totalCount").isNumber())
                .andExpected(jsonPath("$.data.completedCount").isNumber())
                .andExpected(jsonPath("$.data.overdueCount").isNumber());
    }

    @Test
    @DisplayName("测试逾期分期查询")
    void testOverdueInstallments() throws Exception {
        mockMvc.perform(get("/crm/paymentPlan/installments/overdue")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试即将到期分期查询")
    void testUpcomingInstallments() throws Exception {
        mockMvc.perform(get("/crm/paymentPlan/installments/upcoming")
                .param("days", "7") // 查询7天内到期的分期
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试分期完成度报表")
    void testInstallmentCompletionReport() throws Exception {
        mockMvc.perform(get("/crm/paymentPlan/installments/report/completion")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-12-31"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").isMap());
    }

    @Test
    @DisplayName("测试按客户查询分期")
    void testInstallmentsByCustomer() throws Exception {
        mockMvc.perform(get("/crm/paymentPlan/installments/customer/{customerId}", 1L)
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试分期参数验证")
    void testInstallmentValidation() throws Exception {
        // 测试必填字段验证
        CrmPaymentInstallment invalidInstallment = new CrmPaymentInstallment();
        String jsonContent = objectMapper.writeValueAsString(invalidInstallment);

        mockMvc.perform(post("/crm/paymentPlan/installments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpected(status().is4xxClientError());
    }

    @Test
    @DisplayName("测试批量操作分期")
    void testBatchOperateInstallments() throws Exception {
        String requestBody = """
            {
                "operation": "update_status",
                "installmentIds": [1, 2, 3],
                "newStatus": "已回款",
                "remark": "批量确认回款"
            }
            """;

        mockMvc.perform(post("/crm/paymentPlan/installments/batchOperate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("测试分期导出")
    void testExportInstallments() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(post("/crm/paymentPlan/installments/export")
                .param("planId", planId.toString())
                .param("status", ""))
                .andDo(print())
                .andExpected(status().isOk());
    }

    @Test
    @DisplayName("测试分期时间线查询")
    void testInstallmentTimeline() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/installments/timeline/{planId}", planId))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试分期风险评估")
    void testInstallmentRiskAssessment() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/installments/risk/{planId}", planId))
                .andDo(print())
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data.riskLevel").isString())
                .andExpected(jsonPath("$.data.riskFactors").isArray());
    }
}