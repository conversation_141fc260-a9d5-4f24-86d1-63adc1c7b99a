import type { TableColumn, TableOperationsConfig } from '@/types';
import type { ButtonType } from 'element-plus';

// 表格列配置
export const tableColumns: TableColumn[] = [
    { type: 'selection', width: 55 },
    { prop: 'name', label: '客户名称', width: 180, sortable: true },
    { prop: 'industry', label: '所属行业', width: 120, sortable: true },
    { prop: 'level', label: '客户等级', width: 120, sortable: true },
    { prop: 'status', label: '客户状态', width: 120, sortable: true },
    { prop: 'phone', label: '联系电话', width: 150, sortable: true },
    { prop: 'email', label: '电子邮箱', width: 180, sortable: true },
    { prop: 'address', label: '地址', sortable: true },
    { prop: 'owner', label: '负责人', width: 120, sortable: true },
    { prop: 'createTime', label: '创建时间', width: 180, sortable: true }
];

// 表格操作列配置
export const tableOperations: TableOperationsConfig = {
    width: 280,
    fixed: 'right' as const,
    buttons: [
        {
            label: '编辑',
            type: 'primary' as ButtonType,
            link: true,
            handler: 'handleEdit',
            icon: 'Edit',
            show: true
        },
        {
            label: '分配',
            type: 'info' as ButtonType,
            link: true,
            handler: 'handleAssignTeam',
            icon: 'Share',
            show: true
        },
        {
            label: '删除',
            type: 'danger' as ButtonType,
            link: true,
            handler: 'handleDelete',
            icon: 'Delete',
            show: true
        }
    ]
};

// 新建客户表单配置
export const newCustomerFormConfig = {
    layout: {
        labelPosition: 'right',
        labelWidth: '100px',
        size: 'default',
        gutter: 20
    },
    fields: [
        {
            field: 'name',
            label: '客户名称',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            maxLength: 50,
            showWordLimit: true,
            placeholder: '请输入客户名称',
            prefixIcon: 'User',
            rules: [
                { required: true, message: '请输入客户名称', trigger: 'blur' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
        },
        {
            field: 'industry',
            label: '所属行业',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            filterable: true,
            placeholder: '请选择所属行业',
            options: [
                { label: '互联网', value: 'internet' },
                { label: '金融', value: 'finance' },
                { label: '教育', value: 'education' },
                { label: '医疗', value: 'medical' },
                { label: '制造业', value: 'manufacturing' },
                { label: '其他', value: 'other' }
            ]
        },
        {
            field: 'level',
            label: '客户等级',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请选择客户等级',
            options: [
                { label: 'A级', value: 'A' },
                { label: 'B级', value: 'B' },
                { label: 'C级', value: 'C' },
                { label: 'D级', value: 'D' }
            ]
        },
        {
            field: 'status',
            label: '客户状态',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请选择客户状态',
            options: [
                { label: '潜在客户', value: 'potential' },
                { label: '意向客户', value: 'intention' },
                { label: '成交客户', value: 'deal' },
                { label: '流失客户', value: 'lost' }
            ]
        },
        {
            field: 'phone',
            label: '联系电话',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入联系电话',
            prefixIcon: 'Phone',
            rules: [
                { required: true, message: '请输入联系电话', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
            ]
        },
        {
            field: 'email',
            label: '电子邮箱',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入电子邮箱',
            prefixIcon: 'Message',
            rules: [
                { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
        },
        {
            field: 'address',
            label: '地址',
            type: 'input',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入地址',
            prefixIcon: 'Location'
        },
        {
            field: 'remarks',
            label: '备注',
            type: 'textarea',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入备注信息',
            maxLength: 500,
            showWordLimit: true,
            rows: 4
        }
    ]
};

// 抽屉配置
export const drawerConfig = {
    headerFields: [
        { label: '客户来源', field: 'source' },
        { label: '手机', field: 'phone' },
        { label: '负责人', field: 'owner' },
        { label: '创建时间', field: 'createTime' }
    ],
    actions: [
        {
            label: '编辑',
            type: 'primary',
            icon: 'Edit',
            handler: (data: any) => {
                console.log('编辑', data);
            }
        },
        {
            label: '转移',
            icon: 'Share',
            handler: (data: any) => {
                console.log('转移', data);
            }
        },
        {
            label: '打印',
            icon: 'Printer',
            handler: (data: any) => {
                console.log('打印', data);
            }
        },
        {
            label: '转化',
            type: 'success',
            icon: 'Promotion',
            handler: (data: any) => {
                console.log('转化', data);
            }
        },
        {
            label: '删除',
            type: 'danger',
            icon: 'Delete',
            handler: (data: any) => {
                console.log('删除', data);
            }
        }
    ],
    menuItems: [
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: 'CustomerDetailsTab'
        },
        {
            key: 'activity',
            label: '活动记录',
            icon: 'Timer',
            component: 'CustomerActivityTab'
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Folder',
            component: 'CustomerAttachmentsTab',
            badge: true
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: 'CustomerOperationsTab'
        }
    ]
};

// 导航配置
export const navConfig = {
    title: '客户管理',
    menuItems: [
        {
            key: 'customers',
            label: '客户',
            icon: 'User'
        },
        // 公海
        {
            key: 'publicPool',
            label: '公海',
            icon: 'User'
        },
        {
            key: 'followup',
            label: '跟进记录',
            icon: 'ChatRound'
        },
        // 拜访计划
        {
            key: 'visitPlan',
            label: '拜访计划',
            icon: 'Calendar'
        }
    ]
}; 