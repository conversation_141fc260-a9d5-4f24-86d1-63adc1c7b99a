<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadAssignmentRecordMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord" id="CrmLeadAssignmentRecordResult">
        <result property="id" column="id" />
        <result property="leadId" column="lead_id" />
        <result property="poolId" column="pool_id" />
        <result property="fromUserId" column="from_user_id" />
        <result property="toUserId" column="to_user_id" />
        <result property="assignmentType" column="assignment_type" />
        <result property="assignmentReason" column="assignment_reason" />
        <result property="assignmentTime" column="assignment_time" />
        <result property="operatorId" column="operator_id" />
        <result property="operatorName" column="operator_name" />
        <result property="remarks" column="remarks" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <!-- 扩展字段 -->
        <result property="leadName" column="lead_name" />
        <result property="customerName" column="customer_name" />
        <result property="fromUserName" column="from_user_name" />
        <result property="toUserName" column="to_user_name" />
    </resultMap>

    <sql id="selectCrmLeadAssignmentRecordVo">
        select r.id, r.lead_id, r.pool_id, r.from_user_id, r.to_user_id, r.assignment_type,
               r.assignment_reason, r.assignment_time, r.operator_id, r.operator_name, r.remarks,
               r.create_by, r.create_time, r.update_by, r.update_time,
               l.lead_name, l.customer_name,
               u1.nick_name as from_user_name,
               u2.nick_name as to_user_name
        from crm_lead_assignment_records r
        left join crm_business_leads l on r.lead_id = l.id
        left join sys_user u1 on r.from_user_id = u1.user_id
        left join sys_user u2 on r.to_user_id = u2.user_id
    </sql>

    <select id="selectCrmLeadAssignmentRecordList" parameterType="com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        <where>  
            <if test="leadId != null "> and r.lead_id = #{leadId}</if>
            <if test="poolId != null "> and r.pool_id = #{poolId}</if>
            <if test="fromUserId != null "> and r.from_user_id = #{fromUserId}</if>
            <if test="toUserId != null "> and r.to_user_id = #{toUserId}</if>
            <if test="assignmentType != null  and assignmentType != ''"> and r.assignment_type = #{assignmentType}</if>
            <if test="operatorId != null "> and r.operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and r.operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(r.assignment_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(r.assignment_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by r.assignment_time desc
    </select>
    
    <select id="selectCrmLeadAssignmentRecordById" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.id = #{id}
    </select>

    <select id="selectRecordsByLeadId" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.lead_id = #{leadId}
        order by r.assignment_time desc
    </select>

    <select id="selectRecordsByToUserId" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.to_user_id = #{userId}
        order by r.assignment_time desc
    </select>

    <select id="selectRecordsByFromUserId" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.from_user_id = #{userId}
        order by r.assignment_time desc
    </select>

    <select id="selectRecordsByOperatorId" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.operator_id = #{operatorId}
        order by r.assignment_time desc
    </select>

    <select id="selectRecordsByAssignmentType" parameterType="String" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.assignment_type = #{assignmentType}
        order by r.assignment_time desc
    </select>

    <select id="selectRecordsByTimeRange" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.assignment_time between #{startTime} and #{endTime}
        order by r.assignment_time desc
    </select>

    <select id="selectLatestRecordByLeadId" parameterType="Long" resultMap="CrmLeadAssignmentRecordResult">
        <include refid="selectCrmLeadAssignmentRecordVo"/>
        where r.lead_id = #{leadId}
        order by r.assignment_time desc
        limit 1
    </select>

    <insert id="insertCrmLeadAssignmentRecord" parameterType="com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord" useGeneratedKeys="true" keyProperty="id">
        insert into crm_lead_assignment_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">lead_id,</if>
            <if test="poolId != null">pool_id,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="assignmentType != null and assignmentType != ''">assignment_type,</if>
            <if test="assignmentReason != null">assignment_reason,</if>
            <if test="assignmentTime != null">assignment_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="poolId != null">#{poolId},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="assignmentType != null and assignmentType != ''">#{assignmentType},</if>
            <if test="assignmentReason != null">#{assignmentReason},</if>
            <if test="assignmentTime != null">#{assignmentTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmLeadAssignmentRecord" parameterType="com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord">
        update crm_lead_assignment_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">lead_id = #{leadId},</if>
            <if test="poolId != null">pool_id = #{poolId},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="assignmentType != null and assignmentType != ''">assignment_type = #{assignmentType},</if>
            <if test="assignmentReason != null">assignment_reason = #{assignmentReason},</if>
            <if test="assignmentTime != null">assignment_time = #{assignmentTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmLeadAssignmentRecordById" parameterType="Long">
        delete from crm_lead_assignment_records where id = #{id}
    </delete>

    <delete id="deleteCrmLeadAssignmentRecordByIds" parameterType="String">
        delete from crm_lead_assignment_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计相关查询 -->
    <select id="countAssignmentRecord" parameterType="com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord" resultType="int">
        select count(*) from crm_lead_assignment_records r
        <where>
            <if test="assignmentType != null and assignmentType != ''"> and r.assignment_type = #{assignmentType}</if>
            <if test="operatorId != null"> and r.operator_id = #{operatorId}</if>
        </where>
    </select>

    <select id="countRecordsByAssignmentType" resultMap="CrmLeadAssignmentRecordResult">
        select assignment_type, count(*) as id
        from crm_lead_assignment_records 
        group by assignment_type
        order by count(*) desc
    </select>

    <select id="countRecordsByToUser" resultMap="CrmLeadAssignmentRecordResult">
        select r.to_user_id, u.nick_name as to_user_name, count(*) as id
        from crm_lead_assignment_records r
        left join sys_user u on r.to_user_id = u.user_id
        where r.to_user_id is not null
        group by r.to_user_id, u.nick_name
        order by count(*) desc
    </select>

    <select id="countRecordsByOperator" resultMap="CrmLeadAssignmentRecordResult">
        select operator_id, operator_name, count(*) as id
        from crm_lead_assignment_records 
        group by operator_id, operator_name
        order by count(*) desc
    </select>

    <select id="countRecordsByTimeRange" resultType="int">
        select count(*) 
        from crm_lead_assignment_records 
        where assignment_time between #{startTime} and #{endTime}
    </select>

</mapper>
