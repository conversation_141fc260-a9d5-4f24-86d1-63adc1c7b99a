package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmLeadPool;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.common.mapper.CrmLeadPoolMapper;
import com.ruoyi.common.mapper.CrmLeadsMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;
import com.ruoyi.crm.service.ICrmLeadPoolService;

/**
 * 线索池Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class CrmLeadPoolServiceImpl implements ICrmLeadPoolService {
    
    @Autowired
    private CrmLeadPoolMapper crmLeadPoolMapper;
    
    @Autowired
    private CrmLeadsMapper crmLeadsMapper;
    
    @Autowired
    private ICrmLeadAssignmentRecordService assignmentRecordService;

    /**
     * 查询线索池
     * 
     * @param id 线索池主键
     * @return 线索池
     */
    @Override
    public CrmLeadPool selectCrmLeadPoolById(Long id) {
        return crmLeadPoolMapper.selectCrmLeadPoolById(id);
    }

    /**
     * 查询线索池列表
     * 
     * @param crmLeadPool 线索池
     * @return 线索池
     */
    @Override
    public List<CrmLeadPool> selectCrmLeadPoolList(CrmLeadPool crmLeadPool) {
        return crmLeadPoolMapper.selectCrmLeadPoolList(crmLeadPool);
    }

    /**
     * 查询可用的线索池列表
     * 
     * @param crmLeadPool 线索池查询条件
     * @return 线索池集合
     */
    @Override
    public List<CrmLeadPool> selectAvailableLeadPoolList(CrmLeadPool crmLeadPool) {
        return crmLeadPoolMapper.selectAvailableLeadPoolList(crmLeadPool);
    }

    /**
     * 新增线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    @Override
    public int insertCrmLeadPool(CrmLeadPool crmLeadPool) {
        crmLeadPool.setCreateTime(DateUtils.getNowDate());
        crmLeadPool.setCreateBy(SecurityUtils.getUsername());
        return crmLeadPoolMapper.insertCrmLeadPool(crmLeadPool);
    }

    /**
     * 修改线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    @Override
    public int updateCrmLeadPool(CrmLeadPool crmLeadPool) {
        crmLeadPool.setUpdateTime(DateUtils.getNowDate());
        crmLeadPool.setUpdateBy(SecurityUtils.getUsername());
        return crmLeadPoolMapper.updateCrmLeadPool(crmLeadPool);
    }

    /**
     * 批量删除线索池
     * 
     * @param ids 需要删除的线索池主键
     * @return 结果
     */
    @Override
    public int deleteCrmLeadPoolByIds(Long[] ids) {
        return crmLeadPoolMapper.deleteCrmLeadPoolByIds(ids);
    }

    /**
     * 删除线索池信息
     * 
     * @param id 线索池主键
     * @return 结果
     */
    @Override
    public int deleteCrmLeadPoolById(Long id) {
        return crmLeadPoolMapper.deleteCrmLeadPoolById(id);
    }

    /**
     * 手动分配线索
     * 
     * @param poolIds 线索池ID数组
     * @param toUserId 分配给的用户ID
     * @param reason 分配原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignLeads(Long[] poolIds, Long toUserId, String reason) {
        int successCount = 0;
        String currentUser = SecurityUtils.getUsername();
        Long currentUserId = SecurityUtils.getUserId();
        
        for (Long poolId : poolIds) {
            CrmLeadPool leadPool = crmLeadPoolMapper.selectCrmLeadPoolById(poolId);
            if (leadPool != null && leadPool.isAvailable()) {
                // 1. 更新线索池状态为已分配
                leadPool.setAssigned();
                leadPool.setUpdateBy(currentUser);
                leadPool.setUpdateTime(DateUtils.getNowDate());
                crmLeadPoolMapper.updateCrmLeadPool(leadPool);
                
                // 2. 如果有关联的线索ID，创建或更新线索记录
                if (leadPool.getLeadId() != null) {
                    CrmLeads lead = crmLeadsMapper.selectCrmLeadsById(leadPool.getLeadId());
                    if (lead != null) {
                        lead.setResponsiblePersonId(toUserId.toString());
                        lead.setUpdateBy(currentUser);
                        lead.setUpdateTime(DateUtils.getNowDate());
                        crmLeadsMapper.updateCrmLeads(lead);
                    }
                }
                
                // 3. 记录分配历史
                assignmentRecordService.createManualAssignmentRecord(
                    leadPool.getLeadId(), poolId, toUserId, reason);
                
                successCount++;
            }
        }
        
        return successCount;
    }

    /**
     * 批量分配线索
     * 
     * @param poolIds 线索池ID数组
     * @param userIds 用户ID数组
     * @param reason 分配原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAssignLeads(Long[] poolIds, Long[] userIds, String reason) {
        int successCount = 0;
        int userIndex = 0;
        
        for (Long poolId : poolIds) {
            Long toUserId = userIds[userIndex % userIds.length];
            int result = assignLeads(new Long[]{poolId}, toUserId, reason);
            successCount += result;
            userIndex++;
        }
        
        return successCount;
    }

    /**
     * 抢单 - 销售人员主动获取线索
     * 
     * @param poolId 线索池ID
     * @param userId 用户ID
     * @param reason 抢单原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int grabLead(Long poolId, Long userId, String reason) {
        CrmLeadPool leadPool = crmLeadPoolMapper.selectCrmLeadPoolById(poolId);
        if (leadPool == null || !leadPool.isAvailable()) {
            return 0;
        }
        
        String currentUser = SecurityUtils.getUsername();
        
        // 1. 更新线索池状态为已分配
        leadPool.setAssigned();
        leadPool.setUpdateBy(currentUser);
        leadPool.setUpdateTime(DateUtils.getNowDate());
        crmLeadPoolMapper.updateCrmLeadPool(leadPool);
        
        // 2. 如果有关联的线索ID，创建或更新线索记录
        if (leadPool.getLeadId() != null) {
            CrmLeads lead = crmLeadsMapper.selectCrmLeadsById(leadPool.getLeadId());
            if (lead != null) {
                lead.setResponsiblePersonId(userId.toString());
                lead.setUpdateBy(currentUser);
                lead.setUpdateTime(DateUtils.getNowDate());
                crmLeadsMapper.updateCrmLeads(lead);
            }
        }
        
        // 3. 记录抢单历史
        assignmentRecordService.createGrabAssignmentRecord(
            leadPool.getLeadId(), poolId, userId, reason);
        
        return 1;
    }

    /**
     * 回收线索到池中
     * 
     * @param leadId 线索ID
     * @param reason 回收原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recycleLead(Long leadId, String reason) {
        CrmLeads lead = crmLeadsMapper.selectCrmLeadsById(leadId);
        if (lead == null) {
            return 0;
        }
        
        String currentUser = SecurityUtils.getUsername();
        Long currentUserId = SecurityUtils.getUserId();
        Long fromUserId = Long.valueOf(lead.getResponsiblePersonId());
        
        // 1. 检查是否已在线索池中
        CrmLeadPool existingPool = crmLeadPoolMapper.selectCrmLeadPoolByLeadId(leadId);
        if (existingPool != null) {
            // 如果已在池中，更新状态为可用
            existingPool.setAvailable();
            existingPool.setSourceType("recycled");
            existingPool.setEnterPoolTime(new Date());
            existingPool.setUpdateBy(currentUser);
            existingPool.setUpdateTime(DateUtils.getNowDate());
            crmLeadPoolMapper.updateCrmLeadPool(existingPool);
            
            // 记录回收历史
            assignmentRecordService.createRecycleRecord(leadId, existingPool.getId(), fromUserId, reason);
        } else {
            // 2. 创建新的线索池记录
            CrmLeadPool newPool = new CrmLeadPool(leadId, "recycled");
            newPool.setRegion(lead.getAddress());
            newPool.setIndustry(lead.getCustomerIndustry());
            newPool.setCreateBy(currentUser);
            newPool.setCreateTime(DateUtils.getNowDate());
            crmLeadPoolMapper.insertCrmLeadPool(newPool);
            
            // 记录回收历史
            assignmentRecordService.createRecycleRecord(leadId, newPool.getId(), fromUserId, reason);
        }
        
        // 3. 清空线索的负责人
        lead.setResponsiblePersonId("");
        lead.setUpdateBy(currentUser);
        lead.setUpdateTime(DateUtils.getNowDate());
        crmLeadsMapper.updateCrmLeads(lead);
        
        return 1;
    }

    /**
     * 批量回收线索到池中
     * 
     * @param leadIds 线索ID数组
     * @param reason 回收原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchRecycleLeads(Long[] leadIds, String reason) {
        int successCount = 0;
        
        for (Long leadId : leadIds) {
            int result = recycleLead(leadId, reason);
            successCount += result;
        }
        
        return successCount;
    }

    /**
     * 添加线索到池中
     * 
     * @param leadId 线索ID（可为空，表示纯线索池数据）
     * @param qualityLevel 质量等级
     * @param priority 优先级
     * @param region 地区
     * @param industry 行业
     * @param estimatedValue 预估价值
     * @param remarks 备注
     * @return 结果
     */
    @Override
    public int addToPool(Long leadId, String qualityLevel, Integer priority, 
                        String region, String industry, String estimatedValue, String remarks) {
        CrmLeadPool leadPool = new CrmLeadPool(leadId, "new");
        leadPool.setQualityLevel(qualityLevel);
        leadPool.setPriority(priority);
        leadPool.setRegion(region);
        leadPool.setIndustry(industry);
        if (estimatedValue != null && !estimatedValue.isEmpty()) {
            leadPool.setEstimatedValue(new BigDecimal(estimatedValue));
        }
        leadPool.setRemarks(remarks);
        
        return insertCrmLeadPool(leadPool);
    }

    /**
     * 更新线索池状态
     * 
     * @param poolId 线索池ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updatePoolStatus(Long poolId, String status) {
        return crmLeadPoolMapper.updateLeadPoolStatus(poolId, status, SecurityUtils.getUsername());
    }

    /**
     * 获取线索池统计信息
     *
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getLeadPoolStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总数统计
        CrmLeadPool query = new CrmLeadPool();
        int totalCount = crmLeadPoolMapper.countLeadPool(query);
        stats.put("totalCount", totalCount);

        // 可用数量
        query.setPoolStatus("available");
        int availableCount = crmLeadPoolMapper.countLeadPool(query);
        stats.put("availableCount", availableCount);

        // 已分配数量
        query.setPoolStatus("assigned");
        int assignedCount = crmLeadPoolMapper.countLeadPool(query);
        stats.put("assignedCount", assignedCount);

        // 按状态统计
        List<CrmLeadPool> statusStats = crmLeadPoolMapper.countLeadPoolByStatus();
        stats.put("statusStats", statusStats);

        // 按质量等级统计
        List<CrmLeadPool> qualityStats = crmLeadPoolMapper.countLeadPoolByQualityLevel();
        stats.put("qualityStats", qualityStats);

        // 按地区统计
        List<CrmLeadPool> regionStats = crmLeadPoolMapper.countLeadPoolByRegion();
        stats.put("regionStats", regionStats);

        // 按行业统计
        List<CrmLeadPool> industryStats = crmLeadPoolMapper.countLeadPoolByIndustry();
        stats.put("industryStats", industryStats);

        return stats;
    }

    /**
     * 根据质量等级获取线索池列表
     *
     * @param qualityLevel 质量等级
     * @return 线索池集合
     */
    @Override
    public List<CrmLeadPool> getLeadPoolByQualityLevel(String qualityLevel) {
        return crmLeadPoolMapper.selectLeadPoolByQualityLevel(qualityLevel);
    }

    /**
     * 根据地区获取线索池列表
     *
     * @param region 地区
     * @return 线索池集合
     */
    @Override
    public List<CrmLeadPool> getLeadPoolByRegion(String region) {
        return crmLeadPoolMapper.selectLeadPoolByRegion(region);
    }

    /**
     * 根据行业获取线索池列表
     *
     * @param industry 行业
     * @return 线索池集合
     */
    @Override
    public List<CrmLeadPool> getLeadPoolByIndustry(String industry) {
        return crmLeadPoolMapper.selectLeadPoolByIndustry(industry);
    }

    /**
     * 检查线索是否在池中
     *
     * @param leadId 线索ID
     * @return 是否在池中
     */
    @Override
    public boolean isLeadInPool(Long leadId) {
        CrmLeadPool leadPool = crmLeadPoolMapper.selectCrmLeadPoolByLeadId(leadId);
        return leadPool != null && leadPool.isAvailable();
    }

    /**
     * 根据线索ID获取线索池信息
     *
     * @param leadId 线索ID
     * @return 线索池信息
     */
    @Override
    public CrmLeadPool getLeadPoolByLeadId(Long leadId) {
        return crmLeadPoolMapper.selectCrmLeadPoolByLeadId(leadId);
    }

    /**
     * 清理过期记录（定时任务使用）
     *
     * @return 清理数量
     */
    @Override
    public int cleanupExpiredRecords() {
        // 这里可以实现清理逻辑，比如清理超过一定时间的已分配但未跟进的线索
        // 暂时返回0，具体逻辑可以根据业务需求实现
        return 0;
    }
}
