import request from '@/utils/request'

// 查询合同列表
export function listContract(query) {
  return request({
    url: '/front/crm/contracts/list',
    method: 'get',
    params: query
  })
}

// 查询合同详细
export function getContract(id) {
  return request({
    url: '/front/crm/contracts/' + id,
    method: 'get'
  })
}

// 新增合同
export function addContract(data) {
  return request({
    url: '/front/crm/contracts',
    method: 'post',
    data: data
  })
}

// 修改合同
export function updateContract(data) {
  return request({
    url: '/front/crm/contracts',
    method: 'put',
    data: data
  })
}

// 删除合同
export function delContract(id) {
  return request({
    url: '/front/crm/contracts/' + id,
    method: 'delete'
  })
}

// 根据客户ID查询合同
export function getContractByCustomer(customerId) {
  return request({
    url: '/front/crm/contracts/customer/' + customerId,
    method: 'get'
  })
}

// 搜索合同
export function searchContract(keyword) {
  return request({
    url: '/front/crm/contracts/search',
    method: 'get',
    params: { 
      keyword: keyword || '',
      pageNum: 1,
      pageSize: 100
    }
  })
}