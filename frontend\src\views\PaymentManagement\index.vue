<template>
    <el-container class="payment-management">
        <!-- 使用新的导航组件 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>回款管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openPaymentDialog"
                        class="action-btn primary-btn"
                    >
                        新建回款
                    </el-button>
                    <el-button 
                        type="primary"
                        plain
                        size="small"
                        class="action-btn"
                    >
                        查看
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 根据activeTab显示不同内容 -->
                <div v-if="activeTab === 'payments'">
                    <!-- 筛选区域 -->
                    <el-row :gutter="20" class="filters">
                        <el-col :span="8" style="display: contents;">
                            <el-input v-model="searchInput" style="width: 240px" placeholder="回款编号/合同编号">
                                <template #prefix>
                                    <el-icon class="el-input__icon">
                                        <search />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-text class="mx-1" style="margin-left: 10px;">显示：</el-text>
                            <el-radio-group v-model="filterType" size="default">
                                <el-radio-button value="all">全部回款</el-radio-button>
                                <el-radio-button value="mine">我负责的</el-radio-button>
                                <el-radio-button value="subordinate">下属负责的</el-radio-button>
                                <el-radio-button value="following">我关注的回款</el-radio-button>
                            </el-radio-group>
                        </el-col>
                    </el-row>

                    <!-- 数据表格 -->
                    <el-table ref="paymentTable" :data="payments" border sortable tooltip-effect="dark"
                        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                        style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                        @selection-change="handleSelectionChange">
                        <template v-for="col in tableColumns" :key="col.prop">
                            <el-table-column v-bind="col">
                                <template #default="scope" v-if="col.prop === 'payment_number'">
                                    <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">{{
                                        scope.row.payment_number }}</el-button>
                                </template>
                            </el-table-column>
                        </template>
                        <table-operations :config="tableOperations" @operation="handleTableOperation" />
                    </el-table>
                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" />
                </div>

                <!-- 回款计划Tab -->
                <div v-else-if="activeTab === 'plans'">
                    <payment-plans-tab />
                </div>

                <!-- 回款记录Tab -->
                <div v-else-if="activeTab === 'followup'">
                    <payment-records-tab />
                </div>
            </el-main>
        </el-container>

        <!-- 使用新的抽屉组件 -->
        <common-drawer v-model="drawerVisible" entity-type="回款" :entity-data="currentPayment" :drawer-config="drawerConfig"
            model-name="回款" @update:entity-data="handlePaymentUpdate" />

        <!-- 新建回款对话框 -->
        <el-dialog v-model="dialogVisible" width="900px" draggable>
            <el-row>
                <h1 style="margin-left: 30px;">新建回款</h1>
            </el-row>
            <el-row style="margin: 10; position: contents; display: flex; justify-content: left; align-items: center;">
                <div
                    style="border-radius: 30px; background: blue; width: 4px; height: 1.2em; left: 20px; margin-bottom: 0px; margin-left: 30px">
                </div>
                <h4 style="margin-left: 10px;">请填写回款信息</h4>
            </el-row>
            <el-form :model="newPayment"
                :label-position="newPaymentFormConfig.layout.labelPosition"
                :size="newPaymentFormConfig.layout.size" style="padding: 0 30px 30px;">
                <el-row :gutter="20">
                    <el-col v-for="field in newPaymentFormConfig.fields" :key="field.field" :span="field.colSpan">
                        <el-form-item :label="field.label">
                            <component :is="field.component" v-model="newPayment[field.field]"
                                v-bind="field.props || {}" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelPayment">取消</el-button>
                <el-button type="primary" @click="savePayment">保存</el-button>
            </span>
        </el-dialog>
    </el-container>
</template>

<script lang="ts">
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import SideNav from '@/components/SideNav/index.vue';
import TableOperations from '@/components/TableOperations/index.vue';
import PaymentPlansTab from './tabs/PaymentPlansTab.vue';
import PaymentRecordsTab from './tabs/PaymentRecordsTab.vue';
import { OperationEvent, TableRowData } from '@/components/TableOperations/types';
import { defineComponent } from 'vue';
import { drawerConfig, navConfig, newPaymentFormConfig, tableColumns, tableOperations } from './config';

interface PaymentData extends TableRowData {
    payment_number: string;
    contract_number: string;
    customer_name: string;
    payment_amount: number;
    payment_date: string;
    payment_method: string;
    payment_status: string;
    payment_type: string;
    bank_account: string;
    bank_name: string;
    remarks: string;
    invoice_status: string;
    invoice_number: string;
    invoice_amount: number;
}

type FilterType = 'all' | 'mine' | 'subordinate' | 'following';

export default defineComponent({
    name: 'PaymentManagement',
    components: {
        CommonDrawer,
        TableOperations,
        SideNav,
        PaymentPlansTab,
        PaymentRecordsTab
    },
    data() {
        const emptyPayment: PaymentData = {
            id: 0,
            payment_number: '',
            contract_number: '',
            customer_name: '',
            payment_amount: 0,
            payment_date: '',
            payment_method: '',
            payment_status: '',
            payment_type: '',
            bank_account: '',
            bank_name: '',
            remarks: '',
            invoice_status: '',
            invoice_number: '',
            invoice_amount: 0
        };

        return {
            activeTab: 'payments',
            filterType: 'all' as FilterType,
            searchInput: '',
            total: 10,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
            payments: [
                {
                    id: 1,
                    payment_number: 'PAY20240221001',
                    contract_number: 'CON20240221001',
                    customer_name: '客户A',
                    payment_amount: 50000.00,
                    payment_date: '2024-02-21',
                    payment_method: '银行转账',
                    payment_status: '已收款',
                    payment_type: '预付款',
                    bank_account: '****************',
                    bank_name: '中国工商银行',
                    remarks: '首期付款',
                    invoice_status: '已开票',
                    invoice_number: 'INV20240221001',
                    invoice_amount: 50000.00
                }
            ] as PaymentData[],
            dialogVisible: false,
            newPayment: { ...emptyPayment },
            drawerVisible: false,
            currentPayment: { ...emptyPayment },
            tableColumns,
            newPaymentFormConfig,
            drawerConfig,
            tableOperations,
            navConfig
        };
    },
    watch: {
        filterType(newType: FilterType) {
            this.handleFilterChange(newType);
        }
    },
    methods: {
        handleSelectionChange(val: PaymentData[]): void {
            console.log(val);
        },
        openPaymentDialog(): void {
            this.dialogVisible = true;
        },
        cancelPayment(): void {
            this.dialogVisible = false;
        },
        savePayment(): void {
            console.log('New Payment:', this.newPayment);
            this.payments.push({
                ...this.newPayment,
                id: this.payments.length + 1,
            });
            this.dialogVisible = false;
            this.newPayment = {
                id: 0,
                payment_number: '',
                contract_number: '',
                customer_name: '',
                payment_amount: 0,
                payment_date: '',
                payment_method: '',
                payment_status: '',
                payment_type: '',
                bank_account: '',
                bank_name: '',
                remarks: '',
                invoice_status: '',
                invoice_number: '',
                invoice_amount: 0
            };
        },
        openDrawer(row: PaymentData): void {
            this.currentPayment = row;
            this.drawerVisible = true;
        },
        handlePaymentUpdate(newData: PaymentData): void {
            const index = this.payments.findIndex(payment => payment.id === newData.id);
            if (index > -1) {
                this.payments[index] = { ...newData };
            }
        },
        handleTableOperation({ handler, row }: OperationEvent): void {
            if (typeof handler === 'function') {
                handler(row);
                return;
            }

            if (this[handler as keyof typeof this]) {
                (this[handler as keyof typeof this] as Function)(row);
                return;
            }

            console.warn(`Handler ${handler} not found`);
        },
        handleFilterChange(type: FilterType): void {
            console.log('Filter changed to:', type);
            // 实现筛选逻辑
        }
    },
});
</script>

<style scoped>
.payment-management {
    height: 100vh;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

.filters {
    margin: 20px 0;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.link-button {
    padding: 0;
}
</style>
