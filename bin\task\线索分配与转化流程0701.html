<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线索分配与转化流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>线索分配与转化流程</h1>
        
        <h2>1. 概述</h2>
        <p>本文档旨在明确CRM系统中的线索分配与转化流程，为相关开发和业务人员提供清晰的指引。线索是销售流程的起点，规范化、自动化的处理流程对提升销售效率至关重要。</p>
        
        <h2>2. 线索来源</h2>
        <p>系统中的线索主要有以下几个来源：</p>
        <ul>
            <li><strong>企业微信同步：</strong> 企业微信的外部联系人将自动同步为CRM线索。</li>
            <li><strong>系统内新建：</strong> 销售人员在CRM系统中手动录入的潜在客户信息。</li>
            <li><strong>外部注册：</strong> 用户通过官方网站、小程序等渠道注册后自动生成的线索。</li>
        </ul>

        <h2>3. 线索分配流程</h2>
        <p>线索分配是将新获取的线索指派给合适的销售人员进行跟进的过程。分配方式分为手动和自动两种。</p>

        <h3>3.1. 分配流程图</h3>
        <div class="mermaid">
flowchart TD
    A[新线索进入系统] --> B{分配方式};
    B -->|手动分配| C[管理员/主管在后台选择线索];
    C --> D[选择指定的销售人员];
    D --> F[执行分配];

    B -.->|自动分配-规划中| E[系统根据预设规则自动分配];
    E --> F;
    
    F --> G[更新线索负责人];
    G --> H[更新线索状态为“待跟进”];
    H --> I[向负责人发送通知];
    I --> J[分配完成];

    style E stroke-dasharray: 5, 5
        </div>

        <h3>3.2. 分配规则</h3>
        <div class="highlight">
            <h4>手动分配</h4>
            <p>由具备权限的管理人员（如销售主管）在系统中手动将一条或多条线索分配给指定的销售人员。</p>
            <h4>自动分配（正在规划中）</h4>
            <p>系统可以根据预设的规则自动进行分配，例如：</p>
            <ul>
                <li><strong>轮流分配（Round-Robin）：</strong> 将线索依次分配给销售团队中的每个成员。</li>
                <li><strong>基于区域：</strong> 根据线索的地理位置信息（如省份、城市）分配给对应区域的销售。</li>
                <li><strong>基于负载：</strong> 分配给当前持有线索数量最少的销售人员，以保证工作负载均衡。</li>
            </ul>
        </div>

        <h2>4. 线索转化流程</h2>
        <p>当线索经过跟进和培育，表现出明确的购买意向后，就需要将其转化为客户或联系人，纳入更深层次的管理。</p>

        <h3>4.1. 转化场景</h3>
        <ul>
            <li><strong>转化为新客户：</strong> 当线索代表一个全新的公司/组织时，系统会创建一个新的客户主记录，并将线索信息转化为该客户下的首要联系人。</li>
            <li><strong>转化为联系人：</strong> 当线索所属的公司在系统中已作为客户存在时，仅将该线索转化为此客户下的一个新联系人。</li>
        </ul>

        <h3>4.2. 转化时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant User as 用户
    participant System as CRM系统
    participant Database as 数据库

    User->>System: 1. 选择线索，点击“转化”
    System->>User: 2. 显示转化选项（转为新客户/关联到已有客户）

    alt 转为新客户
        User->>System: 3a. 选择“转为新客户”并填写客户信息
        System->>Database: 4a. 创建新的`crm_business_customers`记录
        System->>Database: 5a. 基于线索信息创建新的`crm_business_contacts`记录
        System->>Database: 6a. 更新原`crm_business_leads`记录状态为“已转化”
        System->>User: 7a. 返回成功信息
    else 关联到已有客户
        User->>System: 3b. 选择“关联到已有客户”并搜索选择客户
        System->>Database: 4b. 基于线索信息创建新的`crm_business_contacts`记录，并关联已有客户
        System->>Database: 5b. 更新原`crm_business_leads`记录状态为“已转化”
        System->>User: 6b. 返回成功信息
    end
        </div>

        <h3>4.3. 转化后的数据处理</h3>
        <table border="1">
            <thead>
                <tr>
                    <th>处理事项</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>状态变更</td>
                    <td>原线索记录的状态更新为“已转化”，不再出现在待跟进线索列表中。</td>
                </tr>
                <tr>
                    <td>数据迁移</td>
                    <td>线索的跟进记录、备注等信息会自动迁移或关联到新创建的客户/联系人下，保证历史信息不丢失。</td>
                </tr>
                <tr>
                    <td>所有权</td>
                    <td>转化后的客户/联系人负责人默认为原线索的负责人。</td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
