-- 拜访计划提醒定时任务初始化脚本

-- 插入定时任务配置
INSERT INTO sys_job (
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    misfire_policy,
    concurrent,
    status,
    create_by,
    create_time,
    remark
) VALUES (
    '拜访计划提醒任务',
    'DEFAULT',
    'visitPlanReminder.processReminders',
    '0 */5 * * * ?',  -- 每5分钟执行一次
    '3',               -- 立即执行
    '1',               -- 禁止并发
    '0',               -- 正常状态
    'admin',
    NOW(),
    '每5分钟检查一次即将到期的拜访计划，并发送提醒通知'
);

-- 如果需要配置多个提醒策略，可以添加更多任务
-- 例如：每天早上9点发送当天的拜访计划汇总
INSERT INTO sys_job (
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    misfire_policy,
    concurrent,
    status,
    create_by,
    create_time,
    remark
) VALUES (
    '每日拜访计划汇总',
    'DEFAULT',
    'visitPlanReminder.dailySummary',
    '0 0 9 * * ?',     -- 每天早上9点执行
    '3',               -- 立即执行
    '1',               -- 禁止并发
    '1',               -- 暂停状态（需要时启用）
    'admin',
    NOW(),
    '每天早上9点发送当天的拜访计划汇总'
);

-- 查询已添加的定时任务
SELECT job_id, job_name, invoke_target, cron_expression, status, remark
FROM sys_job
WHERE invoke_target LIKE 'visitPlanReminder%';
