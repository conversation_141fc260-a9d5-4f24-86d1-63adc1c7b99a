package com.ruoyi.crm.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmCustomerFollowupRecordService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 客户跟进记录Controller
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Anonymous
@RestController
@RequestMapping("/front/crm/customer/followup")
public class CrmCustomerFollowupRecordController extends BaseController {
    
    @Autowired
    private ICrmCustomerFollowupRecordService crmCustomerFollowupRecordService;

    private static final Logger log = LoggerFactory.getLogger(CrmCustomerFollowupRecordController.class);

    /**
     * 查询客户跟进记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        try {
            startPage();
            List<CrmCustomerFollowupRecord> list = crmCustomerFollowupRecordService.selectCrmCustomerFollowupRecordList(crmCustomerFollowupRecord);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询跟进记录列表失败: {}", e.getMessage(), e);
            return getDataTable(java.util.Collections.emptyList());
        }
    }

    /**
     * 根据客户ID查询跟进记录列表
     */
    @GetMapping("/customer/{customerId}")
    public AjaxResult getFollowupRecordsByCustomerId(@PathVariable("customerId") Long customerId) {
        try {
            List<CrmCustomerFollowupRecord> list = crmCustomerFollowupRecordService.selectFollowupRecordsByCustomerId(customerId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询客户跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询客户跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户跟进记录详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        try {
            CrmCustomerFollowupRecord record = crmCustomerFollowupRecordService.selectCrmCustomerFollowupRecordById(id);
            return AjaxResult.success(record);
        } catch (Exception e) {
            log.error("查询跟进记录详情失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询跟进记录详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增客户跟进记录
     */
    @Log(title = "客户跟进记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId == null) {
                return AjaxResult.error("用户未登录");
            }
            
            crmCustomerFollowupRecord.setCreatorId(currentUserId);
            int result = crmCustomerFollowupRecordService.insertCrmCustomerFollowupRecord(crmCustomerFollowupRecord);
            return result > 0 ? AjaxResult.success("新增跟进记录成功") : AjaxResult.error("新增跟进记录失败");
        } catch (Exception e) {
            log.error("新增跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("新增跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 修改客户跟进记录
     */
    @Log(title = "客户跟进记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        try {
            int result = crmCustomerFollowupRecordService.updateCrmCustomerFollowupRecord(crmCustomerFollowupRecord);
            return result > 0 ? AjaxResult.success("修改跟进记录成功") : AjaxResult.error("修改跟进记录失败");
        } catch (Exception e) {
            log.error("修改跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("修改跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 删除客户跟进记录
     */
    @Log(title = "客户跟进记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        try {
            int result = crmCustomerFollowupRecordService.deleteCrmCustomerFollowupRecordByIds(ids);
            return result > 0 ? AjaxResult.success("删除跟进记录成功") : AjaxResult.error("删除跟进记录失败");
        } catch (Exception e) {
            log.error("删除跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 统计客户的跟进记录数量
     */
    @GetMapping("/count/{customerId}")
    public AjaxResult countByCustomerId(@PathVariable("customerId") Long customerId) {
        try {
            int count = crmCustomerFollowupRecordService.countFollowupRecordsByCustomerId(customerId);
            return AjaxResult.success(count);
        } catch (Exception e) {
            log.error("统计跟进记录数量失败: {}", e.getMessage(), e);
            return AjaxResult.error("统计跟进记录数量失败: " + e.getMessage());
        }
    }

    /**
     * 查询最近的跟进记录
     */
    @GetMapping("/recent/{customerId}")
    public AjaxResult getRecentFollowupRecords(@PathVariable("customerId") Long customerId,
                                             @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<CrmCustomerFollowupRecord> list = crmCustomerFollowupRecordService.selectRecentFollowupRecords(customerId, limit);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询最近跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询最近跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询需要跟进的记录
     */
    @GetMapping("/pending")
    public AjaxResult getPendingFollowupRecords() {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId == null) {
                return AjaxResult.error("用户未登录");
            }
            
            List<CrmCustomerFollowupRecord> list = crmCustomerFollowupRecordService.selectPendingFollowupRecords(currentUserId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询待跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询待跟进记录失败: " + e.getMessage());
        }
    }

    /**
     * 快速创建跟进记录
     */
    @Log(title = "客户跟进记录", businessType = BusinessType.INSERT)
    @PostMapping("/quick")
    public AjaxResult quickCreate(@RequestParam Long customerId,
                                @RequestParam String followupType,
                                @RequestParam String content,
                                @RequestParam(required = false) String result,
                                @RequestParam(required = false) String nextFollowupTime,
                                @RequestParam(defaultValue = "false") Boolean isImportant) {
        try {
            java.util.Date nextTime = null;
            if (nextFollowupTime != null && !nextFollowupTime.isEmpty()) {
                // 简单的日期解析，实际项目中应该使用更严格的日期解析
                nextTime = java.sql.Timestamp.valueOf(nextFollowupTime);
            }
            
            int recordResult = crmCustomerFollowupRecordService.createFollowupRecord(
                customerId, followupType, content, result, nextTime, isImportant);
                
            return recordResult > 0 ? AjaxResult.success("创建跟进记录成功") : AjaxResult.error("创建跟进记录失败");
        } catch (Exception e) {
            log.error("快速创建跟进记录失败: {}", e.getMessage(), e);
            return AjaxResult.error("快速创建跟进记录失败: " + e.getMessage());
        }
    }
}