<template>
  <div class="approvals-tab">
    <!-- 审批状态卡片 -->
    <el-card class="approval-status-card" v-if="processInfo">
      <template #header>
        <div class="card-header">
          <span>审批状态</span>
          <el-button 
            v-if="canCancel" 
            type="danger" 
            size="small" 
            @click="handleCancelProcess"
          >
            撤销流程
          </el-button>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="流程状态">
          <el-tag :type="getProcessStatusType(currentPlan.processStatus)">
            {{ getProcessStatusText(currentPlan.processStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getApprovalStatusType(currentPlan.approvalStatus)">
            {{ currentPlan.approvalStatus }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="流程开始时间">
          {{ parseTime(processInfo.startTime, '{y}-{m}-{d} {h}:{i}') }}
        </el-descriptions-item>
        <el-descriptions-item label="流程耗时">
          {{ formatDuration(processInfo.durationInMillis) }}
        </el-descriptions-item>
        <el-descriptions-item label="当前处理人" v-if="currentTask">
          {{ currentTask.assignee || '系统自动处理' }}
        </el-descriptions-item>
        <el-descriptions-item label="当前任务" v-if="currentTask">
          {{ currentTask.taskName }}
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 当前任务操作 -->
      <div class="current-task-actions" v-if="currentTask && canApprove">
        <el-divider content-position="left">当前任务操作</el-divider>
        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalFormRef" label-width="80px">
          <el-form-item label="审批意见" prop="comment">
            <el-input 
              v-model="approvalForm.comment" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入审批意见"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="handleApprove" :loading="approving">
              <el-icon><check /></el-icon>
              审批通过
            </el-button>
            <el-button type="danger" @click="handleReject" :loading="rejecting">
              <el-icon><close /></el-icon>
              审批拒绝
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- 无审批流程提示 -->
    <el-empty v-else description="该回款计划暂未启动审批流程" />
    
    <!-- 审批历史 -->
    <el-card class="approval-history-card" v-if="taskHistory.length > 0">
      <template #header>
        <span>审批历史</span>
      </template>
      
      <el-timeline>
        <el-timeline-item
          v-for="task in taskHistory"
          :key="task.taskId"
          :timestamp="parseTime(task.startTime, '{y}-{m}-{d} {h}:{i}')"
          placement="top"
        >
          <el-card class="timeline-card">
            <div class="task-info">
              <div class="task-header">
                <span class="task-name">{{ task.taskName }}</span>
                <el-tag 
                  :type="task.endTime ? 'success' : 'primary'" 
                  size="small"
                >
                  {{ task.endTime ? '已完成' : '进行中' }}
                </el-tag>
              </div>
              
              <div class="task-details">
                <p><strong>处理人:</strong> {{ task.assignee || '系统自动' }}</p>
                <p v-if="task.endTime">
                  <strong>完成时间:</strong> 
                  {{ parseTime(task.endTime, '{y}-{m}-{d} {h}:{i}') }}
                </p>
                <p v-if="task.durationInMillis">
                  <strong>处理耗时:</strong> 
                  {{ formatDuration(task.durationInMillis) }}
                </p>
                <p v-if="task.comment">
                  <strong>审批意见:</strong> 
                  <span class="comment">{{ task.comment }}</span>
                </p>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
    
    <!-- 流程图 -->
    <el-card class="process-diagram-card" v-if="processInfo">
      <template #header>
        <span>流程图</span>
      </template>
      <div class="process-diagram">
        <p class="text-center text-gray-500">流程图功能开发中...</p>
        <!-- 这里可以集成流程图显示组件 -->
      </div>
    </el-card>
  </div>
</template>

<script setup name="PaymentApprovalsTab">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { 
  getProcessByPlanId, 
  approveTask, 
  rejectTask, 
  cancelProcess,
  getCurrentTask,
  getTaskHistory 
} from '@/api/crm/paymentPlanWorkflow'

const { proxy } = getCurrentInstance()
const { parseTime } = proxy.useParseTime

// 属性
const props = defineProps({
  planId: {
    type: Number,
    required: true
  },
  planData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const processInfo = ref(null)
const currentTask = ref(null)
const taskHistory = ref([])
const currentPlan = ref({})
const approving = ref(false)
const rejecting = ref(false)

// 审批表单
const approvalForm = ref({
  comment: ''
})

const approvalRules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' }
  ]
}

// 计算属性
const canApprove = computed(() => {
  // 这里可以根据当前用户权限和任务分配情况判断是否可以审批
  // 简单示例：如果有当前任务且用户有审批权限
  return currentTask.value && currentTask.value.assignee === proxy.$auth.user?.userId?.toString()
})

const canCancel = computed(() => {
  // 判断是否可以撤销：流程创建人或管理员可以撤销进行中的流程
  return processInfo.value && 
         currentPlan.value.processStatus === 'RUNNING' &&
         (currentPlan.value.createBy === proxy.$auth.user?.userName || proxy.$auth.hasRole('admin'))
})

// 监听计划ID变化
watch(() => props.planId, (newVal) => {
  if (newVal) {
    loadApprovalData()
  }
}, { immediate: true })

// 监听计划数据变化
watch(() => props.planData, (newVal) => {
  if (newVal) {
    currentPlan.value = newVal
  }
}, { immediate: true })

// 加载审批数据
const loadApprovalData = async () => {
  if (!props.planId) return
  
  try {
    const response = await getProcessByPlanId(props.planId)
    const data = response.data
    
    processInfo.value = data.processInfo
    currentTask.value = data.currentTask
    taskHistory.value = data.taskHistory || []
    
    // 如果有流程实例，获取详细信息
    if (processInfo.value) {
      // 可以在这里获取更多流程相关信息
    }
  } catch (error) {
    console.error('加载审批数据失败:', error)
  }
}

// 审批通过
const handleApprove = async () => {
  if (!currentTask.value) {
    ElMessage.error('没有可处理的任务')
    return
  }
  
  try {
    await proxy.$refs.approvalFormRef.validate()
    
    approving.value = true
    await approveTask({
      taskId: currentTask.value.taskId,
      comment: approvalForm.value.comment
    })
    
    ElMessage.success('审批通过成功')
    approvalForm.value.comment = ''
    
    // 重新加载数据
    await loadApprovalData()
    
    // 通知父组件刷新
    proxy.$emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批通过失败: ' + error.message)
    }
  } finally {
    approving.value = false
  }
}

// 审批拒绝
const handleReject = async () => {
  if (!currentTask.value) {
    ElMessage.error('没有可处理的任务')
    return
  }
  
  try {
    await proxy.$refs.approvalFormRef.validate()
    
    await ElMessageBox.confirm(
      '确定要拒绝该回款计划的审批吗？',
      '审批拒绝确认',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    rejecting.value = true
    await rejectTask({
      taskId: currentTask.value.taskId,
      comment: approvalForm.value.comment
    })
    
    ElMessage.success('审批拒绝成功')
    approvalForm.value.comment = ''
    
    // 重新加载数据
    await loadApprovalData()
    
    // 通知父组件刷新
    proxy.$emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批拒绝失败: ' + error.message)
    }
  } finally {
    rejecting.value = false
  }
}

// 撤销流程
const handleCancelProcess = async () => {
  if (!processInfo.value) {
    ElMessage.error('没有可撤销的流程')
    return
  }
  
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入撤销原因:',
      '撤销审批流程',
      {
        confirmButtonText: '确定撤销',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '撤销原因不能为空'
      }
    )
    
    await cancelProcess({
      processInstanceId: processInfo.value.processInstanceId,
      reason: reason
    })
    
    ElMessage.success('流程撤销成功')
    
    // 重新加载数据
    await loadApprovalData()
    
    // 通知父组件刷新
    proxy.$emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤销流程失败: ' + error.message)
    }
  }
}

// 获取流程状态类型
const getProcessStatusType = (status) => {
  const statusMap = {
    'RUNNING': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'warning',
    'REJECTED': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取流程状态文本
const getProcessStatusText = (status) => {
  const statusMap = {
    'RUNNING': '运行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已撤销',
    'REJECTED': '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取审批状态类型
const getApprovalStatusType = (status) => {
  const statusMap = {
    '待提交': 'info',
    '审批中': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger',
    '已撤销': 'warning'
  }
  return statusMap[status] || 'info'
}

// 格式化持续时间
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '-'
  
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${seconds}秒`
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadApprovalData()
})

// 定义 emits
defineEmits(['refresh'])
</script>

<style scoped>
.approvals-tab {
  padding: 20px;
}

.approval-status-card,
.approval-history-card,
.process-diagram-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-task-actions {
  margin-top: 20px;
}

.timeline-card {
  margin-bottom: 10px;
}

.task-info {
  padding: 10px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-name {
  font-weight: bold;
  font-size: 16px;
}

.task-details {
  line-height: 1.6;
}

.task-details p {
  margin: 5px 0;
}

.comment {
  color: #666;
  font-style: italic;
  background: #f5f7fa;
  padding: 5px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 5px;
}

.process-diagram {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 4px;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #909399;
}
</style>