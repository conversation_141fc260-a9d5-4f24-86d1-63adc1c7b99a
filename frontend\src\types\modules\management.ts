import type { TableRowData } from '@/types';

// 客户数据接口
export interface CustomerData extends TableRowData {
    id: number;
    name: string;
    industry: string;
    level: string;
    status: string;
    phone: string;
    email: string;
    address?: string;
    remarks?: string;
    createTime?: string;
    lastContactTime?: string;
    nextContactTime?: string;
    owner?: string;
}

// 联系人数据接口
export interface ContactData extends TableRowData {
    id: number;
    name: string;
    position: string;
    phone: string;
    email: string;
    customerId: number;
    customerName: string;
    remarks?: string;
    createTime?: string;
    lastContactTime?: string;
}

// 商机数据接口
export interface OpportunityData extends TableRowData {
    id: number;
    name: string;
    customerId: number;
    customerName: string;
    stage: string;
    amount: number;
    probability: number;
    expectedClosingDate: string;
    owner?: string;
    createTime?: string;
    lastUpdateTime?: string;
}

// 合同数据接口
export interface ContractData extends TableRowData {
    id: number;
    contractNo: string;
    name: string;
    customerId: number;
    customerName: string;
    amount: number;
    signDate: string;
    startDate: string;
    endDate: string;
    status: string;
    owner?: string;
    createTime?: string;
}

// 付款数据接口
export interface PaymentData extends TableRowData {
    id: number;
    paymentNo: string;
    contractId: number;
    contractNo: string;
    customerId: number;
    customerName: string;
    amount: number;
    paymentDate: string;
    paymentMethod: string;
    status: string;
    remarks?: string;
    createTime?: string;
}

// 查询参数接口
export interface QueryParams {
    pageNum: number;
    pageSize: number;
    searchKeyword?: string;
    filterType?: string;
    [key: string]: any;
}

// 表单数据接口
export interface FormData {
    id?: number;
    [key: string]: any;
} 