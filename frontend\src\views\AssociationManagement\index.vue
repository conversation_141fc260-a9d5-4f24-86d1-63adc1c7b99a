/**
 * @description 线索管理主页面
 * <AUTHOR>
 * @date 2024-03-20
 */

<template>
    <el-container class="leads-management">
        <!-- 使用新的导航组件 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>线索管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openLeadDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建线索
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 根据activeTab显示不同的组件 -->
                <component :is="currentComponent" />
            </el-main>
        </el-container>

        <!-- 使用新的抽屉组件 -->
        <common-drawer 
            v-model="drawerVisible" 
            entity-type="lead" 
            model-name="线索" 
            :entity-data="currentLead" 
            :drawer-config="localDrawerConfig"
            :header-component="headerComponent"
            :actions="drawerActions"
            @update:entity-data="handleLeadUpdate" 
        />

        <!-- 新建线索对话框 -->
        <common-form-dialog 
            v-model="leadDialogVisible" 
            title="新建线索" 
            sub-title="请填写线索信息" 
            :form-config="newLeadFormConfig"
            :initial-data="newLead" 
            @cancel="cancelLead" 
            @submit="handleLeadSubmit" 
            @close="handleLeadClose" />

        <!-- 分配负责人对话框 -->
        <assign-dialog
            v-model="assignDialogVisible"
            :user-options="userOptions"
            @confirm="confirmAssign"
            @cancel="assignDialogVisible = false"
        />

        <!-- 转化线索对话框 -->
        <convert-lead-dialog
            v-model="convertDialogVisible"
            :lead-id="currentLeadId"
            :lead-name="currentLead.name"
            @success="handleConvertSuccess"
        />
    </el-container>
</template>

<script setup lang="ts">
import AssignDialog from '@/components/AssignDialog/index.vue';
import type { Action } from '@/components/CommonDrawer/index.vue';
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import CommonFormDialog from '@/components/CommonFormDialog/index.vue';
import ConvertLeadDialog from '@/components/ConvertLeadDialog/index.vue';
import SideNav from '@/components/SideNav/index.vue';
import type { TableButton } from '@/components/TableOperations/index.vue';
import { FilterType } from '@/types';
import { LeadEntity } from '@/types/entity';
import { DEFAULT_LEAD } from '@/views/AssociationManagement/types';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, markRaw, onMounted, reactive, ref, watch } from 'vue';
import {
    addLeads,
    assignLead,
    deleteLeads,
    getLeads,
    listLeads,
    updateLeads,
    type LeadForm
} from './api';
import { drawerConfig as baseDrawerConfig, navConfig } from './config';
import { newLeadFormConfig as defaultFormConfig } from './config/formConfig';
import LeadActivityTab from './tabs/LeadActivityTab.vue';
import LeadAttachmentsTab from './tabs/LeadAttachmentsTab.vue';
import LeadDetailsTab from './tabs/LeadDetailsTab.vue';
import LeadHeaderTab from './tabs/LeadHeaderTab.vue';
import LeadOperationsTab from './tabs/LeadOperationsTab.vue';

// ++ 变量区域 +
// activeTab 当前选中的导航
// filterType 当前选中的筛选类型
// searchInput 当前搜索输入
// totalLeads 总线索数
// queryParams 查询参数
// leads 线索列表
// leadDialogVisible 新建线索对话框是否可见
// newLead 新建线索数据
// drawerVisible 线索详情抽屉是否可见
// currentLead 当前选中的线索
// newLeadFormConfig 新建线索表单配置
// loading 加载状态
// assignDialogVisible 分配对话框是否可见
// assignForm 分配表单数据
// convertDialogVisible 转化对话框是否可
// currentLeadId 当前选中的线索ID
// userOptions 用户选项
// defaultUsers 默认用户数据

const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    searchKeyword: '',
    filterType: 'all' as FilterType
});
// 状态定义
const activeTab = ref('leads');
const filterType = ref<FilterType>('all');
const searchInput = ref('');
const totalLeads = ref(0);
const leads = ref<LeadEntity[]>([]);
const leadDialogVisible = ref(false);
const newLead = ref<LeadEntity>({ ...DEFAULT_LEAD });
const drawerVisible = ref(false);
const currentLead = ref<LeadEntity>({ ...DEFAULT_LEAD });
const newLeadFormConfig = ref(defaultFormConfig);
const loading = ref(false);

// 分配对话框相关状态
const assignDialogVisible = ref(false);
const userOptions = ref<Array<{ label: string, value: number }>>([]);

// 转化对话框相关状态
const convertDialogVisible = ref(false);
const currentLeadId = ref(0);

// 表格高度计算
const tableMaxHeight = computed(() => {
    // 计算可用高度：视窗高度 - 头部高度 - 筛选区域高度 - 分页区域高度 - 内边距
    // 56px(header) + 80px(filter) + 60px(pagination) + 32px(padding) = 228px
    // 使用更保守的计算，确保分页组件始终可见
    return `calc(100vh - 250px)`;
});

// 组件切换逻辑
const currentComponent = computed(() => {
    switch (activeTab.value) {
        case 'leads':
            return markRaw(Leads);
        case 'leadPool':
            return markRaw(LeadPool);
        case 'assignmentRecords':
            return markRaw(AssignmentRecords);
        default:
            return markRaw(Leads);
    }
});

// 表格操作按钮配置
const tableButtons: TableButton[] = [
    {
        label: '分配',
        type: 'primary',
        link: true,
        icon: 'Share',
        handler: (row: LeadEntity) => handleAssign(row)
    },
    {
        label: '转化',
        type: 'success',
        link: true,
        icon: 'Promotion',
        handler: (row: LeadEntity) => handleConvert(row)
    },
    {
        label: '删除',
        type: 'danger',
        link: true,
        icon: 'Delete',
        handler: (row: LeadEntity) => handleDelete(row)
    }
];

// 抽屉操作按钮配置
const drawerActions: Action[] = [

    {
        label: '分配',
        icon: 'Share',
        handler: (data: LeadEntity) => {
            handleAssign(data);
        }
    },
    {
        label: '打印',
        icon: 'Printer',
        handler: (data: LeadEntity) => {
            console.log('打印', data);
        }
    },
    {
        label: '转化',
        type: 'success',
        icon: 'Promotion',
        handler: (data: LeadEntity) => {
            handleConvert(data);
        }
    },
    {
        label: '删除',
        type: 'danger',
        icon: 'Delete',
        handler: (data: LeadEntity) => {
            handleDelete(data);
        }
    }
];

// 抽屉配置
const localDrawerConfig = reactive({
    ...baseDrawerConfig,
    menuItems: [
        {
            key: 'activity',
            label: '跟进记录',
            icon: 'Timer',
            component: markRaw(LeadActivityTab)
        },
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: markRaw(LeadDetailsTab)
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: markRaw(LeadOperationsTab)
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Folder',
            component: markRaw(LeadAttachmentsTab),
            badge: true
        }
    ]
});

// 头部组件
const headerComponent = markRaw(LeadHeaderTab);

// ++ 监听器区域 ++
watch(filterType, (newType: FilterType) => {
    queryParams.filterType = newType;
    getList();
});

// 监听搜索输入
watch(searchInput, (newValue: string) => {
    queryParams.searchKeyword = newValue;
    getList();
});


// ++ 初始化区域 ++
onMounted(() => {
    getList();
});


// ++ 方法定义 ++
// 方法列表
// getList 获取线索列表
// handleSelectionChange 处理选中
// openLeadDialog 打开新建线索对话框
// cancelLead 取消新建线索对话框
// handleLeadSubmit 新建线索
// handleLeadClose 关闭新建线索对话框
// openDrawer 打开线索详情抽屉
// handleLeadUpdate 更新线索
// handleTableOperation 处理表格操作
// handleAssign 分配线索
// confirmAssign 确认分配
// handleConvert 转化线索
// handleConvertSuccess 转化成功回调
// getUserOptions 获取用户选项
// handleDelete 删除线索
// handleSearch 搜索
// handleFilterChange 筛选
// handlePagination 处理分页变化

// 获取线索列表
const getList = async () => {
    try {
        loading.value = true;
        console.log("queryParams : ", JSON.stringify(queryParams, null, 2));
        const response = await listLeads(queryParams);
        const { code, msg, rows = [], total = 0 } = response;

        if (code === 200) {
            leads.value = (rows || []).map((item: LeadEntity) => new LeadEntity(item));
            totalLeads.value = total;
        } else {
            ElMessage.error(msg || '获取线索列表失败');
        }
    } catch (error) {
        console.error('获取线索列表失败:', error);
        ElMessage.error('获取线索列表失败');
    } finally {
        loading.value = false;
    }
};

const handleSelectionChange = (val: LeadEntity[]): void => {
    console.log('选中的线索:', val);
};

// 打开新建线索对话框
const openLeadDialog = (): void => {
    newLead.value = { ...DEFAULT_LEAD };
    leadDialogVisible.value = true;
    // 在对话框打开后获取用户选项
    newLeadFormConfig.value = {
        ...newLeadFormConfig.value,
        fields: newLeadFormConfig.value.fields.map(field => {
            if (field.field === 'responsiblePersonId') {
                return {
                    ...field,
                    options: [] // 从API获取或在getUserOptions中设置
                };
            }
            return field;
        })
    };
    // 获取用户选项
    getUserOptions().then(response => {
        if (response && response.code === 200) {
            newLeadFormConfig.value = {
                ...newLeadFormConfig.value,
                fields: newLeadFormConfig.value.fields.map(field => {
                    if (field.field === 'responsiblePersonId') {
                        return {
                            ...field,
                            options: response.data || []
                        };
                    }
                    return field;
                })
            };
        }
    });
    console.log("newLeadFormConfig : ", JSON.stringify(newLeadFormConfig.value, null, 2));
};

// 取消新建线索对话框
const cancelLead = (): void => {
    leadDialogVisible.value = false;
    newLead.value = { ...DEFAULT_LEAD };
};

// 新建线索
const handleLeadSubmit = async (formData: any): Promise<void> => {
    try {
        const leadData: LeadForm = {
            leadName: formData.name,
            leadSource: formData.source,
            customerName: '', // 添加必需的字段
            phone: formData.phone,
            email: formData.email,
            customerIndustry: formData.industry,
            nextContactTime: formData.nextContactTime,
            remarks: formData.remarks,
            responsiblePersonId: formData.responsiblePersonId
        };

        const response = await addLeads(leadData);
        if (response.code === 200) {
            ElMessage.success('新建线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '新建线索失败');
        }
    } catch (error) {
        console.error('新建线索失败:', error);
        ElMessage.error('新建线索失败');
    }
};

const handleLeadClose = (): void => {
    newLead.value = { ...DEFAULT_LEAD };
};

// 打开线索详情抽屉
const openDrawer = async (row: LeadEntity): Promise<void> => {
    try {
        const response = await getLeads(row.id);
        if (response.code === 200 && response.data) {
            currentLead.value = {
                id: response.data.id,
                name: response.data.leadName,
                leadName: response.data.leadName,
                customerName: response.data.customerName,
                leadSource: response.data.leadSource,
                status: response.data.status,
                mobile: response.data.mobile,
                address: response.data.address,
                detailedAddress: response.data.detailedAddress,
                phone: response.data.phone,
                email: response.data.email,
                customerIndustry: response.data.customerIndustry,
                customerLevel: response.data.customerLevel,
                remarks: response.data.remarks,
                createTime: response.data.createdAt,
                nextContactTime: response.data.nextContactTime,
                responsiblePersonId: response.data.responsiblePersonId,
                selectedDate: response.data.selectedDate,
                delFlag: response.data.delFlag
            };
            drawerVisible.value = true;
        } else {
            ElMessage.error(response.msg || '获取线索详情失败');
        }
    } catch (error) {
        console.error('获取线索详情失败:', error);
        ElMessage.error('获取线索详情失败');
    }
};

// 更新线索到API
const updateLead = async (newData: LeadEntity): Promise<void> => {
    try {
        const updateData: LeadForm = {
            id: newData.id,
            leadName: newData.leadName,
            leadSource: newData.leadSource,
            customerName: newData.customerName,
            phone: newData.phone,
            email: newData.email,
            customerIndustry: newData.customerIndustry,
            remarks: newData.remarks,
            nextContactTime: newData.nextContactTime,
            responsiblePersonId: Number(newData.responsiblePersonId)
        };

        const response = await updateLeads(updateData);
        if (response.code === 200) {
            ElMessage.success('更新线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '更新线索失败');
        }
    } catch (error) {
        console.error('更新线索失败:', error);
        ElMessage.error('更新线索失败');
    }
};

// 处理线索更新（抽屉中的更新回调）
const handleLeadUpdate = (newData: Record<string, any>) => {
    currentLead.value = { ...currentLead.value, ...newData };
};

// 分配线索
const handleAssign = async (row: LeadEntity): Promise<void> => {
    try {
        console.log('开始准备分配线索:', row);
        // 获取用户列表，这里假设和新建线索表单使用同样的用户选项
        const response = await getUserOptions();
        if (response && response.code === 200) {
            console.log("获取用户选项成功:", response.data);
            userOptions.value = response.data || [];
        } else {
            console.log("使用缓存的用户选项");
            // 如果没有专门的API，也可以使用之前缓存的用户选项
            userOptions.value = newLeadFormConfig.value.fields.find(
                field => field.field === 'responsiblePersonId'
            )?.options || [];
        }
        // 设置当前选中的线索ID
        currentLeadId.value = row.id;
        assignDialogVisible.value = true;
        console.log('分配对话框已打开，当前线索ID:', currentLeadId.value);
    } catch (error) {
        console.error('准备分配线索失败:', error);
        ElMessage.error('准备分配线索失败');
    }
};

// 确认分配
const confirmAssign = async (newOwnerId: number): Promise<void> => {
    try {
        console.log('开始确认分配，新负责人ID:', newOwnerId);
        if (!newOwnerId) {
            console.log('未选择负责人');
            ElMessage.warning('请选择负责人');
            return;
        }

        console.log('发送分配请求:', {
            leadId: currentLeadId.value,
            newOwnerId: newOwnerId
        });
        const response = await assignLead({
            leadId: currentLeadId.value,
            newOwnerId: newOwnerId
        });
        
        if (response.code === 200) {
            console.log('分配线索成功');
            ElMessage.success('分配线索成功');
            assignDialogVisible.value = false;
            getList();
        } else {
            console.error('分配线索失败:', response.msg);
            ElMessage.error(response.msg || '分配线索失败');
        }
    } catch (error) {
        console.error('分配线索失败:', error);
        ElMessage.error('分配线索失败');
    }
};

// 转化线索
const handleConvert = async (row: LeadEntity): Promise<void> => {
    currentLeadId.value = row.id;
    currentLead.value = row;
    convertDialogVisible.value = true;
};

// 转化成功回调
const handleConvertSuccess = () => {
    convertDialogVisible.value = false;
    getList(); // 刷新列表
};

// 获取用户选项的方法
const getUserOptions = async () => {
    // 从公共函数中获取用户选项
    const { getUserOptions: fetchUserOptions } = await import('@/utils/userOptions');
    return await fetchUserOptions();
};

// 删除线索
const handleDelete = async (row: LeadEntity): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该线索吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        const response = await deleteLeads(row.id);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(response.msg || '删除线索失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除线索失败:', error);
            ElMessage.error('删除线索失败');
        }
    }
};

// 搜索
const handleSearch = (value: string): void => {
    queryParams.searchKeyword = value;
    getList();
};

// 筛选
const handleFilterChange = (value: FilterType): void => {
    queryParams.filterType = value;
    getList();
};

// 处理分页变化
const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    getList();
};

</script>

<style scoped>
.leads-management {
    background-color: #fff;
    height: 100vh;
    display: flex;
    flex-direction: row;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background-color: #fff;
}

.main-container .el-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 20px;
    overflow: hidden;
    min-height: 0;
}

/* 表格容器样式 */
.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    margin-bottom: 16px;
    overflow: hidden;
}

/* 表格样式 */
.el-table {
    flex: 1;
    margin-bottom: 0;
}

/* 确保表格内容可以滚动 */
:deep(.el-table__body-wrapper) {
    overflow-y: auto !important;
}

/* 表格行样式优化 */
:deep(.el-table .el-table__row) {
    transition: background-color 0.25s ease;
}

:deep(.el-table .el-table__row:hover) {
    background-color: #f5f7fa;
}

/* 筛选区域样式 */
.filter-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 分页容器样式 */
.pagination-wrapper {
    flex-shrink: 0;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
    background-color: #fff;
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

.filters {
    margin: 16px 0;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
}

.drawer-nav {
    width: 180px;
    border-right: 1px solid #ebeef5;
    height: 100%;
    transition: width 0.3s;
    background-color: #f6f8fa;
}

.nav-collapsed {
    width: 64px;
}

.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    height: 50px;
    cursor: pointer;
    border-bottom: 1px solid #ebeef5;
}

.nav-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    color: #606266;
}

.nav-list {
    padding: 6px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    margin: 4px 0;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s;
    position: relative;
    
    &:hover {
        background-color: #ecf5ff;
        color: #409eff;
    }
    
    &.active {
        background-color: #ecf5ff;
        color: #409eff;
        
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #409eff;
        }
    }

    .el-icon {
        font-size: 16px;
        margin-right: 8px;
        color: inherit;
    }
}

.nav-label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-table .el-table__body-wrapper tbody tr {
    height: 20px;
}

.el-table .el-table__body-wrapper tbody td {
    padding: 5px 0;
}

.link-button {
    padding: 0;
    height: auto;
    font-weight: normal;

    &:hover {
        text-decoration: underline;
    }
}

.activity-timeline {
    padding: 20px;
}

.attachment-list {
    padding: 20px;
}

.el-drawer__body {
    padding: 20px;
}

.el-timeline {
    margin-top: 20px;
}

.convert-content {
    padding: 20px 0;
    text-align: center;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
