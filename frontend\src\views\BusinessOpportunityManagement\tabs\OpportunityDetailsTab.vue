<template>
  <div class="opportunity-details-tab">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="商机名称">
        {{ opportunityData.name }}
      </el-descriptions-item>
      <el-descriptions-item label="所属客户">
        {{ opportunityData.customerName }}
      </el-descriptions-item>
      <el-descriptions-item label="商机阶段">
        <el-tag :type="getStageTagType(opportunityData.stage)">
          {{ opportunityData.stageName }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="预计金额">
        {{ formatAmount(opportunityData.expectedAmount) }}
      </el-descriptions-item>
      <el-descriptions-item label="成交概率">
        <el-progress
          :percentage="opportunityData.probability"
          :status="getProbabilityStatus(opportunityData.probability)"
        />
      </el-descriptions-item>
      <el-descriptions-item label="预计成交日期">
        {{ formatDate(opportunityData.expectedClosingDate) }}
      </el-descriptions-item>
      <el-descriptions-item label="负责人">
        {{ opportunityData.ownerName }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDateTime(opportunityData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="最后更新时间">
        {{ formatDateTime(opportunityData.updateTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ opportunityData.remarks || '暂无备注' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
interface OpportunityData {
  id?: number
  name?: string
  customerName?: string
  stage?: string
  stageName?: string
  expectedAmount?: number
  probability?: number
  expectedClosingDate?: string
  ownerName?: string
  createTime?: string
  updateTime?: string
  remarks?: string
}

interface Props {
  opportunityData: OpportunityData
}

const props = defineProps<Props>()

// 工具函数
const getStageTagType = (stage: string) => {
  const typeMap: Record<string, string> = {
    initial: '',
    requirement: 'info',
    solution: 'warning',
    negotiation: 'success',
    contract: 'danger'
  }
  return typeMap[stage] || ''
}

const getProbabilityStatus = (probability: number) => {
  if (probability >= 80) return 'success'
  if (probability >= 60) return 'warning'
  if (probability >= 40) return 'exception'
  return ''
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }) || '¥0.00'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}
</script>

<style scoped>
.opportunity-details-tab {
  padding: 16px;
}
</style>
