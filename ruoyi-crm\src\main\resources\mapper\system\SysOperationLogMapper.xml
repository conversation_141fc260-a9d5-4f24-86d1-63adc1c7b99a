<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysOperationLogMapper">

    <resultMap type="com.ruoyi.common.domain.entity.SysOperationLog" id="SysOperationLogResult">
        <id property="id" column="id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessType" column="business_type"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationContent" column="operation_content"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operationTime" column="operation_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.common.domain.entity.SysOperationLog">
        insert into sys_operation_log (
            business_id,
            business_type,
            operation_type,
            operation_content,
            operator_id,
            operator_name,
            operation_time
        ) values (
            #{businessId},
            #{businessType},
            #{operationType},
            #{operationContent},
            #{operatorId},
            #{operatorName},
            #{operationTime}
        )
    </insert>

    <select id="selectByBusiness" resultMap="SysOperationLogResult">
        select *
        from sys_operation_log
        where business_type = #{businessType} 
        and business_id = #{businessId}
        order by operation_time desc
    </select>

    <select id="selectByBusinessType" resultMap="SysOperationLogResult">
        select *
        from sys_operation_log
        where business_type = #{businessType}
        order by operation_time desc
    </select>

    <select id="selectByTimeRange" resultMap="SysOperationLogResult">
        select *
        from sys_operation_log
        where operation_time between #{startTime} and #{endTime}
        order by operation_time desc
    </select>

</mapper>
