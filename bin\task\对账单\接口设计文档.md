# CRM对账单系统接口设计文档

## 📋 API接口设计规范

### 接口命名规范
- **基本路径**: 所有接口均以 `/front/crm/` 开头。
- **RESTful风格**: 遵循RESTful设计原则，使用HTTP方法（GET, POST, PUT, DELETE）表示操作。
- **资源命名**: 资源名称使用复数形式，例如 `reconciliations`。

### 响应格式统一
- **列表数据**: 使用 `TableDataInfo` 对象，包含 `total` 和 `rows`。
- **其他操作**: 使用 `AjaxResult` 对象，包含 `code`, `msg`, `data`。

### 状态码规范
- **200**: 操作成功
- **401**: 未授权
- **403**: 禁止访问
- **404**: 资源未找到
- **500**: 服务器内部错误

## 🔗 对账单管理接口

### 1. 获取对账单列表
`GET /front/crm/reconciliation/list`

**请求参数 (Query Parameters):**
- `pageNum`, `pageSize`
- `customerName`, `reconciliationNo`, `status`, `startDate`, `endDate`, `responsibleUserId`

**响应示例 (`TableDataInfo`):**
```json
{
  "total": 1,
  "rows": [
    {
      "reconciliationId": 1,
      "reconciliationNo": "REC20250702000001",
      "customerName": "杭州科德旺有限公司",
      "status": "approved",
      "totalAmount": 156800.00
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

### 2. 获取对账单详情
`GET /front/crm/reconciliation/{id}`

**响应示例 (`AjaxResult`):**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "reconciliationId": 1,
    "reconciliationNo": "REC20250702000001"
  }
}
```

### 3. 创建对账单
`POST /front/crm/reconciliation`

**请求体 (`@RequestBody`):**
```json
{
  "customerId": 100,
  "reconciliationPeriod": "2025年7月"
}
```

### 4. 更新对账单
`PUT /front/crm/reconciliation`

**请求体 (`@RequestBody`):**
```json
{
  "reconciliationId": 1,
  "remark": "更新备注信息"
}
```

### 5. 删除对账单
`DELETE /front/crm/reconciliation/{ids}`

**路径参数:**
- `ids`: 一个或多个对账单ID，用逗号分隔。

### 6. 提交对账单审核
`PUT /front/crm/reconciliation/submit`

**请求体 (`@RequestBody`):**
```json
{
  "reconciliationId": 1,
  "status": "pending_approval"
}
```

### 7. 审批对账单
`PUT /front/crm/reconciliation/approve`

**请求体 (`@RequestBody`):**
```json
{
  "reconciliationId": 1,
  "approvalResult": "approved",
  "approvalNotes": "审批通过"
}
```

## 📄 开票申请接口

### 1. 获取开票申请列表
`GET /front/crm/invoice-application/list`

### 2. 创建开票申请
`POST /front/crm/invoice-application`

### 3. 审批开票申请
`PUT /front/crm/invoice-application/approve`

### 4. 开具发票
`PUT /front/crm/invoice-application/issue`

## 💰 回款管理接口

### 1. 获取回款记录列表
`GET /front/crm/payment-record/list`

### 2. 创建回款记录
`POST /front/crm/payment-record`

## 📊 统计报表接口

### 1. 对账单统计
`GET /front/crm/reconciliation/statistics`

### 2. 回款统计
`GET /front/crm/payment-record/statistics`

## 🔧 辅助接口

### 1. 获取待对账订单列表
`GET /front/crm/reconciliation/pending-orders`

### 2. 数据导出
`POST /front/crm/reconciliation/export`

