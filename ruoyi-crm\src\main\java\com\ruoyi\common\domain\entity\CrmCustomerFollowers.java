package com.ruoyi.common.domain.entity;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户关注实体类
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmCustomerFollowers extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 关注者用户ID */
    private Long followerId;

    /** 关注时间 */
    private Date followTime;

    /** 是否有效关注（0否 1是） */
    private Integer isActive;

    // 关联查询字段
    /** 关联的客户信息 */
    private CrmCustomer customer;
    
    /** 关注者用户名 */
    private String followerName;
    
    /** 客户名称 */
    private String customerName;
    
    // 状态常量
    public static final int ACTIVE_YES = 1;
    public static final int ACTIVE_NO = 0;
    
    /**
     * 判断是否为有效关注
     * @return true-有效关注，false-无效关注
     */
    public boolean isActiveFollow() {
        return ACTIVE_YES == this.isActive;
    }
    
    /**
     * 创建新的关注关系
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @param followerName 关注者用户名
     * @return 客户关注实体
     */
    public static CrmCustomerFollowers createFollow(Long customerId, Long followerId, String followerName) {
        CrmCustomerFollowers followers = new CrmCustomerFollowers();
        followers.setCustomerId(customerId);
        followers.setFollowerId(followerId);
        followers.setFollowerName(followerName);
        followers.setFollowTime(new Date());
        followers.setIsActive(ACTIVE_YES);
        followers.setCreateTime(new Date());
        return followers;
    }
    
    /**
     * 激活关注
     */
    public void activate() {
        this.isActive = ACTIVE_YES;
        this.followTime = new Date();
    }
    
    /**
     * 取消关注
     */
    public void deactivate() {
        this.isActive = ACTIVE_NO;
    }
}