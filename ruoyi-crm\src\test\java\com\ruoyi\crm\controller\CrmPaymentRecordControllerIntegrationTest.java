package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPaymentRecord;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.BaseTestCase;

@AutoConfigureWebMvc
@DisplayName("回款记录Controller集成测试")
class CrmPaymentRecordControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    private MockMvc mockMvc;

    private CrmPaymentRecord testPaymentRecord;
    private CrmPaymentPlan testPaymentPlan;

    @BeforeEach
    void setUp() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 创建测试回款计划
        testPaymentPlan = new CrmPaymentPlan();
        testPaymentPlan.setPlanNumber("PLN20240724001");
        testPaymentPlan.setCustomerId(1L);
        testPaymentPlan.setCustomerName("测试客户A");
        testPaymentPlan.setResponsibleUserId(1L);
        testPaymentPlan.setResponsibleUserName("张三");
        testPaymentPlan.setTotalAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setReceivedAmount(new BigDecimal("0.00"));
        testPaymentPlan.setRemainingAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setPlanStatus("执行中");
        testPaymentPlan.setApprovalStatus("已通过");
        testPaymentPlan.setPaymentMethod("银行转账");
        testPaymentPlan.setCurrency("CNY");
        testPaymentPlan.setPlanType("普通");
        testPaymentPlan.setRiskLevel("低");
        testPaymentPlan.setCreateTime(new Date());
        testPaymentPlan.setCreateBy("test");
        testPaymentPlan.setDelFlag("0");

        // 创建测试回款记录
        testPaymentRecord = new CrmPaymentRecord();
        testPaymentRecord.setRecordNumber("REC20240724001");
        testPaymentRecord.setCustomerName("测试客户A");
        testPaymentRecord.setPaymentAmount(new BigDecimal("50000.00"));
        testPaymentRecord.setPaymentDate(new Date());
        testPaymentRecord.setPaymentMethod("银行转账");
        testPaymentRecord.setRecordStatus("待确认");
        testPaymentRecord.setBankAccount("****************");
        testPaymentRecord.setBankName("中国工商银行");
        testPaymentRecord.setRemark("首期回款");
        testPaymentRecord.setCreateTime(new Date());
        testPaymentRecord.setCreateBy("test");
        testPaymentRecord.setDelFlag("0");
    }

    @Test
    @DisplayName("测试回款记录列表查询")
    void testListPaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());

        mockMvc.perform(get("/crm/paymentRecord/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @DisplayName("测试新增回款记录")
    void testAddPaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());

        String jsonContent = objectMapper.writeValueAsString(testPaymentRecord);

        MvcResult result = mockMvc.perform(post("/crm/paymentRecord")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("新增回款记录响应: " + response);
    }

    @Test
    @DisplayName("测试根据ID查询回款记录详情")
    void testGetPaymentRecord() throws Exception {
        // 先插入回款计划和记录
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());

        mockMvc.perform(get("/crm/paymentRecord/{id}", 1L))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("测试确认回款记录")
    void testConfirmPaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());

        mockMvc.perform(post("/crm/paymentRecord/confirm/{id}", 1L)
                .param("confirmUserId", "1")
                .param("confirmRemark", "确认收款"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试退回回款记录")
    void testRejectPaymentRecord() throws Exception {
        mockMvc.perform(post("/crm/paymentRecord/reject/{id}", 1L)
                .param("rejectReason", "金额有误"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试按回款计划查询记录")
    void testGetRecordsByPlan() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentRecord/plan/{planId}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试按客户查询回款记录")
    void testGetRecordsByCustomer() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/customer/{customerId}", 1L))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试回款记录统计")
    void testGetPaymentStatistics() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/statistics")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-12-31"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    @Test
    @DisplayName("测试回款记录导出")
    void testExportPaymentRecord() throws Exception {
        mockMvc.perform(post("/crm/paymentRecord/export")
                .param("customerName", "")
                .param("recordStatus", ""))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试批量确认回款记录")
    void testBatchConfirmPaymentRecord() throws Exception {
        String requestBody = "[{\"id\": 1, \"confirmRemark\": \"批量确认\"}]";

        mockMvc.perform(post("/crm/paymentRecord/batchConfirm")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("测试回款记录参数验证")
    void testPaymentRecordValidation() throws Exception {
        // 测试必填字段验证
        CrmPaymentRecord invalidRecord = new CrmPaymentRecord();
        String jsonContent = objectMapper.writeValueAsString(invalidRecord);

        mockMvc.perform(post("/crm/paymentRecord")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().is4xxClientError());
    }

    @Test
    @DisplayName("测试按状态查询回款记录")
    void testGetRecordsByStatus() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/list")
                .param("recordStatus", "待确认"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试按日期范围查询回款记录")
    void testGetRecordsByDateRange() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/list")
                .param("startDate", "2024-07-01")
                .param("endDate", "2024-07-31"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试修改回款记录")
    void testUpdatePaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());
        testPaymentRecord.setId(1L);

        // 修改数据
        testPaymentRecord.setPaymentAmount(new BigDecimal("60000.00"));
        testPaymentRecord.setRemark("修改后的备注");

        String jsonContent = objectMapper.writeValueAsString(testPaymentRecord);

        mockMvc.perform(put("/crm/paymentRecord")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试删除回款记录")
    void testDeletePaymentRecord() throws Exception {
        mockMvc.perform(delete("/crm/paymentRecord/{ids}", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试搜索回款记录")
    void testSearchPaymentRecord() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/search")
                .param("keyword", "测试客户"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试生成回款记录编号")
    void testGenerateRecordNumber() throws Exception {
        mockMvc.perform(get("/crm/paymentRecord/generateNumber"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isString())
                .andExpect(jsonPath("$.data").value(org.hamcrest.Matchers.startsWith("REC")));
    }

    @Test
    @DisplayName("测试回款记录审核流程")
    void testPaymentRecordAuditFlow() throws Exception {
        // 1. 创建记录（待确认状态）
        paymentPlanService.insertCrmPaymentPlan(testPaymentPlan);
        testPaymentRecord.setPlanId(testPaymentPlan.getId());
        testPaymentRecord.setRecordStatus("待确认");

        // 2. 确认记录
        mockMvc.perform(post("/crm/paymentRecord/confirm/{id}", 1L)
                .param("confirmUserId", "1")
                .param("confirmRemark", "确认收款"))
                .andDo(print())
                .andExpect(status().isOk());

        // 3. 验证状态变更
        mockMvc.perform(get("/crm/paymentRecord/{id}", 1L))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.recordStatus").value("已确认"));
    }
}