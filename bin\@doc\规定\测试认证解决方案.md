# CRM测试认证优雅解决方案

## 一、问题背景

在CRM系统的集成测试中，许多业务方法（如`getMyLeadList`、`insertCrmLeads`等）需要通过`SecurityUtils.getUsername()`获取当前用户信息。在测试环境中，如果没有正确设置认证信息，这些方法会抛出`ServiceException: 获取用户账户异常`。

### 1.1 问题表现
- 测试方法调用`SecurityUtils.getUsername()`时失败
- 每个测试类都需要重复编写认证设置代码
- 代码冗余，维护困难

### 1.2 传统解决方案的问题
```java
// 每个测试类都要写这样的代码
@BeforeEach
void setUp() {
    // 创建测试用户
    SysUser testUser = new SysUser();
    testUser.setUserId(1L);
    testUser.setUserName("testuser");
    // ... 更多设置代码
    
    // 创建认证对象
    LoginUser loginUser = new LoginUser(testUser.getUserId(), testUser.getDeptId(), testUser, null);
    UsernamePasswordAuthenticationToken authentication = 
        new UsernamePasswordAuthenticationToken(loginUser, null, null);
    SecurityContextHolder.getContext().setAuthentication(authentication);
}

@AfterEach
void tearDown() {
    SecurityContextHolder.clearContext();
}
```

---

## 二、优雅解决方案

### 2.1 方案架构

```
TestAuthenticationUtils (工具类)
    ↓
BaseTestCase (测试基类)
    ↓
具体测试类 (继承BaseTestCase)
```

### 2.2 核心组件

#### 2.2.1 TestAuthenticationUtils 工具类
```java
public class TestAuthenticationUtils {
    // 提供统一的认证设置和清理功能
    public static void setupDefaultAuthentication()
    public static void setupAuthentication(Long userId, String username, String nickName, Long deptId)
    public static void setupAuthenticationWithUserId(Long userId)
    public static void clearAuthentication()
    public static boolean isAuthenticated()
    // ... 其他工具方法
}
```

#### 2.2.2 BaseTestCase 测试基类
```java
public abstract class BaseTestCase {
    @BeforeEach
    void setUpBaseAuthentication() {
        if (shouldAutoSetupAuthentication()) {
            setupTestAuthentication();
        }
    }
    
    @AfterEach
    void tearDownBaseAuthentication() {
        if (shouldAutoSetupAuthentication()) {
            clearTestAuthentication();
        }
    }
    
    // 提供可重写的配置方法
    protected boolean shouldAutoSetupAuthentication() { return true; }
    protected Long getTestUserId() { return 1L; }
    protected String getTestUsername() { return "testuser"; }
}
```

---

## 三、使用方式

### 3.1 默认使用（推荐）
```java
@DisplayName("线索服务集成测试")
class CrmLeadServiceImplIntegrationTest extends BaseTestCase {
    
    @Autowired
    private ICrmLeadService crmLeadService;
    
    @Test
    void testInsertCrmLeads() {
        // 认证信息已自动设置，直接使用
        CrmLeads lead = new CrmLeads();
        // ... 设置线索信息
        
        int result = crmLeadService.insertCrmLeads(lead);
        assertEquals(1, result);
    }
}
```

### 3.2 使用不同用户
```java
@Test
void testGetMyLeadListWithDifferentUser() {
    // 切换到用户ID为2的用户
    clearTestAuthentication();
    setupTestAuthentication(2L);
    
    List<CrmLeads> result = crmLeadService.getMyLeadList(new CrmLeads());
    assertNotNull(result);
}
```

### 3.3 使用自定义用户信息
```java
@Test
void testWithCustomUser() {
    clearTestAuthentication();
    setupTestAuthentication(999L, "customuser", "自定义用户", 200L);
    
    // 验证认证信息
    assertEquals(999L, getCurrentUserId());
    assertEquals("customuser", getCurrentUsername());
    
    // 执行业务方法
    List<CrmLeads> result = crmLeadService.getMyLeadList(new CrmLeads());
    assertNotNull(result);
}
```

### 3.4 禁用自动认证设置
```java
class SpecialTest extends BaseTestCase {
    
    @Override
    protected boolean shouldAutoSetupAuthentication() {
        return false; // 禁用自动认证设置
    }
    
    @Test
    void testWithoutAuthentication() {
        // 手动控制认证设置
        setupTestAuthentication();
        // ... 测试代码
        clearTestAuthentication();
    }
}
```

---

## 四、方案优势

### 4.1 代码复用
- ✅ 所有测试类继承`BaseTestCase`即可自动获得认证功能
- ✅ 消除重复代码，提高开发效率
- ✅ 统一的认证设置逻辑，便于维护

### 4.2 灵活性
- ✅ 支持默认用户、指定用户ID、完全自定义用户信息
- ✅ 可以在测试方法中动态切换用户
- ✅ 可以选择性禁用自动认证设置

### 4.3 易用性
- ✅ 零配置使用：继承`BaseTestCase`即可
- ✅ 提供便捷的工具方法：`getCurrentUserId()`、`getCurrentUsername()`等
- ✅ 自动清理：测试结束后自动清理认证信息

### 4.3 可维护性
- ✅ 集中管理认证逻辑，修改一处即可影响所有测试
- ✅ 清晰的分层架构，职责明确
- ✅ 完善的文档和示例代码

---

## 五、最佳实践

### 5.1 测试类设计
```java
// 推荐：继承BaseTestCase，使用默认认证
class CrmLeadServiceImplIntegrationTest extends BaseTestCase {
    // 测试代码
}

// 特殊情况：需要禁用自动认证
class SpecialAuthTest extends BaseTestCase {
    @Override
    protected boolean shouldAutoSetupAuthentication() {
        return false;
    }
}
```

### 5.2 认证信息验证
```java
@BeforeEach
void setUp() {
    // 验证认证是否正确设置
    assertTrue(isAuthenticated(), "应该已经设置了认证信息");
    assertEquals(1L, getCurrentUserId(), "当前用户ID应该为1");
    assertEquals("testuser", getCurrentUsername(), "当前用户名应该为testuser");
}
```

### 5.3 多用户测试
```java
@Test
void testMultipleUsers() {
    // 测试用户1
    List<CrmLeads> user1Leads = crmLeadService.getMyLeadList(new CrmLeads());
    
    // 切换到用户2
    clearTestAuthentication();
    setupTestAuthentication(2L);
    List<CrmLeads> user2Leads = crmLeadService.getMyLeadList(new CrmLeads());
    
    // 验证不同用户的数据隔离
    // ...
}
```

---

## 六、扩展说明

### 6.1 权限扩展
如需测试权限相关功能，可以在`TestAuthenticationUtils`中扩展权限设置：

```java
public static void setupAuthenticationWithPermissions(Long userId, Set<String> permissions) {
    // 创建带特定权限的认证对象
}
```

### 6.2 部门隔离测试
如需测试部门数据隔离，可以设置不同的部门ID：

```java
setupTestAuthentication(1L, "user1", "用户1", 100L); // 部门100
setupTestAuthentication(2L, "user2", "用户2", 200L); // 部门200
```

### 6.3 角色测试
可以扩展支持角色设置：

```java
public static void setupAuthenticationWithRoles(Long userId, List<String> roles) {
    // 创建带特定角色的认证对象
}
```

---

## 七、总结

这个优雅的解决方案通过以下方式解决了测试认证的问题：

1. **统一管理**：通过`TestAuthenticationUtils`工具类统一管理认证逻辑
2. **自动化**：通过`BaseTestCase`基类自动设置和清理认证信息
3. **灵活性**：支持多种使用方式，满足不同测试场景需求
4. **可维护性**：消除代码重复，便于维护和扩展

使用这个方案后，开发人员只需要：
- 让测试类继承`BaseTestCase`
- 专注于业务逻辑测试
- 在需要时使用提供的工具方法切换用户或验证认证信息

这大大提高了测试开发的效率和代码质量。 