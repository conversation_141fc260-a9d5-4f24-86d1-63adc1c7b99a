<template>
    <div class="chart-container">
        <div ref="chartPaymentAmount" class="payment-chart"></div>

        <el-table :data="paymentRankingData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="rank" label="公司总排名" width="120" />
            <el-table-column prop="signer" label="签订人" width="150" />
            <el-table-column prop="department" label="部门" width="150" />
            <el-table-column prop="paymentAmount" label="回款金额" width="150" />
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            chart: null,
            paymentRankingData: [
                {
                    rank: 1,
                    signer: '王五',
                    department: '销售',
                    paymentAmount: 600000,
                },
                {
                    rank: 2,
                    signer: '赵六',
                    department: '市场',
                    paymentAmount: 400000,
                },
            ],
            chartData: {
                months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                payments: Array.from({ length: 12 }, () => Math.random() * 100),
                goals: Array.from({ length: 12 }, () => Math.random() * 100),
                completionRates: Array.from({ length: 12 }, () => (Math.random() * 100).toFixed(2)),
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chartPaymentAmount;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const options = {
                title: {
                    text: '回款金额趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.months,
                },
                yAxis: {
                    type: 'value',
                    name: '金额（元）'
                },
                series: [
                    {
                        name: '回款金额',
                        data: this.chartData.payments,
                        type: 'line',
                    },
                    {
                        name: '目标',
                        data: this.chartData.goals,
                        type: 'line',
                    },
                    {
                        name: '完成率',
                        data: this.chartData.completionRates,
                        type: 'line',
                    },
                ],
            };
            this.chart.setOption(options);
        },
    },
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.payment-chart {
    width: 100%;
    height: 400px;
    margin-top: 20px;
}
</style>