<template>
    <!-- 报价单管理主容器 -->
    <el-container class="quotation-management">
        <!-- 主内容区域容器 -->
        <el-container class="main-container">
            <!-- 页面头部，包含标题和操作按钮 -->
            <el-header class="header">
                <h1>报价单管理</h1>
                <div class="header-actions">
                    <el-button
                        type="primary"
                        size="small"
                        @click="handleAdd"
                        v-hasPermi="['crm:quotation:add']"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建报价单
                    </el-button>
                </div>
            </el-header>

            <el-main class="main-content">
                <!-- 搜索和筛选区域 -->
                <div class="filter-section">
                    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="报价单号" prop="quotationNo">
        <el-input
          v-model="queryParams.quotationNo"
          placeholder="请输入报价单编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报价单名称" prop="quotationName">
        <el-input
          v-model="queryParams.quotationName"
          placeholder="请输入报价单名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.crm_quotation_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
          <el-option label="待审批" value="pending" />
          <el-option label="审批通过" value="approved" />
          <el-option label="审批驳回" value="rejected" />
        </el-select>
      </el-form-item>
      <el-form-item label="报价日期">
        <el-date-picker
          v-model="daterangeQuotationDate"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 操作按钮区域 -->
                <div class="toolbar-section">
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button
                                type="success"
                                plain
                                icon="Edit"
                                :disabled="single"
                                @click="handleUpdate"
                                v-hasPermi="['crm:quotation:edit']"
                            >修改</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button
                                type="danger"
                                plain
                                icon="Delete"
                                :disabled="multiple"
                                @click="handleDelete"
                                v-hasPermi="['crm:quotation:remove']"
                            >删除</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button
                                type="warning"
                                plain
                                icon="Download"
                                @click="handleExport"
                                v-hasPermi="['crm:quotation:export']"
                            >导出</el-button>
                        </el-col>
                        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                    </el-row>
                </div>

                <!-- 报价单数据表格 -->
                <div class="table-container">
                    <el-table 
                        v-loading="loading" 
                        :data="quotationList" 
                        @selection-change="handleSelectionChange"
                        border 
                        sortable 
                        tooltip-effect="dark"
                        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                        :height="'100%'"
                        :max-height="'100%'"
                        class="quotation-table">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报价单号" align="center" prop="quotationNo" />
      <el-table-column label="报价单名称" align="center" prop="quotationName" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="联系人" align="center" prop="contactName" />
      <el-table-column label="报价金额" align="center" prop="totalAmount">
        <template #default="scope">
          <span>{{ scope.row.currency }} {{ parseFloat(scope.row.totalAmount).toLocaleString() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="dict.type.crm_quotation_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="approvalStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.approvalStatus === 'pending'" type="warning">待审批</el-tag>
          <el-tag v-else-if="scope.row.approvalStatus === 'approved'" type="success">审批通过</el-tag>
          <el-tag v-else-if="scope.row.approvalStatus === 'rejected'" type="danger">审批驳回</el-tag>
          <el-tag v-else type="info">{{ scope.row.approvalStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="报价日期" align="center" prop="quotationDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.quotationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效期至" align="center" prop="validUntil" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.validUntil, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['crm:quotation:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:quotation:edit']">修改</el-button>
          <el-dropdown @command="handleCommand" trigger="click" v-hasPermi="['crm:quotation:approval']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'submit', row: scope.row}" v-if="scope.row.status === 'draft'">
                  提交审批
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'cancel', row: scope.row}" v-if="scope.row.status === 'submitted'">
                  撤销审批
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'invoice', row: scope.row}" v-if="scope.row.approvalStatus === 'approved'">
                  创建发票
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
                </div>
                
                <!-- 分页组件 -->
                <div class="pagination-section">
                    <pagination 
                        v-show="total>0" 
                        :total="total" 
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList" />
                </div>
            </el-main>
        </el-container>

    <!-- 添加或修改报价单对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <el-form ref="quotationRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="报价单编号" prop="quotationNo">
              <el-input v-model="form.quotationNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报价单名称" prop="quotationName">
              <el-input v-model="form.quotationName" placeholder="请输入报价单名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户" prop="customerId">
              <el-select v-model="form.customerId" placeholder="请选择客户" @change="handleCustomerChange" filterable>
                <el-option
                  v-for="customer in customerList"
                  :key="customer.id"
                  :label="customer.customerName"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactId">
              <el-select v-model="form.contactId" placeholder="请选择联系人" @change="handleContactChange" filterable>
                <el-option
                  v-for="contact in contactList"
                  :key="contact.id"
                  :label="contact.contactName"
                  :value="contact.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="报价日期" prop="quotationDate">
              <el-date-picker
                v-model="form.quotationDate"
                type="date"
                placeholder="选择报价日期"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效期至" prop="validUntil">
              <el-date-picker
                v-model="form.validUntil"
                type="date"
                placeholder="选择有效期"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="币种" prop="currency">
              <el-select v-model="form.currency" placeholder="请选择币种">
                <el-option label="人民币" value="CNY" />
                <el-option label="美元" value="USD" />
                <el-option label="欧元" value="EUR" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="交货条款" prop="deliveryTerms">
              <el-input v-model="form.deliveryTerms" placeholder="请输入交货条款" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款条款" prop="paymentTerms">
              <el-input v-model="form.paymentTerms" placeholder="请输入付款条款" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 报价单明细 -->
        <el-divider content-position="left">报价单明细</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" size="small" @click="handleAddItem">添加明细</el-button>
          </el-col>
        </el-row>
        <el-table :data="form.quotationItems" border>
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="产品名称" width="150">
            <template #default="{ row }">
              <el-input v-model="row.productName" placeholder="请输入产品名称" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="规格" width="120">
            <template #default="{ row }">
              <el-input v-model="row.specification" placeholder="规格" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="数量" width="100">
            <template #default="{ row }">
              <el-input-number v-model="row.quantity" :precision="2" :min="0" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="单位" width="80">
            <template #default="{ row }">
              <el-input v-model="row.unit" placeholder="单位" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row }">
              <el-input-number v-model="row.unitPrice" :precision="2" :min="0" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              <span>{{ row.totalPrice || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="折扣率%" width="100">
            <template #default="{ row }">
              <el-input-number v-model="row.discountRate" :precision="2" :min="0" :max="1" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="最终金额" width="120">
            <template #default="{ row }">
              <span>{{ row.finalAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="120">
            <template #default="{ row }">
              <el-input v-model="row.remarks" placeholder="备注" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" icon="Delete" size="small" @click="handleRemoveItem($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-row class="mt20">
          <el-col :span="24" style="text-align: right;">
            <el-form-item label="报价总金额：" label-width="120px" style="margin-bottom: 0;">
              <span style="font-size: 18px; font-weight: bold; color: #409EFF;">
                {{ form.currency }} {{ parseFloat(form.totalAmount || 0).toLocaleString() }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
        </template>
      </el-dialog>
    </el-container>
</template>

<script setup name="Quotation">
import { getCurrentInstance, ref, reactive, toRefs, onMounted, nextTick } from 'vue';
import { listQuotation, getQuotation, delQuotation, addQuotation, updateQuotation, generateQuotationNo, submitQuotationApproval, cancelQuotationApproval } from "@/api/crm/quotation";
import { listCustomers } from "@/api/crm/customers";
import { listContacts } from "@/api/crm/contacts";
import { createInvoiceFromQuotation } from "@/api/crm/invoice";
import { useDict } from '@/composables';
import { Plus } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { dict } = useDict('crm_quotation_status');

const quotationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeQuotationDate = ref([]);
const customerList = ref([]);
const contactList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    quotationNo: null,
    quotationName: null,
    customerName: null,
    status: null,
    approvalStatus: null,
  },
  rules: {
    quotationName: [
      { required: true, message: "报价单名称不能为空", trigger: "blur" }
    ],
    customerId: [
      { required: true, message: "客户不能为空", trigger: "change" }
    ],
    contactId: [
      { required: true, message: "联系人不能为空", trigger: "change" }
    ],
    quotationDate: [
      { required: true, message: "报价日期不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询报价单列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (daterangeQuotationDate.value && daterangeQuotationDate.value.length === 2) {
    queryParams.value.params["beginQuotationDate"] = daterangeQuotationDate.value[0];
    queryParams.value.params["endQuotationDate"] = daterangeQuotationDate.value[1];
  }
  listQuotation(queryParams.value).then(response => {
    quotationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    quotationNo: null,
    quotationName: null,
    customerId: null,
    customerName: null,
    contactId: null,
    contactName: null,
    responsiblePersonId: null,
    totalAmount: 0,
    currency: "CNY",
    status: "draft",
    approvalStatus: "pending",
    validUntil: null,
    quotationDate: null,
    deliveryTerms: null,
    paymentTerms: null,
    remarks: null,
    quotationItems: []
  };
  proxy.resetForm("quotationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeQuotationDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
async function handleAdd() {
  reset();
  
  // 生成报价单编号
  try {
    const response = await generateQuotationNo();
    form.value.quotationNo = response.data;
  } catch (error) {
    console.error('生成报价单编号失败:', error);
  }
  
  // 设置默认报价日期
  form.value.quotationDate = new Date().toISOString().split('T')[0];
  
  // 加载客户列表
  loadCustomerList();
  
  open.value = true;
  title.value = "添加报价单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getQuotation(id).then(response => {
    form.value = response.data;
    loadCustomerList();
    loadContactList(form.value.customerId);
    open.value = true;
    title.value = "修改报价单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["quotationRef"].validate(valid => {
    if (valid) {
      // 计算总金额
      calculateTotalAmount();
      
      if (form.value.id != null) {
        updateQuotation(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addQuotation(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除报价单编号为"' + _ids + '"的数据项？').then(function() {
    return delQuotation(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('crm/quotation/export', {
    ...queryParams.value
  }, `quotation_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  // TODO: 实现详情页面
  console.log('查看详情:', row);
}

/** 下拉菜单操作 */
function handleCommand(command) {
  const { action, row } = command;
  
  switch (action) {
    case 'submit':
      handleSubmitApproval(row);
      break;
    case 'cancel':
      handleCancelApproval(row);
      break;
    case 'invoice':
      handleCreateInvoice(row);
      break;
  }
}

/** 提交审批 */
function handleSubmitApproval(row) {
  proxy.$modal.confirm('确认提交报价单"' + row.quotationName + '"进行审批？').then(function() {
    return submitQuotationApproval(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("提交审批成功");
  }).catch(() => {});
}

/** 撤销审批 */
function handleCancelApproval(row) {
  proxy.$prompt('请输入撤销原因', '撤销审批', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '撤销原因不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    return cancelQuotationApproval(row.id, { reason: value });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("撤销审批成功");
  }).catch(() => {});
}

/** 创建发票 */
function handleCreateInvoice(row) {
  proxy.$modal.confirm('确认基于报价单"' + row.quotationName + '"创建发票？').then(function() {
    return createInvoiceFromQuotation(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("发票创建成功");
    // TODO: 跳转到发票页面或刷新发票列表
  }).catch(() => {});
}

/** 加载客户列表 */
function loadCustomerList() {
  listCustomers().then(response => {
    customerList.value = response.rows || [];
  });
}

/** 加载联系人列表 */
function loadContactList(customerId) {
  if (!customerId) {
    contactList.value = [];
    return;
  }
  
  listContacts({ customerId: customerId }).then(response => {
    contactList.value = response.rows || [];
  });
}

/** 客户变更事件 */
function handleCustomerChange(customerId) {
  const customer = customerList.value.find(c => c.id === customerId);
  if (customer) {
    form.value.customerName = customer.customerName;
  }
  
  // 清空联系人选择并重新加载
  form.value.contactId = null;
  form.value.contactName = null;
  loadContactList(customerId);
}

/** 联系人变更事件 */
function handleContactChange(contactId) {
  const contact = contactList.value.find(c => c.id === contactId);
  if (contact) {
    form.value.contactName = contact.contactName;
  }
}

/** 添加明细行 */
function handleAddItem() {
  if (!form.value.quotationItems) {
    form.value.quotationItems = [];
  }
  
  form.value.quotationItems.push({
    productName: '',
    specification: '',
    quantity: 1,
    unit: '',
    unitPrice: 0,
    totalPrice: 0,
    discountRate: 0,
    discountAmount: 0,
    finalAmount: 0,
    remarks: '',
    sortOrder: form.value.quotationItems.length + 1
  });
}

/** 删除明细行 */
function handleRemoveItem(index) {
  form.value.quotationItems.splice(index, 1);
  calculateTotalAmount();
}

/** 计算明细金额 */
function calculateItemAmount(item) {
  if (item.quantity && item.unitPrice) {
    item.totalPrice = item.quantity * item.unitPrice;
    
    // 计算折扣后金额
    if (item.discountRate && item.discountRate > 0) {
      item.discountAmount = item.totalPrice * item.discountRate;
      item.finalAmount = item.totalPrice - item.discountAmount;
    } else {
      item.discountAmount = 0;
      item.finalAmount = item.totalPrice;
    }
  } else {
    item.totalPrice = 0;
    item.finalAmount = 0;
  }
  
  calculateTotalAmount();
}

/** 计算总金额 */
function calculateTotalAmount() {
  if (!form.value.quotationItems || form.value.quotationItems.length === 0) {
    form.value.totalAmount = 0;
    return;
  }
  
  let totalAmount = 0;
  form.value.quotationItems.forEach(item => {
    totalAmount += item.finalAmount || 0;
  });
  
  form.value.totalAmount = totalAmount;
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 报价单管理容器样式 */
.quotation-management {
    background-color: #fff;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主容器样式 */
.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 20px 16px;
    overflow: hidden;
    min-height: 0;
}

/* 筛选区域样式 */
.filter-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 工具栏区域样式 */
.toolbar-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 表格容器样式 */
.table-container {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    min-height: 0;
    flex: 1;
}

/* 表格样式 */
.quotation-table {
    flex: 1;
    border-radius: 8px;
}

/* 表格列对齐 */
:deep(.el-table .cell) {
    text-align: left;
}

/* 表格头部对齐 */
:deep(.el-table th .cell) {
    text-align: left;
}

/* 分页区域样式 */
.pagination-section {
    flex-shrink: 0;
    display: flex;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
}

/* 表格行高调整 */
:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-table th) {
    padding: 14px 0;
    background-color: #fafafa !important;
}

/* 表格操作按钮样式 */
:deep(.el-table .el-button) {
    font-size: var(--ep-font-size-base);
}

:deep(.el-table .el-button .el-icon) {
    font-size: var(--ep-font-size-base);
}

/* 表格边框优化 */
:deep(.el-table--border) {
    border: 1px solid #ebeef5;
}

:deep(.el-table--border td) {
    border-right: 1px solid #f0f2f5;
}

:deep(.el-table--border th) {
    border-right: 1px solid #f0f2f5;
}
</style>