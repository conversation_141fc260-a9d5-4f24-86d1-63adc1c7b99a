package com.ruoyi.crm.controller;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.OperationLog;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmOpportunity;
import com.ruoyi.common.service.impl.CrmOpportunityOperationLogServiceImpl;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.crm.service.ICrmOpportunityService;

/**
 * 商机管理控制器
 * 
 * 新的@OperationLog设计说明：
 * 1. 类级别配置：businessType、entityClass、serviceClass 全局生效
 * 2. 方法级别配置：只需要配置特殊操作类型，如 @OperationLog("STAGE_ADVANCE")
 * 3. 常规CRUD操作不需要配置，自动根据方法名识别（add->CREATE, edit->UPDATE, delete->DELETE）
 * 4. 支持禁用某个方法的日志：@OperationLog(enabled = false)
 */
@OperationLog(
    businessType = "商机",
    entityClass = CrmOpportunity.class,
    serviceClass = CrmOpportunityOperationLogServiceImpl.class
)
@RestController
@RequestMapping("/front/crm/opportunities")
public class CrmOpportunityController extends BaseController {
    
    @Autowired
    private ICrmOpportunityService crmOpportunityService;

    private static final Logger log = LoggerFactory.getLogger(CrmOpportunityController.class);
    
    /**
     * 获取商机列表
     */
    @GetMapping("/list")
    public TableDataInfo getOpportunityList(CrmOpportunity crmOpportunity) {
        startPage();
        List<CrmOpportunity> list = crmOpportunityService.getOpportunityList(crmOpportunity);
        return getDataTable(list);
    }

    /**
     * 获取商机详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmOpportunityService.selectCrmOpportunityById(id));
    }

    /**
     * 新增商机
     * 无需任何注解配置，自动识别为CREATE操作
     */
    @PostMapping
    public AjaxResult addWithRecord(@RequestBody CrmOpportunity crmOpportunity) {
        try {
            int rows = crmOpportunityService.insertCrmOpportunity(crmOpportunity);
            if (rows > 0) {
                // 插入成功后，crmOpportunity 对象中已经包含了生成的 ID
                log.info("新增商机成功，ID: {}", crmOpportunity.getId());
                return AjaxResult.success(crmOpportunity);
            }
            return AjaxResult.error();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改商机
     * 无需任何注解配置，自动识别为UPDATE操作
     */
    @PutMapping
    public AjaxResult editWithRecord(@RequestBody CrmOpportunity crmOpportunity) {
        try {
            return toAjax(crmOpportunityService.updateCrmOpportunity(crmOpportunity));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除商机
     * 无需任何注解配置，自动识别为DELETE操作
     */
    @DeleteMapping("/{ids}")
    public AjaxResult removeWithRecord(@PathVariable Long[] ids) {
        try {
            // 日志
            log.info("删除商机: {}", Arrays.toString(ids));
            return toAjax(crmOpportunityService.deleteCrmOpportunityByIds(ids));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 导出商机
     */
    @PostMapping("/export")
    public AjaxResult export(CrmOpportunity crmOpportunity) {
        List<CrmOpportunity> list = crmOpportunityService.getOpportunityList(crmOpportunity);
        ExcelUtil<CrmOpportunity> util = new ExcelUtil<CrmOpportunity>(CrmOpportunity.class);
        return util.exportExcel(list, "商机数据");
    }

    /**
     * 获取我负责的商机列表
     */
    @GetMapping("/my")
    public TableDataInfo getMyOpportunityList(CrmOpportunity crmOpportunity) {
        startPage();
        List<CrmOpportunity> list = crmOpportunityService.getMyOpportunityList(crmOpportunity);
        return getDataTable(list);
    }

    /**
     * 获取下属的商机列表
     */
    @GetMapping("/subordinate")
    public TableDataInfo getSubordinateOpportunityList(CrmOpportunity crmOpportunity) {
        startPage();
        List<CrmOpportunity> list = crmOpportunityService.getSubordinateOpportunityList(crmOpportunity);
        return getDataTable(list);
    }

    /**
     * 获取我关注的商机列表
     */
    @GetMapping("/followed")
    public TableDataInfo getFollowedOpportunityList(CrmOpportunity crmOpportunity) {
        startPage();
        List<CrmOpportunity> list = crmOpportunityService.getFollowedOpportunityList(crmOpportunity);
        return getDataTable(list);
    }

    /**
     * 分配商机
     * 特殊操作，需要指定操作类型
     */
    @PostMapping("/assign")
    @OperationLog("ASSIGN")
    public AjaxResult assignOpportunity(@RequestBody AssignForm form) {
        try {
            log.info("开始分配商机，商机ID: {}, 新负责人ID: {}", form.getOpportunityId(), form.getNewOwnerId());
            
            // 获取当前商机信息
            CrmOpportunity opportunity = crmOpportunityService.selectCrmOpportunityById(form.getOpportunityId());
            if (opportunity == null) {
                log.error("商机不存在，ID: {}", form.getOpportunityId());
                return AjaxResult.error("商机不存在");
            }
            
            // 记录旧负责人
            String oldOwnerId = opportunity.getResponsiblePersonId();
            log.info("商机原负责人ID: {}", oldOwnerId);
            
            // 更新商机负责人
            opportunity.setResponsiblePersonId(form.getNewOwnerId().toString());
            int result = crmOpportunityService.updateCrmOpportunity(opportunity);
            
            if (result > 0) {
                log.info("商机分配成功，商机ID: {}, 新负责人ID: {}", form.getOpportunityId(), form.getNewOwnerId());
                return AjaxResult.success();
            } else {
                log.error("商机分配失败，商机ID: {}, 新负责人ID: {}", form.getOpportunityId(), form.getNewOwnerId());
                return AjaxResult.error("分配失败");
            }
        } catch (Exception e) {
            log.error("分配商机时发生错误", e);
            return AjaxResult.error("分配失败：" + e.getMessage());
        }
    }

    /**
     * 推进商机阶段
     * 特殊操作，需要指定操作类型
     */
    @PostMapping("/advance-stage")
    @OperationLog("STAGE_ADVANCE")
    public AjaxResult advanceStage(@RequestBody StageAdvanceForm form) {
        try {
            log.info("推进商机阶段，商机ID: {}, 新阶段: {}", form.getOpportunityId(), form.getNewStage());
            
            int result = crmOpportunityService.advanceOpportunityStage(form.getOpportunityId(), form.getNewStage(), null);
            
            if (result > 0) {
                log.info("商机阶段推进成功，商机ID: {}, 新阶段: {}", form.getOpportunityId(), form.getNewStage());
                return AjaxResult.success();
            } else {
                log.error("商机阶段推进失败，商机ID: {}, 新阶段: {}", form.getOpportunityId(), form.getNewStage());
                return AjaxResult.error("阶段推进失败");
            }
        } catch (Exception e) {
            log.error("推进商机阶段时发生错误", e);
            return AjaxResult.error("阶段推进失败：" + e.getMessage());
        }
    }

    /**
     * 商机转化为合同
     * 特殊操作，需要指定操作类型
     */
    @PostMapping("/convert-to-contract")
    @OperationLog("CONVERT_TO_CONTRACT")
    public AjaxResult convertToContract(@RequestBody ConvertForm form) {
        try {
            log.info("商机转化为合同，商机ID: {}", form.getOpportunityId());
            
            int result = crmOpportunityService.convertToContract(form.getOpportunityId());
            
            if (result > 0) {
                log.info("商机转化为合同成功，商机ID: {}", form.getOpportunityId());
                return AjaxResult.success();
            } else {
                log.error("商机转化为合同失败，商机ID: {}", form.getOpportunityId());
                return AjaxResult.error("转化失败");
            }
        } catch (Exception e) {
            log.error("商机转化为合同时发生错误", e);
            return AjaxResult.error("转化失败：" + e.getMessage());
        }
    }
    
    /**
     * 商机分配表单
     */
    public static class AssignForm {
        private Long opportunityId;
        private Long newOwnerId;
        
        public Long getOpportunityId() {
            return opportunityId;
        }
        
        public void setOpportunityId(Long opportunityId) {
            this.opportunityId = opportunityId;
        }
        
        public Long getNewOwnerId() {
            return newOwnerId;
        }
        
        public void setNewOwnerId(Long newOwnerId) {
            this.newOwnerId = newOwnerId;
        }
    }

    /**
     * 阶段推进表单
     */
    public static class StageAdvanceForm {
        private Long opportunityId;
        private String newStage;
        private String remarks;
        
        public Long getOpportunityId() {
            return opportunityId;
        }
        
        public void setOpportunityId(Long opportunityId) {
            this.opportunityId = opportunityId;
        }
        
        public String getNewStage() {
            return newStage;
        }
        
        public void setNewStage(String newStage) {
            this.newStage = newStage;
        }
        
        public String getRemarks() {
            return remarks;
        }
        
        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }
    }

    /**
     * 转化表单
     */
    public static class ConvertForm {
        private Long opportunityId;
        private String contractName;
        private String remarks;
        
        public Long getOpportunityId() {
            return opportunityId;
        }
        
        public void setOpportunityId(Long opportunityId) {
            this.opportunityId = opportunityId;
        }
        
        public String getContractName() {
            return contractName;
        }
        
        public void setContractName(String contractName) {
            this.contractName = contractName;
        }
        
        public String getRemarks() {
            return remarks;
        }
        
        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }
    }

    /**
     * 根据客户ID查询商机
     */
    @GetMapping("/customer/{customerId}")
    public TableDataInfo getOpportunityByCustomer(@PathVariable("customerId") Long customerId) {
        try {
            CrmOpportunity searchCondition = new CrmOpportunity();
            searchCondition.setCustomerId(customerId);
            
            startPage();
            List<CrmOpportunity> opportunities = crmOpportunityService.getOpportunityList(searchCondition);
            return getDataTable(opportunities);
        } catch (Exception e) {
            log.error("根据客户ID查询商机失败", e);
            return new TableDataInfo(new java.util.ArrayList<>(), 0);
        }
    }

    /**
     * 搜索商机
     */
    @GetMapping("/search")
    public TableDataInfo searchOpportunity(@RequestParam(required = false) String keyword,
                                          @RequestParam(defaultValue = "1") Integer pageNum,
                                          @RequestParam(defaultValue = "100") Integer pageSize) {
        try {
            CrmOpportunity searchCondition = new CrmOpportunity();
            if (keyword != null && !keyword.trim().isEmpty()) {
                searchCondition.setOpportunityName(keyword);
            }
            
            startPage();
            List<CrmOpportunity> opportunities = crmOpportunityService.getOpportunityList(searchCondition);
            return getDataTable(opportunities);
        } catch (Exception e) {
            log.error("搜索商机失败", e);
            return new TableDataInfo(new java.util.ArrayList<>(), 0);
        }
    }
} 