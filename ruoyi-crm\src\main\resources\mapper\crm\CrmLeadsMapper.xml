<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadsMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmLeads" id="CrmLeadsResult">
        <id property="id" column="id"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="leadName" column="lead_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="leadSource" column="lead_source"/>
        <result property="mobile" column="mobile"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="address" column="address"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="customerIndustry" column="customer_industry"/>
        <result property="customerLevel" column="customer_level"/>
        <result property="nextContactTime" column="next_contact_time"/>
        <result property="selectedDate" column="selected_date"/>
        <result property="remarks" column="remarks"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <!-- BaseEntity的字段 -->
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        
    </resultMap>
    
    <sql id="selectLeadsVo">
        select id, responsible_person_id, lead_name, customer_name, lead_source,
        mobile, phone, email, address, detailed_address, customer_industry,
        customer_level, next_contact_time, selected_date, remarks, status,
        create_by, create_time, update_by, update_time, del_flag
        from crm_business_leads
    </sql>
    
    <select id="selectLeadList" parameterType="com.ruoyi.common.domain.entity.CrmLeads" resultMap="CrmLeadsResult">
        <include refid="selectLeadsVo"/>
        <where>
            del_flag = '0'
            <if test="responsiblePersonId != null and responsiblePersonId != ''">
                AND responsible_person_id = #{responsiblePersonId}
            </if>
            <if test="leadName != null and leadName != ''">
                AND lead_name like concat('%', #{leadName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="leadSource != null and leadSource != ''">
                AND lead_source = #{leadSource}
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile like concat('%', #{mobile}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone like concat('%', #{phone}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email like concat('%', #{email}, '%')
            </if>
            <if test="customerIndustry != null and customerIndustry != ''">
                AND customer_industry = #{customerIndustry}
            </if>
            <if test="customerLevel != null and customerLevel != ''">
                AND customer_level = #{customerLevel}
            </if>
            <if test="nextContactTime != null">
                AND next_contact_time = #{nextContactTime}
            </if>
            <if test="selectedDate != null">
                AND selected_date = #{selectedDate}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCrmLeadsById" parameterType="Long" resultMap="CrmLeadsResult">
        <include refid="selectLeadsVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <insert id="insertCrmLeads" parameterType="com.ruoyi.common.domain.entity.CrmLeads" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_leads (
            responsible_person_id, lead_name, customer_name, lead_source,
            mobile, phone, email, address, detailed_address, customer_industry,
            customer_level, next_contact_time, selected_date, remarks, status,
            create_by, create_time, update_by, update_time, del_flag
        ) values (
            #{responsiblePersonId}, #{leadName}, #{customerName}, #{leadSource},
            #{mobile}, #{phone}, #{email}, #{address}, #{detailedAddress}, #{customerIndustry},
            #{customerLevel}, #{nextContactTime}, #{selectedDate}, #{remarks}, #{status},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{delFlag}
        )
    </insert>

    <update id="updateCrmLeads" parameterType="com.ruoyi.common.domain.entity.CrmLeads">
        update crm_business_leads
        <trim prefix="SET" suffixOverrides=",">
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="leadName != null">lead_name = #{leadName},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="leadSource != null">lead_source = #{leadSource},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="address != null">address = #{address},</if>
            <if test="detailedAddress != null">detailed_address = #{detailedAddress},</if>
            <if test="customerIndustry != null">customer_industry = #{customerIndustry},</if>
            <if test="customerLevel != null">customer_level = #{customerLevel},</if>
            <if test="nextContactTime != null">next_contact_time = #{nextContactTime},</if>
            <if test="selectedDate != null">selected_date = #{selectedDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id} and del_flag = '0'
    </update>

    <update id="updateLeadStatus">
        update crm_business_leads
        set status = #{status},
            update_time = CURRENT_TIMESTAMP,
            update_by = #{updateBy}
        where id = #{leadId}
        and del_flag = '0'
    </update>

    <update id="deleteCrmLeadsById">
        update crm_business_leads 
        set del_flag = '2',
            update_time = CURRENT_TIMESTAMP,
            update_by = #{updateBy}
        where id = #{id}
    </update>

    <update id="deleteCrmLeadsByIds">
        update crm_business_leads 
        set del_flag = '2',
            update_time = CURRENT_TIMESTAMP,
            update_by = #{updateBy}
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMyLeadsList" parameterType="com.ruoyi.common.domain.entity.CrmLeads" resultMap="CrmLeadsResult">
        <include refid="selectLeadsVo"/>
        <where>
            del_flag = '0'
            AND responsible_person_id = #{responsiblePersonId}
            <if test="leadName != null and leadName != ''">
                AND lead_name like concat('%', #{leadName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="leadSource != null and leadSource != ''">
                AND lead_source = #{leadSource}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSubordinateLeadsList" parameterType="com.ruoyi.common.domain.entity.CrmLeads" resultMap="CrmLeadsResult">
        <include refid="selectLeadsVo"/>
        <where>
            del_flag = '0'
            AND responsible_person_id IN (
                SELECT user_id FROM sys_user WHERE dept_id IN (
                    SELECT dept_id FROM sys_user WHERE user_id = #{responsiblePersonId}
                )
            )
            <if test="leadName != null and leadName != ''">
                AND lead_name like concat('%', #{leadName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="leadSource != null and leadSource != ''">
                AND lead_source = #{leadSource}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFollowedLeadsList" parameterType="CrmLeads" resultMap="CrmLeadsResult">
        <include refid="selectLeadsVo"/>
        <where>
            del_flag = '0'
            AND id IN (
                SELECT lead_id FROM crm_business_lead_followers 
                WHERE follower_id = #{responsiblePersonId} AND del_flag = '0'
            )
            <if test="leadName != null and leadName != ''">
                AND lead_name like concat('%', #{leadName}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="leadSource != null and leadSource != ''">
                AND lead_source = #{leadSource}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>
    
</mapper>
