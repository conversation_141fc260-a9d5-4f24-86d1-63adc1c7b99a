package com.ruoyi.crm.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmCustomerOperationLog;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmCustomerOperationLogService;

/**
 * 客户操作日志Controller
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Anonymous
@RestController
@RequestMapping("/front/crm/customer/operationlog")
public class CrmCustomerOperationLogController extends BaseController {
    
    @Autowired
    private ICrmCustomerOperationLogService crmCustomerOperationLogService;

    private static final Logger log = LoggerFactory.getLogger(CrmCustomerOperationLogController.class);

    /**
     * 查询客户操作日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CrmCustomerOperationLog crmCustomerOperationLog) {
        try {
            startPage();
            List<CrmCustomerOperationLog> list = crmCustomerOperationLogService.selectCrmCustomerOperationLogList(crmCustomerOperationLog);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询操作日志列表失败: {}", e.getMessage(), e);
            return getDataTable(java.util.Collections.emptyList());
        }
    }

    /**
     * 根据客户ID查询操作日志
     */
    @GetMapping("/customer/{customerId}")
    public AjaxResult getOperationLogsByCustomerId(@PathVariable("customerId") Long customerId) {
        try {
            List<CrmCustomerOperationLog> list = crmCustomerOperationLogService.getOperationLogs(customerId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询客户操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询客户操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户操作日志详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        try {
            CrmCustomerOperationLog operationLog = crmCustomerOperationLogService.selectCrmCustomerOperationLogById(id);
            return AjaxResult.success(operationLog);
        } catch (Exception e) {
            log.error("查询操作日志详情失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询操作日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增客户操作日志
     */
    @Log(title = "客户操作日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmCustomerOperationLog crmCustomerOperationLog) {
        try {
            int result = crmCustomerOperationLogService.insertCrmCustomerOperationLog(crmCustomerOperationLog);
            return result > 0 ? AjaxResult.success("新增操作日志成功") : AjaxResult.error("新增操作日志失败");
        } catch (Exception e) {
            log.error("新增操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("新增操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 修改客户操作日志
     */
    @Log(title = "客户操作日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmCustomerOperationLog crmCustomerOperationLog) {
        try {
            int result = crmCustomerOperationLogService.updateCrmCustomerOperationLog(crmCustomerOperationLog);
            return result > 0 ? AjaxResult.success("修改操作日志成功") : AjaxResult.error("修改操作日志失败");
        } catch (Exception e) {
            log.error("修改操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("修改操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 删除客户操作日志
     */
    @Log(title = "客户操作日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        try {
            int result = crmCustomerOperationLogService.deleteCrmCustomerOperationLogByIds(ids);
            return result > 0 ? AjaxResult.success("删除操作日志成功") : AjaxResult.error("删除操作日志失败");
        } catch (Exception e) {
            log.error("删除操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 查询时间段内的操作日志
     */
    @GetMapping("/timerange")
    public AjaxResult getOperationLogsByTimeRange(@RequestParam String startTime, @RequestParam String endTime) {
        try {
            List<CrmCustomerOperationLog> list = crmCustomerOperationLogService.getOperationLogsByTimeRange(startTime, endTime);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询时间段操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询时间段操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作类型统计
     */
    @GetMapping("/statistics/operation-types")
    public AjaxResult getOperationTypeStatistics(@RequestParam(required = false) Long customerId,
                                               @RequestParam(required = false) String startTime,
                                               @RequestParam(required = false) String endTime) {
        try {
            CrmCustomerOperationLog query = new CrmCustomerOperationLog();
            if (customerId != null) {
                query.setCustomerId(customerId);
            }
            if (startTime != null) {
                query.setBeginTime(startTime);
            }
            if (endTime != null) {
                query.setEndTime(endTime);
            }
            
            List<CrmCustomerOperationLog> list = crmCustomerOperationLogService.selectCrmCustomerOperationLogList(query);
            
            // 统计操作类型
            java.util.Map<String, Integer> statistics = new java.util.HashMap<>();
            for (CrmCustomerOperationLog log : list) {
                String type = log.getOperationType();
                statistics.put(type, statistics.getOrDefault(type, 0) + 1);
            }
            
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取操作类型统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取操作类型统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的操作日志
     */
    @GetMapping("/recent")
    public AjaxResult getRecentOperationLogs(@RequestParam(defaultValue = "10") Integer limit,
                                           @RequestParam(required = false) Long customerId) {
        try {
            CrmCustomerOperationLog query = new CrmCustomerOperationLog();
            if (customerId != null) {
                query.setCustomerId(customerId);
            }
            
            List<CrmCustomerOperationLog> list = crmCustomerOperationLogService.selectCrmCustomerOperationLogList(query);
            
            // 手动限制数量
            if (list.size() > limit) {
                list = list.subList(0, limit);
            }
            
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询最近操作日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询最近操作日志失败: " + e.getMessage());
        }
    }
}