import { TableOperationsConfig } from '@/components/TableOperations/types';
import type { ButtonType } from 'element-plus';
import ContractBasicInfo from '../tabs/ContractBasicInfo.vue';
import ContractProducts from '../tabs/ContractProducts.vue';
import ContractPayment from '../tabs/ContractPayment.vue';

// 表格列配置
export const tableColumns = [
    {
        prop: 'contract_number',
        label: '合同编号',
        width: '150'
    },
    {
        prop: 'contract_name',
        label: '合同名称',
        width: '200'
    },
    {
        prop: 'customer_name',
        label: '客户名称',
        width: '150'
    },
    {
        prop: 'quotation_number',
        label: '报价单号',
        width: '150'
    },
    {
        prop: 'opportunity_name',
        label: '关联商机',
        width: '150'
    },
    {
        prop: 'contract_amount',
        label: '合同金额',
        width: '120'
    },
    {
        prop: 'order_date',
        label: '签订日期',
        width: '120'
    },
    {
        prop: 'start_date',
        label: '开始日期',
        width: '120'
    },
    {
        prop: 'end_date',
        label: '结束日期',
        width: '120'
    }
];

// 表格操作配置
export const tableOperations: TableOperationsConfig = {
    width: 220,
    fixed: 'right' as const,
    buttons: [
        {
            label: '分配',
            type: 'primary' as ButtonType,
            link: true,
            handler: 'handleAssign',
            icon: 'Share',
            show: true
        },
        {
            label: '转化',
            type: 'success' as ButtonType,
            link: true,
            handler: 'handleConvert',
            icon: 'Promotion',
            show: true
        },
        {
            label: '删除',
            type: 'danger' as ButtonType,
            link: true,
            handler: 'handleDelete',
            icon: 'Delete',
            show: true
        }
    ]
};

// 新建合同表单配置
export const newContractFormConfig = {
    layout: {
        labelPosition: 'right' as const,
        size: 'default' as const
    },
    fields: [
        {
            field: 'contract_number',
            label: '合同编号',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'contract_name',
            label: '合同名称',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'customer_name',
            label: '客户名称',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'quotation_number',
            label: '报价单号',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'opportunity_name',
            label: '关联商机',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'contract_amount',
            label: '合同金额',
            component: 'el-input-number',
            colSpan: 12
        },
        {
            field: 'order_date',
            label: '签订日期',
            component: 'el-date-picker',
            colSpan: 12
        },
        {
            field: 'start_date',
            label: '开始日期',
            component: 'el-date-picker',
            colSpan: 12
        },
        {
            field: 'end_date',
            label: '结束日期',
            component: 'el-date-picker',
            colSpan: 12
        },
        {
            field: 'customer_signatory',
            label: '客户签约人',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'company_signatory',
            label: '公司签约人',
            component: 'el-input',
            colSpan: 12
        },
        {
            field: 'remarks',
            label: '备注',
            component: 'el-input',
            colSpan: 24
        }
    ]
};

// 抽屉配置
export const drawerConfig = {
    headerFields: [
        { label: '合同编号', field: 'contract_number' },
        { label: '合同名称', field: 'contract_name' },
        { label: '客户名称', field: 'customer_name' }
    ],
    actions: [
        {
            label: '编辑',
            icon: 'Edit',
            handler: (data: any) => console.log('Edit', data)
        },
        {
            label: '删除',
            icon: 'Delete',
            type: 'danger',
            handler: (data: any) => console.log('Delete', data)
        }
    ],
    menuItems: [
        {
            key: 'basic',
            label: '基本信息',
            icon: 'Document',
            component: ContractBasicInfo
        },
        {
            key: 'products',
            label: '产品信息',
            icon: 'Goods',
            component: ContractProducts
        },
        {
            key: 'payment',
            label: '付款信息',
            icon: 'Money',
            component: ContractPayment
        }
    ]
};

// 导航配置
export const navConfig = {
    title: '合同管理',
    menuItems: [
        {
            key: 'contracts',
            label: '合同列表',
            icon: 'Document'
        },
        {
            key: 'analysis',
            label: '合同分析',
            icon: 'DataAnalysis'
        }
    ]
}; 