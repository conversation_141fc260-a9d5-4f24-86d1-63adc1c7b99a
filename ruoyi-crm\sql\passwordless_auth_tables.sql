-- CRM无密码认证系统数据库创建脚本
-- 执行前请备份现有数据库
-- 日期：2025-06-30
-- 版本：v1.0

-- ==============================================
-- 创建无密码认证相关表
-- ==============================================

-- 1. 用户注册表（无密码设计）
DROP TABLE IF EXISTS `crm_user_registration`;
CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码（加密存储）',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `passwordless_token` varchar(255) DEFAULT NULL COMMENT '无密码登录令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='用户注册记录表（无密码设计）';

-- 2. 用户认证方式表（支持无密码认证）
DROP TABLE IF EXISTS `crm_user_auth_methods`;
CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `auth_token` varchar(500) DEFAULT NULL COMMENT '认证令牌（无密码登录）',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`),
  KEY `idx_auth_identifier` (`auth_identifier`),
  KEY `idx_last_used_time` (`last_used_time`)
) ENGINE=InnoDB COMMENT='用户认证方式表（支持无密码认证）';

-- 3. 客户线索表增强（无密码注册）
DROP TABLE IF EXISTS `crm_customer_leads`;
CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `registration_method` varchar(20) DEFAULT 'sms' COMMENT '注册方式：sms-短信无密码注册',
  `access_token` varchar(255) DEFAULT NULL COMMENT '客户专用访问令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_access_token` (`access_token`)
) ENGINE=InnoDB COMMENT='客户线索表（无密码注册）';

-- 4. 线索文件表
DROP TABLE IF EXISTS `crm_lead_files`;
CREATE TABLE `crm_lead_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小',
  `upload_status` varchar(20) DEFAULT 'uploaded' COMMENT '上传状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='线索文件表';

-- 5. 线索沟通记录表
DROP TABLE IF EXISTS `crm_lead_communications`;
CREATE TABLE `crm_lead_communications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '沟通记录ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `sales_user_id` bigint(20) NOT NULL COMMENT '销售人员ID',
  `communication_type` varchar(50) NOT NULL COMMENT '沟通类型：phone-电话,email-邮件,meeting-会议,visit-拜访',
  `content` text COMMENT '沟通内容',
  `communication_time` datetime NOT NULL COMMENT '沟通时间',
  `status` varchar(20) DEFAULT 'completed' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_sales_user_id` (`sales_user_id`),
  KEY `idx_communication_time` (`communication_time`)
) ENGINE=InnoDB COMMENT='线索沟通记录表';

-- ==============================================
-- 添加外键约束
-- ==============================================

-- 用户认证方式表外键
ALTER TABLE `crm_user_auth_methods` 
ADD CONSTRAINT `fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE;

-- 客户线索表外键
ALTER TABLE `crm_customer_leads` 
ADD CONSTRAINT `fk_leads_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_leads_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL;

-- 线索文件表外键
ALTER TABLE `crm_lead_files`
ADD CONSTRAINT `fk_files_lead_id` FOREIGN KEY (`lead_id`) REFERENCES `crm_customer_leads` (`id`) ON DELETE CASCADE;

-- 线索沟通记录表外键
ALTER TABLE `crm_lead_communications`
ADD CONSTRAINT `fk_comm_lead_id` FOREIGN KEY (`lead_id`) REFERENCES `crm_customer_leads` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_comm_sales_user_id` FOREIGN KEY (`sales_user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE;

-- ==============================================
-- 数据迁移脚本
-- ==============================================

-- 备份现有数据（如果表存在）
-- CREATE TABLE sys_user_backup AS SELECT * FROM sys_user;

-- 清理客户用户的密码字段（安全起见，先注释）
-- UPDATE sys_user 
-- SET password = NULL 
-- WHERE user_type = '01' 
--    AND del_flag = '0';

-- 为现有企业微信用户创建认证方式记录
INSERT IGNORE INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active, bind_time)
SELECT 
    tw.user_id,
    'wechat' as auth_type,
    tw.wecom_user_id as auth_identifier,
    1 as is_primary,
    1 as is_active,
    NOW() as bind_time
FROM crm_thirdparty_wechat tw
INNER JOIN sys_user su ON tw.user_id = su.user_id
WHERE su.del_flag = '0';

-- 为现有用户创建手机号认证方式记录
INSERT IGNORE INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active, bind_time)
SELECT 
    user_id,
    'phone' as auth_type,
    phonenumber as auth_identifier,
    CASE WHEN user_id NOT IN (SELECT user_id FROM crm_user_auth_methods WHERE auth_type = 'wechat') THEN 1 ELSE 0 END as is_primary,
    1 as is_active,
    NOW() as bind_time
FROM sys_user 
WHERE phonenumber IS NOT NULL 
  AND phonenumber != ''
  AND del_flag = '0';

-- ==============================================
-- 创建性能优化索引
-- ==============================================

-- 注册表索引
CREATE INDEX idx_registration_phone_type ON crm_user_registration(phone_number, registration_type);
CREATE INDEX idx_registration_status_time ON crm_user_registration(status, create_time);

-- 认证方式表索引
CREATE INDEX idx_auth_methods_user_type ON crm_user_auth_methods(user_id, auth_type);
CREATE INDEX idx_auth_methods_identifier_active ON crm_user_auth_methods(auth_identifier, is_active);

-- 线索表索引
CREATE INDEX idx_leads_phone_status ON crm_customer_leads(phone_number, status);
CREATE INDEX idx_leads_assigned_time ON crm_customer_leads(assigned_to, create_time);
CREATE INDEX idx_leads_status_time ON crm_customer_leads(status, create_time);

-- ==============================================
-- 插入示例数据（测试用）
-- ==============================================

-- 插入测试短信服务配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES 
('无密码认证-短信服务商', 'passwordless.sms.provider', 'aliyun', 'N', 'admin', NOW(), '短信服务提供商'),
('无密码认证-验证码长度', 'passwordless.sms.code.length', '6', 'N', 'admin', NOW(), '短信验证码长度'),
('无密码认证-验证码过期时间', 'passwordless.sms.code.expire', '300', 'N', 'admin', NOW(), '短信验证码过期时间（秒）'),
('无密码认证-JWT密钥', 'passwordless.jwt.secret', 'CRM_PASSWORDLESS_SECRET_KEY_2025', 'Y', 'admin', NOW(), 'JWT签名密钥'),
('无密码认证-员工令牌过期时间', 'passwordless.jwt.expire.employee', '**********', 'N', 'admin', NOW(), '员工令牌过期时间（毫秒）'),
('无密码认证-客户令牌过期时间', 'passwordless.jwt.expire.customer', '604800000', 'N', 'admin', NOW(), '客户令牌过期时间（毫秒）')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- ==============================================
-- 权限和菜单配置
-- ==============================================

-- 插入无密码认证菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
('无密码认证管理', 0, 10, 'passwordless', NULL, 1, 0, 'M', '0', '0', NULL, 'lock', 'admin', NOW(), '无密码认证管理菜单'),
('用户注册记录', (SELECT menu_id FROM sys_menu WHERE path = 'passwordless' LIMIT 1), 1, 'registration', 'crm/passwordless/registration/index', 1, 0, 'C', '0', '0', 'crm:registration:list', 'user', 'admin', NOW(), '用户注册记录菜单'),
('认证方式管理', (SELECT menu_id FROM sys_menu WHERE path = 'passwordless' LIMIT 1), 2, 'auth-methods', 'crm/passwordless/auth/index', 1, 0, 'C', '0', '0', 'crm:auth:list', 'phone', 'admin', NOW(), '认证方式管理菜单'),
('客户线索管理', (SELECT menu_id FROM sys_menu WHERE path = 'passwordless' LIMIT 1), 3, 'leads', 'crm/passwordless/leads/index', 1, 0, 'C', '0', '0', 'crm:leads:list', 'peoples', 'admin', NOW(), '客户线索管理菜单');

-- 插入按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time)
VALUES 
-- 注册记录权限
('注册记录查询', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:registration:list' LIMIT 1), 1, '', '', 1, 0, 'F', '0', '0', 'crm:registration:query', '#', 'admin', NOW()),
('注册记录新增', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:registration:list' LIMIT 1), 2, '', '', 1, 0, 'F', '0', '0', 'crm:registration:add', '#', 'admin', NOW()),
('注册记录修改', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:registration:list' LIMIT 1), 3, '', '', 1, 0, 'F', '0', '0', 'crm:registration:edit', '#', 'admin', NOW()),
('注册记录删除', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:registration:list' LIMIT 1), 4, '', '', 1, 0, 'F', '0', '0', 'crm:registration:remove', '#', 'admin', NOW()),

-- 认证方式权限
('认证方式查询', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:auth:list' LIMIT 1), 1, '', '', 1, 0, 'F', '0', '0', 'crm:auth:query', '#', 'admin', NOW()),
('认证方式新增', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:auth:list' LIMIT 1), 2, '', '', 1, 0, 'F', '0', '0', 'crm:auth:add', '#', 'admin', NOW()),
('认证方式修改', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:auth:list' LIMIT 1), 3, '', '', 1, 0, 'F', '0', '0', 'crm:auth:edit', '#', 'admin', NOW()),
('认证方式删除', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:auth:list' LIMIT 1), 4, '', '', 1, 0, 'F', '0', '0', 'crm:auth:remove', '#', 'admin', NOW()),

-- 线索管理权限
('线索查询', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:leads:list' LIMIT 1), 1, '', '', 1, 0, 'F', '0', '0', 'crm:leads:query', '#', 'admin', NOW()),
('线索新增', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:leads:list' LIMIT 1), 2, '', '', 1, 0, 'F', '0', '0', 'crm:leads:add', '#', 'admin', NOW()),
('线索修改', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:leads:list' LIMIT 1), 3, '', '', 1, 0, 'F', '0', '0', 'crm:leads:edit', '#', 'admin', NOW()),
('线索删除', (SELECT menu_id FROM sys_menu WHERE perms = 'crm:leads:list' LIMIT 1), 4, '', '', 1, 0, 'F', '0', '0', 'crm:leads:remove', '#', 'admin', NOW());

-- ==============================================
-- 数据清理定时任务设置
-- ==============================================

-- 创建清理过期数据的存储过程
DELIMITER $$

CREATE PROCEDURE CleanExpiredPasswordlessData()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理过期的注册记录（7天前）
    DELETE FROM crm_user_registration 
    WHERE status = 'pending' 
      AND create_time < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- 清理过期的验证码记录（1小时前）
    UPDATE crm_user_registration 
    SET verification_code = NULL, 
        verification_expire_time = NULL 
    WHERE verification_expire_time < DATE_SUB(NOW(), INTERVAL 1 HOUR);
    
    -- 清理过期的认证令牌
    UPDATE crm_user_auth_methods 
    SET auth_token = NULL 
    WHERE token_expire_time < NOW();
    
    -- 记录清理日志
    INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, oper_time)
    VALUES ('无密码认证数据清理', 0, 'CleanExpiredPasswordlessData', 'PROCEDURE', 0, 'system', NOW());
    
    COMMIT;
END$$

DELIMITER ;

-- ==============================================
-- 验证脚本
-- ==============================================

-- 验证表是否创建成功
SELECT 
    table_name,
    table_comment,
    table_rows,
    create_time
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name LIKE 'crm_%'
  AND table_name IN ('crm_user_registration', 'crm_user_auth_methods', 'crm_customer_leads', 'crm_lead_files', 'crm_lead_communications')
ORDER BY table_name;

-- 验证索引是否创建成功
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name LIKE 'crm_%'
  AND table_name IN ('crm_user_registration', 'crm_user_auth_methods', 'crm_customer_leads')
ORDER BY table_name, index_name, seq_in_index;

-- 验证外键约束是否创建成功
SELECT 
    table_name,
    constraint_name,
    column_name,
    referenced_table_name,
    referenced_column_name
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
  AND referenced_table_name IS NOT NULL
  AND table_name LIKE 'crm_%'
ORDER BY table_name, constraint_name;

-- ==============================================
-- 脚本执行完成提示
-- ==============================================

SELECT 'CRM无密码认证系统数据库初始化完成!' as message,
       NOW() as completion_time,
       '请检查上述验证结果，确保所有表和约束都已正确创建' as next_step;
