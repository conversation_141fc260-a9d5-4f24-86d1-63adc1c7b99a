<template>
    <div class="chart-container">
        <!-- Chart Section -->
        <div ref="customerVolumeChart" class="volume-chart"></div>
        <!-- Table Section -->
        <el-table :data="tableData" style="width: 100%; margin-top: 20px">
            <el-table-column prop="employeeName" label="员工姓名" width="120" />
            <el-table-column prop="currentCustomerCount" label="当前客户数" width="120" />
            <el-table-column prop="newCustomerCount" label="新增客户数" width="120" />
            <el-table-column prop="successfulCustomerCount" label="成交客户数" width="120" />
            <el-table-column prop="customerSuccessRate" label="客户成交率" width="120">
                <!-- <template slot-scope="scope">
                    {{ (scope.row.customerSuccessRate * 100).toFixed(2) }}%
                </template> -->
            </el-table-column>
            <el-table-column prop="totalContractAmount" label="合同总金额" width="120" />
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            chart: null,
            tableData: [
                {
                    employeeName: '张三',
                    currentCustomerCount: 100,
                    newCustomerCount: 20,
                    successfulCustomerCount: 10,
                    customerSuccessRate: 0.1,
                    totalContractAmount: 50000,
                },
                // Add more data as needed
            ]
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.customerVolumeChart;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '客户总量分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['当前客户数', '新增客户数', '成交客户数']
                },
                xAxis: {
                    type: 'category',
                    data: this.tableData.map(item => item.employeeName)
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '当前客户数',
                        type: 'bar',
                        data: this.tableData.map(item => item.currentCustomerCount)
                    },
                    {
                        name: '新增客户数',
                        type: 'bar',
                        data: this.tableData.map(item => item.newCustomerCount)
                    },
                    {
                        name: '成交客户数',
                        type: 'bar',
                        data: this.tableData.map(item => item.successfulCustomerCount)
                    }
                ]
            };
            this.chart.setOption(option);
        }
    }
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.volume-chart {
    width: 100%;
    height: 400px;
}
</style>