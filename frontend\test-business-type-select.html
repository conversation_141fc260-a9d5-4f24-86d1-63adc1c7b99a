<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务类型下拉框测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #303133;
        }
        .filter-section {
            margin-bottom: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .filter-section .el-select {
            width: 100%;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f0f9ff;
            border-radius: 4px;
            border-left: 4px solid #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>业务类型下拉框测试</h1>
            <p>这个页面用于测试业务类型下拉框的显示和功能是否正常。</p>

            <div class="test-section">
                <div class="test-title">测试1：基本下拉框</div>
                <el-select v-model="selectedType1" placeholder="请选择业务类型" clearable style="width: 100%">
                    <el-option label="全部类型" value="" />
                    <el-option label="联系人" value="CONTACT" />
                    <el-option label="线索" value="LEAD" />
                    <el-option label="客户" value="CUSTOMER" />
                    <el-option label="商机" value="OPPORTUNITY" />
                    <el-option label="合同" value="CONTRACT" />
                    <el-option label="拜访计划" value="VISIT_PLAN" />
                </el-select>
                <div class="result" v-if="selectedType1 !== undefined">
                    选中的值：{{ selectedType1 || '空值' }}
                </div>
            </div>

            <div class="test-section">
                <div class="test-title">测试2：模拟 TeamBusinessObjects 组件的筛选区域</div>
                <div class="filter-section">
                    <el-row :gutter="16" align="middle">
                        <el-col :span="6">
                            <el-select
                                v-model="selectedType2"
                                placeholder="业务类型"
                                clearable
                                style="width: 100%"
                            >
                                <el-option label="全部类型" value="" />
                                <el-option label="联系人" value="CONTACT" />
                                <el-option label="线索" value="LEAD" />
                                <el-option label="客户" value="CUSTOMER" />
                                <el-option label="商机" value="OPPORTUNITY" />
                                <el-option label="合同" value="CONTRACT" />
                                <el-option label="拜访计划" value="VISIT_PLAN" />
                            </el-select>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                v-model="searchKeyword"
                                placeholder="搜索业务对象名称"
                                clearable
                            >
                                <template #prefix>
                                    <el-icon><Search /></el-icon>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :span="6">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                            />
                        </el-col>
                        <el-col :span="4">
                            <el-button @click="resetFilters">重置</el-button>
                        </el-col>
                    </el-row>
                </div>
                <div class="result">
                    <p>业务类型：{{ selectedType2 || '未选择' }}</p>
                    <p>搜索关键词：{{ searchKeyword || '无' }}</p>
                    <p>日期范围：{{ dateRange ? dateRange.join(' 至 ') : '未选择' }}</p>
                </div>
            </div>

            <div class="test-section">
                <div class="test-title">测试3：业务类型标签显示</div>
                <div style="margin-bottom: 15px;">
                    <el-tag v-for="type in businessTypes" :key="type.value" 
                           :type="getBizTypeTagType(type.value)" 
                           style="margin-right: 8px; margin-bottom: 8px;">
                        {{ type.label }}
                    </el-tag>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElSelect, ElOption, ElInput, ElDatePicker, ElButton, ElRow, ElCol, ElTag, ElIcon } = ElementPlus;

        createApp({
            components: {
                ElSelect,
                ElOption,
                ElInput,
                ElDatePicker,
                ElButton,
                ElRow,
                ElCol,
                ElTag,
                ElIcon
            },
            setup() {
                const selectedType1 = ref('');
                const selectedType2 = ref('');
                const searchKeyword = ref('');
                const dateRange = ref(null);

                const businessTypes = ref([
                    { label: '联系人', value: 'CONTACT' },
                    { label: '线索', value: 'LEAD' },
                    { label: '客户', value: 'CUSTOMER' },
                    { label: '商机', value: 'OPPORTUNITY' },
                    { label: '合同', value: 'CONTRACT' },
                    { label: '拜访计划', value: 'VISIT_PLAN' }
                ]);

                const getBizTypeTagType = (bizType) => {
                    const typeMap = {
                        CONTACT: 'primary',
                        LEAD: 'warning',
                        CUSTOMER: 'success',
                        OPPORTUNITY: 'info',
                        CONTRACT: 'danger',
                        VISIT_PLAN: 'info'
                    };
                    return typeMap[bizType] || 'info';
                };

                const resetFilters = () => {
                    selectedType2.value = '';
                    searchKeyword.value = '';
                    dateRange.value = null;
                };

                return {
                    selectedType1,
                    selectedType2,
                    searchKeyword,
                    dateRange,
                    businessTypes,
                    getBizTypeTagType,
                    resetFilters
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
