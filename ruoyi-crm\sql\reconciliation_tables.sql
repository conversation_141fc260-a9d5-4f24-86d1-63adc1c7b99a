CREATE TABLE `crm_reconciliation` (
  `reconciliation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '对账单ID',
  `reconciliation_no` varchar(50) NOT NULL COMMENT '对账单编号',
  
  -- === 关联关系优化：支持客户和联系人两种关联方式 ===
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID（当relation_type=customer时使用）',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint DEFAULT NULL COMMENT '联系人ID（当relation_type=contact时使用）',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `contact_customer_id` bigint DEFAULT NULL COMMENT '联系人所属客户ID（当relation_type=contact时）',
  `contact_customer_name` varchar(100) DEFAULT NULL COMMENT '联系人所属客户名称',
  
  -- === 业务字段 ===
  `reconciliation_period` varchar(20) DEFAULT NULL COMMENT '对账周期',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '订单总金额',
  `manual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '手动添加金额',
  `subtotal_amount` decimal(15,2) DEFAULT '0.00' COMMENT '小计金额',
  `prepayment_amount` decimal(15,2) DEFAULT '0.00' COMMENT '预收款抵扣金额',
  `net_amount` decimal(15,2) DEFAULT '0.00' COMMENT '净对账金额',
  `paid_amount` decimal(15,2) DEFAULT '0.00' COMMENT '已回款金额',
  `outstanding_amount` decimal(15,2) DEFAULT '0.00' COMMENT '待回款金额',
  
  -- === 业务状态管理 ===
  `status` varchar(20) DEFAULT 'draft' COMMENT '业务状态:draft-草稿,submitted-已提交,approved-已审核,rejected-已驳回,invoiced-已开票,paid-已回款,completed-已完成,cancelled-已取消',
  
  -- === Activiti流程引擎集成字段 ===
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'reconciliation-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态:not_started-未启动,running-运行中,suspended-已挂起,completed-已完成,terminated-已终止',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `process_start_time` datetime DEFAULT NULL COMMENT '流程启动时间',
  `process_end_time` datetime DEFAULT NULL COMMENT '流程结束时间',
  
  -- === 审批相关（保持兼容，但主要由流程引擎管理） ===
  `approval_status` varchar(20) DEFAULT 'pending' COMMENT '审批状态:pending-待审批,approved-已通过,rejected-已驳回',
  `approval_notes` text COMMENT '审批备注',
  `approved_by` bigint DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  
  -- === 团队管理 ===
  `responsible_user_id` bigint NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(50) NOT NULL COMMENT '负责人姓名',
  `team_id` bigint DEFAULT NULL COMMENT '团队ID',
  `team_name` varchar(50) DEFAULT NULL COMMENT '团队名称',
  
  -- === 系统字段 ===
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  
  PRIMARY KEY (`reconciliation_id`),
  UNIQUE KEY `uk_reconciliation_no` (`reconciliation_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_status` (`status`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_current_assignee` (`current_assignee`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_responsible_user` (`responsible_user_id`),
  
  -- === 约束检查 ===
  CONSTRAINT `chk_relation_customer` CHECK (
    (relation_type = 'customer' AND customer_id IS NOT NULL AND contact_id IS NULL) 
    OR 
    (relation_type = 'contact' AND contact_id IS NOT NULL AND contact_customer_id IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账单主表';

CREATE TABLE `crm_reconciliation_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `reconciliation_id` bigint NOT NULL COMMENT '对账单ID',
  `detail_type` varchar(20) NOT NULL COMMENT '明细类型:order-订单,manual-手动添加',
  `order_id` bigint DEFAULT NULL COMMENT '关联订单ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `item_description` text COMMENT '项目描述',
  `quantity` decimal(10,2) DEFAULT '1.00' COMMENT '数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(15,2) DEFAULT '0.00' COMMENT '单价',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率',
  `tax_amount` decimal(15,2) DEFAULT '0.00' COMMENT '税额',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '含税总额',
  `remark` text COMMENT '备注',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`detail_id`),
  KEY `idx_reconciliation_id` (`reconciliation_id`),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_reconciliation_detail_main` FOREIGN KEY (`reconciliation_id`) REFERENCES `crm_reconciliation` (`reconciliation_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账单明细表';

CREATE TABLE `crm_invoice_application` (
  `application_id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `application_no` varchar(50) NOT NULL COMMENT '申请编号',
  
  -- === 关联关系优化 ===
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  
  `reconciliation_ids` text COMMENT '关联对账单ID列表,逗号分隔',
  `invoice_type` varchar(20) NOT NULL COMMENT '发票类型:special-专用发票,ordinary-普通发票,electronic-电子发票',
  `invoice_amount` decimal(15,2) NOT NULL COMMENT '开票金额',
  `tax_rate` decimal(5,2) NOT NULL COMMENT '税率',
  `tax_amount` decimal(15,2) NOT NULL COMMENT '税额',
  `total_amount` decimal(15,2) NOT NULL COMMENT '价税合计',
  `invoice_date` date NOT NULL COMMENT '开票日期',
  `invoice_title` varchar(200) NOT NULL COMMENT '发票抬头',
  `tax_no` varchar(50) DEFAULT NULL COMMENT '纳税人识别号',
  `bank_account` varchar(100) DEFAULT NULL COMMENT '开户行及账号',
  `address_phone` varchar(200) DEFAULT NULL COMMENT '地址电话',
  
  -- === 业务状态 ===
  `application_status` varchar(20) DEFAULT 'draft' COMMENT '申请状态:draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,invoiced-已开票,cancelled-已取消',
  
  -- === Activiti流程引擎集成字段 ===
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'invoice-application-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  
  `approval_notes` text COMMENT '审批备注',
  `approved_by` bigint DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `invoice_no` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `invoice_code` varchar(20) DEFAULT NULL COMMENT '发票代码',
  `invoice_time` datetime DEFAULT NULL COMMENT '开票时间',
  `invoice_user_id` bigint DEFAULT NULL COMMENT '开票人ID',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  
  PRIMARY KEY (`application_id`),
  UNIQUE KEY `uk_application_no` (`application_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_current_assignee` (`current_assignee`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开票申请表';

CREATE TABLE `crm_payment_record` (
  `payment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回款ID',
  `payment_no` varchar(50) NOT NULL COMMENT '回款编号',
  
  -- === 关联优化 ===
  `reconciliation_id` bigint DEFAULT NULL COMMENT '对账单ID（可为空，支持独立回款）',
  `reconciliation_no` varchar(50) DEFAULT NULL COMMENT '对账单编号',
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  
  `payment_amount` decimal(15,2) NOT NULL COMMENT '回款金额',
  `payment_method` varchar(20) NOT NULL COMMENT '回款方式:bank_transfer-银行转账,check-支票,cash-现金,draft-承兑汇票',
  `payment_date` date NOT NULL COMMENT '回款日期',
  `bank_info` varchar(200) DEFAULT NULL COMMENT '银行信息',
  `voucher_no` varchar(50) DEFAULT NULL COMMENT '凭证号码',
  
  -- === 业务状态 ===
  `payment_status` varchar(20) DEFAULT 'draft' COMMENT '回款状态:draft-草稿,submitted-已提交,confirmed-已确认,cancelled-已取消',
  
  -- === Activiti流程引擎集成字段 ===
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'payment-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  
  `allocation_details` text COMMENT '分配明细JSON',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_reconciliation_id` (`reconciliation_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_payment_date` (`payment_date`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_current_assignee` (`current_assignee`),
  CONSTRAINT `fk_payment_reconciliation` FOREIGN KEY (`reconciliation_id`) REFERENCES `crm_reconciliation` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回款记录表';

CREATE TABLE `crm_reconciliation_process_history` (
  `history_id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型:reconciliation-对账单,invoice-开票申请,payment-回款记录',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `business_no` varchar(50) NOT NULL COMMENT '业务编号',
  `process_instance_id` varchar(64) NOT NULL COMMENT 'Activiti流程实例ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
  `task_name` varchar(100) DEFAULT NULL COMMENT '任务名称',
  `action_type` varchar(20) NOT NULL COMMENT '操作类型:submit-提交,approve-通过,reject-驳回,cancel-取消,complete-完成',
  `action_user_id` bigint NOT NULL COMMENT '操作人ID',
  `action_user_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `action_comment` text COMMENT '操作意见',
  `from_status` varchar(20) DEFAULT NULL COMMENT '变更前状态',
  `to_status` varchar(20) DEFAULT NULL COMMENT '变更后状态',
  `form_data` text COMMENT '表单数据JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`history_id`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_action_user` (`action_user_id`),
  KEY `idx_action_time` (`action_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程操作历史表';

CREATE TABLE `crm_reconciliation_task_todo` (
  `todo_id` bigint NOT NULL AUTO_INCREMENT COMMENT '待办ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型:reconciliation-对账单,invoice-开票申请,payment-回款记录',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `business_no` varchar(50) NOT NULL COMMENT '业务编号',
  `business_title` varchar(200) NOT NULL COMMENT '业务标题',
  `process_instance_id` varchar(64) NOT NULL COMMENT 'Activiti流程实例ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_definition_key` varchar(64) NOT NULL COMMENT '任务定义Key',
  `assignee` varchar(50) NOT NULL COMMENT '处理人',
  `candidate_users` text COMMENT '候选人列表',
  `candidate_groups` text COMMENT '候选组列表',
  `priority` int DEFAULT '50' COMMENT '优先级',
  `task_create_time` datetime NOT NULL COMMENT '任务创建时间',
  `due_date` datetime DEFAULT NULL COMMENT '到期时间',
  `form_key` varchar(100) DEFAULT NULL COMMENT '表单Key',
  `is_read` char(1) DEFAULT '0' COMMENT '是否已读:0-未读,1-已读',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`todo_id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_assignee` (`assignee`),
  KEY `idx_task_create_time` (`task_create_time`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程任务待办表'; 