# 线索模块业务文档

## 一、功能介绍

线索模块是CRM系统中获取潜在客户信息的首要环节，主要用于收集、管理和跟进各种市场活动中获取的潜在商机。该模块实现了线索的全流程管理，包括线索录入、分配、跟进、评估和转化，帮助企业提高销售前端漏斗的转化效率，实现销售线索的标准化管理和数据沉淀。

---

## 二、API

| 接口名称         | 路径                           | 方法   | 描述           | 主要参数                                          | 返回值说明                     |
|------------------|--------------------------------|--------|----------------|---------------------------------------------------|--------------------------------|
| 获取线索列表     | `/crm/api/leads/list`          | GET    | 查询线索列表   | page:int, size:int, filters:object(可选)          | 分页线索数据                   |
| 获取线索详情     | `/crm/api/leads/{id}`          | GET    | 查询线索详情   | id:long(必填)                                     | 线索详细信息                   |
| 新增线索         | `/crm/api/leads`               | POST   | 创建新线索     | name:string(必填), mobile:string(必填), 其他字段  | 操作结果                       |
| 修改线索         | `/crm/api/leads`               | PUT    | 编辑线索信息   | id:long(必填), 修改字段                           | 操作结果                       |
| 删除线索         | `/crm/api/leads/{ids}`         | DELETE | 批量删除线索   | ids:long[](必填)                                  | 操作结果                       |
| 线索分配         | `/crm/api/leads/assign`        | POST   | 分配线索给销售 | leadsIds:long[](必填), userId:long(必填)          | 操作结果                       |
| 线索转换         | `/crm/api/leads/convert`       | POST   | 线索转为客户   | leadId:long(必填), customerInfo:object(必填)      | 操作结果                       |
| 线索跟进记录     | `/crm/api/leads/follow-up`     | POST   | 记录跟进情况   | leadId:long(必填), content:string(必填), type:int | 操作结果                       |
| 线索跟进记录列表 | `/crm/api/leads/follow-up/list`| GET    | 查询跟进记录   | leadId:long(必填), page:int, size:int            | 分页跟进记录数据               |
| 线索导入         | `/crm/api/leads/import`        | POST   | 批量导入线索   | file:File(必填,Excel格式)                         | 导入结果(成功数量,失败数量)    |
| 线索导出         | `/crm/api/leads/export`        | POST   | 导出线索数据   | filters:object(可选)                              | 返回Excel文件                  |

---

## 三、流程图

### 3.1 整体业务流程

```mermaid
flowchart TD
    A[线索录入/导入] --> B[线索筛选/审核]
    B --> C[线索分配]
    C --> D[销售跟进]
    D --> E{评估价值}
    E -- 有价值 --> F[进一步沟通]
    F --> G{是否有购买意向}
    G -- 是 --> H[转为客户]
    G -- 否 --> I[继续跟进]
    I --> D
    E -- 无价值 --> J[标记为无效线索]
    J --> K[线索关闭]
```

### 3.2 线索分配详细流程

```mermaid
flowchart TD
    A[开始分配] --> B{手动分配/自动分配?}
    
    B -- 手动分配 --> C[管理员选择线索]
    C --> D[选择目标销售人员]
    
    B -- 自动分配 --> E[系统分析销售人员负载]
    E --> F[基于规则匹配最佳销售]
    F --> G[系统选择目标销售人员]
    
    D --> H[校验线索状态]
    G --> H
    
    H --> I{状态是否为待分配?}
    I -- 否 --> J[提示状态错误]
    J --> Z[分配失败]
    
    I -- 是 --> K[校验销售人员状态]
    K --> L{销售是否可接收新线索?}
    L -- 否 --> M[提示销售不可用]
    M --> Z
    
    L -- 是 --> N[更新线索负责人ID]
    N --> O[更新线索状态为待跟进]
    O --> P[生成分配操作记录]
    P --> Q[创建线索跟进人关联]
    Q --> R[发送通知给销售人员]
    R --> S[分配成功]
```

### 3.3 线索转化流程 (泳道图)

```mermaid
flowchart TD
    subgraph 用户
        A[发起线索转化]
        O{确认合并信息?}
    end
    
    subgraph 系统
        B[接收转化请求]
        C[校验线索]
        D{状态/跟进次数/信息是否符合要求?}
        E[提示不满足转化条件]
        F[校验客户是否存在]
        G{客户已存在?}
        H[提示客户重复]
        I[创建新客户]
        J[合并客户信息]
        K[更新线索状态为'已转化']
        L[生成转化操作日志]
        M[创建客户跟进计划]
        N[转化成功]
        Z[转化失败]
    end
    
    A --> B
    B --> C
    C --> D
    D -- 是 --> F
    D -- 否 --> E --> Z
    F --> G
    G -- 是 --> H
    H --> O
    O -- 是 --> J
    O -- 否 --> Z
    G -- 否 --> I
    I --> K
    J --> K
    K --> L
    L --> M
    M --> N
```

---

## 四、架构与时序图

### 4.1 模块结构图

```mermaid
graph TD
    A[CrmLeadsController] --> B[ICrmLeadsService]
    B --> C[CrmLeadsServiceImpl]
    C --> D[CrmLeadsMapper]
    D --> E[(Database)]
    C --> F[CrmLeadFollowerService]
    C --> G[CrmLeadOperationLogService]
    C --> H[CrmCustomerService]
    A --> I[线索DTO/VO]
```

### 4.2 线索转化时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as CrmLeadsController
    participant Service as CrmLeadsService
    participant CustomerSvc as CrmCustomerService
    participant LogSvc as OperationLogService
    participant DB as 数据库

    User->>+Controller: POST /crm/api/leads/convert
    Controller->>+Service: convertLead(leadId, ...)
    
    Service->>DB: 1. 查询线索信息
    DB-->>Service: 返回线索
    
    Service->>Service: 2. 校验线索状态、跟进次数等
    alt 校验失败
        Service-->>-Controller: 抛出业务异常
        Controller-->>-User: 返回错误信息
    end

    Service->>+CustomerSvc: 3. 检查客户是否存在（按手机/公司名）
    CustomerSvc->>DB: 查询客户表
    DB-->>CustomerSvc: 返回查询结果
    CustomerSvc-->>-Service: 返回是否存在

    alt 客户已存在 (需要合并)
        Service->>+CustomerSvc: 4a. 合并客户信息
        CustomerSvc->>DB: 更新客户数据
        DB-->>CustomerSvc: 操作成功
        CustomerSvc-->>-Service: 返回合并后的客户
    else 客户不存在
        Service->>+CustomerSvc: 4b. 根据线索创建新客户
        CustomerSvc->>DB: 插入新客户数据
        DB-->>CustomerSvc: 操作成功
        CustomerSvc->>-Service: 返回新客户
    end

    Service->>DB: 5. 更新线索状态为'已转化'
    
    Service->>+LogSvc: 6. 记录转化操作日志
    LogSvc->>DB: 插入日志记录
    DB-->>LogSvc: 操作成功
    LogSvc-->>-Service: 
    
    Service-->>-Controller: 返回成功
    Controller-->>-User: 转化成功
```

---

## 五、实体图

```mermaid
erDiagram
    crm_business_leads {
        Integer id PK "主键"
        String responsible_person_id "负责人"
        String lead_name "线索名称"
        String lead_source "线索来源"
        String mobile "手机号码"
        String phone "电话号码"
        String email "电子邮件地址"
        String address "地址"
        String detailed_address "详细地址"
        String customer_industry "客户所属行业"
        String customer_level "客户级别"
        DateTime next_contact_time "下次联系时间"
        Date selected_date "选择日期"
        Text remarks "备注信息"
        String customer_name "客户名称"
        String status "状态"
        String del_flag "删除标志"
        String create_by "创建人"
        DateTime create_time "创建时间"
        String update_by "更新人"
        DateTime update_time "更新时间"
    }
    
    crm_business_lead_followers {
        Integer id PK "主键"
        Integer lead_id FK "线索ID"
        Integer follower_id "关注者ID"
        String status "关注状态"
        String del_flag "删除标志"
        String create_by "创建人"
        DateTime create_time "创建时间"
        String update_by "更新人"
        DateTime update_time "更新时间"
    }
    
    crm_business_follow_up_records {
        Integer id PK "主键"
        String module_type "所属模块"
        Text follow_up_content "跟进内容"
        String follow_up_method "跟进方式"
        String next_contact_method "下次联系方式"
        Integer visit_plan_id "拜访计划ID"
        JSON related_files "相关文件"
        Integer related_customer_id "关联客户ID"
        Integer related_contact_id "关联联系人ID"
        Integer related_opportunity_id "关联商机ID"
        Integer related_contract_id "关联合同ID"
        Integer related_payment_id "关联回款ID"
        Integer related_product_id "关联产品ID"
        String follow_up_type "跟进类型"
        String effective_follow_up_person "有效跟进人"
        String creator_id "创建人ID"
        DateTime created_at "创建时间"
        DateTime updated_at "更新时间"
    }
    
    crm_lead_operation_log {
        Long id PK "主键"
        Long lead_id FK "线索ID"
        String business_type "业务类型"
        String operation_type "操作类型"
        Text operation_content "操作内容"
        JSON operation_details "操作细则"
        Long operator_id "操作人ID"
        String operator_name "操作人名称"
        DateTime operation_time "操作时间"
    }
    
    crm_business_leads ||--o{ crm_business_lead_followers : "关联"
    crm_business_leads ||--o{ crm_lead_operation_log : "记录"
    crm_business_leads |o..o| crm_business_follow_up_records : "跟进"
```

---

## 六、其他说明

- **线索有效性规则**：
  - 手机号码必须唯一，导入/新增时需校验
  - 名称和手机号为必填项，其他字段可选
  - 线索分配后状态自动变为"待跟进"

- **线索转化规则**：
  - 线索转化为客户时需确认客户不存在重复
  - 转化成功后，原线索状态变更为"已转化"
  - 转化后的客户自动继承线索的大部分信息
  - 转化时的字段映射关系如下：
    | 线索字段       | 客户字段       | 处理规则               |
    |----------------|----------------|------------------------|
    | name           | contactName    | 直接映射               |
    | mobile         | mobile         | 直接映射               |
    | company        | companyName    | 直接映射               |
    | position       | position       | 直接映射               |
    | industry       | industry       | 直接映射               |
    | remarks        | sourceRemarks  | 添加"来自线索"前缀     |

- **数据安全**：
  - 线索信息中的手机号、邮箱等敏感信息在API返回时需脱敏处理
  - 线索删除为逻辑删除，保留数据便于后期分析

- **权限控制**：
  - 普通销售仅能查看/编辑自己负责的线索
  - 销售主管可查看/分配团队所有线索
  - 管理员拥有全部权限

- **业务扩展点**：
  - 线索质量评分功能（未来）
  - 线索自动分配规则（未来）
  - 线索来源统计分析（已实现） 