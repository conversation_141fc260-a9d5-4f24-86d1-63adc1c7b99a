<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmVisitPlanReminderMapper">
    
    <resultMap type="CrmVisitPlanReminder" id="CrmVisitPlanReminderResult">
        <result property="id"    column="id"    />
        <result property="visitPlanId"    column="visit_plan_id"    />
        <result property="remindType"    column="remind_type"    />
        <result property="remindTime"    column="remind_time"    />
        <result property="remindStatus"    column="remind_status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="recipientId"    column="recipient_id"    />
        <result property="recipientName"    column="recipient_name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCrmVisitPlanReminderVo">
        select id, visit_plan_id, remind_type, remind_time, remind_status, send_time, 
               error_msg, recipient_id, recipient_name, create_time 
        from crm_visit_plan_reminders
    </sql>

    <select id="selectCrmVisitPlanReminderList" parameterType="CrmVisitPlanReminder" resultMap="CrmVisitPlanReminderResult">
        <include refid="selectCrmVisitPlanReminderVo"/>
        <where>
            <if test="visitPlanId != null "> and visit_plan_id = #{visitPlanId}</if>
            <if test="remindType != null  and remindType != ''"> and remind_type = #{remindType}</if>
            <if test="remindStatus != null  and remindStatus != ''"> and remind_status = #{remindStatus}</if>
            <if test="recipientId != null "> and recipient_id = #{recipientId}</if>
            <if test="recipientName != null  and recipientName != ''"> and recipient_name like concat('%', #{recipientName}, '%')</if>
        </where>
        order by remind_time desc
    </select>
    
    <select id="selectCrmVisitPlanReminderById" parameterType="Long" resultMap="CrmVisitPlanReminderResult">
        <include refid="selectCrmVisitPlanReminderVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByVisitPlanId" parameterType="Long" resultMap="CrmVisitPlanReminderResult">
        <include refid="selectCrmVisitPlanReminderVo"/>
        where visit_plan_id = #{visitPlanId}
        order by remind_time desc
    </select>
    
    <select id="selectPendingReminders" parameterType="java.util.Date" resultMap="CrmVisitPlanReminderResult">
        <include refid="selectCrmVisitPlanReminderVo"/>
        where remind_status = 'pending'
        and remind_time &lt;= #{remindTime}
        order by remind_time asc
    </select>
        
    <insert id="insertCrmVisitPlanReminder" parameterType="CrmVisitPlanReminder" useGeneratedKeys="true" keyProperty="id">
        insert into crm_visit_plan_reminders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitPlanId != null">visit_plan_id,</if>
            <if test="remindType != null and remindType != ''">remind_type,</if>
            <if test="remindTime != null">remind_time,</if>
            <if test="remindStatus != null and remindStatus != ''">remind_status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="recipientId != null">recipient_id,</if>
            <if test="recipientName != null and recipientName != ''">recipient_name,</if>
            <if test="createTime != null">create_time</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitPlanId != null">#{visitPlanId},</if>
            <if test="remindType != null and remindType != ''">#{remindType},</if>
            <if test="remindTime != null">#{remindTime},</if>
            <if test="remindStatus != null and remindStatus != ''">#{remindStatus},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="recipientId != null">#{recipientId},</if>
            <if test="recipientName != null and recipientName != ''">#{recipientName},</if>
            <if test="createTime != null">#{createTime}</if>
        </trim>
    </insert>
    
    <insert id="batchInsertCrmVisitPlanReminder" parameterType="java.util.List">
        insert into crm_visit_plan_reminders(visit_plan_id, remind_type, remind_time, remind_status, 
                                            send_time, error_msg, recipient_id, recipient_name, create_time)
        values
        <foreach collection="reminders" item="reminder" separator=",">
            (#{reminder.visitPlanId}, #{reminder.remindType}, #{reminder.remindTime}, #{reminder.remindStatus}, 
             #{reminder.sendTime}, #{reminder.errorMsg}, #{reminder.recipientId}, #{reminder.recipientName}, #{reminder.createTime})
        </foreach>
    </insert>

    <update id="updateCrmVisitPlanReminder" parameterType="CrmVisitPlanReminder">
        update crm_visit_plan_reminders
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitPlanId != null">visit_plan_id = #{visitPlanId},</if>
            <if test="remindType != null and remindType != ''">remind_type = #{remindType},</if>
            <if test="remindTime != null">remind_time = #{remindTime},</if>
            <if test="remindStatus != null and remindStatus != ''">remind_status = #{remindStatus},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="recipientId != null">recipient_id = #{recipientId},</if>
            <if test="recipientName != null and recipientName != ''">recipient_name = #{recipientName},</if>
        </trim>
        where id = #{id}
    </update>
    
    <update id="updateReminderStatus">
        update crm_visit_plan_reminders
        set remind_status = #{remindStatus},
            send_time = #{sendTime},
            error_msg = #{errorMsg}
        where id = #{id}
    </update>

    <delete id="deleteCrmVisitPlanReminderById" parameterType="Long">
        delete from crm_visit_plan_reminders where id = #{id}
    </delete>

    <delete id="deleteCrmVisitPlanReminderByIds" parameterType="String">
        delete from crm_visit_plan_reminders where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteByVisitPlanId" parameterType="Long">
        delete from crm_visit_plan_reminders where visit_plan_id = #{visitPlanId}
    </delete>
</mapper>
