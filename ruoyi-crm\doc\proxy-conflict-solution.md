# Spring代理冲突问题解决方案

## 🔍 **问题描述**

启动应用时出现以下错误：

```
The bean 'mapper' could not be injected because it is a JDK dynamic proxy

The bean is of type 'jdk.proxy2.$Proxy251' and implements:
        com.baomidou.mybatisplus.core.mapper.Mapper

Expected a bean of type 'com.ruoyi.common.mapper.CrmBusinessFollowUpRecordMapper' which implements:
```

## 📋 **问题分析**

### 🎯 **根本原因**

1. **代理方式冲突**：
   - Spring默认使用JDK动态代理（只能代理接口）
   - MyBatis-Plus期望使用CGLib代理（可以代理类）
   - 两种代理方式在某些情况下不兼容

2. **注入方式问题**：
   - `@Resource` 注解按名称注入，对代理类型要求更严格
   - `@Autowired` 注解按类型注入，对代理更宽容

3. **配置不一致**：
   - 项目中同时存在多种AOP配置
   - `@EnableCaching`、`@EnableAspectJAutoProxy` 等注解的代理策略不统一

### 🔧 **具体触发条件**

- 使用了 `@EnableCaching` 注解
- 使用了 `@EnableAspectJAutoProxy` 注解  
- 使用了 `@Resource` 注解注入Mapper
- MyBatis-Plus动态表名插件与Spring AOP产生冲突

## ✅ **解决方案**

### **方案一：统一代理策略（核心解决方案）**

#### 1. 修改缓存配置
```java
// ruoyi-framework/src/main/java/com/ruoyi/framework/config/RedisConfig.java
@Configuration
@EnableCaching(proxyTargetClass = true)  // 强制使用CGLib代理
public class RedisConfig extends CachingConfigurerSupport
```

#### 2. 修改AOP配置
```java
// ruoyi-framework/src/main/java/com/ruoyi/framework/config/ApplicationConfig.java
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)  // 强制使用CGLib代理
```

#### 3. 添加Spring Boot全局配置
```yaml
# ruoyi-crm/src/main/resources/application.yml
spring:
  aop:
    proxy-target-class: true  # 强制使用CGLib代理
```

#### 4. 创建专门的代理配置类
```java
// ruoyi-crm/src/main/java/com/ruoyi/crm/config/ProxyConfiguration.java
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class ProxyConfiguration {
    // 强制Spring使用CGLib代理
}
```

### **方案二：修改注入方式**

#### 1. 替换@Resource为@Autowired
```java
// 修改前
@Resource
private CrmBusinessFollowUpRecordMapper mapper;

// 修改后
@Autowired
private CrmBusinessFollowUpRecordMapper mapper;
```

#### 2. 统一注入注解
- ✅ 推荐使用 `@Autowired`（按类型注入，对代理更友好）
- ❌ 避免使用 `@Resource`（按名称注入，对代理类型敏感）

### **方案三：接口注入（备选方案）**

如果上述方案不行，可以注入接口而不是具体类：

```java
// 如果Mapper有父接口，注入父接口
@Autowired
private BaseMapper<CrmBusinessFollowUpRecord> mapper;
```

## 📊 **修改文件清单**

### ✅ **已修改的文件**

1. **`ruoyi-framework/src/main/java/com/ruoyi/framework/config/RedisConfig.java`**
   - 添加 `proxyTargetClass = true` 到 `@EnableCaching`

2. **`ruoyi-framework/src/main/java/com/ruoyi/framework/config/ApplicationConfig.java`**
   - 添加 `proxyTargetClass = true` 到 `@EnableAspectJAutoProxy`

3. **`ruoyi-crm/src/main/resources/application.yml`**
   - 添加 `spring.aop.proxy-target-class: true`

4. **`ruoyi-crm/src/main/java/com/ruoyi/crm/service/impl/CrmBusinessFollowUpRecordServiceImpl.java`**
   - 将 `@Resource` 替换为 `@Autowired`

5. **`ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmBusinessFollowUpRecordController.java`**
   - 将 `@Resource` 替换为 `@Autowired`

6. **`ruoyi-crm/src/main/java/com/ruoyi/crm/config/ProxyConfiguration.java`**
   - 新增代理配置类

## 🎯 **技术原理**

### **JDK动态代理 vs CGLib代理**

| 特性 | JDK动态代理 | CGLib代理 |
|------|-------------|-----------|
| **代理对象** | 只能代理接口 | 可以代理类和接口 |
| **性能** | 较快 | 稍慢（字节码生成） |
| **依赖** | JDK内置 | 需要CGLib库 |
| **继承关系** | 实现接口 | 继承目标类 |
| **final方法** | 不影响 | 无法代理 |

### **Spring代理选择策略**

```java
// Spring代理选择逻辑
if (config.isProxyTargetClass()) {
    // 强制使用CGLib代理
    return new CglibAopProxy(config);
} else if (targetClass.isInterface() || Proxy.isProxyClass(targetClass)) {
    // 使用JDK动态代理
    return new JdkDynamicAopProxy(config);
} else {
    // 使用CGLib代理
    return new CglibAopProxy(config);
}
```

### **@Resource vs @Autowired**

| 注解 | 注入方式 | 代理兼容性 | JSR标准 |
|------|----------|------------|---------|
| **@Resource** | 按名称注入 | 较差 | JSR-250 |
| **@Autowired** | 按类型注入 | 较好 | Spring专有 |

## 🚀 **验证方法**

### 1. **编译验证**
```bash
cd ruoyi-crm
mvn clean compile -DskipTests
```

### 2. **启动验证**
```bash
# 启动应用，观察是否有代理相关错误
java -jar ruoyi-admin.jar
```

### 3. **日志验证**
启动时查看日志中是否包含：
```
The following profiles are active: druid, sharding
```

### 4. **功能验证**
```bash
# 测试分表功能API
curl http://localhost:8080/crm/table-sharding/config
```

## 🔍 **常见问题排查**

### **问题1：仍然出现代理错误**

**解决方案**：
1. 检查是否有其他 `@EnableAspectJAutoProxy` 注解没有添加 `proxyTargetClass = true`
2. 确保所有 `@Resource` 都替换为 `@Autowired`
3. 清理target目录重新编译

### **问题2：某些AOP功能失效**

**解决方案**：
1. 确保 `exposeProxy = true` 已设置
2. 使用 `AopContext.currentProxy()` 获取代理对象
3. 检查切面配置是否正确

### **问题3：性能下降**

**解决方案**：
1. CGLib代理性能略低于JDK代理，但差异很小
2. 可以通过JVM参数优化：`-XX:+OptimizeStringConcat`
3. 生产环境建议进行性能测试

## 📝 **最佳实践**

### **1. 统一代理策略**
- 在项目中统一使用CGLib代理
- 避免混合使用JDK代理和CGLib代理

### **2. 统一注入注解**
- 优先使用 `@Autowired` 进行依赖注入
- 避免使用 `@Resource`，除非有特殊需求

### **3. 配置集中管理**
- 将代理相关配置集中在一个配置类中
- 避免在多个地方重复配置

### **4. 文档化配置**
- 在配置类中添加详细注释
- 说明为什么选择特定的代理策略

## 🎉 **解决结果**

✅ **编译成功**：应用可以正常编译  
✅ **启动成功**：代理冲突问题已解决  
✅ **功能正常**：分表功能和其他AOP功能正常工作  
✅ **配置统一**：项目中的代理策略已统一为CGLib代理  

---

## 总结

通过统一使用CGLib代理和替换注入注解，成功解决了Spring代理冲突问题。这个解决方案不仅修复了当前问题，还为项目建立了一致的代理策略，避免了未来可能出现的类似问题。 