<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadFollowerMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmLeadFollower" id="CrmLeadFollowerResult">
        <id     property="id"          column="id"           />
        <result property="leadId"      column="lead_id"      />
        <result property="followerId"  column="follower_id"  />
        <result property="status"      column="status"       />
        <result property="delFlag"     column="del_flag"     />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"  column="create_time"  />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"  column="update_time"  />
    </resultMap>

    <sql id="selectCrmLeadFollowerVo">
        select id, lead_id, follower_id, status, del_flag, create_by, create_time, update_by, update_time
        from crm_business_lead_followers
    </sql>

    <select id="selectCrmLeadFollowerList" parameterType="com.ruoyi.common.domain.entity.CrmLeadFollower" resultMap="CrmLeadFollowerResult">
        <include refid="selectCrmLeadFollowerVo"/>
        <where>
            <if test="leadId != null "> and lead_id = #{leadId}</if>
            <if test="followerId != null "> and follower_id = #{followerId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCrmLeadFollowerById" parameterType="Long" resultMap="CrmLeadFollowerResult">
        <include refid="selectCrmLeadFollowerVo"/>
        where id = #{id} and del_flag = '0'
    </select>
        
    <insert id="insertCrmLeadFollower" parameterType="com.ruoyi.common.domain.entity.CrmLeadFollower" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_lead_followers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">lead_id,</if>
            <if test="followerId != null">follower_id,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="followerId != null">#{followerId},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmLeadFollower" parameterType="com.ruoyi.common.domain.entity.CrmLeadFollower">
        update crm_business_lead_followers
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">lead_id = #{leadId},</if>
            <if test="followerId != null">follower_id = #{followerId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmLeadFollowerById" parameterType="Long">
        delete from crm_business_lead_followers where id = #{id}
    </delete>

    <delete id="deleteCrmLeadFollowerByIds" parameterType="String">
        delete from crm_business_lead_followers where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 