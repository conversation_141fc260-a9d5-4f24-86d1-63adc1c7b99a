// 客户实体接口（匹配后端 CrmCustomer 实体）
export interface Customer {
  id: number
  responsiblePersonId?: string
  customerName: string
  customerSource?: string
  mobile?: string
  phone?: string
  email?: string
  website?: string
  customerIndustry?: string
  customerLevel?: string
  customerAddress?: string
  primaryContact?: string
  dealStatus?: string
  nextContactTime?: string
  selectedDate?: string
  remarks?: string
  status?: string
  delFlag?: string
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
}

// 查询参数接口（匹配后端字段）
export interface CustomerQueryParams {
  pageNum?: number
  pageSize?: number
  customerName?: string
  mobile?: string
  phone?: string
  email?: string
  customerSource?: string
  customerIndustry?: string
  customerLevel?: string
  status?: string
  responsiblePersonId?: string
  startDate?: string
  endDate?: string
}

// 创建客户DTO（匹配后端字段）
export interface CreateCustomerDTO {
  customerName: string
  responsiblePersonId?: string
  customerSource?: string
  mobile?: string
  phone?: string
  email?: string
  website?: string
  customerIndustry?: string
  customerLevel?: string
  customerAddress?: string
  primaryContact?: string
  dealStatus?: string
  nextContactTime?: string
  selectedDate?: string
  remarks?: string
  status?: string
}

// 更新客户DTO
export type UpdateCustomerDTO = Partial<CreateCustomerDTO>

// 客户统计信息接口
export interface CustomerStatistics {
  totalCount: number
  activeCount: number
  inactiveCount: number
  pendingCount: number
  monthlyGrowth: number
  industryDistribution: Record<string, number>
  sourceDistribution: Record<string, number>
}

// 客户搜索参数（匹配后端 CustomerSearchDTO）
export interface CustomerSearchParams {
    keyword?: string;
    recent?: boolean;
    limit?: number;
    pageNum?: number;
    pageSize?: number;
}

// 客户数据（用于表格显示）
export interface CustomerData {
    id: number;
    customerName: string;
    mobile?: string;
    phone?: string;
    email?: string;
    website?: string;
    customerIndustry?: string;
    customerLevel?: string;
    customerSource?: string;
    customerAddress?: string;
    primaryContact?: string;
    dealStatus?: string;
    nextContactTime?: string;
    selectedDate?: string;
    remarks?: string;
    status?: string;
    responsiblePersonId?: string;
    createTime?: string;
    updateTime?: string;
    delFlag?: string;
    // 关注状态
    isFollowing?: boolean;
}

// 分配客户数据
export interface AssignCustomerData {
    customerId: number;
    assignToUserId: number;
    reason?: string;
}

// 批量分配客户数据
export interface BatchAssignCustomerData {
    customerIds: number[];
    assignToUserId: number;
    reason?: string;
}

// 用户信息（用于分配选择）
export interface UserOption {
    userId: number;
    userName: string;
    nickName: string;
    deptName?: string;
}

// 跟进记录数据
export interface FollowupRecord {
    id?: number;
    customerId: number;
    userId?: number;
    followupType: string;
    followupContent: string;
    followupResult?: string;
    nextFollowupTime?: string;
    attachmentUrls?: string;
    isImportant?: number;
    createTime?: string;
    updateTime?: string;
    // 关联信息
    customerName?: string;
    userName?: string;
    userNickName?: string;
}

// 创建跟进记录DTO
export interface CreateFollowupRecordDTO {
    customerId: number;
    followupType: string;
    followupContent: string;
    followupResult?: string;
    nextFollowupTime?: string;
    isImportant?: boolean;
}

// 跟进记录查询参数
export interface FollowupRecordQueryParams {
    customerId?: number;
    userId?: number;
    followupType?: string;
    followupResult?: string;
    isImportant?: number;
    pageNum?: number;
    pageSize?: number;
    beginTime?: string;
    endTime?: string;
}

// 操作日志数据
export interface OperationLog {
    id?: number;
    customerId?: number;
    businessType: string;
    operationType: string;
    operationContent: string;
    operationDetails?: string;
    operatorId?: number;
    operatorName?: string;
    operationTime?: string;
    extraData?: string;
    createTime?: string;
    updateTime?: string;
}

// 操作日志查询参数
export interface OperationLogQueryParams {
    customerId?: number;
    businessType?: string;
    operationType?: string;
    operatorId?: number;
    operatorName?: string;
    pageNum?: number;
    pageSize?: number;
    beginTime?: string;
    endTime?: string;
}

// 操作类型统计
export interface OperationTypeStatistics {
    [operationType: string]: number;
}
