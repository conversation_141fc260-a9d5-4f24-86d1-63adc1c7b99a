import request from '@/utils/request';

interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

// 线索池查询参数
export interface LeadPoolQuery {
    pageNum: number;
    pageSize: number;
    poolStatus?: string;
    qualityLevel?: string;
    region?: string;
    industry?: string;
    sourceType?: string;
    [key: string]: any;
}

// 线索池实体
export interface LeadPool {
    id?: number;
    leadId?: number;
    poolStatus: string;
    qualityLevel: string;
    priority: number;
    sourceType: string;
    enterPoolTime: string;
    lastAssignTime?: string;
    assignCount: number;
    region?: string;
    industry?: string;
    estimatedValue?: number;
    remarks?: string;
    // 扩展字段
    leadName?: string;
    customerName?: string;
    mobile?: string;
    email?: string;
    leadSource?: string;
}

// 分配请求参数
export interface AssignLeadsRequest {
    poolIds: number[];
    toUserId: number;
    reason: string;
}

// 批量分配请求参数
export interface BatchAssignLeadsRequest {
    poolIds: number[];
    userIds: number[];
    reason: string;
}

// 抢单请求参数
export interface GrabLeadRequest {
    userId: number;
    reason: string;
}

// 回收请求参数
export interface RecycleLeadsRequest {
    leadIds: number[];
    reason: string;
}

// 添加到池请求参数
export interface AddToPoolRequest {
    leadId?: number;
    qualityLevel: string;
    priority: number;
    region?: string;
    industry?: string;
    estimatedValue?: string;
    remarks?: string;
}

// 查询线索池列表
export function listLeadPool(query: LeadPoolQuery): Promise<ApiResponse<LeadPool>> {
    return request({
        url: '/front/crm/leadPool/list',
        method: 'get',
        params: query
    });
}

// 查询可用的线索池列表
export function listAvailableLeadPool(query: LeadPoolQuery): Promise<ApiResponse<LeadPool>> {
    return request({
        url: '/front/crm/leadPool/available',
        method: 'get',
        params: query
    });
}

// 查询线索池详细
export function getLeadPool(id: number): Promise<ApiResponse<LeadPool>> {
    return request({
        url: `/front/crm/leadPool/${id}`,
        method: 'get'
    });
}

// 新增线索池
export function addLeadPool(data: LeadPool): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool',
        method: 'post',
        data: data
    });
}

// 修改线索池
export function updateLeadPool(data: LeadPool): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool',
        method: 'put',
        data: data
    });
}

// 删除线索池
export function deleteLeadPool(ids: number | number[]): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/leadPool/${typeof ids === 'number' ? ids : ids.join(',')}`,
        method: 'delete'
    });
}

// 手动分配线索
export function assignLeads(data: AssignLeadsRequest): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/assign',
        method: 'post',
        data: data
    });
}

// 批量分配线索
export function batchAssignLeads(data: BatchAssignLeadsRequest): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/batchAssign',
        method: 'post',
        data: data
    });
}

// 抢单
export function grabLead(poolId: number, data: GrabLeadRequest): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/leadPool/grab/${poolId}`,
        method: 'post',
        data: data
    });
}

// 回收线索到池中
export function recycleLeads(data: RecycleLeadsRequest): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/recycle',
        method: 'post',
        data: data
    });
}

// 添加线索到池中
export function addToPool(data: AddToPoolRequest): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/addToPool',
        method: 'post',
        data: data
    });
}

// 获取线索池统计信息
export function getLeadPoolStats(): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/stats',
        method: 'get'
    });
}

// 根据质量等级获取线索池列表
export function getLeadPoolByQualityLevel(qualityLevel: string): Promise<ApiResponse<LeadPool>> {
    return request({
        url: `/front/crm/leadPool/quality/${qualityLevel}`,
        method: 'get'
    });
}

// 根据地区获取线索池列表
export function getLeadPoolByRegion(region: string): Promise<ApiResponse<LeadPool>> {
    return request({
        url: `/front/crm/leadPool/region/${region}`,
        method: 'get'
    });
}

// 根据行业获取线索池列表
export function getLeadPoolByIndustry(industry: string): Promise<ApiResponse<LeadPool>> {
    return request({
        url: `/front/crm/leadPool/industry/${industry}`,
        method: 'get'
    });
}

// 检查线索是否在池中
export function checkLeadInPool(leadId: number): Promise<ApiResponse<boolean>> {
    return request({
        url: `/front/crm/leadPool/check/${leadId}`,
        method: 'get'
    });
}

// 导出线索池列表
export function exportLeadPool(query: LeadPoolQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leadPool/export',
        method: 'post',
        params: query
    });
}
