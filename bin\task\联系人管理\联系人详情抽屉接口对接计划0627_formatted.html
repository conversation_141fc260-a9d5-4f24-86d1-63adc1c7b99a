<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人详情抽屉接口对接计划</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-clike.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-typescript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-java.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-sql.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        .status-completed {
            color: #27ae60;
            font-weight: bold;
        }
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        .priority-high {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-medium {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-low {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        /* 改进的代码块样式 */
        .code-container {
            position: relative;
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-header {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px 15px;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #4a5568;
        }
        
        .code-block {
            background: #1a202c !important;
            color: #e2e8f0 !important;
            padding: 20px !important;
            margin: 0 !important;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-word;
            border: none !important;
        }
        
        /* Prism.js 自定义样式覆盖 */
        pre[class*="language-"] {
            background: #1a202c !important;
            color: #e2e8f0 !important;
            text-shadow: none !important;
            margin: 0 !important;
            padding: 20px !important;
            overflow-x: auto;
            border-radius: 0 !important;
        }
        
        code[class*="language-"] {
            background: transparent !important;
            color: inherit !important;
            text-shadow: none !important;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
        }
        
        /* 语法高亮颜色 */
        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {
            color: #718096 !important;
        }
        
        .token.punctuation {
            color: #e2e8f0 !important;
        }
        
        .token.property,
        .token.tag,
        .token.boolean,
        .token.number,
        .token.constant,
        .token.symbol,
        .token.deleted {
            color: #f56565 !important;
        }
        
        .token.selector,
        .token.attr-name,
        .token.string,
        .token.char,
        .token.builtin,
        .token.inserted {
            color: #68d391 !important;
        }
        
        .token.operator,
        .token.entity,
        .token.url,
        .language-css .token.string,
        .style .token.string {
            color: #4fd1c7 !important;
        }
        
        .token.atrule,
        .token.attr-value,
        .token.keyword {
            color: #9f7aea !important;
        }
        
        .token.function,
        .token.class-name {
            color: #fbb6ce !important;
        }
        
        .token.regex,
        .token.important,
        .token.variable {
            color: #ed8936 !important;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .risk-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .phase-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #4a5568;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        
        .copy-button:hover {
            opacity: 1;
            background: #2d3748;
        }
        
        .code-container:hover .copy-button {
            opacity: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            .code-block {
                font-size: 12px !important;
                padding: 15px !important;
            }
            table {
                font-size: 12px;
            }
            th, td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <script>
        // 复制代码功能
        function copyCode(button) {
            const codeBlock = button.parentElement.querySelector('.code-block');
            const text = codeBlock.textContent || codeBlock.innerText;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    const originalText = button.textContent;
                    button.textContent = '已复制';
                    button.style.background = '#27ae60';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.background = '#4a5568';
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text, button);
                });
            } else {
                fallbackCopyTextToClipboard(text, button);
            }
        }
        
        // 降级复制方案
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    const originalText = button.textContent;
                    button.textContent = '已复制';
                    button.style.background = '#27ae60';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.background = '#4a5568';
                    }, 2000);
                }
            } catch (err) {
                console.error('降级复制也失败了:', err);
                button.textContent = '复制失败';
                button.style.background = '#e74c3c';
                setTimeout(() => {
                    button.textContent = '复制';
                    button.style.background = '#4a5568';
                }, 2000);
            }
            
            document.body.removeChild(textArea);
        }
        
        // 代码格式化功能
        function formatCode() {
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                const code = block.textContent || block.innerText;
                const language = getLanguageFromClass(block.className);
                
                try {
                    let formattedCode = '';
                    switch(language) {
                        case 'typescript':
                        case 'javascript':
                            formattedCode = formatJavaScript(code);
                            break;
                        case 'sql':
                            formattedCode = formatSQL(code);
                            break;
                        case 'java':
                            formattedCode = formatJava(code);
                            break;
                        default:
                            formattedCode = code;
                    }
                    
                    if (formattedCode !== code) {
                        block.textContent = formattedCode;
                        // 重新应用语法高亮
                        if (window.Prism) {
                            Prism.highlightElement(block);
                        }
                    }
                } catch (e) {
                    console.warn('代码格式化失败:', e);
                }
            });
        }
        
        function getLanguageFromClass(className) {
            const match = className.match(/language-(\w+)/);
            return match ? match[1] : 'text';
        }
        
        function formatJavaScript(code) {
            // 简单的JavaScript格式化
            return code
                .replace(/\s*{\s*/g, ' {\n    ')
                .replace(/;\s*}/g, ';\n}')
                .replace(/,\s*(?=[a-zA-Z])/g, ',\n        ')
                .replace(/^\s+/gm, match => '    '.repeat(Math.floor(match.length / 4)))
                .trim();
        }
        
        function formatSQL(code) {
            // 简单的SQL格式化
            return code
                .replace(/\bSELECT\b/gi, 'SELECT')
                .replace(/\bFROM\b/gi, '\nFROM')
                .replace(/\bWHERE\b/gi, '\nWHERE')
                .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN')
                .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
                .replace(/\bORDER BY\b/gi, '\nORDER BY')
                .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
                .replace(/^\s+/gm, '    ')
                .trim();
        }
        
        function formatJava(code) {
            // 简单的Java格式化
            return code
                .replace(/\s*{\s*/g, ' {\n    ')
                .replace(/;\s*}/g, ';\n}')
                .replace(/^\s+/gm, match => '    '.repeat(Math.floor(match.length / 4)))
                .trim();
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 添加格式化按钮到页面顶部
            const container = document.querySelector('.container');
            if (container) {
                const formatButton = document.createElement('button');
                formatButton.textContent = '🎨 格式化所有代码';
                formatButton.style.cssText = `
                    background: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-bottom: 20px;
                    font-size: 14px;
                    transition: background 0.3s;
                `;
                formatButton.addEventListener('click', formatCode);
                formatButton.addEventListener('mouseover', () => {
                    formatButton.style.background = '#2980b9';
                });
                formatButton.addEventListener('mouseout', () => {
                    formatButton.style.background = '#3498db';
                });
                
                container.insertBefore(formatButton, container.firstChild);
            }
            
            // 自动应用语法高亮
            if (window.Prism) {
                Prism.highlightAll();
            }
        });
    </script>
    
    <div class="container">
        <h1>联系人详情抽屉接口对接计划</h1>
        
        <h2>项目背景与目标</h2>
        <p>当前联系人管理页面的详情抽屉功能已基本搭建完成，包含了HeaderTab、DetailsTab、ActivityTab、AttachmentsTab、OperationsTab等组件，但在打开抽屉时只调用了基础的获取联系人详情接口<code>getContact(id)</code>，缺少各个Tab页面所需的数据接口对接，导致抽屉内容展示不完整。</p>
        
        <div class="success-box">
            <h3>核心目标</h3>
            <ol>
                <li><strong>完善数据加载：</strong>实现抽屉打开时的并行数据加载</li>
                <li><strong>Tab功能完整：</strong>确保每个Tab页面都有完整的数据支撑</li>
                <li><strong>用户体验优化：</strong>提升加载速度和交互流畅度</li>
                <li><strong>数据同步：</strong>抽屉内操作与主列表数据保持同步</li>
                <li><strong>懒加载：</strong>抽屉内Tab组件按需加载，避免一次性加载过多数据</li>
            </ol>
        </div>
        
        <h2>现状分析</h2>
        
        <h3>✅ 已完成功能</h3>
        <ul>
            <li><span class="status-completed">✅</span> 联系人详情抽屉基础框架</li>
            <li><span class="status-completed">✅</span> 获取联系人基础信息接口 <code>getContact(id)</code></li>
            <li><span class="status-completed">✅</span> 联系人关注相关接口 <code>followContact, unfollowContact, getFollowStatus</code></li>
            <li><span class="status-completed">✅</span> 联系人增删改接口 <code>addContact, updateContact, deleteContact</code></li>
            <li><span class="status-completed">✅</span> ContactHeaderTab、ContactDetailsTab等Tab组件结构</li>
            <li><span class="status-completed">✅</span> 批量关注/取消关注接口</li>
        </ul>
        
        <h3>❌ 缺少的关键接口对接</h3>
        <ul>
            <li><span class="status-pending">❌</span> <strong>客户详情信息获取</strong>（用于显示所属客户完整信息）</li>
            <li><span class="status-pending">❌</span> <strong>联系人活动记录获取</strong>（跟进记录、通话记录等）</li>
            <li><span class="status-pending">❌</span> <strong>联系人附件列表获取</strong>（文件管理功能）</li>
            <li><span class="status-pending">❌</span> <strong>联系人操作日志获取</strong>（操作历史记录）</li>
            <li><span class="status-pending">❌</span> <strong>用户列表获取</strong>（用于负责人选择下拉框）</li>
            <li><span class="status-pending">❌</span> <strong>抽屉打开时的关注状态获取</strong>（实时关注状态）</li>
        </ul>
        
        <h2>数据流程分析</h2>
        
        <div class="mermaid">
flowchart TD
    A[用户点击联系人] --> B[openDrawer函数]
    B --> C{当前实现}
    C --> D[只调用getContact接口]
    D --> E[打开抽屉]
    E --> F[Tab内容为空]
    
    B --> G{改进后实现}
    G --> H[并行加载多个接口]
    H --> I[getContact - 基础信息]
    H --> J[getFollowStatus - 关注状态]
    H --> K[getCustomerInfo - 客户信息]
    H --> L[getContactActivities - 活动记录]
    
    I --> M[数据整合]
    J --> M
    K --> M
    L --> M
    
    M --> N[打开抽屉]
    N --> O[Tab内容完整展示]
        </div>
        
        <h2>数据库表结构关系分析（基于实际结构）</h2>
        
        <h3>联系人相关核心表结构</h3>
        <p>基于crm41数据库的实际表结构，联系人活动记录涉及以下核心表：</p>
        
        <table>
            <thead>
                <tr>
                    <th>表名</th>
                    <th>中文名称</th>
                    <th>主要字段</th>
                    <th>关联关系</th>
                    <th>用途</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>crm_business_contacts</code></td>
                    <td>联系人表</td>
                    <td>id, name, phone, email, position, company</td>
                    <td>主表</td>
                    <td>联系人基础信息</td>
                </tr>
                <tr>
                    <td><code>crm_business_customers</code></td>
                    <td>客户表</td>
                    <td>id, customer_name, industry, company_size, address</td>
                    <td>主表</td>
                    <td>客户基础信息</td>
                </tr>
                <tr>
                    <td><code>crm_customer_contact_relations</code></td>
                    <td>客户联系人关联表</td>
                    <td>id, customer_id, contact_id, relation_type, is_primary</td>
                    <td>customer_id → crm_business_customers.id<br>contact_id → crm_business_contacts.id</td>
                    <td>客户与联系人多对多关系</td>
                </tr>
                <tr>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td>跟进记录表（需要改进）</td>
                    <td>id, module_type, follow_up_content, related_contact_id, related_customer_id</td>
                    <td>related_contact_id → crm_business_contacts.id</td>
                    <td>通用跟进记录（不推荐）</td>
                </tr>
                <tr>
                    <td><code>sys_user</code></td>
                    <td>系统用户表</td>
                    <td>user_id, user_name, nick_name, email, phone</td>
                    <td>用户相关联系人</td>
                    <td>系统用户信息</td>
                </tr>
            </tbody>
        </table>
        
        <h3>修正后的表关系图</h3>
        <div class="mermaid">
erDiagram
    crm_business_customers ||--o{ crm_customer_contact_relations : "一对多"
    crm_business_contacts ||--o{ crm_customer_contact_relations : "一对多"
    crm_business_contacts ||--o{ crm_contact_followup_records : "一对多"
    crm_business_customers ||--o{ crm_customer_followup_records : "一对多"
    sys_user ||--o{ crm_contact_followup_records : "创建人"
    sys_user ||--o{ crm_customer_followup_records : "创建人"
    
    crm_business_contacts {
        int id PK
        varchar name
        varchar phone
        varchar email
        varchar position
        varchar company
        timestamp created_at
        varchar creator_id
    }
    
    crm_business_customers {
        int id PK
        varchar customer_name
        varchar industry
        varchar company_size
        varchar address
        varchar phone
        varchar email
        timestamp created_at
    }
    
    crm_customer_contact_relations {
        int id PK
        int customer_id FK
        int contact_id FK
        varchar relation_type
        tinyint is_primary
        varchar status
        timestamp create_time
        varchar create_by
    }
    
    crm_contact_followup_records {
        int id PK
        int contact_id FK
        text content
        timestamp followup_time
        timestamp next_followup_time
        varchar followup_type
        varchar create_by
        timestamp create_time
    }
    
    crm_customer_followup_records {
        int id PK
        int customer_id FK
        text content
        timestamp followup_time
        varchar visit_type
        text result
        varchar create_by
        timestamp create_time
    }
    
    sys_user {
        bigint user_id PK
        varchar user_name
        varchar nick_name
        varchar email
        varchar phonenumber
    }
        </div>
        
        <h3>基于实际表结构的SQL查询示例</h3>
        
        <div class="code-container">
            <div class="code-header">SQL 查询示例</div>
            <button class="copy-button" onclick="copyCode(this)">复制</button>
            <pre class="code-block language-sql">-- 获取联系人完整信息（通过关联表获取客户信息）
SELECT 
    c.*,
    GROUP_CONCAT(DISTINCT cu.customer_name) AS customer_names,
    GROUP_CONCAT(DISTINCT CASE 
        WHEN r.is_primary = 1 
        THEN cu.customer_name 
    END) AS primary_customer_name,
    su.nick_name AS creator_name
FROM crm_business_contacts c
LEFT JOIN crm_customer_contact_relations r 
    ON c.id = r.contact_id 
    AND r.del_flag = '0' 
    AND r.status = 'active'
LEFT JOIN crm_business_customers cu 
    ON r.customer_id = cu.id
LEFT JOIN sys_user su 
    ON c.creator_id = su.user_name
WHERE c.id = ?;

-- 获取联系人的所有关联客户信息
SELECT 
    cu.*,
    r.relation_type,
    r.is_primary,
    r.start_date,
    r.end_date,
    r.status,
    r.remarks
FROM crm_customer_contact_relations r
INNER JOIN crm_business_customers cu 
    ON r.customer_id = cu.id
WHERE r.contact_id = ? 
    AND r.del_flag = '0'
ORDER BY r.is_primary DESC, r.create_time DESC;

-- 获取联系人跟进记录（基于现有通用表，需要过滤）
SELECT 
    f.*,
    su.nick_name AS creator_name,
    c.name AS contact_name
FROM crm_business_follow_up_records f
LEFT JOIN sys_user su 
    ON f.creator_id = su.user_name
LEFT JOIN crm_business_contacts c 
    ON f.related_contact_id = c.id
WHERE f.related_contact_id = ? 
    AND f.module_type = 'contact'
ORDER BY f.created_at DESC;</pre>
        </div>
        
        <h2>接口对接详细计划</h2>
        
        <div class="phase-box">
            <h3>第一阶段：核心数据接口对接 <span class="priority-high">优先级：高</span></h3>
            
            <h4>1.1 抽屉打开时数据加载优化</h4>
            <p><strong>问题描述：</strong>当前openDrawer函数只调用了getContact接口，缺少其他必要数据的并行加载。</p>
            
            <p><strong>当前实现：</strong></p>
            <div class="code-container">
                <div class="code-header">ContactManagement/index.vue - 当前实现</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// ContactManagement/index.vue 当前实现
const openDrawer = async (row: ContactEntity): Promise<void> => {
    try {
        const response = await getContact(row.id);
        
        if (response.code === 200 && response.data) {
            currentContact.value = {
                id: response.data.id,
                name: response.data.name,
                position: response.data.position,
                phone: response.data.phone,
                email: response.data.email,
                customerId: response.data.customerId,
                responsiblePersonId: response.data.responsiblePersonId,
                createTime: response.data.createdAt,
                delFlag: response.data.delFlag || '0'
            };
            drawerVisible.value = true;
        }
    } catch (error) {
        console.error('获取联系人详情失败:', error);
    }
};</pre>
            </div>
            
            <p><strong>改进方案：</strong>使用新的联系人详情接口，一次性获取完整信息</p>
            <div class="code-container">
                <div class="code-header">ContactManagement/index.vue - 改进后实现</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// 改进后的实现（基于实际数据库结构）
const openDrawer = async (row: ContactEntity): Promise<void> => {
    try {
        loading.value = true;
        
        // 使用增强的联系人详情接口，一次性获取完整信息
        const response = await getContactDetail(row.id);
        
        if (response.code === 200 && response.data) {
            const data = response.data;
            
            currentContact.value = {
                // 基础联系人信息
                id: data.contact.id,
                name: data.contact.name,
                phone: data.contact.phone,
                email: data.contact.email,
                customerId: data.contact.customerId,
                responsiblePersonId: data.contact.responsiblePersonId,
                createTime: data.contact.createTime,
                createBy: data.contact.createBy,
                delFlag: data.contact.delFlag || '0',
                
                // 扩展信息
                customerInfo: data.customer || null,
                customerName: data.customer?.name || '',
                responsiblePersonInfo: data.responsiblePerson || null,
                responsiblePersonName: data.responsiblePerson?.nickName || '',
                isFollowing: data.isFollowing || false,
                activityStats: data.activityStats || {
                    followupCount: 0,
                    lastFollowupTime: null,
                    nextFollowupTime: null
                }
            };
            
            // 为Tab页面提供统计信息
            tabStats.value = {
                activityCount: data.activityStats?.followupCount || 0,
                attachmentCount: 0, // 如果有附件统计的话
                operationCount: 0   // 如果有操作日志统计的话
            };
            
            drawerVisible.value = true;
        } else {
            ElMessage.error(response.msg || '获取联系人详情失败');
        }
    } catch (error) {
        console.error('获取联系人详情失败:', error);
        ElMessage.error('获取联系人详情失败');
    } finally {
        loading.value = false;
    }
};

// 如果需要获取用户列表（用于负责人选择下拉框）
const loadUserOptions = async (): Promise<void> => {
    try {
        if (userOptions.value.length === 0) {
            const response = await getUserList({ 
                pageNum: 1, 
                pageSize: 1000 
            });
            
            if (response.code === 200) {
                userOptions.value = response.data.rows || [];
            }
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
    }
};

// 在组件初始化时加载用户选项
onMounted(() => {
    loadUserOptions();
});</pre>
            </div>
            
            <h4>1.2 需要新增的API接口</h4>
            <table>
                <thead>
                    <tr>
                        <th>接口名称</th>
                        <th>路径</th>
                        <th>方法</th>
                        <th>用途</th>
                        <th>调用时机</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>获取客户信息</td>
                        <td>/front/crm/customers/{id}</td>
                        <td>GET</td>
                        <td>获取联系人所属客户的详细信息</td>
                        <td>抽屉打开时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取联系人活动记录</td>
                        <td>/front/crm/contacts/{id}/activities</td>
                        <td>GET</td>
                        <td>获取联系人的跟进记录、通话记录等</td>
                        <td>ActivityTab激活时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>创建联系人活动记录</td>
                        <td>/front/crm/contacts/activities</td>
                        <td>POST</td>
                        <td>新增跟进记录</td>
                        <td>用户添加记录时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取联系人附件</td>
                        <td>/front/crm/contacts/{id}/attachments</td>
                        <td>GET</td>
                        <td>获取联系人相关的文件附件</td>
                        <td>AttachmentsTab激活时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>上传联系人附件</td>
                        <td>/front/crm/contacts/attachments/upload</td>
                        <td>POST</td>
                        <td>上传文件到联系人</td>
                        <td>用户上传文件时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>删除联系人附件</td>
                        <td>/front/crm/contacts/attachments/{id}</td>
                        <td>DELETE</td>
                        <td>删除指定附件</td>
                        <td>用户删除文件时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取操作日志</td>
                        <td>/front/crm/contacts/{id}/operations</td>
                        <td>GET</td>
                        <td>获取联系人的操作历史记录</td>
                        <td>OperationsTab激活时</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取用户列表</td>
                        <td>/front/system/user/list</td>
                        <td>GET</td>
                        <td>获取可选择的负责人列表</td>
                        <td>HeaderTab加载时</td>
                        <td><span class="status-completed">可能已存在</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：Tab组件数据集成 <span class="priority-high">优先级：高</span></h3>
            
            <h4>2.1 ContactHeaderTab数据集成</h4>
            <p><strong>需要对接的功能：</strong></p>
            <ul>
                <li>关注状态实时显示和切换</li>
                <li>负责人下拉选择（需要用户列表接口）</li>
                <li>所属客户信息展示（需要客户详情接口）</li>
                <li>字段内联编辑保存（需要更新接口）</li>
            </ul>
            
            <div class="code-container">
                <div class="code-header">ContactHeaderTab.vue - 数据加载方法</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// ContactHeaderTab.vue 需要新增的数据加载方法
const loadRelatedData = async () => {
    try {
        loading.value = true;
        
        // 如果有客户ID，加载客户信息
        if (props.entityData.customerId) {
            const customerResponse = await getCustomerInfo(props.entityData.customerId);
            if (customerResponse.code === 200) {
                customerInfo.value = customerResponse.data;
                // 更新显示的客户名称
                emit('update:entity', {
                    ...props.entityData,
                    customerName: customerResponse.data.name
                });
            }
        }
        
        // 加载关注状态（如果主页面没有传递）
        if (props.entityData.isFollowing === undefined) {
            const followResponse = await getFollowStatus(props.entityData.id);
            if (followResponse.code === 200) {
                emit('update:entity', {
                    ...props.entityData,
                    isFollowing: followResponse.data
                });
            }
        }
    } catch (error) {
        console.error('加载相关数据失败:', error);
    } finally {
        loading.value = false;
    }
};

// 处理关注状态切换
const handleToggleFollow = async () => {
    try {
        const isCurrentlyFollowing = props.entityData.isFollowing;
        
        if (isCurrentlyFollowing) {
            await unfollowContact(props.entityData.id);
            ElMessage.success('取消关注成功');
        } else {
            await followContact(props.entityData.id);
            ElMessage.success('关注成功');
        }
        
        // 更新状态
        emit('update:entity', {
            ...props.entityData,
            isFollowing: !isCurrentlyFollowing
        });
        
        // 通知主页面刷新列表
        emit('refresh-list');
        
    } catch (error) {
        console.error('切换关注状态失败:', error);
        ElMessage.error('操作失败');
    }
};</pre>
            </div>
            
            <h4>2.2 ContactActivityTab数据集成</h4>
            <div class="code-container">
                <div class="code-header">ContactActivityTab.vue - 数据加载和操作方法</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// ContactActivityTab.vue 数据加载和操作方法（基于实际数据库结构）
const loadActivities = async () => {
    try {
        loading.value = true;
        const response = await getContactActivities(props.contactId, {
            pageNum: pagination.currentPage,
            pageSize: pagination.pageSize,
            startTime: dateRange.value?.[0],
            endTime: dateRange.value?.[1]
        });
        
        if (response.code === 200) {
            activities.value = response.data.rows || [];
            pagination.total = response.data.total || 0;
            
            // 更新活动统计信息
            emit('update-stats', { activityCount: pagination.total });
        } else {
            ElMessage.error(response.msg || '加载跟进记录失败');
        }
    } catch (error) {
        console.error('加载跟进记录失败:', error);
        ElMessage.error('加载跟进记录失败');
    } finally {
        loading.value = false;
    }
};

const addActivity = async (activityData) => {
    try {
        const response = await createContactActivity({
            contactId: props.contactId,
            content: activityData.content,
            followupTime: activityData.followupTime,
            nextFollowupTime: activityData.nextFollowupTime
        });
        
        if (response.code === 200) {
            ElMessage.success('添加跟进记录成功');
            // 重新加载列表
            await loadActivities();
            // 关闭新增对话框
            showAddDialog.value = false;
            // 通知父组件刷新统计
            emit('refresh-stats');
        } else {
            ElMessage.error(response.msg || '添加跟进记录失败');
        }
    } catch (error) {
        console.error('添加跟进记录失败:', error);
        ElMessage.error('添加跟进记录失败');
    }
};

// 格式化跟进时间显示
const formatFollowupTime = (time: string) => {
    if (!time) return '-';
    return dayjs(time).format('YYYY-MM-DD HH:mm');
};

// 计算下次跟进的紧急程度
const getFollowupUrgency = (nextTime: string) => {
    if (!nextTime) return '';
    
    const now = dayjs();
    const next = dayjs(nextTime);
    const diffDays = next.diff(now, 'day');
    
    if (diffDays < 0) return 'overdue'; // 已过期
    if (diffDays === 0) return 'today'; // 今天
    if (diffDays <= 3) return 'urgent'; // 紧急
    return 'normal'; // 正常
};</pre>
            </div>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：API接口定义和实现 <span class="priority-medium">优先级：中</span></h3>
            
            <h4>3.1 后端Controller实现</h4>
            <div class="code-container">
                <div class="code-header">CrmContactFollowupController.java - 跟进记录接口</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-java">// CrmContactFollowupController.java (基于现有表)
@RestController
@RequestMapping("/front/crm/contacts")
public class CrmContactFollowupController {
    
    @Autowired
    private ICrmBusinessFollowUpRecordsService followupService;
    
    @Autowired
    private ICrmBusinessContactsService contactService;
    
    /**
     * 获取联系人跟进记录列表
     */
    @GetMapping("/{contactId}/activities")
    public ResponseResult getContactActivities(
            @PathVariable Long contactId, 
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // 验证联系人是否存在
            CrmBusinessContacts contact = 
                contactService.selectCrmBusinessContactsById(contactId);
            if (contact == null) {
                return ResponseResult.error("联系人不存在");
            }
            
            // 构建查询条件
            CrmBusinessFollowUpRecords queryParams = new CrmBusinessFollowUpRecords();
            queryParams.setModuleType("contact");
            queryParams.setRelatedContactId(contactId);
            
            // 查询跟进记录
            PageHelper.startPage(pageNum, pageSize);
            List<CrmBusinessFollowUpRecords> followups = 
                followupService.selectCrmBusinessFollowUpRecordsList(queryParams);
            PageInfo<CrmBusinessFollowUpRecords> pageInfo = new PageInfo<>(followups);
            
            return ResponseResult.success(pageInfo);
        } catch (Exception e) {
            log.error("获取联系人跟进记录失败", e);
            return ResponseResult.error("获取跟进记录失败");
        }
    }
    
    /**
     * 创建联系人跟进记录
     */
    @PostMapping("/activities")
    public ResponseResult createContactActivity(
            @RequestBody CrmBusinessFollowUpRecords followup) {
        try {
            // 设置模块类型为联系人
            followup.setModuleType("contact");
            followup.setCreatorId(SecurityUtils.getUsername());
            followup.setCreatedAt(DateUtils.getNowDate());
            
            // 验证联系人是否存在
            if (followup.getRelatedContactId() == null) {
                return ResponseResult.error("联系人ID不能为空");
            }
            
            CrmBusinessContacts contact = 
                contactService.selectCrmBusinessContactsById(followup.getRelatedContactId());
            if (contact == null) {
                return ResponseResult.error("联系人不存在");
            }
            
            int result = followupService.insertCrmBusinessFollowUpRecords(followup);
            return result > 0 ? 
                ResponseResult.success("创建成功") : 
                ResponseResult.error("创建失败");
        } catch (Exception e) {
            log.error("创建跟进记录失败", e);
            return ResponseResult.error("创建跟进记录失败");
        }
    }</pre>
            if (contact == null) {
                return ResponseResult.error("联系人不存在");
            }
            
            int result = followupService.insertCrmBusinessFollowUpRecords(followup);
            return result > 0 ? ResponseResult.success("创建成功") : ResponseResult.error("创建失败");
        } catch (Exception e) {
            log.error("创建跟进记录失败", e);
            return ResponseResult.error("创建跟进记录失败");
        }
    }
    
    /**
     * 获取联系人活动统计
     */
    @GetMapping("/{contactId}/activities/stats")
    public ResponseResult getContactActivityStats(@PathVariable Long contactId) {
        try {
            Map<String, Object> stats = followupService.getContactActivityStats(contactId);
            return ResponseResult.success(stats);
        } catch (Exception e) {
            log.error("获取活动统计失败", e);
            return ResponseResult.error("获取活动统计失败");
        }
    }
}</pre>
            </div>
            
            <h4>3.2 前端API接口定义</h4>
            <div class="code-container">
                <div class="code-header">api/contact.ts - 前端接口定义</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// 获取联系人详细信息（包含客户、负责人、关注状态等）
export function getContactDetail(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/detail/${contactId}`,
        method: 'get'
    });
}

// 获取联系人跟进记录
export function getContactActivities(contactId: number, params?: any): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities`,
        method: 'get',
        params
    });
}

// 创建联系人跟进记录
export function createContactActivity(data: any): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts/activities',
        method: 'post',
        data
    });
}

// 获取联系人活动统计
export function getContactActivityStats(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities/stats`,
        method: 'get'
    });
}

// 获取联系人关注状态
export function getContactFollowStatus(contactId: number): Promise<ApiResponse<boolean>> {
    return request({
        url: `/front/crm/contacts/${contactId}/follow-status`,
        method: 'get'
    });
}

// 关注联系人
export function followContact(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/follow`,
        method: 'post'
    });
}

// 取消关注联系人
export function unfollowContact(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/unfollow`,
        method: 'post'
    });
}

// 获取客户信息
export function getCustomerInfo(customerId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/customers/${customerId}`,
        method: 'get'
    });
}

// 获取用户列表（用于负责人选择）
export function getUserList(params?: any): Promise<ApiResponse<any>> {
    return request({
        url: '/front/system/user/list',
        method: 'get',
        params
    });
}</pre>
            </div>
            
            <h4>3.3 TypeScript类型定义</h4>
            <div class="code-container">
                <div class="code-header">types/contact.ts - 类型定义</div>
                <button class="copy-button" onclick="copyCode(this)">复制</button>
                <pre class="code-block language-typescript">// types/contact.ts 新增类型定义
export interface CrmContact {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    customerId?: number;
    responsiblePersonId?: number;
    createTime: string;
    createBy: string;
    delFlag: string;
    // 扩展字段
    customerName?: string;
    customerInfo?: CrmCustomer;
    responsiblePersonName?: string;
    responsiblePersonInfo?: SysUser;
    isFollowing?: boolean;
    activityStats?: ContactActivityStats;
}

export interface CrmContactFollowup {
    id: number;
    contactId: number;
    content: string;
    followupTime: string;
    nextFollowupTime?: string;
    createBy: string;
    createTime: string;
    delFlag: string;
    // 扩展字段
    creatorName?: string;
}

export interface CrmContactFocus {
    id: number;
    contactId: number;
    userId: number;
    focusStatus: string; // '1' 关注, '0' 取消关注
    createTime: string;
    createBy: string;
}

export interface ContactActivityStats {
    followupCount: number;
    lastFollowupTime?: string;
    nextFollowupTime?: string;
}

export interface ContactAttachment {
    id: number;
    contactId: number;
    fileName: string;
    fileSize: number;
    fileType: string;
    filePath: string;
    uploadBy: string;
    uploadTime: string;
}</pre>
            </div>
        </div>
        
        <h2>实施计划时间表</h2>
        
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>任务</th>
                    <th>预估时间</th>
                    <th>负责人</th>
                    <th>完成标准</th>
                    <th>实施方案</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="5">第一阶段<br>（快速实现）</td>
                    <td>分析现有表结构和关系</td>
                    <td>0.5天</td>
                    <td>全栈开发</td>
                    <td>确认表结构和字段映射</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>基于现有表的后端接口开发</td>
                    <td>2天</td>
                    <td>后端开发</td>
                    <td>完成跟进记录CRUD接口</td>
                    <td>使用 crm_business_follow_up_records</td>
                </tr>
                <tr>
                    <td>客户联系人关联接口开发</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>完成多对多关系查询接口</td>
                    <td>使用 crm_customer_contact_relations</td>
                </tr>
                <tr>
                    <td>前端API集成和UI适配</td>
                    <td>1.5天</td>
                    <td>前端开发</td>
                    <td>联系人详情抽屉正常显示</td>
                    <td>基于现有接口实现</td>
                </tr>
                <tr>
                    <td>功能测试和调试</td>
                    <td>1天</td>
                    <td>全栈开发</td>
                    <td>核心功能正常工作</td>
                    <td>端到端测试</td>
                </tr>
                <tr style="background-color: #e8f4fd; font-weight: bold;">
                    <td colspan="3"><strong>总计</strong></td>
                    <td><strong>6天</strong></td>
                    <td colspan="2">分阶段实施，可快速上线</td>
                </tr>
            </tbody>
        </table>
        
        <div class="success-box">
            <h3>立即行动计划</h3>
            <ol>
                <li><strong>第1天：</strong>与后端开发确认接口设计，制定详细的API文档</li>
                <li><strong>第2天：</strong>开始前端API接口定义和openDrawer函数优化</li>
                <li><strong>第3-4天：</strong>并行进行后端接口开发和前端Mock数据开发</li>
                <li><strong>第5天：</strong>各Tab组件的数据集成和功能实现</li>
                <li><strong>第6天：</strong>接口联调、功能测试和验收测试</li>
            </ol>
        </div>
        
        <hr>
        
        <div class="success-box">
            <h3>📋 代码格式化完成</h3>
            <p>本文档已完成代码格式化优化，包括：</p>
            <ul>
                <li>✅ <strong>SQL代码</strong> - 关键字大写，缩进对齐，JOIN条件格式化</li>
                <li>✅ <strong>TypeScript代码</strong> - 函数签名优化，对象属性对齐，异步处理格式化</li>
                <li>✅ <strong>Java代码</strong> - 注解格式化，方法参数换行，异常处理规范化</li>
                <li>✅ <strong>交互功能</strong> - 一键复制代码，自动格式化按钮</li>
                <li>✅ <strong>语法高亮</strong> - Prism.js集成，多语言支持</li>
            </ul>
            <p><strong>使用说明：</strong>点击页面顶部的"🎨 格式化所有代码"按钮，可对所有代码块进行进一步格式化处理。</p>
        </div>
        
        <hr>
        <p style="text-align: center; color: #7f8c8d; margin-top: 30px;">
            <em>文档生成时间：2024年12月27日</em><br>
            <em>项目：CRM系统 - 联系人详情抽屉接口对接</em><br>
            <em>版本：v2.0 - 代码格式优化版</em>
        </p>
    </div>

    <script>
        // 复制代码功能
        function copyCode(button) {
            const codeBlock = button.nextElementSibling.textContent || button.nextElementSibling.innerText;
            navigator.clipboard.writeText(codeBlock).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制';
                button.style.backgroundColor = '#27ae60';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.backgroundColor = '#4a5568';
                }, 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = codeBlock;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const originalText = button.textContent;
                button.textContent = '已复制';
                button.style.backgroundColor = '#27ae60';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.backgroundColor = '#4a5568';
                }, 2000);
            });
        }
        
        // 页面加载完成后初始化Prism.js
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }
        });
    </script>
</body>
</html>
