<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadUserAssociationMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmLeadUserAssociation" id="CrmLeadUserAssociationResult">
        <id     property="id"              column="id"                />
        <result property="leadId"          column="lead_id"          />
        <result property="userId"          column="user_id"          />
        <result property="status"          column="status"           />
        <result property="currentOwnerId"  column="current_owner_id" />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
    </resultMap>

    <sql id="selectCrmLeadUserAssociationVo">
        select id, lead_id, user_id, status, current_owner_id, del_flag, create_by, create_time, update_by, update_time
        from crm_business_lead_user_associations
    </sql>

    <select id="selectCrmLeadUserAssociationList" parameterType="com.ruoyi.common.domain.entity.CrmLeadUserAssociation" resultMap="CrmLeadUserAssociationResult">
        <include refid="selectCrmLeadUserAssociationVo"/>
        <where>
            <if test="leadId != null "> and lead_id = #{leadId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="currentOwnerId != null "> and current_owner_id = #{currentOwnerId}</if>
        </where>
    </select>
    
    <select id="selectCrmLeadUserAssociationById" parameterType="Long" resultMap="CrmLeadUserAssociationResult">
        <include refid="selectCrmLeadUserAssociationVo"/>
        where id = #{id} and del_flag = '0'
    </select>
        
    <insert id="insertCrmLeadUserAssociation" parameterType="com.ruoyi.common.domain.entity.CrmLeadUserAssociation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_lead_user_associations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">lead_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="currentOwnerId != null">current_owner_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="currentOwnerId != null">#{currentOwnerId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmLeadUserAssociation" parameterType="com.ruoyi.common.domain.entity.CrmLeadUserAssociation">
        update crm_business_lead_user_associations
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">lead_id = #{leadId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="currentOwnerId != null">current_owner_id = #{currentOwnerId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmLeadUserAssociationById" parameterType="Long">
        delete from crm_business_lead_user_associations where id = #{id}
    </delete>

    <delete id="deleteCrmLeadUserAssociationByIds" parameterType="String">
        delete from crm_business_lead_user_associations where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 