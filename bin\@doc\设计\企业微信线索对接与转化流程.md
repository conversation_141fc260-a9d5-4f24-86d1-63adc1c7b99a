# 企业微信线索对接与转化流程

## 一、概述

本文档详细说明企业微信与CRM系统线索模块的对接流程，包括数据同步、线索创建、管理及转化等环节。通过本文档，开发和业务人员可以全面了解企业微信客户数据如何在CRM系统中形成线索，以及如何将线索转化为正式客户的完整流程。

## 二、系统架构

### 1. 整体架构

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|   企业微信     | ---> |   CRM系统      | ---> |   客户管理     |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
      |                       |                       |
      |                       |                       |
      v                       v                       v
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
| 外部联系人数据  | ---> |   线索数据     | ---> | 客户/联系人数据 |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
```

### 2. 数据流向

- **企业微信 → CRM系统**：通过API接口和回调机制，企业微信的外部联系人数据同步到CRM系统
- **线索 → 客户**：通过线索转化流程，将潜在客户(线索)转化为正式客户或联系人

## 三、企业微信数据同步

### 1. 同步方式

#### 1.1 定时同步
系统每日凌晨自动执行全量同步任务，确保数据一致性。

#### 1.2 实时同步
通过企业微信回调机制，当企业微信中的客户数据发生变化时，实时推送到CRM系统。

#### 1.3 手动同步
用户可在界面上手动触发同步操作，适用于特殊情况下的数据同步需求。

### 2. 同步内容

- **基础信息**：姓名、电话、邮箱等个人信息
- **企业信息**：所属公司、职位等
- **标签信息**：企业微信中设置的标签
- **互动记录**：聊天记录、跟进状态等

### 3. 同步实现

```typescript
// 企业微信数据同步到线索的主要流程
async function syncExternalContactsToLeads() {
  try {
    // 1. 获取access_token
    const tokenResponse = await getAccessToken();
    const accessToken = tokenResponse.access_token;
    
    // 2. 获取可使用客户联系功能的员工列表
    const followUsers = await getFollowUserList(accessToken);
    
    // 3. 遍历每个员工，获取其客户列表
    for (const userId of followUsers) {
      const externalContacts = await getExternalContactList(accessToken, userId);
      
      // 4. 批量获取客户详情
      const contactDetails = await batchGetExternalContactDetail(accessToken, userId, externalContacts);
      
      // 5. 将客户信息转换为线索数据并保存
      for (const contact of contactDetails) {
        await createOrUpdateLead({
          leadName: contact.name,
          leadSource: '企业微信',
          phone: contact.mobile,
          email: contact.email,
          customerName: contact.corp_name,
          customerIndustry: contact.corp_industry,
          remarks: contact.remark,
          responsiblePersonId: userId
        });
      }
    }
    
    // 6. 同步标签信息
    await syncCorpTags(accessToken);
    
    return { success: true, message: '同步成功' };
  } catch (error) {
    console.error('企业微信数据同步失败:', error);
    return { success: false, message: '同步失败' };
  }
}
```

## 四、线索管理界面

### 1. 线索列表

系统通过`AssociationManagement`组件实现线索管理功能，主要包括：

- 导航菜单：使用`SideNav`组件展示线索管理相关的导航选项
- 线索列表：使用`el-table`组件展示线索数据
- 筛选功能：支持按名称、来源、状态等条件筛选线索
- 分页控件：支持大量线索数据的分页浏览

```vue
<!-- 线索列表界面核心代码 -->
<el-container class="leads-management">
    <!-- 导航菜单 -->
    <side-nav
        v-model="activeTab"
        :title="navConfig.title"
        :menu-items="navConfig.menuItems"
    />

    <!-- 主内容区域 -->
    <el-container class="main-container">
        <el-header class="header">
            <h1>线索管理</h1>
            <div class="header-actions">
                <el-button type="primary" @click="openLeadDialog">新建线索</el-button>
                <el-button>查看</el-button>
            </div>
        </el-header>

        <el-main>
            <!-- 筛选区域 -->
            <common-filter
                v-model:searchValue="searchInput"
                v-model:filterValue="filterType"
                :config="leadsFilterConfig"
                @search="handleSearch"
                @filter="handleFilterChange"
            />

            <!-- 数据表格 -->
            <el-table ref="leadsTable" :data="leads" border sortable>
                <template v-for="col in tableColumns" :key="col.prop">
                    <el-table-column v-bind="col">
                        <template #default="scope" v-if="col.prop === 'name'">
                            <el-button link type="primary" @click="openDrawer(scope.row)">
                                {{ scope.row.name }}
                            </el-button>
                        </template>
                    </el-table-column>
                </template>
                <table-operations :config="tableOperations" @operation="handleTableOperation" />
            </el-table>
            <pagination v-show="totalLeads > 0" :total="totalLeads" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" />
        </el-main>
    </el-container>
</el-container>
```

### 2. 线索数据获取

系统通过`listLeads`接口获取线索列表数据：

```typescript
// 获取线索列表数据
const getList = async () => {
    try {
        loading.value = true;
        const response = await listLeads(queryParams);
        const { code, msg, rows = [], total = 0 } = response;

        if (code === 200) {
            leads.value = (rows || []).map((item: any) => ({
                id: item.id,
                name: item.leadName,
                source: item.leadSource,
                status: item.status,
                phone: item.phone,
                email: item.email,
                industry: item.customerIndustry,
                remarks: item.remarks,
                createTime: item.createdAt,
                nextFollowUpTime: item.nextContactTime,
                owner: item.responsiblePersonId
            }));
            totalLeads.value = total;
        } else {
            ElMessage.error(msg || '获取线索列表失败');
        }
    } catch (error) {
        console.error('获取线索列表失败:', error);
        ElMessage.error('获取线索列表失败');
    } finally {
        loading.value = false;
    }
};
```

## 五、线索创建流程

### 1. 企业微信自动创建

当企业微信中新增外部联系人时，系统会自动将其创建为线索：

1. 企业微信推送添加客户事件到CRM系统
2. CRM系统接收事件并解析客户数据
3. 系统创建新的线索记录，来源标记为"企业微信"

### 2. 手动创建线索

系统支持在界面上手动创建线索：

```typescript
// 打开新建线索对话框
const openLeadDialog = (): void => {
    newLead.value = { ...DEFAULT_LEAD };
    leadDialogVisible.value = true;
    // 在对话框打开后获取用户选项
    newLeadFormConfig.value = {
        ...newLeadFormConfig.value,
        fields: newLeadFormConfig.value.fields.map(field => {
            if (field.field === 'responsiblePersonId') {
                return {
                    ...field,
                    options: store.getters.userOptions || []
                };
            }
            return field;
        })
    };
};

// 提交新建线索
const handleLeadSubmit = async (formData: any): Promise<void> => {
    try {
        const leadData: LeadForm = {
            leadName: formData.name,
            leadSource: formData.source,
            phone: formData.phone,
            email: formData.email,
            customerIndustry: formData.industry,
            nextContactTime: formData.nextContactTime,
            remarks: formData.remarks,
            responsiblePersonId: formData.responsiblePersonId,
            customerName: formData.customerName || ''
        };

        const response = await addLeads(leadData);
        if (response.code === 200) {
            ElMessage.success('新建线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '新建线索失败');
        }
    } catch (error) {
        console.error('新建线索失败:', error);
        ElMessage.error('新建线索失败');
    }
};
```

## 六、线索详情与编辑

### 1. 线索详情查看

系统使用`CommonDrawer`组件展示线索详情：

```typescript
// 打开线索详情抽屉
const openDrawer = async (row: LeadData): Promise<void> => {
    try {
        const response = await getLeads(row.id);
        if (response.code === 200 && response.data) {
            currentLead.value = {
                id: response.data.id,
                name: response.data.leadName,
                customerName: response.data.customerName,
                source: response.data.leadSource,
                status: response.data.status,
                phone: response.data.phone,
                email: response.data.email,
                industry: response.data.customerIndustry,
                remarks: response.data.remarks,
                createTime: response.data.createdAt,
                nextFollowUpTime: response.data.nextContactTime,
                owner: response.data.responsiblePersonId
            };
            drawerVisible.value = true;
        } else {
            ElMessage.error(response.msg || '获取线索详情失败');
        }
    } catch (error) {
        console.error('获取线索详情失败:', error);
        ElMessage.error('获取线索详情失败');
    }
};
```

### 2. 线索信息编辑

系统支持编辑线索信息：

```typescript
// 更新线索信息
const handleLeadUpdate = async (newData: LeadData): Promise<void> => {
    try {
        const updateData: LeadForm = {
            id: newData.id,
            leadName: newData.name,
            leadSource: newData.source,
            customerName: newData.customerName,
            phone: newData.phone,
            email: newData.email,
            customerIndustry: newData.industry,
            remarks: newData.remarks,
            nextContactTime: newData.nextFollowUpTime,
            responsiblePersonId: newData.owner
        };

        const response = await updateLeads(updateData);
        if (response.code === 200) {
            ElMessage.success('更新线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '更新线索失败');
        }
    } catch (error) {
        console.error('更新线索失败:', error);
        ElMessage.error('更新线索失败');
    }
};
```

## 七、线索转化流程

### 1. 转化流程概述

线索转化是将潜在客户转变为正式客户的过程，分为两种形式：
- 转化为联系人：当客户单位已存在时
- 转化为客户主体：当需要创建新的客户单位时

### 2. 转化实现代码

```typescript
// 转化线索
const handleConvert = async (row: LeadData): Promise<void> => {
    try {
        // 打开转化对话框，选择转化类型和目标客户
        // ...转化对话框代码...

        // 更新线索状态为已转化
        const convertData: StatusForm = {
            leadId: row.id,
            status: 'converted'
        };

        const response = await updateLeadStatus(convertData);
        if (response.code === 200) {
            // 根据转化类型执行不同的后续操作
            if (convertType === 'contact') {
                // 转化为联系人
                await createContactFromLead(row, selectedCustomerId);
            } else if (convertType === 'customer') {
                if (isNewCustomer) {
                    // 创建新客户主体
                    await createCustomerFromLead(row, newCustomerData);
                } else {
                    // 关联到已有客户主体
                    await associateLeadToCustomer(row, selectedCustomerId);
                }
            }
            
            ElMessage.success('转化线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '转化线索失败');
        }
    } catch (error) {
        console.error('转化线索失败:', error);
        ElMessage.error('转化线索失败');
    }
};

// 将线索转化为联系人
async function createContactFromLead(lead: LeadData, customerId: number) {
    // 创建联系人数据
    const contactData = {
        customerId: customerId,
        name: lead.name,
        phone: lead.phone,
        email: lead.email,
        position: '',
        remarks: lead.remarks,
        leadId: lead.id  // 关联原线索ID
    };
    
    // 调用创建联系人接口
    return await createContact(contactData);
}

// 将线索转化为客户主体
async function createCustomerFromLead(lead: LeadData, customerData: any) {
    // 创建客户主体
    const newCustomer = await createCustomer({
        customerName: customerData.name,
        industry: lead.industry,
        scale: customerData.scale,
        address: customerData.address,
        remarks: lead.remarks,
        leadId: lead.id  // 关联原线索ID
    });
    
    // 创建首要联系人
    if (newCustomer.code === 200) {
        await createContact({
            customerId: newCustomer.data.id,
            name: lead.name,
            phone: lead.phone,
            email: lead.email,
            position: '',
            remarks: lead.remarks,
            isPrimary: true,
            leadId: lead.id
        });
    }
    
    return newCustomer;
}

// 将线索关联到已有客户主体
async function associateLeadToCustomer(lead: LeadData, customerId: number) {
    // 更新线索关联的客户ID
    return await updateLeadCustomer({
        leadId: lead.id,
        customerId: customerId
    });
}
```

### 3. 转化流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|     线索       | ---> |    转化选择    | ---> |   转化结果     |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                              |
                              |
                              v
                 +-------------------------+
                 |                         |
                 |  选择转化类型和目标客户  |
                 |                         |
                 +-------------------------+
                              |
                              |
              +---------------+---------------+
              |                               |
              v                               v
+-------------------------+      +-------------------------+
|                         |      |                         |
|    转化为联系人         |      |    转化为客户主体       |
|                         |      |                         |
+-------------------------+      +-------------------------+
              |                               |
              |                               |
              v                               v
+-------------------------+      +-------------------------+
|                         |      |                         |
|  选择已有客户主体       |      |  新建客户主体或选择已有 |
|                         |      |                         |
+-------------------------+      +-------------------------+
              |                               |
              |                               |
              v                               v
+-------------------------+      +-------------------------+
|                         |      |                         |
|  创建联系人记录         |      |  创建客户主体和联系人   |
|                         |      |                         |
+-------------------------+      +-------------------------+
              |                               |
              |                               |
              v                               v
+-------------------------+      +-------------------------+
|                         |      |                         |
|  更新线索状态为已转化   |      |  更新线索状态为已转化   |
|                         |      |                         |
+-------------------------+      +-------------------------+
```

## 八、其他线索管理功能

### 1. 线索分配

系统支持将线索分配给不同的负责人：

```typescript
// 分配线索
const handleAssign = async (row: LeadData): Promise<void> => {
    try {
        // 打开分配对话框，选择负责人
        const assignData: AssignForm = {
            leadId: row.id,
            newOwnerId: selectedUserId // 从对话框获取的用户ID
        };

        const response = await assignLead(assignData);
        if (response.code === 200) {
            ElMessage.success('分配线索成功');
            getList();
        } else {
            ElMessage.error(response.msg || '分配线索失败');
        }
    } catch (error) {
        console.error('分配线索失败:', error);
        ElMessage.error('分配线索失败');
    }
};
```

### 2. 线索删除

系统支持删除不再需要的线索：

```typescript
// 删除线索
const handleDelete = async (row: LeadData): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该线索吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        const response = await deleteLeads(row.id);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(response.msg || '删除线索失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除线索失败:', error);
            ElMessage.error('删除线索失败');
        }
    }
};
```

## 九、接口清单

### 1. 企业微信接口

| 接口名称 | 接口地址 | 说明 |
|---------|---------|------|
| 获取access_token | GET https://qyapi.weixin.qq.com/cgi-bin/gettoken | 获取调用接口凭证 |
| 获取成员列表 | GET https://qyapi.weixin.qq.com/cgi-bin/externalcontact/follow_user/list | 获取可使用客户联系功能的员工列表 |
| 获取客户列表 | GET https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list | 获取指定成员添加的客户列表 |
| 获取客户详情 | GET https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get | 获取客户的详细信息 |
| 批量获取客户详情 | POST https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user | 批量获取客户详情 |
| 获取企业标签库 | GET https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_corp_tag_list | 获取企业设置的标签 |

### 2. CRM系统接口

| 接口名称 | 接口地址 | 说明 |
|---------|---------|------|
| 获取线索列表 | GET /api/leads | 获取线索列表数据 |
| 获取线索详情 | GET /api/leads/{id} | 获取指定线索的详细信息 |
| 新建线索 | POST /api/leads | 创建新的线索 |
| 更新线索 | PUT /api/leads | 更新线索信息 |
| 删除线索 | DELETE /api/leads/{id} | 删除指定线索 |
| 分配线索 | POST /api/leads/assign | 将线索分配给指定负责人 |
| 更新线索状态 | POST /api/leads/status | 更新线索状态，包括转化状态 |
| 创建客户 | POST /api/customers | 创建新的客户主体 |
| 创建联系人 | POST /api/contacts | 创建新的联系人 |

## 十、开发与部署建议

### 1. 开发建议

1. **分阶段实施**：
   - 第一阶段：实现基础的线索管理功能
   - 第二阶段：集成企业微信API，实现数据同步
   - 第三阶段：实现线索转化流程

2. **数据同步优化**：
   - 实现增量同步机制，减少数据传输量
   - 建立数据缓存层，减少API调用频率
   - 优化同步任务调度，避免高峰期执行大量同步

3. **用户界面优化**：
   - 在线索列表中显示企业微信来源标识
   - 提供企业微信数据手动同步按钮
   - 简化线索转化步骤，减少用户操作

### 2. 部署流程

1. **测试环境部署**：
   - 部署企业微信回调接收服务
   - 配置企业微信API调用权限
   - 测试数据同步和线索转化流程

2. **生产环境部署**：
   - 确保服务器安全配置
   - 设置定时同步任务
   - 监控系统运行状态

3. **上线后优化**：
   - 收集用户反馈
   - 优化性能和用户体验
   - 定期检查数据一致性

## 十一、常见问题解答

1. **问**：企业微信中删除的联系人会自动从CRM系统中删除吗？
   **答**：不会自动删除，但会标记为"已删除"状态，保留历史数据。

2. **问**：如何处理企业微信中的群聊信息？
   **答**：群聊信息会同步到CRM系统，并关联到群内的所有相关客户。

3. **问**：线索转化后，原线索数据会保留吗？
   **答**：原线索数据会保留，但状态会更新为"已转化"，并关联到转化后的联系人或客户主体。

4. **问**：如何判断一个线索应该转化为联系人还是客户主体？
   **答**：如果线索代表的公司已在系统中存在，应转化为联系人；如果代表新的公司/组织，则应转化为客户主体。

5. **问**：企业微信标签如何与CRM系统标签对应？
   **答**：系统会自动建立企业微信标签与CRM标签的映射关系，确保数据同步的一致性。 