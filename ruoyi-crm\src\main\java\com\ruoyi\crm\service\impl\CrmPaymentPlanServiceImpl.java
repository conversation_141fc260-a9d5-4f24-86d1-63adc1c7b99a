package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.ruoyi.common.mapper.CrmPaymentPlanMapper;
import com.ruoyi.common.mapper.CrmPaymentInstallmentMapper;
import com.ruoyi.common.mapper.CrmPaymentApprovalMapper;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.service.ICrmPaymentInstallmentService;
import com.ruoyi.crm.service.ICrmPaymentApprovalService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;

/**
 * 回款计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@Service
public class CrmPaymentPlanServiceImpl implements ICrmPaymentPlanService {
    
    @Autowired
    private CrmPaymentPlanMapper paymentPlanMapper;
    
    @Autowired
    private CrmPaymentInstallmentMapper installmentMapper;
    
    @Autowired
    private CrmPaymentApprovalMapper approvalMapper;
    
    @Autowired
    private ICrmPaymentInstallmentService installmentService;
    
    @Autowired
    private ICrmPaymentApprovalService approvalService;

    /**
     * 查询回款计划列表
     *
     * @param plan 回款计划信息
     * @return 回款计划集合
     */
    @Override
    public List<CrmPaymentPlan> selectPaymentPlanList(CrmPaymentPlan plan) {
        return paymentPlanMapper.selectPaymentPlanList(plan);
    }

    /**
     * 查询回款计划详细信息
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    @Override
    public CrmPaymentPlan selectPaymentPlanById(Long id) {
        return paymentPlanMapper.selectPaymentPlanById(id);
    }

    /**
     * 查询包含分期和审批信息的回款计划
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    @Override
    public CrmPaymentPlan selectPaymentPlanWithDetailsById(Long id) {
        return paymentPlanMapper.selectPaymentPlanWithDetailsById(id);
    }

    /**
     * 根据合同ID查询回款计划列表
     *
     * @param contractId 合同ID
     * @return 回款计划集合
     */
    @Override
    public List<CrmPaymentPlan> selectPaymentPlanByContractId(Long contractId) {
        return paymentPlanMapper.selectPaymentPlanByContractId(contractId);
    }

    /**
     * 新增回款计划（包含分期信息）
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPaymentPlan(CrmPaymentPlan plan) {
        // 1. 生成计划编号
        if (plan.getPlanNumber() == null || plan.getPlanNumber().isEmpty()) {
            plan.setPlanNumber(generatePlanNumber());
        }
        
        // 2. 设置默认值
        plan.setCreateTime(DateUtils.getNowDate());
        plan.setCreateBy(SecurityUtils.getUsername());
        if (plan.getPlanStatus() == null) {
            plan.setPlanStatus("草稿");
        }
        if (plan.getApprovalStatus() == null) {
            plan.setApprovalStatus("待提交");
        }
        if (plan.getCurrency() == null) {
            plan.setCurrency("CNY");
        }
        if (plan.getExchangeRate() == null) {
            plan.setExchangeRate(new BigDecimal("1.0000"));
        }
        if (plan.getPlanType() == null) {
            plan.setPlanType("普通");
        }
        if (plan.getRiskLevel() == null) {
            plan.setRiskLevel("低");
        }
        
        // 3. 计算剩余金额
        plan.setRemainingAmount(plan.getTotalAmount());
        if (plan.getReceivedAmount() == null) {
            plan.setReceivedAmount(BigDecimal.ZERO);
        }
        
        // 4. 插入主记录
        int result = paymentPlanMapper.insertPaymentPlan(plan);
        
        // 5. 插入分期信息
        if (result > 0 && !CollectionUtils.isEmpty(plan.getInstallments())) {
            validateInstallments(plan.getInstallments(), plan.getTotalAmount());
            
            for (CrmPaymentInstallment installment : plan.getInstallments()) {
                installment.setPlanId(plan.getId());
                installment.setCreateBy(SecurityUtils.getUsername());
                installment.setCreateTime(DateUtils.getNowDate());
                if (installment.getInstallmentStatus() == null) {
                    installment.setInstallmentStatus("待回款");
                }
                if (installment.getActualAmount() == null) {
                    installment.setActualAmount(BigDecimal.ZERO);
                }
            }
            installmentMapper.batchInsertInstallments(plan.getInstallments());
        }
        
        return result;
    }

    /**
     * 修改回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePaymentPlan(CrmPaymentPlan plan) {
        plan.setUpdateTime(DateUtils.getNowDate());
        plan.setUpdateBy(SecurityUtils.getUsername());
        
        // 更新主记录
        int result = paymentPlanMapper.updatePaymentPlan(plan);
        
        // 如果有分期信息更新
        if (result > 0 && !CollectionUtils.isEmpty(plan.getInstallments())) {
            // 先删除原有分期
            installmentMapper.deleteInstallmentsByPlanId(plan.getId());
            
            // 重新插入分期
            validateInstallments(plan.getInstallments(), plan.getTotalAmount());
            
            for (CrmPaymentInstallment installment : plan.getInstallments()) {
                installment.setPlanId(plan.getId());
                installment.setCreateBy(SecurityUtils.getUsername());
                installment.setCreateTime(DateUtils.getNowDate());
                if (installment.getInstallmentStatus() == null) {
                    installment.setInstallmentStatus("待回款");
                }
            }
            installmentMapper.batchInsertInstallments(plan.getInstallments());
        }
        
        return result;
    }

    /**
     * 删除回款计划信息
     *
     * @param id 回款计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePaymentPlanById(Long id) {
        // 删除相关分期和审批记录
        installmentMapper.deleteInstallmentsByPlanId(id);
        approvalMapper.deleteApprovalsByPlanId(id);
        
        // 删除主记录
        return paymentPlanMapper.deletePaymentPlanById(id);
    }

    /**
     * 批量删除回款计划信息
     *
     * @param ids 需要删除的回款计划ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePaymentPlanByIds(Long[] ids) {
        for (Long id : ids) {
            deletePaymentPlanById(id);
        }
        return ids.length;
    }

    /**
     * 提交审批
     *
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int submitForApproval(Long planId) {
        // 1. 检查计划状态
        CrmPaymentPlan plan = paymentPlanMapper.selectPaymentPlanById(planId);
        if (plan == null) {
            throw new ServiceException("回款计划不存在");
        }
        
        if (!"草稿".equals(plan.getPlanStatus())) {
            throw new ServiceException("只有草稿状态的计划可以提交审批");
        }
        
        // 2. 创建审批流程（这里简化为单级审批，实际可以配置多级）
        List<Long> approverIds = getApproverIds(plan);
        if (CollectionUtils.isEmpty(approverIds)) {
            throw new ServiceException("未找到审批人");
        }
        
        approvalService.createApprovalFlow(planId, approverIds);
        
        // 3. 更新计划状态
        paymentPlanMapper.updatePaymentPlanStatus(planId, "待审批", "审批中");
        
        return 1;
    }

    /**
     * 审批处理
     *
     * @param approvalId 审批ID
     * @param approved 是否通过
     * @param comments 审批意见
     * @return 结果
     */
    @Override
    @Transactional
    public int processApproval(Long approvalId, boolean approved, String comments) {
        // 1. 获取审批记录
        CrmPaymentApproval approval = approvalMapper.selectCrmPaymentApprovalById(approvalId);
        if (approval == null) {
            throw new ServiceException("审批记录不存在");
        }
        
        if (!"待审批".equals(approval.getApprovalStatus())) {
            throw new ServiceException("该审批已处理");
        }
        
        // 2. 更新审批记录
        approval.setApprovalStatus(approved ? "已通过" : "已拒绝");
        approval.setApprovalTime(new Date());
        approval.setApprovalComments(comments);
        approval.setUpdateBy(SecurityUtils.getUsername());
        approval.setUpdateTime(DateUtils.getNowDate());
        
        approvalMapper.updateCrmPaymentApproval(approval);
        
        // 3. 检查是否需要下一级审批或完成审批流程
        if (approved) {
            CrmPaymentApproval nextApprover = approvalService.getNextApprover(approval.getPlanId());
            if (nextApprover == null) {
                // 所有审批完成，更新计划状态
                paymentPlanMapper.updatePaymentPlanStatus(approval.getPlanId(), "已通过", "已通过");
            }
        } else {
            // 拒绝审批，更新计划状态
            paymentPlanMapper.updatePaymentPlanStatus(approval.getPlanId(), "已拒绝", "已拒绝");
        }
        
        return 1;
    }

    /**
     * 生成回款计划编号
     *
     * @return 编号
     */
    @Override
    public String generatePlanNumber() {
        return paymentPlanMapper.generatePlanNumber("HK");
    }

    /**
     * 更新回款金额统计
     *
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePaymentAmounts(Long planId) {
        // 获取所有分期的实际回款金额
        List<CrmPaymentInstallment> installments = installmentMapper.selectInstallmentsByPlanId(planId);
        
        BigDecimal totalReceived = BigDecimal.ZERO;
        for (CrmPaymentInstallment installment : installments) {
            if (installment.getActualAmount() != null) {
                totalReceived = totalReceived.add(installment.getActualAmount());
            }
        }
        
        // 更新计划的已回款金额和剩余金额
        CrmPaymentPlan plan = paymentPlanMapper.selectPaymentPlanById(planId);
        plan.setReceivedAmount(totalReceived);
        plan.setRemainingAmount(plan.getTotalAmount().subtract(totalReceived));
        
        return paymentPlanMapper.updatePaymentPlan(plan);
    }

    /**
     * 获取客户的回款计划统计
     *
     * @param customerId 客户ID
     * @return 统计信息
     */
    @Override
    public int getPaymentPlanCount(Long customerId) {
        return paymentPlanMapper.countByCustomerId(customerId);
    }

    /**
     * 验证分期信息
     */
    private void validateInstallments(List<CrmPaymentInstallment> installments, BigDecimal totalAmount) {
        if (CollectionUtils.isEmpty(installments)) {
            return;
        }
        
        BigDecimal totalInstallmentAmount = BigDecimal.ZERO;
        BigDecimal totalPercentage = BigDecimal.ZERO;
        
        for (CrmPaymentInstallment installment : installments) {
            if (installment.getInstallmentAmount() != null) {
                totalInstallmentAmount = totalInstallmentAmount.add(installment.getInstallmentAmount());
            }
            if (installment.getInstallmentPercentage() != null) {
                totalPercentage = totalPercentage.add(installment.getInstallmentPercentage());
            }
        }
        
        // 检查分期金额是否等于总金额
        if (totalInstallmentAmount.compareTo(totalAmount) != 0) {
            throw new ServiceException("分期金额总和必须等于总回款金额");
        }
        
        // 检查分期比例是否等于100%
        if (totalPercentage.compareTo(new BigDecimal("100.00")) != 0) {
            throw new ServiceException("分期比例总和必须等于100%");
        }
    }

    /**
     * 获取审批人ID列表（简化实现，实际应该从配置中获取）
     */
    private List<Long> getApproverIds(CrmPaymentPlan plan) {
        List<Long> approverIds = new ArrayList<>();
        
        // 这里简化处理，实际应该根据金额、客户等级等因素确定审批流程
        if (plan.getTotalAmount().compareTo(new BigDecimal("100000")) > 0) {
            // 大额审批需要多级
            approverIds.add(2L); // 销售经理
            approverIds.add(1L); // 总经理
        } else {
            // 小额审批只需一级
            approverIds.add(2L); // 销售经理
        }
        
        return approverIds;
    }
}