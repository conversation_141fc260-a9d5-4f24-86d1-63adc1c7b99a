<template>
    <el-card>
        <div slot="header" class="clearfix">
            <el-row>
                <span><b>合同金额目标及完成情况</b></span>
            </el-row>
            <el-row style="margin-top: 10px; display: flex;align-items: center;">
                <el-button type="success" size="small" plain>本人</el-button>
                <el-button type="success" size="small" plain>本月</el-button>
                <el-col :span="3" style="margin-left: 10px;">
                    <el-select v-model="selectedMonth" placeholder="选择月份">
                        <el-option label="本月" value="thisMonth"></el-option>
                        <el-option label="上月" value="lastMonth"></el-option>
                    </el-select>
                </el-col>
            </el-row>
        </div>
        <div ref="contractChartRef" class="chart" style="width: 100%; height: 400px;"></div>
    </el-card>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';

const selectedMonth = ref('thisMonth');
const contractChartRef = ref(null);

const initChart = () => {
    if (contractChartRef.value) {
        const contractChart = echarts.init(contractChartRef.value);
        const contractChartOptions = {
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', data: ['2024-01', '2024-02', '2024-03'] },
            yAxis: { type: 'value' },
            series: [{ name: '实际完成金额', type: 'line', data: [0.2, 0.4, 1] }],
        };
        contractChart.setOption(contractChartOptions);
    }
};

onMounted(() => {
    initChart();
});
</script>

<style scoped>
.chart {
    width: 100%;
    height: 400px;
}
</style>
