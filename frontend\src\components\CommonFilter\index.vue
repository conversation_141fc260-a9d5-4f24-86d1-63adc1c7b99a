<template>
  <el-row :gutter="20" class="common-filter">
    <el-col :span="24">
      <div class="filter-content">
        <!-- 搜索框 -->
        <template v-if="config.search">
          <el-input
            v-model="searchValue"
            :style="{ width: config.search.width || '240px' }"
            :placeholder="config.search.placeholder"
            @input="handleSearchInput"
          >
            <template #prefix>
              <el-icon class="el-input__icon">
                <component :is="config.search.icon || 'Search'" />
              </el-icon>
            </template>
          </el-input>
        </template>

        <!-- 筛选项 -->
        <template v-if="config.filter">
          <el-text class="mx-1" :style="{ marginLeft: '10px' }">
            {{ config.filter.label }}
          </el-text>
          <el-radio-group
            v-model="filterValue"
            :size="config.filter.size || 'default'"
            @change="handleFilterChange"
          >
            <template v-if="config.filter.buttonStyle">
              <el-radio-button
                v-for="option in config.filter.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </el-radio-button>
            </template>
            <template v-else>
              <el-radio
                v-for="option in config.filter.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </el-radio>
            </template>
          </el-radio-group>
        </template>

        <!-- 额外的操作按钮 -->
        <template v-if="config.actions">
          <div class="filter-actions">
            <el-button
              v-for="action in config.actions"
              :key="action.key"
              v-bind="action.props"
              @click="handleAction(action)"
            >
              <template v-if="action.icon" #icon>
                <el-icon>
                  <component :is="action.icon" />
                </el-icon>
              </template>
              {{ action.label }}
            </el-button>
          </div>
        </template>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';
import { ref, watch } from 'vue';

// 筛选选项接口
interface FilterOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

// 操作按钮接口
interface ActionButton {
  key: string;
  label: string;
  icon?: string;
  props?: Record<string, any>;
  handler?: () => void;
}

// 搜索配置接口
interface SearchConfig {
  placeholder: string;
  width?: string;
  icon?: string;
  debounceTime?: number;
}

// 筛选配置接口
interface FilterConfig {
  label: string;
  options: FilterOption[];
  buttonStyle?: boolean;
  size?: 'default' | 'small' | 'large';
}

// 组件配置接口
export interface CommonFilterConfig {
  search?: SearchConfig;
  filter?: FilterConfig;
  actions?: ActionButton[];
}

interface Props {
  config: CommonFilterConfig;
  searchValue?: string;
  filterValue?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  searchValue: '',
  filterValue: ''
});

const emit = defineEmits<{
  (e: 'update:searchValue', value: string): void;
  (e: 'update:filterValue', value: string | number): void;
  (e: 'search', value: string): void;
  (e: 'filter', value: string | number): void;
  (e: 'action', key: string): void;
}>();

const searchValue = ref(props.searchValue);
const filterValue = ref(props.filterValue);

// 监听搜索值变化
watch(() => props.searchValue, (val) => {
  searchValue.value = val;
});

// 监听筛选值变化
watch(() => props.filterValue, (val) => {
  filterValue.value = val;
});

// 处理搜索输入
const handleSearchInput = debounce((value: string) => {
  emit('update:searchValue', value);
  emit('search', value);
}, props.config.search?.debounceTime || 300);

// 处理筛选变化
const handleFilterChange = (value: string | number) => {
  emit('update:filterValue', value);
  emit('filter', value);
};

// 处理操作按钮点击
const handleAction = (action: ActionButton) => {
  if (action.handler) {
    action.handler();
  }
  emit('action', action.key);
};
</script>

<style scoped>
.common-filter {
  margin: 16px 0;
  border-radius: 4px;
}

.filter-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-radio-button__inner) {
  border-radius: 4px;
}

:deep(.el-radio-group) {
  margin-left: 8px;
}
</style> 