<template>
  <el-dialog v-model="dialogVisible" :title="modelInfo?.name || '3D模型预览'" width="90%" class="model-viewer-dialog"
    :destroy-on-close="true">
    <div class="model-viewer-container">
      <div class="viewer-section">
        <div ref="threeContainer" class="three-container"></div>
        <div class="camera-controls">
          <el-tooltip content="重置视角">
            <el-button circle @click="resetCamera">
              <el-icon>
                <Refresh />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="info-section">
        <div class="info-card">
          <h3>模型信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="名称">{{ modelInfo?.name || '-' }}</el-descriptions-item>
            <el-descriptions-item label="体积">{{ formatNumber(modelVolume) }} mm³</el-descriptions-item>
            <el-descriptions-item label="表面积">{{ formatNumber(surfaceArea) }} mm²</el-descriptions-item>
            <el-descriptions-item label="尺寸">
              {{ `${formatNumber(dimensions.x)} × ${formatNumber(dimensions.y)} × ${formatNumber(dimensions.z)} mm` }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="camera-info">
          <p>相机位置: ({{ formatNumber(cameraPosition.x) }}, {{ formatNumber(cameraPosition.y) }}, 
             {{ formatNumber(cameraPosition.z) }})</p>
          <p>缩放比例: {{ formatNumber(zoomLevel) }}</p>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
// import { STEPLoader } from 'three/examples/jsm/loaders/STEPLoader';
import { onMounted, onUnmounted, ref, watch } from 'vue';

interface ModelInfo {
  name?: string;
  [key: string]: any;
}

interface Dimensions {
  x: number;
  y: number;
  z: number;
}

interface Position {
  x: number;
  y: number;
  z: number;
}

interface Props {
  modelInfo?: ModelInfo;
}

const props = withDefaults(defineProps<Props>(), {
  modelInfo: () => ({})
});

const dialogVisible = ref(false);
const threeContainer = ref<HTMLElement | null>(null);
const cameraPosition = ref<Position>({ x: 0, y: 0, z: 0 });
const zoomLevel = ref(1);
const modelVolume = ref(0);
const surfaceArea = ref(0);
const dimensions = ref<Dimensions>({ x: 0, y: 0, z: 0 });

let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let mesh: THREE.Mesh;

// 初始化三维场景
function initThreeJS() {
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  if (!threeContainer.value) return;

  camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000);
  camera.position.set(130, 189, 153);

  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight);
  threeContainer.value.appendChild(renderer.domElement);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(200, 400, 200);
  scene.add(directionalLight);

  // 初始化控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.25;
  controls.target.set(118, 0, 51);

  // 添加网格
  const gridHelper = new THREE.GridHelper(200, 20, 0x888888, 0x888888);
  scene.add(gridHelper);

  // 添加坐标轴
  const axesHelper = new THREE.AxesHelper(100);
  scene.add(axesHelper);

  // 监听窗口变化
  window.addEventListener('resize', onWindowResize);
}

// 计算几何体信息
function calculateGeometryInfo(geometry: THREE.BufferGeometry) {
  // 计算体积
  let volume = 0;
  const position = geometry.attributes.position;
  const faces = position.count / 3;

  for (let i = 0; i < faces; i++) {
    const p1 = new THREE.Vector3().fromBufferAttribute(position, i * 3);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 1);
    const p3 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 2);
    volume += signedVolumeOfTriangle(p1, p2, p3);
  }

  // 计算表面积和尺寸
  geometry.computeBoundingBox();
  const boundingBox = geometry.boundingBox;
  
  if (boundingBox) {
    dimensions.value = {
      x: boundingBox.max.x - boundingBox.min.x,
      y: boundingBox.max.y - boundingBox.min.y,
      z: boundingBox.max.z - boundingBox.min.z
    };
  }

  // 计算表面积
  let area = 0;
  for (let i = 0; i < faces; i++) {
    const p1 = new THREE.Vector3().fromBufferAttribute(position, i * 3);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 1);
    const p3 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 2);
    area += calculateTriangleArea(p1, p2, p3);
  }

  modelVolume.value = Math.abs(volume);
  surfaceArea.value = area;
}

function signedVolumeOfTriangle(p1: THREE.Vector3, p2: THREE.Vector3, p3: THREE.Vector3): number {
  return p1.dot(p2.cross(p3)) / 6.0;
}

function calculateTriangleArea(p1: THREE.Vector3, p2: THREE.Vector3, p3: THREE.Vector3): number {
  const a = new THREE.Vector3().subVectors(p2, p1);
  const b = new THREE.Vector3().subVectors(p3, p1);
  const cross = new THREE.Vector3().crossVectors(a, b);
  return cross.length() / 2;
}

// 加载STL模型
function loadModel(url: string) {
  if (!props.modelInfo) {
    ElMessage.warning('模型信息为空');
    return;
  }

  const extension = url.split('.').pop()?.toLowerCase();
  let loader: STLLoader;

  switch(extension) {
    case 'stl':
      loader = new STLLoader();
      break;
    default:
      ElMessage.error('不支持的模型格式');
      return;
  }

  loader.load(
    url,
    (geometry) => {
      if (mesh) {
        scene.remove(mesh);
      }

      const material = new THREE.MeshPhongMaterial({
        color: 0x3399ff,
        specular: 0x111111,
        shininess: 200
      });
      
      mesh = new THREE.Mesh(geometry, material);
      scene.add(mesh);

      // 计算几何体信息
      calculateGeometryInfo(geometry);

      // 自动调整相机位置
      fitCameraToObject(mesh);

      // 生成缩略图
      generateThumbnail();
    },
    (xhr) => {
      console.log((xhr.loaded / xhr.total * 100) + '% loaded');
    },
    (error) => {
      ElMessage.error('模型加载失败');
      console.error(error);
    }
  );
}

function generateThumbnail(): Promise<string> {
  return new Promise((resolve) => {
    // 等待一帧确保渲染完成
    requestAnimationFrame(() => {
      const dataURL = renderer.domElement.toDataURL('image/png');
      resolve(dataURL);
    });
  });
}

// 自动调整相机位置
function fitCameraToObject(object: THREE.Object3D, offset = 1.5) {
  const boundingBox = new THREE.Box3();
  boundingBox.setFromObject(object);
  
  const center = boundingBox.getCenter(new THREE.Vector3());
  const size = boundingBox.getSize(new THREE.Vector3());
  
  const maxDim = Math.max(size.x, size.y, size.z);
  const fov = camera.fov * (Math.PI / 180);
  let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2)) * offset;
  
  camera.position.set(center.x + cameraZ, center.y + cameraZ, center.z + cameraZ);
  controls.target.copy(center);
  
  camera.updateProjectionMatrix();
  controls.update();
}

// 重置相机位置
function resetCamera() {
  if (mesh) {
    fitCameraToObject(mesh);
  }
}

// 窗口大小变化处理
function onWindowResize() {
  if (camera && renderer && threeContainer.value) {
    camera.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight);
  }
}

// 动画循环
function animate() {
  requestAnimationFrame(animate);
  
  if (controls) {
    controls.update();
  }
  
  if (camera) {
    const pos = camera.position;
    cameraPosition.value = { x: pos.x, y: pos.y, z: pos.z };
    zoomLevel.value = camera.zoom;
  }
  
  // 确保renderer、scene和camera都已初始化
  if (renderer && scene && camera) {
    renderer.render(scene, camera);
  }
}

// 格式化数字
function formatNumber(num: number): string {
  return num.toFixed(2);
}

// 打开查看器
function open(modelUrl: string) {
  if (!props.modelInfo) {
    ElMessage.warning('模型信息为空');
    return;
  }

  dialogVisible.value = true;
  
  // 确保DOM已经更新
  setTimeout(() => {
    if (!scene) {
      initThreeJS();
      animate();
    }
    
    if (modelUrl) {
      loadModel(modelUrl);
    }
  }, 0);
}

// 清理资源
function cleanup() {
  if (renderer) {
    renderer.dispose();
  }
  if (mesh) {
    mesh.geometry.dispose();
    // 处理材质可能是数组的情况
    if (Array.isArray(mesh.material)) {
      mesh.material.forEach(material => material.dispose());
    } else {
      mesh.material.dispose();
    }
  }
  window.removeEventListener('resize', onWindowResize);
}

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    cleanup();
  }
});

// 暴露方法给父组件
defineExpose({
  open
});

// 组件挂载时初始化
onMounted(() => {
  // 只在对话框打开时才初始化Three.js
  // initThreeJS();
  // animate();
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', onWindowResize);
  if (renderer) {
    renderer.dispose();
  }
  if (controls) {
    controls.dispose();
  }
});
</script>

<style scoped>
.model-viewer-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.model-viewer-container {
  display: flex;
  height: 80vh;
}

.viewer-section {
  flex: 1;
  position: relative;
}

.three-container {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
}

.info-section {
  width: 300px;
  padding: 20px;
  background: #f5f7fa;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.info-card {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.camera-info {
  margin-top: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  font-size: 12px;
  color: #606266;
}

.camera-info p {
  margin: 5px 0;
}
</style>
