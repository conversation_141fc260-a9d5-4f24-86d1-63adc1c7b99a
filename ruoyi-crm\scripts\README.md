# CRM 项目工具脚本

## BPMN 质量检查脚本

### 文件位置
- **脚本**: `scripts/check-bpmn-quality.ps1`
- **文档**: `doc/bpmn-format-rules.md`

### 使用方法

```powershell
# 切换到 ruoyi-crm 目录
cd ruoyi-crm

# 检查默认路径的 BPMN 文件
pwsh scripts\check-bpmn-quality.ps1

# 检查指定路径
pwsh scripts\check-bpmn-quality.ps1 -Path "src\main\resources\processes\reference_bpmn"

# 显示详细信息
pwsh scripts\check-bpmn-quality.ps1 -Verbose
```

### 检查内容

1. **基本结构检查**
   - `bpmn:process` 定义
   - `bpmndi:BPMNDiagram` 图形定义
   - `bpmndi:BPMNShape` 节点图形

2. **关键检查**
   - `bpmndi:BPMNEdge` 连接线定义（**最重要**）
   - `sequenceFlow` 与 `BPMNEdge` 数量匹配

### 输出示例

```
🔍 BPMN 文件质量检查器
检查路径: src\main\resources\processes

找到 6 个 BPMN 文件

📄 检查文件: task-assignment.bpmn
   ✅ 文件格式正常

📄 检查文件: expense.bpmn
   ✅ 文件格式正常

📋 检查总结
总文件数: 6
正常文件: 6
问题文件: 0

🎉 所有文件都符合质量标准！
```

### 故障排除

如果发现问题文件：
1. 查看详细错误信息
2. 参考 `doc/bpmn-format-rules.md` 文档
3. 使用标准 BPMN 编辑器重新保存文件

### 重要提醒

⚠️ **BPMN 文件无法显示的主要原因是缺少 `BPMNEdge` 定义，而不是 `relationship` 节点！** 