<template>
  <div class="installments-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            size="small"
            @click="handleAdd"
            v-hasPermi="['crm:paymentInstall:add']"
          >新增分期</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Upload"
            size="small"
            @click="handleCreateTemplate"
            v-hasPermi="['crm:paymentInstall:add']"
          >创建模板</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Refresh"
            size="small"
            @click="handleUpdateOverdue"
            v-hasPermi="['crm:paymentInstall:edit']"
          >更新逾期</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            size="small"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['crm:paymentInstall:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分期状态" prop="installmentStatus">
        <el-select v-model="queryParams.installmentStatus" placeholder="请选择分期状态" clearable>
          <el-option label="待回款" value="待回款" />
          <el-option label="部分回款" value="部分回款" />
          <el-option label="已回款" value="已回款" />
          <el-option label="逾期" value="逾期" />
        </el-select>
      </el-form-item>
      <el-form-item label="分期名称" prop="installmentName">
        <el-input
          v-model="queryParams.installmentName"
          placeholder="请输入分期名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 分期统计卡片 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
            <div class="stat-label">总分期数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value completed">{{ statistics.completedCount || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value overdue">{{ statistics.overdueCount || 0 }}</div>
            <div class="stat-label">逾期分期</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.completionRate || 0 }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分期列表 -->
    <el-table v-loading="loading" :data="installmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分期序号" align="center" prop="installmentNumber" width="80" />
      <el-table-column label="分期名称" align="center" prop="installmentName" width="120" />
      <el-table-column label="分期金额" align="center" prop="installmentAmount" width="120">
        <template #default="scope">
          <span class="amount">¥{{ parseFloat(scope.row.installmentAmount || 0).toLocaleString() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分期比例" align="center" prop="installmentPercentage" width="100">
        <template #default="scope">
          <span>{{ scope.row.installmentPercentage }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="计划日期" align="center" prop="plannedDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plannedDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际回款" align="center" width="150">
        <template #default="scope">
          <div>
            <div class="amount">¥{{ parseFloat(scope.row.actualAmount || 0).toLocaleString() }}</div>
            <div v-if="scope.row.actualDate" class="actual-date">
              {{ parseTime(scope.row.actualDate, '{y}-{m}-{d}') }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分期状态" align="center" prop="installmentStatus" width="100">
        <template #default="scope">
          <dict-tag :options="installmentStatusOptions" :value="scope.row.installmentStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="逾期天数" align="center" prop="overdueDays" width="100">
        <template #default="scope">
          <span v-if="scope.row.overdueDays > 0" class="overdue-days">
            {{ scope.row.overdueDays }}天
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['crm:paymentInstall:edit']"
          >修改</el-button>
          <el-button
            link
            type="success"
            icon="Money"
            @click="handleRecordPayment(scope.row)"
            v-hasPermi="['crm:paymentInstall:edit']"
            v-if="scope.row.installmentStatus !== '已回款'"
          >记录回款</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['crm:paymentInstall:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改分期对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分期序号" prop="installmentNumber">
              <el-input-number v-model="form.installmentNumber" :min="1" :max="99" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分期名称" prop="installmentName">
              <el-input v-model="form.installmentName" placeholder="请输入分期名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分期金额" prop="installmentAmount">
              <el-input-number 
                v-model="form.installmentAmount" 
                :precision="2" 
                :min="0.01" 
                placeholder="请输入分期金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分期比例" prop="installmentPercentage">
              <el-input-number 
                v-model="form.installmentPercentage" 
                :precision="2" 
                :min="0.01" 
                :max="100"
                placeholder="请输入分期比例"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划日期" prop="plannedDate">
              <el-date-picker
                v-model="form.plannedDate"
                type="date"
                placeholder="选择计划回款日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分期状态" prop="installmentStatus">
              <el-select v-model="form.installmentStatus" placeholder="请选择分期状态" style="width: 100%">
                <el-option label="待回款" value="待回款" />
                <el-option label="部分回款" value="部分回款" />
                <el-option label="已回款" value="已回款" />
                <el-option label="逾期" value="逾期" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 记录回款对话框 -->
    <el-dialog title="记录实际回款" v-model="paymentOpen" width="500px" append-to-body>
      <el-form ref="paymentForm" :model="paymentForm" :rules="paymentRules" label-width="100px">
        <el-form-item label="分期信息">
          <div class="installment-info">
            <div><strong>{{ paymentForm.installmentName }}</strong></div>
            <div>计划金额: ¥{{ parseFloat(paymentForm.installmentAmount || 0).toLocaleString() }}</div>
            <div>已回款: ¥{{ parseFloat(paymentForm.actualAmount || 0).toLocaleString() }}</div>
          </div>
        </el-form-item>
        <el-form-item label="实际回款" prop="newActualAmount">
          <el-input-number 
            v-model="paymentForm.newActualAmount" 
            :precision="2" 
            :min="0.01" 
            placeholder="请输入实际回款金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="付款凭证" prop="paymentVoucher">
          <el-input v-model="paymentForm.paymentVoucher" placeholder="请输入付款凭证号或路径" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPayment">确 定</el-button>
          <el-button @click="cancelPayment">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建模板对话框 -->
    <el-dialog title="创建分期模板" v-model="templateOpen" width="600px" append-to-body>
      <el-form ref="templateForm" :model="templateForm" label-width="100px">
        <el-form-item label="模板类型">
          <el-radio-group v-model="templateForm.templateType">
            <el-radio label="2_phase">2期模板 (50%-50%)</el-radio>
            <el-radio label="3_phase">3期模板 (40%-30%-30%)</el-radio>
            <el-radio label="4_phase">4期模板 (25%-25%-25%-25%)</el-radio>
            <el-radio label="custom">自定义模板</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 自定义模板配置 -->
        <div v-if="templateForm.templateType === 'custom'">
          <el-form-item label="分期配置">
            <div v-for="(phase, index) in templateForm.phases" :key="index" class="phase-config">
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-input v-model="phase.name" placeholder="分期名称" />
                </el-col>
                <el-col :span="6">
                  <el-input-number 
                    v-model="phase.percentage" 
                    placeholder="比例%" 
                    :min="0.01" 
                    :max="100" 
                    :precision="2"
                  />
                </el-col>
                <el-col :span="6">
                  <el-input-number 
                    v-model="phase.daysOffset" 
                    placeholder="延迟天数" 
                    :min="0"
                  />
                </el-col>
                <el-col :span="6">
                  <el-button type="danger" icon="Delete" size="small" @click="removePhase(index)" />
                </el-col>
              </el-row>
            </div>
            <el-button type="primary" icon="Plus" size="small" @click="addPhase">添加分期</el-button>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTemplate">确 定</el-button>
          <el-button @click="cancelTemplate">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PaymentInstallmentsTab">
import { listInstallments, getInstallment, delInstallment, addInstallment, updateInstallment, getInstallmentsByPlan, recordPayment, updateOverdue, createTemplate, getStatistics } from "@/api/crm/paymentInstallment";

const { proxy } = getCurrentInstance();
const { parseTime } = proxy.useParseTime;

// 响应式数据
const installmentList = ref([]);
const open = ref(false);
const paymentOpen = ref(false);
const templateOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const statistics = ref({});

// 属性
const props = defineProps({
  planId: {
    type: Number,
    required: true
  }
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  planId: null,
  installmentName: null,
  installmentStatus: null
});

// 表单数据
const form = ref({});
const paymentForm = ref({});
const templateForm = ref({
  templateType: '3_phase',
  phases: [
    { name: '首期款', percentage: 40, daysOffset: 0 },
    { name: '中期款', percentage: 30, daysOffset: 30 },
    { name: '尾期款', percentage: 30, daysOffset: 60 }
  ]
});

// 字典选项
const installmentStatusOptions = ref([
  { label: '待回款', value: '待回款' },
  { label: '部分回款', value: '部分回款' },
  { label: '已回款', value: '已回款' },
  { label: '逾期', value: '逾期' }
]);

// 表单验证
const rules = {
  installmentNumber: [
    { required: true, message: "分期序号不能为空", trigger: "blur" }
  ],
  installmentName: [
    { required: true, message: "分期名称不能为空", trigger: "blur" }
  ],
  installmentAmount: [
    { required: true, message: "分期金额不能为空", trigger: "blur" }
  ],
  installmentPercentage: [
    { required: true, message: "分期比例不能为空", trigger: "blur" }
  ],
  plannedDate: [
    { required: true, message: "计划日期不能为空", trigger: "blur" }
  ]
};

const paymentRules = {
  newActualAmount: [
    { required: true, message: "实际回款金额不能为空", trigger: "blur" }
  ]
};

// 监听计划ID变化
watch(() => props.planId, (newVal) => {
  if (newVal) {
    queryParams.value.planId = newVal;
    getList();
    getStatisticsData();
  }
}, { immediate: true });

/** 查询分期列表 */
function getList() {
  loading.value = true;
  if (props.planId) {
    getInstallmentsByPlan(props.planId).then(response => {
      installmentList.value = response.data;
      loading.value = false;
    });
  } else {
    listInstallments(queryParams.value).then(response => {
      installmentList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
}

/** 获取统计数据 */
function getStatisticsData() {
  if (props.planId) {
    getStatistics(props.planId).then(response => {
      statistics.value = response.data;
    });
  }
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    planId: props.planId,
    installmentNumber: null,
    installmentName: null,
    installmentAmount: null,
    installmentPercentage: null,
    plannedDate: null,
    installmentStatus: "待回款",
    remark: null
  };
  proxy.resetForm("form");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加回款分期";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getInstallment(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改回款分期";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["form"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateInstallment(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          getStatisticsData();
        });
      } else {
        addInstallment(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          getStatisticsData();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const installmentIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除回款分期编号为"' + installmentIds + '"的数据项？').then(function() {
    return delInstallment(installmentIds);
  }).then(() => {
    getList();
    getStatisticsData();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 记录回款操作 */
function handleRecordPayment(row) {
  paymentForm.value = {
    installmentId: row.id,
    installmentName: row.installmentName,
    installmentAmount: row.installmentAmount,
    actualAmount: row.actualAmount || 0,
    newActualAmount: null,
    paymentVoucher: null
  };
  paymentOpen.value = true;
}

/** 提交回款记录 */
function submitPayment() {
  proxy.$refs["paymentForm"].validate(valid => {
    if (valid) {
      const params = {
        installmentId: paymentForm.value.installmentId,
        actualAmount: paymentForm.value.newActualAmount,
        paymentVoucher: paymentForm.value.paymentVoucher
      };
      recordPayment(params).then(response => {
        proxy.$modal.msgSuccess("记录成功");
        paymentOpen.value = false;
        getList();
        getStatisticsData();
      });
    }
  });
}

/** 取消回款记录 */
function cancelPayment() {
  paymentOpen.value = false;
  paymentForm.value = {};
}

/** 更新逾期天数 */
function handleUpdateOverdue() {
  proxy.$modal.confirm('是否确认更新所有分期的逾期天数？').then(function() {
    return updateOverdue();
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("更新成功");
  }).catch(() => {});
}

/** 创建模板操作 */
function handleCreateTemplate() {
  templateOpen.value = true;
}

/** 提交模板创建 */
function submitTemplate() {
  const params = {
    planId: props.planId,
    templateType: templateForm.value.templateType
  };
  
  if (templateForm.value.templateType === 'custom') {
    params.phases = templateForm.value.phases;
  }
  
  createTemplate(params).then(response => {
    proxy.$modal.msgSuccess("创建成功");
    templateOpen.value = false;
    getList();
    getStatisticsData();
  });
}

/** 取消模板创建 */
function cancelTemplate() {
  templateOpen.value = false;
}

/** 添加分期 */
function addPhase() {
  templateForm.value.phases.push({
    name: '',
    percentage: 0,
    daysOffset: 0
  });
}

/** 移除分期 */
function removePhase(index) {
  templateForm.value.phases.splice(index, 1);
}
</script>

<style scoped>
.installments-tab {
  padding: 20px;
}

.stat-card {
  text-align: center;
  margin-bottom: 20px;
}

.stat-item {
  padding: 15px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.completed {
  color: #67c23a;
}

.stat-value.overdue {
  color: #f56c6c;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.amount {
  color: #e6a23c;
  font-weight: bold;
}

.actual-date {
  font-size: 12px;
  color: #909399;
}

.overdue-days {
  color: #f56c6c;
  font-weight: bold;
}

.installment-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.installment-info div {
  margin-bottom: 5px;
}

.installment-info div:last-child {
  margin-bottom: 0;
}

.phase-config {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>