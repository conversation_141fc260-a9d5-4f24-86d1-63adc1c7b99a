<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.activiti.org/test">

    <process id="expense" name="报销审批流程" isExecutable="true">
        <!-- 开始事件 -->
        <startEvent id="startEvent" name="提交报销申请"></startEvent>
        
        <!-- 直属主管审批 -->
        <userTask id="managerApproval" name="直属主管审批" 
                  activiti:assignee="${supervisor}">
            <documentation>直属主管审批报销申请</documentation>
        </userTask>
        
        <!-- 排他网关 - 根据金额判断 -->
        <exclusiveGateway id="amountGateway" name="金额判断"></exclusiveGateway>
        
        <!-- 财务审批（小额报销<1000） -->
        <userTask id="financeApproval" name="财务审批" 
                  activiti:candidateGroups="finance">
            <documentation>小额报销，财务审批后完成</documentation>
        </userTask>
        
        <!-- 部门总监审批（中额报销1000-5000） -->
        <userTask id="deptDirectorApproval" name="部门总监审批" 
                  activiti:assignee="${deptDirector}">
            <documentation>中额报销，需要部门总监审批</documentation>
        </userTask>
        
        <!-- 总经理审批（大额报销>5000） -->
        <userTask id="ceoApproval" name="总经理审批" 
                  activiti:assignee="${ceo}">
            <documentation>大额报销，需要总经理审批</documentation>
        </userTask>
        
        <!-- 汇聚网关 -->
        <exclusiveGateway id="mergeGateway" name="流程汇聚"></exclusiveGateway>
        
        <!-- 财务付款 -->
        <serviceTask id="paymentProcess" name="财务付款" 
                     activiti:class="com.ruoyi.activiti.service.PaymentService">
            <documentation>自动处理付款流程</documentation>
        </serviceTask>
        
        <!-- 结束事件 -->
        <endEvent id="endEvent" name="报销完成"></endEvent>
        
        <!-- 序列流 -->
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"></sequenceFlow>
        <sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="amountGateway"></sequenceFlow>
        
        <!-- 条件分支 -->
        <sequenceFlow id="flowLowAmount" name="小额报销(<1000)" 
                      sourceRef="amountGateway" targetRef="financeApproval">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount < 1000}]]></conditionExpression>
        </sequenceFlow>
        
        <sequenceFlow id="flowMidAmount" name="中额报销(1000-5000)" 
                      sourceRef="amountGateway" targetRef="deptDirectorApproval">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount >= 1000 && amount <= 5000}]]></conditionExpression>
        </sequenceFlow>
        
        <sequenceFlow id="flowHighAmount" name="大额报销(>5000)" 
                      sourceRef="amountGateway" targetRef="ceoApproval">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount > 5000}]]></conditionExpression>
        </sequenceFlow>
        
        <!-- 汇聚流程 -->
        <sequenceFlow id="flow6" sourceRef="financeApproval" targetRef="mergeGateway"></sequenceFlow>
        <sequenceFlow id="flow7" sourceRef="deptDirectorApproval" targetRef="mergeGateway"></sequenceFlow>
        <sequenceFlow id="flow8" sourceRef="ceoApproval" targetRef="mergeGateway"></sequenceFlow>
        <sequenceFlow id="flow9" sourceRef="mergeGateway" targetRef="paymentProcess"></sequenceFlow>
        <sequenceFlow id="flow10" sourceRef="paymentProcess" targetRef="endEvent"></sequenceFlow>
    </process>
</definitions>