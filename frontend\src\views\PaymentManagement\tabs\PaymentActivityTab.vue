<template>
    <div class="payment-activity-tab">
        <div class="activity-header">
            <h3>活动记录</h3>
            <el-button type="primary" size="small" @click="addActivity">
                <el-icon><Plus /></el-icon>
                添加记录
            </el-button>
        </div>
        
        <el-timeline class="activity-timeline">
            <el-timeline-item
                v-for="activity in activities"
                :key="activity.id"
                :timestamp="activity.timestamp"
                :type="getActivityType(activity.type)"
            >
                <el-card class="activity-card">
                    <div class="activity-content">
                        <div class="activity-title">
                            <span class="activity-type">{{ activity.type }}</span>
                            <span class="activity-user">{{ activity.user }}</span>
                        </div>
                        <div class="activity-description">{{ activity.description }}</div>
                        <div class="activity-details" v-if="activity.details">
                            <el-tag size="small" v-for="detail in activity.details" :key="detail">{{ detail }}</el-tag>
                        </div>
                    </div>
                </el-card>
            </el-timeline-item>
        </el-timeline>
        
        <!-- 添加活动对话框 -->>
        <el-dialog v-model="dialogVisible" title="添加活动记录" width="500px">
            <el-form :model="newActivity" label-width="80px">
                <el-form-item label="类型">
                    <el-select v-model="newActivity.type" placeholder="请选择活动类型">
                        <el-option label="回款" value="回款" />
                        <el-option label="跟进" value="跟进" />
                        <el-option label="审核" value="审核" />
                        <el-option label="修改" value="修改" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input v-model="newActivity.description" type="textarea" :rows="3" placeholder="请输入活动描述" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveActivity">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Plus } from '@element-plus/icons-vue';

interface Props {
    entityData: any;
}

defineProps<Props>();

const dialogVisible = ref(false);
const newActivity = ref({
    type: '',
    description: ''
});

// 模拟活动数据
const activities = ref([
    {
        id: 1,
        type: '创建回款',
        user: '张三',
        timestamp: '2024-02-21 10:30',
        description: '创建了新的回款记录',
        details: ['回款金额: ¥50,000.00']
    },
    {
        id: 2,
        type: '状态更新',
        user: '李四',
        timestamp: '2024-02-21 14:20',
        description: '更新回款状态为已收款',
        details: ['状态: 已收款']
    },
    {
        id: 3,
        type: '审核通过',
        user: '王五',
        timestamp: '2024-02-21 16:45',
        description: '回款审核通过',
        details: ['审核人: 王五', '审核时间: 2024-02-21 16:45']
    }
]);

// 获取活动类型对应的时间线类型
const getActivityType = (type: string) => {
    switch (type) {
        case '创建回款':
            return 'primary';
        case '状态更新':
            return 'success';
        case '审核通过':
            return 'success';
        case '审核拒绝':
            return 'danger';
        case '修改':
            return 'warning';
        default:
            return 'info';
    }
};

// 添加活动
const addActivity = () => {
    dialogVisible.value = true;
};

// 保存活动
const saveActivity = () => {
    const activity = {
        id: activities.value.length + 1,
        type: newActivity.value.type,
        user: '当前用户', // 实际应该从用户信息获取
        timestamp: new Date().toLocaleString(),
        description: newActivity.value.description,
        details: []
    };
    
    activities.value.unshift(activity);
    dialogVisible.value = false;
    newActivity.value = { type: '', description: '' };
};
</script>

<style scoped>
.payment-activity-tab {
    padding: 20px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.activity-header h3 {
    margin: 0;
    color: #303133;
}

.activity-timeline {
    margin-top: 20px;
}

.activity-card {
    margin-bottom: 10px;
}

.activity-content {
    padding: 10px;
}

.activity-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.activity-type {
    font-weight: bold;
    color: #409EFF;
}

.activity-user {
    font-size: 12px;
    color: #909399;
}

.activity-description {
    color: #606266;
    margin-bottom: 8px;
}

.activity-details {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}
</style>