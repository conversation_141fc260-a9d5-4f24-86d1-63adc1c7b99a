import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    permissions: ['*:*:*'], // 默认给所有权限，避免组件报错
    roles: ['admin'],
    userInfo: {}
  }),
  
  getters: {
    getPermissions: (state) => state.permissions,
    getRoles: (state) => state.roles,
    getUserInfo: (state) => state.userInfo
  },
  
  actions: {
    setPermissions(permissions) {
      this.permissions = permissions
    },
    
    setRoles(roles) {
      this.roles = roles
    },
    
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    }
  }
})