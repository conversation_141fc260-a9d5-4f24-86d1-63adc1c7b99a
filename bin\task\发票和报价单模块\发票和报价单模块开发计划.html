<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票模块和报价单模块开发计划</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .phase {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .phase h3 {
            color: white;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧾 发票模块和报价单模块开发计划</h1>
        
        <div class="highlight">
            <strong>📋 项目概述：</strong>基于现有CRM系统架构，新增发票模块和报价单模块，实现与客户、联系人的关联管理，并集成Activiti审批流程。
        </div>

        <h2>🎯 一、需求分析</h2>
        
        <h3>1.1 报价单模块需求</h3>
        <div class="success">
            <strong>核心需求：</strong>
            <ul>
                <li><strong>必要字段：</strong>客户、客户联系人（必填）</li>
                <li><strong>关联关系：</strong>负责人、客户、联系人</li>
                <li><strong>审批流程：</strong>需要集成Activiti工作流</li>
                <li><strong>状态管理：</strong>草稿、待审批、已审批、已驳回、已取消</li>
            </ul>
        </div>

        <h3>1.2 发票模块需求</h3>
        <div class="success">
            <strong>基础需求：</strong>
            <ul>
                <li><strong>关联对象：</strong>可关联报价单、客户、联系人</li>
                <li><strong>发票类型：</strong>增值税专用发票、普通发票</li>
                <li><strong>状态管理：</strong>待开票、已开票、已作废</li>
                <li><strong>金额管理：</strong>含税金额、不含税金额、税额</li>
            </ul>
        </div>

        <h2>🏗️ 二、技术架构设计</h2>
        
        <h3>2.1 系统架构</h3>
        <table>
            <tr>
                <th>层次</th>
                <th>技术栈</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>前端层</td>
                <td>Vue.js + Element Plus</td>
                <td>基于现有前端框架</td>
            </tr>
            <tr>
                <td>控制器层</td>
                <td>Spring Boot + REST API</td>
                <td>ruoyi-crm/controller包</td>
            </tr>
            <tr>
                <td>服务层</td>
                <td>Spring Service</td>
                <td>ruoyi-crm/service包</td>
            </tr>
            <tr>
                <td>数据访问层</td>
                <td>MyBatis + MySQL</td>
                <td>ruoyi-crm/mapper包</td>
            </tr>
            <tr>
                <td>工作流引擎</td>
                <td>Activiti 7</td>
                <td>集成现有工作流</td>
            </tr>
        </table>

        <h3>2.2 模块依赖关系</h3>
        <div class="code">
报价单模块 ← 依赖 → 客户模块
报价单模块 ← 依赖 → 联系人模块  
报价单模块 ← 依赖 → 用户模块（负责人）
报价单模块 ← 依赖 → Activiti工作流

发票模块 ← 依赖 → 报价单模块
发票模块 ← 依赖 → 客户模块
发票模块 ← 依赖 → 联系人模块
        </div>

        <h2>📊 三、数据库设计</h2>
        
        <h3>3.1 报价单主表 (crm_quotations)</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>说明</th>
                <th>约束</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
            </tr>
            <tr>
                <td>quotation_no</td>
                <td>VARCHAR(50)</td>
                <td>报价单编号</td>
                <td>NOT NULL, UNIQUE</td>
            </tr>
            <tr>
                <td>quotation_name</td>
                <td>VARCHAR(200)</td>
                <td>报价单名称</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>customer_id</td>
                <td>BIGINT</td>
                <td>客户ID</td>
                <td>NOT NULL, FK</td>
            </tr>
            <tr>
                <td>customer_name</td>
                <td>VARCHAR(100)</td>
                <td>客户名称（冗余）</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>contact_id</td>
                <td>BIGINT</td>
                <td>联系人ID</td>
                <td>NOT NULL, FK</td>
            </tr>
            <tr>
                <td>contact_name</td>
                <td>VARCHAR(100)</td>
                <td>联系人姓名（冗余）</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>responsible_person_id</td>
                <td>VARCHAR(64)</td>
                <td>负责人ID</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>total_amount</td>
                <td>DECIMAL(15,2)</td>
                <td>报价总金额</td>
                <td>DEFAULT 0.00</td>
            </tr>
            <tr>
                <td>status</td>
                <td>VARCHAR(20)</td>
                <td>状态</td>
                <td>DEFAULT 'draft'</td>
            </tr>
            <tr>
                <td>approval_status</td>
                <td>VARCHAR(20)</td>
                <td>审批状态</td>
                <td>DEFAULT 'pending'</td>
            </tr>
            <tr>
                <td>process_instance_id</td>
                <td>VARCHAR(64)</td>
                <td>流程实例ID</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>valid_until</td>
                <td>DATE</td>
                <td>有效期至</td>
                <td>NULL</td>
            </tr>
        </table>

        <h3>3.2 报价单明细表 (crm_quotation_items)</h3>
        <table>
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>说明</th>
                <th>约束</th>
            </tr>
            <tr>
                <td>id</td>
                <td>BIGINT</td>
                <td>主键ID</td>
                <td>NOT NULL, AUTO_INCREMENT</td>
            </tr>
            <tr>
                <td>quotation_id</td>
                <td>BIGINT</td>
                <td>报价单ID</td>
                <td>NOT NULL, FK</td>
            </tr>
            <tr>
                <td>product_name</td>
                <td>VARCHAR(200)</td>
                <td>产品名称</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>specification</td>
                <td>VARCHAR(500)</td>
                <td>规格说明</td>
                <td>NULL</td>
            </tr>
            <tr>
                <td>quantity</td>
                <td>DECIMAL(10,2)</td>
                <td>数量</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>unit</td>
                <td>VARCHAR(20)</td>
                <td>单位</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>unit_price</td>
                <td>DECIMAL(10,2)</td>
                <td>单价</td>
                <td>NOT NULL</td>
            </tr>
            <tr>
                <td>total_price</td>
                <td>DECIMAL(15,2)</td>
                <td>小计</td>
                <td>NOT NULL</td>
            </tr>
        </table>

        <div class="warning">
            <strong>⚠️ 注意：</strong>发票表设计将在下一个文件中详细说明，因为内容较多需要分文件展示。
        </div>

        <h2>🔄 四、审批流程设计</h2>
        
        <h3>4.1 报价单审批流程</h3>
        <div class="phase">
            <h3>流程步骤：</h3>
            <ol>
                <li><strong>提交审批：</strong>业务员创建报价单后提交审批</li>
                <li><strong>部门经理审批：</strong>部门经理审核报价内容和价格</li>
                <li><strong>金额判断：</strong>根据报价金额决定是否需要上级审批</li>
                <li><strong>总经理审批：</strong>大额报价需要总经理最终审批</li>
                <li><strong>审批完成：</strong>更新报价单状态，发送通知</li>
            </ol>
        </div>

        <h3>4.2 流程配置</h3>
        <div class="code">
流程定义Key: quotation-approval
流程名称: 报价单审批流程
审批节点:
- 部门经理审批 (deptManagerApproval)
- 总经理审批 (ceoApproval) - 条件触发
- 状态同步监听器 (quotationStatusListener)
        </div>

        <h2>📅 五、开发计划时间安排</h2>
        
        <table>
            <tr>
                <th>阶段</th>
                <th>任务</th>
                <th>预计工期</th>
                <th>负责内容</th>
            </tr>
            <tr>
                <td>第1周</td>
                <td>数据库设计与创建</td>
                <td>2天</td>
                <td>表结构设计、SQL脚本编写</td>
            </tr>
            <tr>
                <td>第1周</td>
                <td>实体类与Mapper</td>
                <td>3天</td>
                <td>Java实体类、MyBatis映射</td>
            </tr>
            <tr>
                <td>第2周</td>
                <td>服务层开发</td>
                <td>4天</td>
                <td>业务逻辑、CRUD操作</td>
            </tr>
            <tr>
                <td>第2周</td>
                <td>审批流程集成</td>
                <td>1天</td>
                <td>Activiti流程配置</td>
            </tr>
            <tr>
                <td>第3周</td>
                <td>控制器层开发</td>
                <td>3天</td>
                <td>REST API接口</td>
            </tr>
            <tr>
                <td>第3周</td>
                <td>前端页面开发</td>
                <td>2天</td>
                <td>Vue组件、页面布局</td>
            </tr>
            <tr>
                <td>第4周</td>
                <td>测试与优化</td>
                <td>5天</td>
                <td>单元测试、集成测试</td>
            </tr>
        </table>

        <div class="success">
            <strong>✅ 下一步行动：</strong>
            <ol>
                <li>确认报价单字段需求和业务规则</li>
                <li>确认发票模块的具体需求</li>
                <li>开始数据库表结构设计</li>
                <li>准备BPMN流程文件</li>
            </ol>
        </div>

        <div class="highlight">
            <strong>📝 备注：</strong>本计划基于现有CRM系统架构制定，充分利用了现有的客户、联系人、工作流等模块，确保系统的一致性和可维护性。
        </div>
    </div>
</body>
</html>
