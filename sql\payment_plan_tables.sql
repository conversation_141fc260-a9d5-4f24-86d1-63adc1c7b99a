-- 回款计划主表
CREATE TABLE crm_payment_plan (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    plan_number VARCHAR(50) NOT NULL COMMENT '回款计划编号',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    customer_name VARCHAR(100) NOT NULL COMMENT '客户名称',
    responsible_user_id BIGINT NOT NULL COMMENT '负责人ID',
    responsible_user_name VARCHAR(50) NOT NULL COMMENT '负责人姓名',
    total_amount DECIMAL(15,2) NOT NULL COMMENT '总回款金额',
    received_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '已回款金额',
    remaining_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '剩余回款金额',
    opportunity_id BIGINT COMMENT '关联商机ID',
    contact_id BIGINT COMMENT '关联联系人ID',
    contract_number VARCHAR(50) COMMENT '合同编号',
    payment_method VARCHAR(20) NOT NULL COMMENT '回款方式',
    plan_status VARCHAR(20) NOT NULL DEFAULT '草稿' COMMENT '计划状态：草稿、待审批、审批中、已通过、执行中、已完成、已终止',
    approval_status VARCHAR(20) NOT NULL DEFAULT '待提交' COMMENT '审批状态：待提交、审批中、已通过、已拒绝',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000 COMMENT '汇率',
    parent_plan_id BIGINT COMMENT '父计划ID',
    plan_type VARCHAR(20) DEFAULT '普通' COMMENT '计划类型：普通、紧急、特殊',
    risk_level VARCHAR(20) DEFAULT '低' COMMENT '风险等级：低、中、高',
    collection_user_id BIGINT COMMENT '催收负责人ID',
    remarks TEXT COMMENT '备注',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_plan_number (plan_number),
    KEY idx_customer_id (customer_id),
    KEY idx_responsible_user_id (responsible_user_id),
    KEY idx_plan_status (plan_status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回款计划表';

-- 回款分期表
CREATE TABLE crm_payment_installment (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    plan_id BIGINT NOT NULL COMMENT '回款计划ID',
    installment_number INT NOT NULL COMMENT '分期序号',
    installment_name VARCHAR(50) NOT NULL COMMENT '分期名称（头款、实施款、尾款等）',
    installment_amount DECIMAL(15,2) NOT NULL COMMENT '分期金额',
    installment_percentage DECIMAL(5,2) NOT NULL COMMENT '分期比例',
    planned_date DATE NOT NULL COMMENT '计划回款日期',
    actual_date DATE COMMENT '实际回款日期',
    actual_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '实际回款金额',
    overdue_days INT DEFAULT 0 COMMENT '逾期天数',
    penalty_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '违约金',
    payment_voucher VARCHAR(200) COMMENT '付款凭证路径',
    installment_status VARCHAR(20) NOT NULL DEFAULT '待回款' COMMENT '分期状态：待回款、部分回款、已回款、已逾期',
    remarks TEXT COMMENT '备注',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_plan_id (plan_id),
    KEY idx_planned_date (planned_date),
    KEY idx_installment_status (installment_status),
    CONSTRAINT fk_installment_plan FOREIGN KEY (plan_id) REFERENCES crm_payment_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回款分期表';

-- 回款审批表
CREATE TABLE crm_payment_approval (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    plan_id BIGINT NOT NULL COMMENT '回款计划ID',
    approval_level INT NOT NULL COMMENT '审批级别',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审批人姓名',
    approval_status VARCHAR(20) NOT NULL DEFAULT '待审批' COMMENT '审批状态：待审批、已通过、已拒绝、已撤回',
    approval_time DATETIME COMMENT '审批时间',
    approval_comments TEXT COMMENT '审批意见',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_plan_id (plan_id),
    KEY idx_approver_id (approver_id),
    KEY idx_approval_status (approval_status),
    CONSTRAINT fk_approval_plan FOREIGN KEY (plan_id) REFERENCES crm_payment_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回款审批表';

-- 回款记录表（记录实际回款）
CREATE TABLE crm_payment_record (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    plan_id BIGINT NOT NULL COMMENT '回款计划ID',
    installment_id BIGINT COMMENT '分期ID',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '回款金额',
    payment_date DATE NOT NULL COMMENT '回款日期',
    payment_method VARCHAR(20) NOT NULL COMMENT '回款方式',
    payment_account VARCHAR(100) COMMENT '回款账户',
    transaction_number VARCHAR(50) COMMENT '交易流水号',
    payment_voucher VARCHAR(200) COMMENT '付款凭证',
    remarks TEXT COMMENT '备注',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_plan_id (plan_id),
    KEY idx_installment_id (installment_id),
    KEY idx_payment_date (payment_date),
    CONSTRAINT fk_record_plan FOREIGN KEY (plan_id) REFERENCES crm_payment_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回款记录表';

-- 注意：回款计划是前端业务模块，不需要在后端管理系统中添加菜单
-- 权限控制应该通过前端的权限系统实现
-- 如果确实需要后端权限控制，请根据实际需求手动添加相应的权限配置