 @echo off
chcp 65001
echo =============================================
echo 联系人管理模块改进 - 第一阶段SQL脚本执行
echo =============================================

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=crm41
set DB_USER=mycrm41
set DB_PASSWORD=mycrm41

echo.
echo 请确认数据库连接信息：
echo 主机: %DB_HOST%:%DB_PORT%
echo 数据库: %DB_NAME%
echo 用户: %DB_USER%
echo.

echo 开始执行第一阶段SQL脚本...
echo.

echo [1/3] 执行主要改进脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < ../sql/contact_management_improvement_v1.0.sql
if %errorlevel% neq 0 (
    echo 错误：主要改进脚本执行失败！
    pause
    exit /b 1
)
echo ✓ 主要改进脚本执行成功

echo.
echo [2/3] 执行用户层级数据初始化脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < ../sql/init_user_hierarchy_data.sql
if %errorlevel% neq 0 (
    echo 错误：用户层级数据初始化脚本执行失败！
    pause
    exit /b 1
)
echo ✓ 用户层级数据初始化脚本执行成功

echo.
echo [3/3] 验证数据库表结构...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SHOW TABLES LIKE 'crm_user_hierarchy';"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SHOW TABLES LIKE 'crm_contact_followers';"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT COUNT(*) as crm_user_hierarchy_count FROM crm_user_hierarchy;"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT COUNT(*) as crm_contact_followers_count FROM crm_contact_followers;"

echo.
echo =============================================
echo 第一阶段SQL脚本执行完成！
echo =============================================
echo.
echo 完成的任务：
echo ✓ 任务1-1: 创建crm_user_hierarchy表SQL脚本
echo ✓ 任务1-2: 创建crm_contact_followers表SQL脚本  
echo ✓ 任务1-3: 执行数据库表创建脚本
echo ✓ 任务1-4: 编写用户层级关系数据初始化脚本
echo ✓ 任务1-5: 执行数据初始化脚本
echo.
echo 接下来可以执行任务1-6: 验证数据库表结构和数据完整性
echo.
pause