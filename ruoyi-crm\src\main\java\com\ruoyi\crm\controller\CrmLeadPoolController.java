package com.ruoyi.crm.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmLeadPool;
import com.ruoyi.crm.service.ICrmLeadPoolService;

/**
 * 线索池Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/front/crm/leadPool")
public class CrmLeadPoolController extends BaseController {
    
    @Autowired
    private ICrmLeadPoolService crmLeadPoolService;

    /**
     * 查询线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmLeadPool crmLeadPool) {
        startPage();
        List<CrmLeadPool> list = crmLeadPoolService.selectCrmLeadPoolList(crmLeadPool);
        return getDataTable(list);
    }

    /**
     * 查询可用的线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/available")
    public TableDataInfo availableList(CrmLeadPool crmLeadPool) {
        startPage();
        List<CrmLeadPool> list = crmLeadPoolService.selectAvailableLeadPoolList(crmLeadPool);
        return getDataTable(list);
    }

    /**
     * 导出线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:export')")
    @Log(title = "线索池", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmLeadPool crmLeadPool) {
        List<CrmLeadPool> list = crmLeadPoolService.selectCrmLeadPoolList(crmLeadPool);
        ExcelUtil<CrmLeadPool> util = new ExcelUtil<CrmLeadPool>(CrmLeadPool.class);
        util.exportExcel(response, list, "线索池数据");
    }

    /**
     * 获取线索池详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmLeadPoolService.selectCrmLeadPoolById(id));
    }

    /**
     * 新增线索池
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:add')")
    @Log(title = "线索池", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmLeadPool crmLeadPool) {
        return toAjax(crmLeadPoolService.insertCrmLeadPool(crmLeadPool));
    }

    /**
     * 修改线索池
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:edit')")
    @Log(title = "线索池", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmLeadPool crmLeadPool) {
        return toAjax(crmLeadPoolService.updateCrmLeadPool(crmLeadPool));
    }

    /**
     * 删除线索池
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:remove')")
    @Log(title = "线索池", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmLeadPoolService.deleteCrmLeadPoolByIds(ids));
    }

    /**
     * 手动分配线索
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:assign')")
    @Log(title = "线索分配", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assignLeads(@RequestBody AssignLeadsRequest request) {
        int result = crmLeadPoolService.assignLeads(
            request.getPoolIds(), 
            request.getToUserId(), 
            request.getReason()
        );
        return result > 0 ? success("成功分配 " + result + " 条线索") : error("分配失败");
    }

    /**
     * 批量分配线索
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:assign')")
    @Log(title = "批量线索分配", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAssign")
    public AjaxResult batchAssignLeads(@RequestBody BatchAssignLeadsRequest request) {
        int result = crmLeadPoolService.batchAssignLeads(
            request.getPoolIds(), 
            request.getUserIds(), 
            request.getReason()
        );
        return result > 0 ? success("成功分配 " + result + " 条线索") : error("分配失败");
    }

    /**
     * 抢单 - 销售人员主动获取线索
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:grab')")
    @Log(title = "线索抢单", businessType = BusinessType.UPDATE)
    @PostMapping("/grab/{poolId}")
    public AjaxResult grabLead(@PathVariable Long poolId, @RequestBody GrabLeadRequest request) {
        int result = crmLeadPoolService.grabLead(
            poolId, 
            request.getUserId(), 
            request.getReason()
        );
        return result > 0 ? success("抢单成功") : error("抢单失败，线索可能已被分配");
    }

    /**
     * 回收线索到池中
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:recycle')")
    @Log(title = "线索回收", businessType = BusinessType.UPDATE)
    @PostMapping("/recycle")
    public AjaxResult recycleLeads(@RequestBody RecycleLeadsRequest request) {
        int result = crmLeadPoolService.batchRecycleLeads(
            request.getLeadIds(), 
            request.getReason()
        );
        return result > 0 ? success("成功回收 " + result + " 条线索") : error("回收失败");
    }

    /**
     * 添加线索到池中
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:add')")
    @Log(title = "添加线索到池", businessType = BusinessType.INSERT)
    @PostMapping("/addToPool")
    public AjaxResult addToPool(@RequestBody AddToPoolRequest request) {
        int result = crmLeadPoolService.addToPool(
            request.getLeadId(),
            request.getQualityLevel(),
            request.getPriority(),
            request.getRegion(),
            request.getIndustry(),
            request.getEstimatedValue(),
            request.getRemarks()
        );
        return toAjax(result);
    }

    /**
     * 获取线索池统计信息
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/stats")
    public AjaxResult getLeadPoolStats() {
        Map<String, Object> stats = crmLeadPoolService.getLeadPoolStats();
        return success(stats);
    }

    /**
     * 根据质量等级获取线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/quality/{qualityLevel}")
    public AjaxResult getLeadPoolByQualityLevel(@PathVariable String qualityLevel) {
        List<CrmLeadPool> list = crmLeadPoolService.getLeadPoolByQualityLevel(qualityLevel);
        return success(list);
    }

    /**
     * 根据地区获取线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/region/{region}")
    public AjaxResult getLeadPoolByRegion(@PathVariable String region) {
        List<CrmLeadPool> list = crmLeadPoolService.getLeadPoolByRegion(region);
        return success(list);
    }

    /**
     * 根据行业获取线索池列表
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:list')")
    @GetMapping("/industry/{industry}")
    public AjaxResult getLeadPoolByIndustry(@PathVariable String industry) {
        List<CrmLeadPool> list = crmLeadPoolService.getLeadPoolByIndustry(industry);
        return success(list);
    }

    /**
     * 检查线索是否在池中
     */
    @PreAuthorize("@ss.hasPermi('crm:leadPool:query')")
    @GetMapping("/check/{leadId}")
    public AjaxResult checkLeadInPool(@PathVariable Long leadId) {
        boolean inPool = crmLeadPoolService.isLeadInPool(leadId);
        return success(inPool);
    }

    // 内部类定义请求参数
    public static class AssignLeadsRequest {
        private Long[] poolIds;
        private Long toUserId;
        private String reason;
        
        // getters and setters
        public Long[] getPoolIds() { return poolIds; }
        public void setPoolIds(Long[] poolIds) { this.poolIds = poolIds; }
        public Long getToUserId() { return toUserId; }
        public void setToUserId(Long toUserId) { this.toUserId = toUserId; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class BatchAssignLeadsRequest {
        private Long[] poolIds;
        private Long[] userIds;
        private String reason;
        
        // getters and setters
        public Long[] getPoolIds() { return poolIds; }
        public void setPoolIds(Long[] poolIds) { this.poolIds = poolIds; }
        public Long[] getUserIds() { return userIds; }
        public void setUserIds(Long[] userIds) { this.userIds = userIds; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class GrabLeadRequest {
        private Long userId;
        private String reason;
        
        // getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class RecycleLeadsRequest {
        private Long[] leadIds;
        private String reason;
        
        // getters and setters
        public Long[] getLeadIds() { return leadIds; }
        public void setLeadIds(Long[] leadIds) { this.leadIds = leadIds; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class AddToPoolRequest {
        private Long leadId;
        private String qualityLevel;
        private Integer priority;
        private String region;
        private String industry;
        private String estimatedValue;
        private String remarks;
        
        // getters and setters
        public Long getLeadId() { return leadId; }
        public void setLeadId(Long leadId) { this.leadId = leadId; }
        public String getQualityLevel() { return qualityLevel; }
        public void setQualityLevel(String qualityLevel) { this.qualityLevel = qualityLevel; }
        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
        public String getRegion() { return region; }
        public void setRegion(String region) { this.region = region; }
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
        public String getEstimatedValue() { return estimatedValue; }
        public void setEstimatedValue(String estimatedValue) { this.estimatedValue = estimatedValue; }
        public String getRemarks() { return remarks; }
        public void setRemarks(String remarks) { this.remarks = remarks; }
    }
}
