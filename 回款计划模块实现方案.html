<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回款计划模块实现方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .section {
            margin-bottom: 30px;
        }
        .feature-list {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
        }
        .table-structure {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .step {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .step-number {
            font-weight: bold;
            color: #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>回款计划模块实现方案</h1>
        
        <div class="section">
            <h2>1. 模块概述</h2>
            <p>回款计划模块是CRM系统中的重要组成部分，主要用于管理客户的回款计划、跟踪回款进度、处理审批流程等。该模块支持灵活的分期付款设置，可以关联客户、商机、联系人等相关信息。</p>
            
            <div class="highlight">
                <strong>核心功能：</strong>创建回款计划、分期管理、审批流程、进度跟踪、数据统计
            </div>
        </div>

        <div class="section">
            <h2>2. 功能需求分析</h2>
            
            <h3>2.1 基础功能</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>回款计划创建：</strong>支持创建新的回款计划，包含基本信息录入</li>
                    <li><strong>客户关联：</strong>必须关联到具体的客户</li>
                    <li><strong>负责人指定：</strong>为每个回款计划指定负责人</li>
                    <li><strong>回款金额设置：</strong>设定总回款金额和分期金额</li>
                    <li><strong>时间计划：</strong>设置预期回款时间</li>
                    <li><strong>回款方式：</strong>支持多种回款方式（银行转账、现金、支票等）</li>
                </ul>
            </div>

            <h3>2.2 高级功能</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>分期付款：</strong>支持灵活的分期设置（如：头款20%、实施款50%、尾款30%）</li>
                    <li><strong>商机关联：</strong>可选择关联相关商机</li>
                    <li><strong>联系人关联：</strong>可关联相关联系人</li>
                    <li><strong>合同编号：</strong>可选填写相关合同编号</li>
                    <li><strong>审批流程：</strong>支持多级审批流程</li>
                    <li><strong>状态跟踪：</strong>实时跟踪回款状态</li>
                </ul>
            </div>

            <h3>2.3 管理功能</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>列表查看：</strong>支持列表形式查看所有回款计划</li>
                    <li><strong>搜索筛选：</strong>支持多条件搜索和筛选</li>
                    <li><strong>详情查看：</strong>查看回款计划详细信息</li>
                    <li><strong>编辑修改：</strong>支持编辑回款计划信息</li>
                    <li><strong>删除操作：</strong>支持删除回款计划</li>
                    <li><strong>导出功能：</strong>支持数据导出</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>3. 数据库设计</h2>
            
            <h3>3.1 主表：crm_payment_plan（回款计划表）</h3>
            <div class="table-structure">
                <table>
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>类型</th>
                            <th>长度</th>
                            <th>是否必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>id</td><td>BIGINT</td><td>-</td><td>是</td><td>主键ID</td></tr>
                        <tr><td>plan_number</td><td>VARCHAR</td><td>50</td><td>是</td><td>回款计划编号</td></tr>
                        <tr><td>customer_id</td><td>BIGINT</td><td>-</td><td>是</td><td>客户ID</td></tr>
                        <tr><td>customer_name</td><td>VARCHAR</td><td>100</td><td>是</td><td>客户名称</td></tr>
                        <tr><td>responsible_user_id</td><td>BIGINT</td><td>-</td><td>是</td><td>负责人ID</td></tr>
                        <tr><td>responsible_user_name</td><td>VARCHAR</td><td>50</td><td>是</td><td>负责人姓名</td></tr>
                        <tr><td>total_amount</td><td>DECIMAL</td><td>15,2</td><td>是</td><td>总回款金额</td></tr>
                        <tr><td>received_amount</td><td>DECIMAL</td><td>15,2</td><td>否</td><td>已回款金额</td></tr>
                        <tr><td>remaining_amount</td><td>DECIMAL</td><td>15,2</td><td>否</td><td>剩余回款金额</td></tr>
                        <tr><td>opportunity_id</td><td>BIGINT</td><td>-</td><td>否</td><td>关联商机ID</td></tr>
                        <tr><td>contact_id</td><td>BIGINT</td><td>-</td><td>否</td><td>关联联系人ID</td></tr>
                        <tr><td>contract_number</td><td>VARCHAR</td><td>50</td><td>否</td><td>合同编号</td></tr>
                        <tr><td>payment_method</td><td>VARCHAR</td><td>20</td><td>是</td><td>回款方式</td></tr>
                        <tr><td>plan_status</td><td>VARCHAR</td><td>20</td><td>是</td><td>计划状态</td></tr>
                        <tr><td>approval_status</td><td>VARCHAR</td><td>20</td><td>是</td><td>审批状态</td></tr>
                        <tr><td>remarks</td><td>TEXT</td><td>-</td><td>否</td><td>备注</td></tr>
                        <tr><td>create_by</td><td>VARCHAR</td><td>50</td><td>是</td><td>创建人</td></tr>
                        <tr><td>create_time</td><td>DATETIME</td><td>-</td><td>是</td><td>创建时间</td></tr>
                        <tr><td>update_by</td><td>VARCHAR</td><td>50</td><td>否</td><td>更新人</td></tr>
                        <tr><td>update_time</td><td>DATETIME</td><td>-</td><td>否</td><td>更新时间</td></tr>
                    </tbody>
                </table>
            </div>

            <h3>3.2 分期表：crm_payment_installment（回款分期表）</h3>
            <div class="table-structure">
                <table>
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>类型</th>
                            <th>长度</th>
                            <th>是否必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>id</td><td>BIGINT</td><td>-</td><td>是</td><td>主键ID</td></tr>
                        <tr><td>plan_id</td><td>BIGINT</td><td>-</td><td>是</td><td>回款计划ID</td></tr>
                        <tr><td>installment_number</td><td>INT</td><td>-</td><td>是</td><td>分期序号</td></tr>
                        <tr><td>installment_name</td><td>VARCHAR</td><td>50</td><td>是</td><td>分期名称（头款、实施款、尾款等）</td></tr>
                        <tr><td>installment_amount</td><td>DECIMAL</td><td>15,2</td><td>是</td><td>分期金额</td></tr>
                        <tr><td>installment_percentage</td><td>DECIMAL</td><td>5,2</td><td>是</td><td>分期比例</td></tr>
                        <tr><td>planned_date</td><td>DATE</td><td>-</td><td>是</td><td>计划回款日期</td></tr>
                        <tr><td>actual_date</td><td>DATE</td><td>-</td><td>否</td><td>实际回款日期</td></tr>
                        <tr><td>actual_amount</td><td>DECIMAL</td><td>15,2</td><td>否</td><td>实际回款金额</td></tr>
                        <tr><td>installment_status</td><td>VARCHAR</td><td>20</td><td>是</td><td>分期状态</td></tr>
                        <tr><td>remarks</td><td>TEXT</td><td>-</td><td>否</td><td>备注</td></tr>
                        <tr><td>create_time</td><td>DATETIME</td><td>-</td><td>是</td><td>创建时间</td></tr>
                        <tr><td>update_time</td><td>DATETIME</td><td>-</td><td>否</td><td>更新时间</td></tr>
                    </tbody>
                </table>
            </div>

            <h3>3.3 审批流程表：crm_payment_approval（回款审批表）</h3>
            <div class="table-structure">
                <table>
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>类型</th>
                            <th>长度</th>
                            <th>是否必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>id</td><td>BIGINT</td><td>-</td><td>是</td><td>主键ID</td></tr>
                        <tr><td>plan_id</td><td>BIGINT</td><td>-</td><td>是</td><td>回款计划ID</td></tr>
                        <tr><td>approval_level</td><td>INT</td><td>-</td><td>是</td><td>审批级别</td></tr>
                        <tr><td>approver_id</td><td>BIGINT</td><td>-</td><td>是</td><td>审批人ID</td></tr>
                        <tr><td>approver_name</td><td>VARCHAR</td><td>50</td><td>是</td><td>审批人姓名</td></tr>
                        <tr><td>approval_status</td><td>VARCHAR</td><td>20</td><td>是</td><td>审批状态</td></tr>
                        <tr><td>approval_time</td><td>DATETIME</td><td>-</td><td>否</td><td>审批时间</td></tr>
                        <tr><td>approval_comments</td><td>TEXT</td><td>-</td><td>否</td><td>审批意见</td></tr>
                        <tr><td>create_time</td><td>DATETIME</td><td>-</td><td>是</td><td>创建时间</td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="section">
            <h2>4. 前端实现方案</h2>
            
            <h3>4.1 路由配置</h3>
            <div class="step">
                <span class="step-number">步骤1：</span>在 <code>frontend/src/router/index.ts</code> 中添加回款计划路由
            </div>
            <div class="code-block">
{
  path: 'payment-plan',
  name: 'PaymentPlanManagement',
  component: () => import('@/views/PaymentPlanManagement/index.vue'),
  meta: {
    title: '回款计划管理',
    icon: 'Calendar'
  }
}
            </div>

            <h3>4.2 目录结构</h3>
            <div class="step">
                <span class="step-number">步骤2：</span>创建模块目录结构
            </div>
            <div class="code-block">
frontend/src/views/PaymentPlanManagement/
├── index.vue                    # 主页面
├── config/
│   └── index.ts                # 配置文件
├── components/
│   ├── PaymentPlanForm.vue     # 回款计划表单
│   ├── InstallmentForm.vue     # 分期表单
│   └── ApprovalFlow.vue        # 审批流程组件
└── tabs/
    ├── PaymentPlanDetailsTab.vue    # 详情标签页
    ├── InstallmentTab.vue           # 分期标签页
    ├── ApprovalTab.vue              # 审批标签页
    ├── PaymentPlanAttachmentsTab.vue # 附件标签页
    └── PaymentPlanOperationsTab.vue  # 操作记录标签页
            </div>

            <h3>4.3 navConfig 配置</h3>
            <div class="step">
                <span class="step-number">步骤3：</span>配置 side-nav 导航菜单
            </div>
            <div class="code-block">
export const navConfig = {
    title: '回款计划管理',
    menuItems: [
        {
            key: 'plans',
            label: '回款计划',
            icon: 'Calendar'
        },
        {
            key: 'installments',
            label: '分期管理',
            icon: 'List'
        },
        {
            key: 'approvals',
            label: '审批管理',
            icon: 'Check'
        },
        {
            key: 'statistics',
            label: '统计报表',
            icon: 'DataAnalysis'
        }
    ]
};
            </div>

            <h3>4.4 表格配置</h3>
            <div class="step">
                <span class="step-number">步骤4：</span>配置数据表格列
            </div>
            <div class="code-block">
export const tableConfig = {
    columns: [
        { label: '计划编号', field: 'plan_number', width: 150 },
        { label: '客户名称', field: 'customer_name', width: 200 },
        { label: '负责人', field: 'responsible_user_name', width: 120 },
        { label: '总金额', field: 'total_amount', width: 120, type: 'currency' },
        { label: '已回款', field: 'received_amount', width: 120, type: 'currency' },
        { label: '剩余金额', field: 'remaining_amount', width: 120, type: 'currency' },
        { label: '计划状态', field: 'plan_status', width: 100, type: 'tag' },
        { label: '审批状态', field: 'approval_status', width: 100, type: 'tag' },
        { label: '创建时间', field: 'create_time', width: 150, type: 'datetime' }
    ]
};
            </div>

            <h3>4.5 表单配置</h3>
            <div class="step">
                <span class="step-number">步骤5：</span>配置表单字段
            </div>
            <div class="code-block">
export const formConfig = {
    fields: [
        { label: '客户', field: 'customer_id', component: 'customer-select', required: true },
        { label: '负责人', field: 'responsible_user_id', component: 'user-select', required: true },
        { label: '总金额', field: 'total_amount', component: 'el-input-number', required: true },
        { label: '关联商机', field: 'opportunity_id', component: 'opportunity-select' },
        { label: '关联联系人', field: 'contact_id', component: 'contact-select' },
        { label: '合同编号', field: 'contract_number', component: 'el-input' },
        { label: '回款方式', field: 'payment_method', component: 'el-select', required: true },
        { label: '备注', field: 'remarks', component: 'el-input', type: 'textarea' }
    ]
};
            </div>
        </div>

        <div class="section">
            <h2>5. 后端实现方案</h2>
            
            <h3>5.1 模块结构</h3>
            <div class="step">
                <span class="step-number">步骤1：</span>按照项目规范创建后端模块结构
            </div>
            <div class="code-block">
ruoyi-crm/src/main/java/com/ruoyi/crm/
├── controller/
│   ├── CrmPaymentPlanController.java      # 回款计划控制器
│   ├── CrmPaymentInstallmentController.java # 分期控制器
│   └── CrmPaymentApprovalController.java   # 审批控制器
└── service/
    ├── ICrmPaymentPlanService.java         # 回款计划服务接口
    ├── ICrmPaymentInstallmentService.java  # 分期服务接口
    ├── ICrmPaymentApprovalService.java     # 审批服务接口
    └── impl/
        ├── CrmPaymentPlanServiceImpl.java
        ├── CrmPaymentInstallmentServiceImpl.java
        └── CrmPaymentApprovalServiceImpl.java

ruoyi-crm/src/main/java/com/ruoyi/common/
├── domain/
│   ├── CrmPaymentPlan.java                 # 回款计划实体
│   ├── CrmPaymentInstallment.java          # 分期实体
│   └── CrmPaymentApproval.java             # 审批实体
└── mapper/
    ├── CrmPaymentPlanMapper.java           # 回款计划Mapper
    ├── CrmPaymentInstallmentMapper.java    # 分期Mapper
    └── CrmPaymentApprovalMapper.java       # 审批Mapper
            </div>

            <h3>5.2 API接口设计</h3>
            <div class="step">
                <span class="step-number">步骤2：</span>设计RESTful API接口
            </div>
            <div class="table-structure">
                <table>
                    <thead>
                        <tr>
                            <th>接口</th>
                            <th>方法</th>
                            <th>路径</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>获取回款计划列表</td><td>GET</td><td>/crm/paymentPlan/list</td><td>分页查询回款计划</td></tr>
                        <tr><td>获取回款计划详情</td><td>GET</td><td>/crm/paymentPlan/{id}</td><td>根据ID获取详情</td></tr>
                        <tr><td>创建回款计划</td><td>POST</td><td>/crm/paymentPlan</td><td>创建新的回款计划</td></tr>
                        <tr><td>更新回款计划</td><td>PUT</td><td>/crm/paymentPlan</td><td>更新回款计划信息</td></tr>
                        <tr><td>删除回款计划</td><td>DELETE</td><td>/crm/paymentPlan/{ids}</td><td>批量删除回款计划</td></tr>
                        <tr><td>获取分期列表</td><td>GET</td><td>/crm/paymentInstallment/list/{planId}</td><td>获取指定计划的分期</td></tr>
                        <tr><td>创建分期</td><td>POST</td><td>/crm/paymentInstallment</td><td>创建分期记录</td></tr>
                        <tr><td>更新分期</td><td>PUT</td><td>/crm/paymentInstallment</td><td>更新分期信息</td></tr>
                        <tr><td>提交审批</td><td>POST</td><td>/crm/paymentApproval/submit</td><td>提交审批申请</td></tr>
                        <tr><td>审批处理</td><td>POST</td><td>/crm/paymentApproval/process</td><td>处理审批</td></tr>
                    </tbody>
                </table>
            </div>

            <h3>5.3 业务逻辑</h3>
            <div class="step">
                <span class="step-number">步骤3：</span>实现核心业务逻辑
            </div>
            <div class="feature-list">
                <ul>
                    <li><strong>自动编号生成：</strong>回款计划编号自动生成（如：HK202412001）</li>
                    <li><strong>分期金额校验：</strong>确保分期金额总和等于总金额</li>
                    <li><strong>状态管理：</strong>自动更新计划状态和审批状态</li>
                    <li><strong>权限控制：</strong>基于角色的权限控制</li>
                    <li><strong>数据关联：</strong>与客户、商机、联系人等数据关联</li>
                    <li><strong>审批流程：</strong>支持多级审批流程</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>6. 实施步骤</h2>
            
            <div class="step">
                <span class="step-number">第一阶段：</span>数据库设计与创建
                <ul>
                    <li>创建数据库表结构</li>
                    <li>添加索引和约束</li>
                    <li>准备测试数据</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">第二阶段：</span>后端开发
                <ul>
                    <li>创建实体类和Mapper</li>
                    <li>实现Service层业务逻辑</li>
                    <li>开发Controller层API接口</li>
                    <li>编写单元测试</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">第三阶段：</span>前端开发
                <ul>
                    <li>创建页面组件和配置</li>
                    <li>实现表格和表单功能</li>
                    <li>开发分期管理功能</li>
                    <li>实现审批流程界面</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">第四阶段：</span>集成测试
                <ul>
                    <li>前后端接口联调</li>
                    <li>功能测试和bug修复</li>
                    <li>性能优化</li>
                    <li>用户体验优化</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">第五阶段：</span>部署上线
                <ul>
                    <li>生产环境部署</li>
                    <li>数据迁移</li>
                    <li>用户培训</li>
                    <li>监控和维护</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>7. 技术要点</h2>
            
            <h3>7.1 前端技术要点</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>Vue 3 + TypeScript：</strong>使用现代前端技术栈</li>
                    <li><strong>Element Plus：</strong>使用成熟的UI组件库</li>
                    <li><strong>响应式设计：</strong>支持多设备访问</li>
                    <li><strong>状态管理：</strong>使用Pinia进行状态管理</li>
                    <li><strong>路由管理：</strong>使用Vue Router进行页面路由</li>
                </ul>
            </div>

            <h3>7.2 后端技术要点</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>Spring Boot：</strong>基于Spring Boot框架</li>
                    <li><strong>MyBatis：</strong>使用MyBatis进行数据访问</li>
                    <li><strong>事务管理：</strong>确保数据一致性</li>
                    <li><strong>参数校验：</strong>使用JSR-303进行参数校验</li>
                    <li><strong>异常处理：</strong>统一异常处理机制</li>
                </ul>
            </div>

            <h3>7.3 安全要点</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>权限控制：</strong>基于角色的访问控制</li>
                    <li><strong>数据校验：</strong>前后端双重数据校验</li>
                    <li><strong>SQL注入防护：</strong>使用参数化查询</li>
                    <li><strong>XSS防护：</strong>输入输出过滤</li>
                    <li><strong>操作日志：</strong>记录关键操作日志</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>8. 预期效果</h2>
            
            <div class="highlight">
                <h3>功能效果</h3>
                <ul>
                    <li>✅ 支持灵活的回款计划创建和管理</li>
                    <li>✅ 实现分期付款的精细化管理</li>
                    <li>✅ 提供完整的审批流程支持</li>
                    <li>✅ 与现有CRM模块无缝集成</li>
                    <li>✅ 提供直观的数据统计和报表</li>
                </ul>
            </div>

            <div class="highlight">
                <h3>用户体验</h3>
                <ul>
                    <li>🎯 界面简洁直观，操作便捷</li>
                    <li>🎯 支持快速搜索和筛选</li>
                    <li>🎯 提供实时状态更新</li>
                    <li>🎯 支持批量操作提高效率</li>
                    <li>🎯 响应式设计适配多设备</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>9. 总结</h2>
            <p>回款计划模块是CRM系统的重要组成部分，通过系统化的设计和实现，将为用户提供完整的回款管理解决方案。该模块不仅满足基本的回款计划管理需求，还支持复杂的分期付款和审批流程，能够显著提升企业的回款管理效率和规范性。</p>
            
            <p>整个实施过程预计需要2-3周时间，包括数据库设计、后端开发、前端开发、测试和部署等环节。通过分阶段实施，可以确保项目的稳定推进和质量保证。</p>
        </div>
    </div>
</body>
</html>