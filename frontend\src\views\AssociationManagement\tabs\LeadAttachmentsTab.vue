<template>
  <div class="lead-attachments-tab">
    <transition name="fade-up">
      <div class="upload-container">
        <el-upload
          class="upload-area"
          :action="uploadUrl"
          :headers="headers"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :on-error="handleFileError"
          :before-upload="beforeUpload"
          :data="uploadData"
          multiple
        >
          <template #trigger>
            <el-button type="primary" class="custom-button">选择文件</el-button>
          </template>
          <el-button class="ml-3 custom-button" type="success" @click="submitUpload">
            上传到服务器
          </el-button>
        </el-upload>
      </div>
    </transition>

    <transition name="fade-up" :duration="400">
      <div class="attachment-list">
        <el-table
          v-loading="loading"
          :data="attachments"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
          class="custom-table"
        >
          <el-table-column prop="fileName" label="文件名" min-width="250">
            <template #default="{ row }">
              <el-link type="primary" :underline="false" class="file-link">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                {{ row.fileName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="fileSize" label="大小" min-width="120">
            <template #default="{ row }">
              {{ formatFileSize(row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="上传时间" min-width="180">
            <template #default="{ row }">
              {{ parseTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="上传人" min-width="120" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" class="action-button" @click="downloadFile(row)">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>
                <el-button link type="danger" class="action-button" @click="deleteFile(row)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { deleteFile as deleteFileApi, downloadFile as downloadFileApi, listModuleFiles, StaticFileRecord } from '@/api/crm/staticfile';
import { LeadEntity } from '@/types/entity';
import { getToken } from '@/utils/auth';
import { parseTime } from '@/utils/ruoyi';
import { Delete, Document, Download } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onActivated, onMounted, ref, watch } from 'vue';

interface Props {
    entityData: LeadEntity;
}

const props = defineProps<Props>();

defineOptions({
  name: 'LeadAttachmentsTab'
});

// 状态定义
const loading = ref(false);
const attachments = ref<StaticFileRecord[]>([]);
const uploadUrl = process.env.VUE_APP_BASE_API + '/crm/staticfile/upload';
const headers = {
    Authorization: 'Bearer ' + getToken()
};
const uploadData = {
    moduleType: 'lead',
    moduleId: props.entityData.id
};

// 表格样式
const headerCellStyle = {
  background: 'rgba(247, 248, 250, 0.9)',
  color: '#333',
  fontWeight: '600',
  borderBottom: '2px solid #e8e8e8',
  padding: '16px',
  backdropFilter: 'blur(10px)'
};

const cellStyle = {
  padding: '16px'
};

// 监听leadId变化
watch(() => props.entityData.id, (newId) => {
  console.log('LeadAttachmentsTab newId', newId);
  if (newId) {
    getFileList();
  }
});

// 获取文件列表
const getFileList = () => {
  loading.value = true;
  listModuleFiles('lead', Number(props.entityData.id))
    .then(response => {
      // 解构出 data
      console.log('getFileList response === :', response);  
      if (response.code === 200) {
        attachments.value = response.data || [];  // 注意这里是 data.data
      } else {
        ElMessage.error(response.msg || '获取文件列表失败');
      }
    })
    .catch(error => {
      console.error('获取文件列表失败:', error);
      ElMessage.error('获取文件列表失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 文件上传相关方法
const handleFileUploadProgress = () => {
  loading.value = true;
};

const handleFileSuccess = (response: any) => {
  loading.value = false;
  if (response.code === 200) {
    ElMessage.success('上传成功');
    getFileList(); // 刷新文件列表
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

const handleFileError = () => {
  loading.value = false;
  ElMessage.error('上传失败');
};

const beforeUpload = (file: File) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 实现缺失的submitUpload方法
const submitUpload = () => {
  console.log('开始上传文件到服务器');
  const uploadRef = document.querySelector('.upload-area');
  if (uploadRef) {
    (uploadRef as any).__vue__?.submit();
  }
};

// 下载文件
const downloadFile = async (file: any) => {
  try {
    const response = await downloadFileApi(file.id);

    // const blob = new Blob([response]);
    // const link = document.createElement('a');
    // link.href = window.URL.createObjectURL(blob);
    // link.download = file.fileName;
    // link.click();
    // window.URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('下载文件失败:', error);
    ElMessage.error('下载文件失败');
  }
};

// 删除文件
const deleteFile = async (file: any) => {
  try {
    await ElMessageBox.confirm('确认删除该文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await deleteFileApi(file.id);
    // if (response.code === 200) {
    //   ElMessage.success('删除成功');
    //   getFileList(); // 刷新文件列表
    // } else {
    //   ElMessage.error(response.msg || '删除失败');
    // }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error);
      ElMessage.error('删除文件失败');
    }
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 初始化
onMounted(() => {
  console.log('LeadAttachmentsTab 组件挂载');

});

// 组件激活时输出日志
onActivated(() => {
  console.log('LeadAttachmentsTab 组件被激活');
  if (props.entityData.id) {
    getFileList();
  }
});
</script>

<style scoped>
.lead-attachments-tab {
  padding: 20px;
}

.upload-container {
  background: rgba(255, 255, 255, 0.95);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  animation: slideDown 0.3s ease-out;
}

.custom-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.attachment-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  animation: slideUp 0.3s ease-out;
}

.custom-table {
  --el-table-border-color: rgba(0, 0, 0, 0.05);
  --el-table-header-bg-color: #f7f8fa;
  --el-table-row-hover-bg-color: #f5f7fa;
}

.file-link {
  display: flex;
  align-items: center;
  font-size: 14px;
  
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }
}

/* 过渡动画 */
.fade-up-enter-active,
.fade-up-leave-active {
  transition: all 0.4s ease;
  transform-origin: top;
}

.fade-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.fade-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>