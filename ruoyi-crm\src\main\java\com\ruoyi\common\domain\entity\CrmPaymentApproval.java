package com.ruoyi.common.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 回款审批对象 crm_payment_approval
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmPaymentApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 回款计划ID */
    @Excel(name = "回款计划ID")
    private Long planId;

    /** 审批级别 */
    @Excel(name = "审批级别")
    private Integer approvalLevel;

    /** 审批人ID */
    @Excel(name = "审批人ID")
    private Long approverId;

    /** 审批人姓名 */
    @Excel(name = "审批人姓名")
    private String approverName;

    /** 审批状态 */
    @Excel(name = "审批状态")
    private String approvalStatus;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审批意见 */
    @Excel(name = "审批意见")
    private String approvalComments;

    /** 删除标志 */
    private String delFlag;
}