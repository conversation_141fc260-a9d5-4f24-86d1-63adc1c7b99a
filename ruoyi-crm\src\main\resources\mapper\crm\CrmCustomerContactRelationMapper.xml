<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerContactRelationMapper">
    
    <resultMap type="CrmCustomerContactRelation" id="CrmCustomerContactRelationResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="relationType" column="relation_type"/>
        <result property="isPrimary" column="is_primary"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="CrmContacts" id="CrmContactsResult">
        <result property="id" column="id"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="position" column="position"/>
        <result property="isKeyDecisionMaker" column="is_key_decision_maker"/>
        <result property="directSuperior" column="direct_superior"/>
        <result property="address" column="address"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="nextContactTime" column="next_contact_time"/>
        <result property="selectedDate" column="selected_date"/>
        <result property="gender" column="gender"/>
        <result property="remarks" column="remarks"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="CrmCustomer" id="CrmCustomerResult">
        <result property="id" column="id"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerSource" column="customer_source"/>
        <result property="mobile" column="mobile"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="website" column="website"/>
        <result property="customerIndustry" column="customer_industry"/>
        <result property="customerLevel" column="customer_level"/>
        <result property="customerAddress" column="customer_address"/>
        <result property="primaryContact" column="primary_contact"/>
        <result property="dealStatus" column="deal_status"/>
        <result property="nextContactTime" column="next_contact_time"/>
        <result property="selectedDate" column="selected_date"/>
        <result property="remarks" column="remarks"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCrmCustomerContactRelationVo">
        select id, customer_id, contact_id, relation_type, is_primary, start_date, end_date, 
        status, remarks, del_flag, create_by, create_time, update_by, update_time
        from crm_customer_contact_relations
    </sql>

    <select id="selectCrmCustomerContactRelationList" parameterType="CrmCustomerContactRelation" resultMap="CrmCustomerContactRelationResult">
        <include refid="selectCrmCustomerContactRelationVo"/>
        <where>  
            <if test="customerId != null"> and customer_id = #{customerId}</if>
            <if test="contactId != null"> and contact_id = #{contactId}</if>
            <if test="relationType != null and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="isPrimary != null and isPrimary != ''"> and is_primary = #{isPrimary}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="delFlag != null"> and del_flag = #{delFlag}</if>
        </where>
    </select>
    
    <select id="selectCrmCustomerContactRelationById" parameterType="Long" resultMap="CrmCustomerContactRelationResult">
        <include refid="selectCrmCustomerContactRelationVo"/>
        where id = #{id}
    </select>

    <select id="selectRelationByCustomerAndContact" resultMap="CrmCustomerContactRelationResult">
        <include refid="selectCrmCustomerContactRelationVo"/>
        where customer_id = #{customerId} and contact_id = #{contactId} and del_flag = '0'
    </select>

    <select id="selectContactsByCustomerId" parameterType="Long" resultMap="CrmContactsResult">
        select c.id, c.responsible_person_id, c.name, c.mobile, c.phone, c.email, c.position, 
        c.is_key_decision_maker, c.direct_superior, c.address, c.detailed_address, c.next_contact_time, 
        c.selected_date, c.gender, c.remarks, c.del_flag, c.create_by, c.create_time, c.update_by, c.update_time,
        r.relation_type, r.is_primary
        from crm_business_contacts c
        inner join crm_customer_contact_relations r on c.id = r.contact_id
        where r.customer_id = #{customerId} and c.del_flag = '0' and r.del_flag = '0'
    </select>

    <select id="selectCustomersByContactId" parameterType="Long" resultMap="CrmCustomerResult">
        select cu.id, cu.responsible_person_id, cu.customer_name, cu.customer_source, cu.mobile, cu.phone, 
        cu.email, cu.website, cu.customer_industry, cu.customer_level, cu.customer_address, cu.primary_contact, 
        cu.deal_status, cu.next_contact_time, cu.selected_date, cu.remarks, cu.status, cu.del_flag, 
        cu.create_by, cu.create_time, cu.update_by, cu.update_time,
        r.relation_type, r.is_primary
        from crm_business_customers cu
        inner join crm_customer_contact_relations r on cu.id = r.customer_id
        where r.contact_id = #{contactId} and cu.del_flag = '0' and r.del_flag = '0'
    </select>
        
    <insert id="insertCrmCustomerContactRelation" parameterType="CrmCustomerContactRelation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_customer_contact_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="isPrimary != null">is_primary,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="isPrimary != null">#{isPrimary},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertCrmCustomerContactRelation" parameterType="java.util.List">
        insert into crm_customer_contact_relations (customer_id, contact_id, relation_type, is_primary, 
        start_date, end_date, status, remarks, del_flag, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.contactId}, #{item.relationType}, #{item.isPrimary}, 
            #{item.startDate}, #{item.endDate}, #{item.status}, #{item.remarks}, #{item.delFlag}, 
            #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateCrmCustomerContactRelation" parameterType="CrmCustomerContactRelation">
        update crm_customer_contact_relations
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="isPrimary != null">is_primary = #{isPrimary},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmCustomerContactRelationById" parameterType="Long">
        update crm_customer_contact_relations set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmCustomerContactRelationByIds" parameterType="String">
        update crm_customer_contact_relations set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCrmCustomerContactRelationByCustomerId" parameterType="Long">
        update crm_customer_contact_relations set del_flag = '2' where customer_id = #{customerId}
    </delete>

    <delete id="deleteCrmCustomerContactRelationByContactId" parameterType="Long">
        update crm_customer_contact_relations set del_flag = '2' where contact_id = #{contactId}
    </delete>
</mapper>