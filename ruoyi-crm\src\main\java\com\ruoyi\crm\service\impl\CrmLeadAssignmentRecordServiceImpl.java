package com.ruoyi.crm.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import com.ruoyi.common.mapper.CrmLeadAssignmentRecordMapper;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.DateUtils;

/**
 * 线索分配记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class CrmLeadAssignmentRecordServiceImpl implements ICrmLeadAssignmentRecordService {
    
    @Autowired
    private CrmLeadAssignmentRecordMapper crmLeadAssignmentRecordMapper;

    /**
     * 查询线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 线索分配记录
     */
    @Override
    public CrmLeadAssignmentRecord selectCrmLeadAssignmentRecordById(Long id) {
        return crmLeadAssignmentRecordMapper.selectCrmLeadAssignmentRecordById(id);
    }

    /**
     * 查询线索分配记录列表
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 线索分配记录
     */
    @Override
    public List<CrmLeadAssignmentRecord> selectCrmLeadAssignmentRecordList(CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        return crmLeadAssignmentRecordMapper.selectCrmLeadAssignmentRecordList(crmLeadAssignmentRecord);
    }

    /**
     * 新增线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    @Override
    public int insertCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        crmLeadAssignmentRecord.setCreateTime(DateUtils.getNowDate());
        crmLeadAssignmentRecord.setCreateBy(SecurityUtils.getUsername());
        return crmLeadAssignmentRecordMapper.insertCrmLeadAssignmentRecord(crmLeadAssignmentRecord);
    }

    /**
     * 修改线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    @Override
    public int updateCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord) {
        crmLeadAssignmentRecord.setUpdateTime(DateUtils.getNowDate());
        crmLeadAssignmentRecord.setUpdateBy(SecurityUtils.getUsername());
        return crmLeadAssignmentRecordMapper.updateCrmLeadAssignmentRecord(crmLeadAssignmentRecord);
    }

    /**
     * 批量删除线索分配记录
     * 
     * @param ids 需要删除的线索分配记录主键
     * @return 结果
     */
    @Override
    public int deleteCrmLeadAssignmentRecordByIds(Long[] ids) {
        return crmLeadAssignmentRecordMapper.deleteCrmLeadAssignmentRecordByIds(ids);
    }

    /**
     * 删除线索分配记录信息
     * 
     * @param id 线索分配记录主键
     * @return 结果
     */
    @Override
    public int deleteCrmLeadAssignmentRecordById(Long id) {
        return crmLeadAssignmentRecordMapper.deleteCrmLeadAssignmentRecordById(id);
    }

    /**
     * 根据线索ID查询分配记录列表
     * 
     * @param leadId 线索ID
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByLeadId(Long leadId) {
        return crmLeadAssignmentRecordMapper.selectRecordsByLeadId(leadId);
    }

    /**
     * 根据用户ID查询分配记录列表（作为分配对象）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByToUserId(Long userId) {
        return crmLeadAssignmentRecordMapper.selectRecordsByToUserId(userId);
    }

    /**
     * 根据用户ID查询分配记录列表（作为原负责人）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByFromUserId(Long userId) {
        return crmLeadAssignmentRecordMapper.selectRecordsByFromUserId(userId);
    }

    /**
     * 根据操作人ID查询分配记录列表
     * 
     * @param operatorId 操作人ID
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByOperatorId(Long operatorId) {
        return crmLeadAssignmentRecordMapper.selectRecordsByOperatorId(operatorId);
    }

    /**
     * 根据分配类型查询分配记录列表
     * 
     * @param assignmentType 分配类型
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByAssignmentType(String assignmentType) {
        return crmLeadAssignmentRecordMapper.selectRecordsByAssignmentType(assignmentType);
    }

    /**
     * 根据时间范围查询分配记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配记录集合
     */
    @Override
    public List<CrmLeadAssignmentRecord> getRecordsByTimeRange(Date startTime, Date endTime) {
        return crmLeadAssignmentRecordMapper.selectRecordsByTimeRange(startTime, endTime);
    }

    /**
     * 创建手动分配记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param toUserId 分配给的用户ID
     * @param reason 分配原因
     * @return 结果
     */
    @Override
    public int createManualAssignmentRecord(Long leadId, Long poolId, Long toUserId, String reason) {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserName = SecurityUtils.getUsername();
        
        CrmLeadAssignmentRecord record = CrmLeadAssignmentRecord.createManualAssignment(
            leadId, poolId, toUserId, reason, currentUserId, currentUserName);
        
        return insertCrmLeadAssignmentRecord(record);
    }

    /**
     * 创建抢单记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param toUserId 抢单用户ID
     * @param reason 抢单原因
     * @return 结果
     */
    @Override
    public int createGrabAssignmentRecord(Long leadId, Long poolId, Long toUserId, String reason) {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserName = SecurityUtils.getUsername();
        
        CrmLeadAssignmentRecord record = CrmLeadAssignmentRecord.createGrabAssignment(
            leadId, poolId, toUserId, reason, currentUserId, currentUserName);
        
        return insertCrmLeadAssignmentRecord(record);
    }

    /**
     * 创建回收记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param fromUserId 原负责人ID
     * @param reason 回收原因
     * @return 结果
     */
    @Override
    public int createRecycleRecord(Long leadId, Long poolId, Long fromUserId, String reason) {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserName = SecurityUtils.getUsername();
        
        CrmLeadAssignmentRecord record = CrmLeadAssignmentRecord.createRecycleRecord(
            leadId, poolId, fromUserId, reason, currentUserId, currentUserName);
        
        return insertCrmLeadAssignmentRecord(record);
    }

    /**
     * 获取分配记录统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getAssignmentRecordStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总记录数
        CrmLeadAssignmentRecord query = new CrmLeadAssignmentRecord();
        int totalCount = crmLeadAssignmentRecordMapper.countAssignmentRecord(query);
        stats.put("totalCount", totalCount);
        
        // 手动分配数量
        query.setAssignmentType("manual");
        int manualCount = crmLeadAssignmentRecordMapper.countAssignmentRecord(query);
        stats.put("manualCount", manualCount);
        
        // 抢单数量
        query.setAssignmentType("grab");
        int grabCount = crmLeadAssignmentRecordMapper.countAssignmentRecord(query);
        stats.put("grabCount", grabCount);
        
        // 回收数量
        query.setAssignmentType("recycle");
        int recycleCount = crmLeadAssignmentRecordMapper.countAssignmentRecord(query);
        stats.put("recycleCount", recycleCount);
        
        // 按分配类型统计
        List<CrmLeadAssignmentRecord> typeStats = crmLeadAssignmentRecordMapper.countRecordsByAssignmentType();
        stats.put("typeStats", typeStats);
        
        // 按用户统计
        List<CrmLeadAssignmentRecord> userStats = crmLeadAssignmentRecordMapper.countRecordsByToUser();
        stats.put("userStats", userStats);
        
        // 按操作人统计
        List<CrmLeadAssignmentRecord> operatorStats = crmLeadAssignmentRecordMapper.countRecordsByOperator();
        stats.put("operatorStats", operatorStats);
        
        return stats;
    }

    /**
     * 统计各分配类型的记录数量
     * 
     * @return 统计结果
     */
    @Override
    public List<CrmLeadAssignmentRecord> countRecordsByAssignmentType() {
        return crmLeadAssignmentRecordMapper.countRecordsByAssignmentType();
    }

    /**
     * 统计各用户的分配数量（作为分配对象）
     * 
     * @return 统计结果
     */
    @Override
    public List<CrmLeadAssignmentRecord> countRecordsByToUser() {
        return crmLeadAssignmentRecordMapper.countRecordsByToUser();
    }

    /**
     * 统计各操作人的操作数量
     * 
     * @return 统计结果
     */
    @Override
    public List<CrmLeadAssignmentRecord> countRecordsByOperator() {
        return crmLeadAssignmentRecordMapper.countRecordsByOperator();
    }

    /**
     * 统计指定时间范围内的分配数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配数量
     */
    @Override
    public int countRecordsByTimeRange(Date startTime, Date endTime) {
        return crmLeadAssignmentRecordMapper.countRecordsByTimeRange(startTime, endTime);
    }

    /**
     * 获取最近的分配记录
     * 
     * @param leadId 线索ID
     * @return 最近的分配记录
     */
    @Override
    public CrmLeadAssignmentRecord getLatestRecordByLeadId(Long leadId) {
        return crmLeadAssignmentRecordMapper.selectLatestRecordByLeadId(leadId);
    }
}
