import type { Component } from "vue";

// 抽屉字段配置接口
export interface DrawerField {
    label: string;
    field: string;
}

// 抽屉操作按钮配置接口
export interface DrawerAction {
    label: string;
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    icon?: string;
    size?: 'small' | 'default' | 'large';
    disabled?: boolean;
    handler: (data: any) => void;
}

// 抽屉菜单项配置接口
export interface MenuItem {
    key: string;
    label: string;
    icon: string;
    component?: Component;
    badge?: boolean;
}

// 抽屉配置接口
export interface DrawerConfig {
    menuItems: MenuItem[];
} 