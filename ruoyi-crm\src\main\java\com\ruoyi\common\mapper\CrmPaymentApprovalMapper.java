package com.ruoyi.common.mapper;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;
import org.apache.ibatis.annotations.Param;

/**
 * 回款审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface CrmPaymentApprovalMapper 
{
    /**
     * 查询回款审批
     * 
     * @param id 回款审批主键
     * @return 回款审批
     */
    public CrmPaymentApproval selectCrmPaymentApprovalById(Long id);

    /**
     * 查询回款审批列表
     * 
     * @param crmPaymentApproval 回款审批
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectCrmPaymentApprovalList(CrmPaymentApproval crmPaymentApproval);

    /**
     * 根据计划ID查询审批列表
     * 
     * @param planId 计划ID
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectApprovalsByPlanId(Long planId);

    /**
     * 新增回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    public int insertCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval);

    /**
     * 批量新增回款审批
     * 
     * @param approvals 回款审批列表
     * @return 结果
     */
    public int batchInsertApprovals(@Param("list") List<CrmPaymentApproval> approvals);

    /**
     * 修改回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    public int updateCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval);

    /**
     * 删除回款审批
     * 
     * @param id 回款审批主键
     * @return 结果
     */
    public int deleteCrmPaymentApprovalById(Long id);

    /**
     * 批量删除回款审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmPaymentApprovalByIds(Long[] ids);

    /**
     * 根据计划ID删除审批
     * 
     * @param planId 计划ID
     * @return 结果
     */
    public int deleteApprovalsByPlanId(Long planId);

    /**
     * 查询待审批的记录
     * 
     * @param approverId 审批人ID
     * @return 回款审批集合
     */
    public List<CrmPaymentApproval> selectPendingApprovals(@Param("approverId") Long approverId);

    /**
     * 获取当前审批级别
     * 
     * @param planId 计划ID
     * @return 当前审批级别
     */
    public Integer getCurrentApprovalLevel(@Param("planId") Long planId);
}