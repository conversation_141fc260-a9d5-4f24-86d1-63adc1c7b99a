<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wechatwork.mapper.ChatMessageMapper">
    <resultMap id="ChatMessageResult" type="com.ruoyi.wechatwork.domain.ChatMessage">
        <id property="msgId" column="msg_id"/>
        <result property="fromUserId" column="from_user_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="msgType" column="msg_type"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="roomId" column="room_id"/>
        <result property="sequence" column="sequence"/>
    </resultMap>
    <insert id="insertChatMessage" parameterType="com.ruoyi.wechatwork.domain.ChatMessage">
        INSERT INTO chat_message (
            msg_id, from_user_id, to_user_id, msg_type, 
            content, create_time, room_id, sequence
        ) VALUES (
            #{msgId}, #{fromUserId}, #{toUserId}, #{msgType}, 
            #{content}, #{createTime}, #{roomId}, #{sequence}
        )
    </insert>
    
    <insert id="batchInsertChatMessages" parameterType="java.util.List">
        INSERT INTO chat_message (
            msg_id, from_user_id, to_user_id, msg_type, 
            content, create_time, room_id, sequence
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.msgId}, #{item.fromUserId}, #{item.toUserId}, #{item.msgType},
                #{item.content}, #{item.createTime}, #{item.roomId}, #{item.sequence}
            )
        </foreach>
    </insert>
    
    <select id="selectChatMessages" resultType="com.ruoyi.wechatwork.domain.ChatMessage">
        SELECT * FROM chat_message 
        WHERE ((from_user_id = #{fromUserId} AND to_user_id = #{toUserId})
           OR (from_user_id = #{toUserId} AND to_user_id = #{fromUserId}))
          AND create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time ASC
    </select>
    
    <select id="selectRoomMessages" resultType="com.ruoyi.wechatwork.domain.ChatMessage">
        SELECT * FROM chat_message 
        WHERE room_id = #{roomId}
          AND create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time ASC
    </select>
    
</mapper> 