﻿/*
 * 所有SYS系统表合并文件
 * 提取时间: 2025-07-23 19:12:58
 * 包含17个sys开头的表结构和数据
 * 来源: crm41.sql
 */

-- ========================================
-- 1. sys_config
-- ========================================
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-09-09 15:12:02', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------


-- ========================================
-- 2. sys_dept
-- ========================================
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '逗你玩科技', 0, '逗你玩', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', 'admin', '2024-09-09 16:39:11');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '***********', '<EMAIL>', '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL);

-- ----------------------------


-- ========================================
-- 3. sys_dict_type
-- ========================================
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (11, '拜访计划状态', 'visit_plan_status', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态列表');
INSERT INTO `sys_dict_type` VALUES (12, '提醒类型', 'reminder_type', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划提醒类型列表');
INSERT INTO `sys_dict_type` VALUES (13, '提醒状态', 'reminder_status', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划提醒状态列表');
INSERT INTO `sys_dict_type` VALUES (14, '拜访计划操作类型', 'visit_plan_operation_type', '0', 'admin', '2025-06-30 21:37:24', '', NULL, '拜访计划操作类型列表');

-- ----------------------------


-- ========================================
-- 4. sys_dict_data
-- ========================================
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-09-09 15:12:02', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (30, 1, '计划中', 'planned', 'visit_plan_status', '', 'primary', 'Y', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态-计划中');
INSERT INTO `sys_dict_data` VALUES (31, 2, '进行中', 'ongoing', 'visit_plan_status', '', 'warning', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态-进行中');
INSERT INTO `sys_dict_data` VALUES (32, 3, '已完成', 'completed', 'visit_plan_status', '', 'success', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态-已完成');
INSERT INTO `sys_dict_data` VALUES (33, 4, '已延期', 'postponed', 'visit_plan_status', '', 'info', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态-已延期');
INSERT INTO `sys_dict_data` VALUES (34, 5, '已取消', 'cancelled', 'visit_plan_status', '', 'danger', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '拜访计划状态-已取消');
INSERT INTO `sys_dict_data` VALUES (35, 1, '系统内提醒', 'system', 'reminder_type', '', 'primary', 'Y', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '系统内消息提醒');
INSERT INTO `sys_dict_data` VALUES (36, 2, '企业微信', 'wechat', 'reminder_type', '', 'success', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '企业微信提醒');
INSERT INTO `sys_dict_data` VALUES (37, 3, '短信提醒', 'sms', 'reminder_type', '', 'warning', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '短信提醒');
INSERT INTO `sys_dict_data` VALUES (38, 4, '邮件提醒', 'email', 'reminder_type', '', 'info', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '邮件提醒');
INSERT INTO `sys_dict_data` VALUES (39, 1, '待发送', 'pending', 'reminder_status', '', 'info', 'Y', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '提醒待发送');
INSERT INTO `sys_dict_data` VALUES (40, 2, '已发送', 'sent', 'reminder_status', '', 'success', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '提醒已发送');
INSERT INTO `sys_dict_data` VALUES (41, 3, '发送失败', 'failed', 'reminder_status', '', 'danger', 'N', '0', 'admin', '2025-06-30 21:36:27', '', NULL, '提醒发送失败');
INSERT INTO `sys_dict_data` VALUES (42, 1, '创建', 'create', 'visit_plan_operation_type', '', 'primary', 'Y', '0', 'admin', '2025-06-30 21:37:25', '', NULL, '创建拜访计划');
INSERT INTO `sys_dict_data` VALUES (43, 2, '更新', 'update', 'visit_plan_operation_type', '', 'info', 'N', '0', 'admin', '2025-06-30 21:37:25', '', NULL, '更新拜访计划');
INSERT INTO `sys_dict_data` VALUES (44, 3, '延期', 'postpone', 'visit_plan_operation_type', '', 'warning', 'N', '0', 'admin', '2025-06-30 21:37:25', '', NULL, '延期拜访计划');
INSERT INTO `sys_dict_data` VALUES (45, 4, '取消', 'cancel', 'visit_plan_operation_type', '', 'danger', 'N', '0', 'admin', '2025-06-30 21:37:25', '', NULL, '取消拜访计划');
INSERT INTO `sys_dict_data` VALUES (46, 5, '完成', 'complete', 'visit_plan_operation_type', '', 'success', 'N', '0', 'admin', '2025-06-30 21:37:25', '', NULL, '完成拜访计划');

-- ----------------------------


-- ========================================
-- 5. sys_user
-- ========================================
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '***********', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-09 16:21:41', 'admin', '2024-09-09 15:12:01', '', '2025-07-09 16:21:40', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2024-09-09 15:12:01', 'admin', '2024-09-09 15:12:01', '', NULL, '测试员');
INSERT INTO `sys_user` VALUES (3, NULL, 'zhuyunsong', 'zhuyunsong', '00', '', '', '0', '', '$2a$10$vnKIGZmx6WaB9ToGpneC0eGKZ7dkiv0gMcTEX1b/AoCZHR5Soi2vW', '0', '0', '', NULL, 'system', '2025-03-02 22:05:38', '', NULL, NULL);

-- ----------------------------


-- ========================================
-- 6. sys_role
-- ========================================
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2024-09-09 15:12:01', '', NULL, '普通角色');
INSERT INTO `sys_role` VALUES (100, '超级管理员', 'super_admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-06-28 16:54:47', '', NULL, '拥有系统全部数据访问权限');
INSERT INTO `sys_role` VALUES (101, '团队管理员', 'team_admin', 2, '4', 1, 1, '0', '0', 'admin', '2025-06-28 16:54:47', '', NULL, '管理本部门及下属部门的联系人团队');

-- ----------------------------


-- ========================================
-- 7. sys_menu
-- ========================================
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int(1) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1204 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 11, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-09-09 15:12:01', 'admin', '2024-09-11 20:58:02', '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-09-09 15:12:01', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-09-09 15:12:01', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (4, '若依官网', 0, 4, 'http://ruoyi.vip', NULL, '', '', 0, 0, 'F', '0', '1', '', 'guide', 'admin', '2024-09-09 15:12:01', 'admin', '2024-09-09 16:32:08', '若依官网地址');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-09-09 15:12:01', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-09-09 15:12:01', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-09-09 15:12:01', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-09-09 15:12:01', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-09-09 15:12:01', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-09-09 15:12:01', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2024-09-09 15:12:01', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-09-09 15:12:01', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-09-09 15:12:01', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-09-09 15:12:01', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2024-09-09 15:12:01', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2024-09-09 15:12:01', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2024-09-09 15:12:01', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2024-09-09 15:12:01', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2024-09-09 15:12:01', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-09-09 15:12:01', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '1', '1', 'tool:gen:list', 'code', 'admin', '2024-09-09 15:12:01', 'admin', '2024-10-30 22:54:17', '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2024-09-09 15:12:01', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2024-09-09 15:12:01', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2024-09-09 15:12:01', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1061, '线索和用户关联', 1097, 1, 'associations', 'crm/associations/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:associations:list', 'dict', 'admin', '2024-09-11 20:49:46', 'admin', '2024-09-11 20:57:01', '线索和用户关联菜单');
INSERT INTO `sys_menu` VALUES (1062, '线索和用户关联查询', 1061, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:associations:query', '#', 'admin', '2024-09-11 20:49:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1063, '线索和用户关联新增', 1061, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:associations:add', '#', 'admin', '2024-09-11 20:49:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1064, '线索和用户关联修改', 1061, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:associations:edit', '#', 'admin', '2024-09-11 20:49:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1065, '线索和用户关联删除', 1061, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:associations:remove', '#', 'admin', '2024-09-11 20:49:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1066, '线索和用户关联导出', 1061, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:associations:export', '#', 'admin', '2024-09-11 20:49:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1067, '联系人', 1097, 1, 'contacts', 'crm/contacts/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:contacts:list', 'documentation', 'admin', '2024-09-11 20:49:53', 'admin', '2024-09-11 20:57:10', '联系人菜单');
INSERT INTO `sys_menu` VALUES (1068, '联系人查询', 1067, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:contacts:query', '#', 'admin', '2024-09-11 20:49:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1069, '联系人新增', 1067, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:contacts:add', '#', 'admin', '2024-09-11 20:49:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1070, '联系人修改', 1067, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:contacts:edit', '#', 'admin', '2024-09-11 20:49:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1071, '联系人删除', 1067, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:contacts:remove', '#', 'admin', '2024-09-11 20:49:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1072, '联系人导出', 1067, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:contacts:export', '#', 'admin', '2024-09-11 20:49:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1073, '客户', 1097, 1, 'customers', 'crm/customers/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:customers:list', 'checkbox', 'admin', '2024-09-11 20:49:58', 'admin', '2024-09-11 20:57:20', '客户菜单');
INSERT INTO `sys_menu` VALUES (1074, '客户查询', 1073, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:customers:query', '#', 'admin', '2024-09-11 20:49:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1075, '客户新增', 1073, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:customers:add', '#', 'admin', '2024-09-11 20:49:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1076, '客户修改', 1073, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:customers:edit', '#', 'admin', '2024-09-11 20:49:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1077, '客户删除', 1073, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:customers:remove', '#', 'admin', '2024-09-11 20:49:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1078, '客户导出', 1073, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:customers:export', '#', 'admin', '2024-09-11 20:49:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1079, '线索分配历史记录', 1097, 1, 'history', 'crm/history/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:history:list', 'search', 'admin', '2024-09-11 20:50:04', 'admin', '2024-09-11 20:57:34', '线索分配历史记录菜单');
INSERT INTO `sys_menu` VALUES (1080, '线索分配历史记录查询', 1079, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:history:query', '#', 'admin', '2024-09-11 20:50:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1081, '线索分配历史记录新增', 1079, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:history:add', '#', 'admin', '2024-09-11 20:50:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1082, '线索分配历史记录修改', 1079, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:history:edit', '#', 'admin', '2024-09-11 20:50:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1083, '线索分配历史记录删除', 1079, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:history:remove', '#', 'admin', '2024-09-11 20:50:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1084, '线索分配历史记录导出', 1079, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:history:export', '#', 'admin', '2024-09-11 20:50:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1085, '线索', 1097, 1, 'leads', 'crm/leads/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:leads:list', 'link', 'admin', '2024-09-11 20:50:09', 'admin', '2024-09-11 20:57:44', '线索菜单');
INSERT INTO `sys_menu` VALUES (1086, '线索查询', 1085, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:leads:query', '#', 'admin', '2024-09-11 20:50:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1087, '线索新增', 1085, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:leads:add', '#', 'admin', '2024-09-11 20:50:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1088, '线索修改', 1085, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:leads:edit', '#', 'admin', '2024-09-11 20:50:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1089, '线索删除', 1085, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:leads:remove', '#', 'admin', '2024-09-11 20:50:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1090, '线索导出', 1085, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:leads:export', '#', 'admin', '2024-09-11 20:50:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1091, '跟进记录', 1097, 1, 'records', 'crm/records/index', NULL, '', 1, 0, 'C', '0', '0', 'crm:records:list', 'druid', 'admin', '2024-09-11 20:50:15', 'admin', '2024-09-11 20:57:49', '跟进记录菜单');
INSERT INTO `sys_menu` VALUES (1092, '跟进记录查询', 1091, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:records:query', '#', 'admin', '2024-09-11 20:50:15', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1093, '跟进记录新增', 1091, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:records:add', '#', 'admin', '2024-09-11 20:50:15', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1094, '跟进记录修改', 1091, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:records:edit', '#', 'admin', '2024-09-11 20:50:15', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1095, '跟进记录删除', 1091, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:records:remove', '#', 'admin', '2024-09-11 20:50:15', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1096, '跟进记录导出', 1091, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'crm:records:export', '#', 'admin', '2024-09-11 20:50:15', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1097, '客户管理', 0, 1, 'crm', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'clipboard', 'admin', '2024-09-11 20:55:34', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1098, '我的申请', 1183, 1, 'application', 'system/application/index', NULL, '', 1, 0, 'C', '0', '0', 'system:application:list', '#', 'admin', '2024-10-29 21:54:12', 'admin', '2024-10-29 22:00:08', '我的申请菜单');
INSERT INTO `sys_menu` VALUES (1099, '我的申请查询', 1098, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:application:query', '#', 'admin', '2024-10-29 21:54:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1100, '我的申请新增', 1098, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:application:add', '#', 'admin', '2024-10-29 21:54:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1101, '我的申请修改', 1098, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:application:edit', '#', 'admin', '2024-10-29 21:54:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1102, '我的申请删除', 1098, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:application:remove', '#', 'admin', '2024-10-29 21:54:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1103, '我的申请导出', 1098, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:application:export', '#', 'admin', '2024-10-29 21:54:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1104, '业务', 1, 1, 'business', 'system/business/index', NULL, '', 1, 0, 'C', '0', '0', 'system:business:list', '#', 'admin', '2024-10-29 21:54:21', 'admin', '2024-10-29 22:05:28', '业务菜单');
INSERT INTO `sys_menu` VALUES (1105, '业务查询', 1104, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:business:query', '#', 'admin', '2024-10-29 21:54:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1106, '业务新增', 1104, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:business:add', '#', 'admin', '2024-10-29 21:54:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1107, '业务修改', 1104, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:business:edit', '#', 'admin', '2024-10-29 21:54:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1108, '业务删除', 1104, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:business:remove', '#', 'admin', '2024-10-29 21:54:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1109, '业务导出', 1104, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:business:export', '#', 'admin', '2024-10-29 21:54:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1110, '合同', 1182, 1, 'contracts', 'system/contracts/index', NULL, '', 1, 0, 'C', '0', '0', 'system:contracts:list', '#', 'admin', '2024-10-29 21:54:28', 'admin', '2024-10-29 21:58:21', '合同菜单');
INSERT INTO `sys_menu` VALUES (1111, '合同查询', 1110, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:contracts:query', '#', 'admin', '2024-10-29 21:54:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1112, '合同新增', 1110, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:contracts:add', '#', 'admin', '2024-10-29 21:54:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1113, '合同修改', 1110, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:contracts:edit', '#', 'admin', '2024-10-29 21:54:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1114, '合同删除', 1110, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:contracts:remove', '#', 'admin', '2024-10-29 21:54:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1115, '合同导出', 1110, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:contracts:export', '#', 'admin', '2024-10-29 21:54:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1116, '审批节点详情', 1183, 1, 'detail', 'system/detail/index', NULL, '', 1, 0, 'C', '0', '0', 'system:detail:list', '#', 'admin', '2024-10-29 21:54:35', 'admin', '2024-10-29 22:00:17', '审批节点详情菜单');
INSERT INTO `sys_menu` VALUES (1117, '审批节点详情查询', 1116, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:detail:query', '#', 'admin', '2024-10-29 21:54:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1118, '审批节点详情新增', 1116, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:detail:add', '#', 'admin', '2024-10-29 21:54:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1119, '审批节点详情修改', 1116, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:detail:edit', '#', 'admin', '2024-10-29 21:54:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1120, '审批节点详情删除', 1116, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:detail:remove', '#', 'admin', '2024-10-29 21:54:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1121, '审批节点详情导出', 1116, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:detail:export', '#', 'admin', '2024-10-29 21:54:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1122, '回款明细', 1184, 1, 'details', 'system/details/index', NULL, '', 1, 0, 'C', '0', '0', 'system:details:list', '#', 'admin', '2024-10-29 21:54:54', 'admin', '2024-10-29 22:03:23', '回款明细菜单');
INSERT INTO `sys_menu` VALUES (1123, '回款明细查询', 1122, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:query', '#', 'admin', '2024-10-29 21:54:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1124, '回款明细新增', 1122, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:add', '#', 'admin', '2024-10-29 21:54:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1125, '回款明细修改', 1122, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:edit', '#', 'admin', '2024-10-29 21:54:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1126, '回款明细删除', 1122, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:remove', '#', 'admin', '2024-10-29 21:54:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1127, '回款明细导出', 1122, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:export', '#', 'admin', '2024-10-29 21:54:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1128, '回款明细', 3, 1, 'details', 'system/details/index', NULL, '', 1, 0, 'C', '1', '1', 'system:details:list', '#', 'admin', '2024-10-29 21:55:02', 'admin', '2024-10-29 22:04:51', '回款明细菜单');
INSERT INTO `sys_menu` VALUES (1129, '回款明细查询', 1128, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:query', '#', 'admin', '2024-10-29 21:55:02', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1130, '回款明细新增', 1128, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:add', '#', 'admin', '2024-10-29 21:55:02', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1131, '回款明细修改', 1128, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:edit', '#', 'admin', '2024-10-29 21:55:02', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1132, '回款明细删除', 1128, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:remove', '#', 'admin', '2024-10-29 21:55:02', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1133, '回款明细导出', 1128, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:details:export', '#', 'admin', '2024-10-29 21:55:02', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1134, '审批历史', 1183, 1, 'history', 'system/history/index', NULL, '', 1, 0, 'C', '0', '0', 'system:history:list', '#', 'admin', '2024-10-29 21:55:12', 'admin', '2024-10-29 22:00:26', '审批历史菜单');
INSERT INTO `sys_menu` VALUES (1135, '审批历史查询', 1134, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:history:query', '#', 'admin', '2024-10-29 21:55:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1136, '审批历史新增', 1134, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:history:add', '#', 'admin', '2024-10-29 21:55:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1137, '审批历史修改', 1134, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:history:edit', '#', 'admin', '2024-10-29 21:55:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1138, '审批历史删除', 1134, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:history:remove', '#', 'admin', '2024-10-29 21:55:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1139, '审批历史导出', 1134, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:history:export', '#', 'admin', '2024-10-29 21:55:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1140, '流程实例', 1183, 1, 'instance', 'system/instance/index', NULL, '', 1, 0, 'C', '0', '0', 'system:instance:list', '#', 'admin', '2024-10-29 21:55:21', 'admin', '2024-10-29 22:00:48', '流程实例菜单');
INSERT INTO `sys_menu` VALUES (1141, '流程实例查询', 1140, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:instance:query', '#', 'admin', '2024-10-29 21:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1142, '流程实例新增', 1140, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:instance:add', '#', 'admin', '2024-10-29 21:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1143, '流程实例修改', 1140, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:instance:edit', '#', 'admin', '2024-10-29 21:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1144, '流程实例删除', 1140, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:instance:remove', '#', 'admin', '2024-10-29 21:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1145, '流程实例导出', 1140, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:instance:export', '#', 'admin', '2024-10-29 21:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1146, '商机', 1097, 1, 'opportunities', 'system/opportunities/index', NULL, '', 1, 0, 'C', '0', '0', 'system:opportunities:list', '#', 'admin', '2024-10-29 21:55:33', 'admin', '2024-10-29 22:05:19', '商机菜单');
INSERT INTO `sys_menu` VALUES (1147, '商机查询', 1146, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:opportunities:query', '#', 'admin', '2024-10-29 21:55:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1148, '商机新增', 1146, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:opportunities:add', '#', 'admin', '2024-10-29 21:55:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1149, '商机修改', 1146, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:opportunities:edit', '#', 'admin', '2024-10-29 21:55:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1150, '商机删除', 1146, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:opportunities:remove', '#', 'admin', '2024-10-29 21:55:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1151, '商机导出', 1146, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:opportunities:export', '#', 'admin', '2024-10-29 21:55:33', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1152, '回款', 1184, 1, 'payments', 'system/payments/index', NULL, '', 1, 0, 'C', '0', '0', 'system:payments:list', '#', 'admin', '2024-10-29 21:55:42', 'admin', '2024-10-29 22:05:02', '回款菜单');
INSERT INTO `sys_menu` VALUES (1153, '回款查询', 1152, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:payments:query', '#', 'admin', '2024-10-29 21:55:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1154, '回款新增', 1152, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:payments:add', '#', 'admin', '2024-10-29 21:55:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1155, '回款修改', 1152, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:payments:edit', '#', 'admin', '2024-10-29 21:55:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1156, '回款删除', 1152, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:payments:remove', '#', 'admin', '2024-10-29 21:55:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1157, '回款导出', 1152, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:payments:export', '#', 'admin', '2024-10-29 21:55:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1158, '回款计划', 1184, 1, 'plans', 'system/plans/index', NULL, '', 1, 0, 'C', '0', '0', 'system:plans:list', '#', 'admin', '2024-10-29 21:55:49', 'admin', '2024-10-29 22:05:07', '回款计划菜单');
INSERT INTO `sys_menu` VALUES (1159, '回款计划查询', 1158, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:plans:query', '#', 'admin', '2024-10-29 21:55:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1160, '回款计划新增', 1158, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:plans:add', '#', 'admin', '2024-10-29 21:55:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1161, '回款计划修改', 1158, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:plans:edit', '#', 'admin', '2024-10-29 21:55:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1162, '回款计划删除', 1158, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:plans:remove', '#', 'admin', '2024-10-29 21:55:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1163, '回款计划导出', 1158, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:plans:export', '#', 'admin', '2024-10-29 21:55:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1164, '审批处理', 1183, 1, 'process', 'system/process/index', NULL, '', 1, 0, 'C', '0', '0', 'system:process:list', '#', 'admin', '2024-10-29 21:55:58', 'admin', '2024-10-29 22:00:36', '审批处理菜单');
INSERT INTO `sys_menu` VALUES (1165, '审批处理查询', 1164, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:process:query', '#', 'admin', '2024-10-29 21:55:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1166, '审批处理新增', 1164, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:process:add', '#', 'admin', '2024-10-29 21:55:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1167, '审批处理修改', 1164, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:process:edit', '#', 'admin', '2024-10-29 21:55:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1168, '审批处理删除', 1164, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:process:remove', '#', 'admin', '2024-10-29 21:55:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1169, '审批处理导出', 1164, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:process:export', '#', 'admin', '2024-10-29 21:55:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1170, '合同用户关系', 1182, 1, 'relations', 'system/relations/index', NULL, '', 1, 0, 'C', '0', '0', 'system:relations:list', '#', 'admin', '2024-10-29 21:56:05', 'admin', '2024-10-29 21:58:36', '合同用户关系菜单');
INSERT INTO `sys_menu` VALUES (1171, '合同用户关系查询', 1170, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:relations:query', '#', 'admin', '2024-10-29 21:56:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1172, '合同用户关系新增', 1170, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:relations:add', '#', 'admin', '2024-10-29 21:56:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1173, '合同用户关系修改', 1170, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:relations:edit', '#', 'admin', '2024-10-29 21:56:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1174, '合同用户关系删除', 1170, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:relations:remove', '#', 'admin', '2024-10-29 21:56:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1175, '合同用户关系导出', 1170, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:relations:export', '#', 'admin', '2024-10-29 21:56:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1176, '合同关注', 1182, 1, 'watchlist', 'system/watchlist/index', NULL, '', 1, 0, 'C', '0', '0', 'system:watchlist:list', '#', 'admin', '2024-10-29 21:56:12', 'admin', '2024-10-29 21:58:43', '合同关注菜单');
INSERT INTO `sys_menu` VALUES (1177, '合同关注查询', 1176, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:watchlist:query', '#', 'admin', '2024-10-29 21:56:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1178, '合同关注新增', 1176, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:watchlist:add', '#', 'admin', '2024-10-29 21:56:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1179, '合同关注修改', 1176, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:watchlist:edit', '#', 'admin', '2024-10-29 21:56:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1180, '合同关注删除', 1176, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:watchlist:remove', '#', 'admin', '2024-10-29 21:56:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1181, '合同关注导出', 1176, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'system:watchlist:export', '#', 'admin', '2024-10-29 21:56:12', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1182, '合同', 0, 2, 'contract', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'documentation', 'admin', '2024-10-29 21:57:50', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1183, '审批设置', 0, 3, 'approval', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'education', 'admin', '2024-10-29 21:59:51', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1184, '回款', 0, 2, 'payment', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'example', 'admin', '2024-10-29 22:03:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1185, '拜访计划', 2000, 4, 'visitPlan', 'crm/visitPlan/index', '', '', 1, 0, 'C', '0', '0', 'crm:visitPlan:list', 'calendar', 'admin', '2025-06-30 21:37:24', '', NULL, '拜访计划菜单');
INSERT INTO `sys_menu` VALUES (1186, '拜访计划查询', 1185, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:query', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1187, '拜访计划新增', 1185, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:add', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1188, '拜访计划修改', 1185, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:edit', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1189, '拜访计划删除', 1185, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:remove', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1190, '拜访计划导出', 1185, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:export', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1191, '拜访计划延期', 1185, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:postpone', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1192, '拜访计划取消', 1185, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:cancel', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1193, '拜访计划完成', 1185, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:complete', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1194, '拜访计划统计', 1185, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:statistics', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1195, '拜访提醒管理', 2000, 5, 'visitPlanReminder', 'crm/visitPlanReminder/index', '', '', 1, 0, 'C', '0', '0', 'crm:visitPlanReminder:list', 'bell', 'admin', '2025-06-30 21:37:24', '', NULL, '拜访提醒管理菜单');
INSERT INTO `sys_menu` VALUES (1196, '拜访提醒查询', 1195, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:query', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1197, '拜访提醒新增', 1195, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:add', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1198, '拜访提醒修改', 1195, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:edit', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1199, '拜访提醒删除', 1195, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:remove', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1200, '拜访日志管理', 2000, 6, 'visitPlanLog', 'crm/visitPlanLog/index', '', '', 1, 0, 'C', '0', '0', 'crm:visitPlanLog:list', 'documentation', 'admin', '2025-06-30 21:37:24', '', NULL, '拜访日志管理菜单');
INSERT INTO `sys_menu` VALUES (1201, '拜访日志查询', 1200, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:query', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1202, '拜访日志删除', 1200, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:remove', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1203, '拜访日志导出', 1200, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:export', '#', 'admin', '2025-06-30 21:37:24', '', NULL, '');

-- ----------------------------


-- ========================================
-- 8. sys_post
-- ========================================
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2024-09-09 15:12:01', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2024-09-09 15:12:01', '', NULL, '');

-- ----------------------------


-- ========================================
-- 9. sys_user_role
-- ========================================
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);

-- ----------------------------
-- View structure for v_customer_contacts
-- ----------------------------
DROP VIEW IF EXISTS `v_customer_contacts`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_customer_contacts` AS select `c`.`id` AS `customer_id`,`c`.`customer_name` AS `customer_name`,`ct`.`id` AS `contact_id`,`ct`.`name` AS `contact_name`,`ct`.`mobile` AS `mobile`,`ct`.`email` AS `email`,`ct`.`position` AS `position`,`r`.`relation_type` AS `relation_type`,`r`.`is_primary` AS `is_primary`,`r`.`status` AS `relation_status`,`r`.`start_date` AS `start_date`,`r`.`end_date` AS `end_date` from ((`crm_business_customers` `c` join `crm_customer_contact_relations` `r` on((`c`.`id` = `r`.`customer_id`))) join `crm_business_contacts` `ct` on((`r`.`contact_id` = `ct`.`id`))) where ((`r`.`del_flag` = '0') and (`c`.`del_flag` = '0') and (`ct`.`del_flag` = '0'));

-- ----------------------------
-- View structure for v_user_followed_contacts_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_user_followed_contacts_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_followed_contacts_stats` AS select `crm_contact_followers`.`follower_id` AS `follower_id`,count(0) AS `followed_count`,count((case when (`crm_contact_followers`.`is_active` = 1) then 1 end)) AS `active_followed_count`,max(`crm_contact_followers`.`follow_time`) AS `latest_follow_time` from `crm_contact_followers` group by `crm_contact_followers`.`follower_id`;

-- ----------------------------
-- View structure for v_user_hierarchy_relations
-- ----------------------------
DROP VIEW IF EXISTS `v_user_hierarchy_relations`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_hierarchy_relations` AS select `h`.`user_id` AS `user_id`,`h`.`superior_id` AS `superior_id`,`u1`.`user_name` AS `user_name`,`u2`.`user_name` AS `superior_name`,`h`.`hierarchy_level` AS `hierarchy_level`,`h`.`create_time` AS `create_time` from ((`crm_user_hierarchy` `h` left join `sys_user` `u1` on((`h`.`user_id` = `u1`.`user_id`))) left join `sys_user` `u2` on((`h`.`superior_id` = `u2`.`user_id`))) where ((`h`.`del_flag` = '0') and (`u1`.`del_flag` = '0') and (`u2`.`del_flag` = '0'));

-- ----------------------------
-- View structure for v_visit_plan_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_visit_plan_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_visit_plan_statistics` AS select `crm_visit_plans`.`owner_id` AS `owner_id`,`crm_visit_plans`.`owner_name` AS `owner_name`,`crm_visit_plans`.`dept_id` AS `dept_id`,`crm_visit_plans`.`dept_name` AS `dept_name`,count(0) AS `total_plans`,sum((case when (`crm_visit_plans`.`status` = 'planned') then 1 else 0 end)) AS `planned_count`,sum((case when (`crm_visit_plans`.`status` = 'ongoing') then 1 else 0 end)) AS `ongoing_count`,sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)) AS `completed_count`,sum((case when (`crm_visit_plans`.`status` = 'postponed') then 1 else 0 end)) AS `postponed_count`,sum((case when (`crm_visit_plans`.`status` = 'cancelled') then 1 else 0 end)) AS `cancelled_count`,round(((sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)) * 100.0) / count(0)),2) AS `completion_rate`,round(((sum((case when ((`crm_visit_plans`.`status` = 'completed') and (`crm_visit_plans`.`actual_visit_time` <= `crm_visit_plans`.`visit_time`)) then 1 else 0 end)) * 100.0) / nullif(sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)),0)),2) AS `on_time_rate` from `crm_visit_plans` where (`crm_visit_plans`.`del_flag` = '0') group by `crm_visit_plans`.`owner_id`,`crm_visit_plans`.`owner_name`,`crm_visit_plans`.`dept_id`,`crm_visit_plans`.`dept_name`;

SET FOREIGN_KEY_CHECKS = 1;


-- ========================================
-- 10. sys_user_post
-- ========================================
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 2);

-- ----------------------------


-- ========================================
-- 11. sys_role_menu
-- ========================================
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1185);
INSERT INTO `sys_role_menu` VALUES (1, 1186);
INSERT INTO `sys_role_menu` VALUES (1, 1187);
INSERT INTO `sys_role_menu` VALUES (1, 1188);
INSERT INTO `sys_role_menu` VALUES (1, 1189);
INSERT INTO `sys_role_menu` VALUES (1, 1190);
INSERT INTO `sys_role_menu` VALUES (1, 1191);
INSERT INTO `sys_role_menu` VALUES (1, 1192);
INSERT INTO `sys_role_menu` VALUES (1, 1193);
INSERT INTO `sys_role_menu` VALUES (1, 1194);
INSERT INTO `sys_role_menu` VALUES (1, 1195);
INSERT INTO `sys_role_menu` VALUES (1, 1196);
INSERT INTO `sys_role_menu` VALUES (1, 1197);
INSERT INTO `sys_role_menu` VALUES (1, 1198);
INSERT INTO `sys_role_menu` VALUES (1, 1199);
INSERT INTO `sys_role_menu` VALUES (1, 1200);
INSERT INTO `sys_role_menu` VALUES (1, 1201);
INSERT INTO `sys_role_menu` VALUES (1, 1202);
INSERT INTO `sys_role_menu` VALUES (1, 1203);
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 2);
INSERT INTO `sys_role_menu` VALUES (2, 3);
INSERT INTO `sys_role_menu` VALUES (2, 4);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 101);
INSERT INTO `sys_role_menu` VALUES (2, 102);
INSERT INTO `sys_role_menu` VALUES (2, 103);
INSERT INTO `sys_role_menu` VALUES (2, 104);
INSERT INTO `sys_role_menu` VALUES (2, 105);
INSERT INTO `sys_role_menu` VALUES (2, 106);
INSERT INTO `sys_role_menu` VALUES (2, 107);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 109);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 111);
INSERT INTO `sys_role_menu` VALUES (2, 112);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 114);
INSERT INTO `sys_role_menu` VALUES (2, 115);
INSERT INTO `sys_role_menu` VALUES (2, 116);
INSERT INTO `sys_role_menu` VALUES (2, 117);
INSERT INTO `sys_role_menu` VALUES (2, 500);
INSERT INTO `sys_role_menu` VALUES (2, 501);
INSERT INTO `sys_role_menu` VALUES (2, 1000);
INSERT INTO `sys_role_menu` VALUES (2, 1001);
INSERT INTO `sys_role_menu` VALUES (2, 1002);
INSERT INTO `sys_role_menu` VALUES (2, 1003);
INSERT INTO `sys_role_menu` VALUES (2, 1004);
INSERT INTO `sys_role_menu` VALUES (2, 1005);
INSERT INTO `sys_role_menu` VALUES (2, 1006);
INSERT INTO `sys_role_menu` VALUES (2, 1007);
INSERT INTO `sys_role_menu` VALUES (2, 1008);
INSERT INTO `sys_role_menu` VALUES (2, 1009);
INSERT INTO `sys_role_menu` VALUES (2, 1010);
INSERT INTO `sys_role_menu` VALUES (2, 1011);
INSERT INTO `sys_role_menu` VALUES (2, 1012);
INSERT INTO `sys_role_menu` VALUES (2, 1013);
INSERT INTO `sys_role_menu` VALUES (2, 1014);
INSERT INTO `sys_role_menu` VALUES (2, 1015);
INSERT INTO `sys_role_menu` VALUES (2, 1016);
INSERT INTO `sys_role_menu` VALUES (2, 1017);
INSERT INTO `sys_role_menu` VALUES (2, 1018);
INSERT INTO `sys_role_menu` VALUES (2, 1019);
INSERT INTO `sys_role_menu` VALUES (2, 1020);
INSERT INTO `sys_role_menu` VALUES (2, 1021);
INSERT INTO `sys_role_menu` VALUES (2, 1022);
INSERT INTO `sys_role_menu` VALUES (2, 1023);
INSERT INTO `sys_role_menu` VALUES (2, 1024);
INSERT INTO `sys_role_menu` VALUES (2, 1025);
INSERT INTO `sys_role_menu` VALUES (2, 1026);
INSERT INTO `sys_role_menu` VALUES (2, 1027);
INSERT INTO `sys_role_menu` VALUES (2, 1028);
INSERT INTO `sys_role_menu` VALUES (2, 1029);
INSERT INTO `sys_role_menu` VALUES (2, 1030);
INSERT INTO `sys_role_menu` VALUES (2, 1031);
INSERT INTO `sys_role_menu` VALUES (2, 1032);
INSERT INTO `sys_role_menu` VALUES (2, 1033);
INSERT INTO `sys_role_menu` VALUES (2, 1034);
INSERT INTO `sys_role_menu` VALUES (2, 1035);
INSERT INTO `sys_role_menu` VALUES (2, 1036);
INSERT INTO `sys_role_menu` VALUES (2, 1037);
INSERT INTO `sys_role_menu` VALUES (2, 1038);
INSERT INTO `sys_role_menu` VALUES (2, 1039);
INSERT INTO `sys_role_menu` VALUES (2, 1040);
INSERT INTO `sys_role_menu` VALUES (2, 1041);
INSERT INTO `sys_role_menu` VALUES (2, 1042);
INSERT INTO `sys_role_menu` VALUES (2, 1043);
INSERT INTO `sys_role_menu` VALUES (2, 1044);
INSERT INTO `sys_role_menu` VALUES (2, 1045);
INSERT INTO `sys_role_menu` VALUES (2, 1046);
INSERT INTO `sys_role_menu` VALUES (2, 1047);
INSERT INTO `sys_role_menu` VALUES (2, 1048);
INSERT INTO `sys_role_menu` VALUES (2, 1049);
INSERT INTO `sys_role_menu` VALUES (2, 1050);
INSERT INTO `sys_role_menu` VALUES (2, 1051);
INSERT INTO `sys_role_menu` VALUES (2, 1052);
INSERT INTO `sys_role_menu` VALUES (2, 1053);
INSERT INTO `sys_role_menu` VALUES (2, 1054);
INSERT INTO `sys_role_menu` VALUES (2, 1055);
INSERT INTO `sys_role_menu` VALUES (2, 1056);
INSERT INTO `sys_role_menu` VALUES (2, 1057);
INSERT INTO `sys_role_menu` VALUES (2, 1058);
INSERT INTO `sys_role_menu` VALUES (2, 1059);
INSERT INTO `sys_role_menu` VALUES (2, 1060);

-- ----------------------------


-- ========================================
-- 12. sys_role_dept
-- ========================================
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------


-- ========================================
-- 13. sys_job
-- ========================================
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2024-09-09 15:12:02', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2024-09-09 15:12:02', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2024-09-09 15:12:02', '', NULL, '');
INSERT INTO `sys_job` VALUES (4, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '0 */5 * * * ?', '3', '1', '0', 'admin', '2025-06-30 21:37:42', '', NULL, '每5分钟执行一次，处理待发送的拜访计划提醒');
INSERT INTO `sys_job` VALUES (5, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '0 0 * * * ?', '3', '1', '0', 'admin', '2025-06-30 21:37:42', '', NULL, '每小时执行一次，自动更新过期拜访计划的状态');
INSERT INTO `sys_job` VALUES (6, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '0 0 2 * * ?', '3', '1', '0', 'admin', '2025-06-30 21:37:42', '', NULL, '每天凌晨2点执行，清理过期的提醒记录');
INSERT INTO `sys_job` VALUES (7, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '0 0 1 * * ?', '3', '1', '0', 'admin', '2025-06-30 21:37:42', '', NULL, '每天凌晨1点执行，生成拜访计划统计报告');

-- ----------------------------


-- ========================================
-- 14. sys_job_log
-- ========================================
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 486 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------
INSERT INTO `sys_job_log` VALUES (1, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-01 02:55:00');
INSERT INTO `sys_job_log` VALUES (2, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：42毫秒', '0', '', '2025-07-01 15:00:00');
INSERT INTO `sys_job_log` VALUES (3, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：35毫秒', '0', '', '2025-07-01 15:00:00');
INSERT INTO `sys_job_log` VALUES (4, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 15:05:00');
INSERT INTO `sys_job_log` VALUES (5, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：13毫秒', '0', '', '2025-07-01 15:10:00');
INSERT INTO `sys_job_log` VALUES (6, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-01 15:15:00');
INSERT INTO `sys_job_log` VALUES (7, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 15:20:00');
INSERT INTO `sys_job_log` VALUES (8, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-01 15:25:00');
INSERT INTO `sys_job_log` VALUES (9, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 15:30:00');
INSERT INTO `sys_job_log` VALUES (10, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 15:35:00');
INSERT INTO `sys_job_log` VALUES (11, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 15:40:00');
INSERT INTO `sys_job_log` VALUES (12, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 15:45:00');
INSERT INTO `sys_job_log` VALUES (13, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 15:50:00');
INSERT INTO `sys_job_log` VALUES (14, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：35毫秒', '0', '', '2025-07-01 15:55:00');
INSERT INTO `sys_job_log` VALUES (15, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 16:00:00');
INSERT INTO `sys_job_log` VALUES (16, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：16毫秒', '0', '', '2025-07-01 16:00:00');
INSERT INTO `sys_job_log` VALUES (17, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 16:05:00');
INSERT INTO `sys_job_log` VALUES (18, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 16:10:00');
INSERT INTO `sys_job_log` VALUES (19, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 16:15:00');
INSERT INTO `sys_job_log` VALUES (20, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 16:20:00');
INSERT INTO `sys_job_log` VALUES (21, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 16:25:00');
INSERT INTO `sys_job_log` VALUES (22, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 16:30:00');
INSERT INTO `sys_job_log` VALUES (23, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-01 16:35:00');
INSERT INTO `sys_job_log` VALUES (24, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 16:40:00');
INSERT INTO `sys_job_log` VALUES (25, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-01 16:45:00');
INSERT INTO `sys_job_log` VALUES (26, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：31毫秒', '0', '', '2025-07-01 17:20:00');
INSERT INTO `sys_job_log` VALUES (27, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：11毫秒', '0', '', '2025-07-01 17:25:00');
INSERT INTO `sys_job_log` VALUES (28, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：10毫秒', '0', '', '2025-07-01 17:30:00');
INSERT INTO `sys_job_log` VALUES (29, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-01 17:35:00');
INSERT INTO `sys_job_log` VALUES (30, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-01 17:40:00');
INSERT INTO `sys_job_log` VALUES (31, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：14毫秒', '0', '', '2025-07-01 17:45:00');
INSERT INTO `sys_job_log` VALUES (32, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-01 17:50:00');
INSERT INTO `sys_job_log` VALUES (33, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-01 17:55:00');
INSERT INTO `sys_job_log` VALUES (34, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：12毫秒', '0', '', '2025-07-01 18:00:00');
INSERT INTO `sys_job_log` VALUES (35, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-01 18:00:00');
INSERT INTO `sys_job_log` VALUES (36, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-01 18:05:00');
INSERT INTO `sys_job_log` VALUES (37, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：10毫秒', '0', '', '2025-07-01 18:10:00');
INSERT INTO `sys_job_log` VALUES (38, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 18:15:00');
INSERT INTO `sys_job_log` VALUES (39, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-01 18:20:00');
INSERT INTO `sys_job_log` VALUES (40, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 18:25:00');
INSERT INTO `sys_job_log` VALUES (41, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 18:30:00');
INSERT INTO `sys_job_log` VALUES (42, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 18:35:00');
INSERT INTO `sys_job_log` VALUES (43, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-01 18:40:00');
INSERT INTO `sys_job_log` VALUES (44, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 18:45:00');
INSERT INTO `sys_job_log` VALUES (45, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-01 18:50:00');
INSERT INTO `sys_job_log` VALUES (46, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-01 18:55:00');
INSERT INTO `sys_job_log` VALUES (47, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-01 19:00:00');
INSERT INTO `sys_job_log` VALUES (48, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：9毫秒', '0', '', '2025-07-01 19:00:00');
INSERT INTO `sys_job_log` VALUES (49, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 19:05:00');
INSERT INTO `sys_job_log` VALUES (50, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-01 19:10:00');
INSERT INTO `sys_job_log` VALUES (51, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 19:15:00');
INSERT INTO `sys_job_log` VALUES (52, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 19:20:00');
INSERT INTO `sys_job_log` VALUES (53, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-01 19:25:00');
INSERT INTO `sys_job_log` VALUES (54, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-01 19:30:00');
INSERT INTO `sys_job_log` VALUES (55, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 19:35:00');
INSERT INTO `sys_job_log` VALUES (56, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 19:40:00');
INSERT INTO `sys_job_log` VALUES (57, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 19:45:00');
INSERT INTO `sys_job_log` VALUES (58, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 19:50:00');
INSERT INTO `sys_job_log` VALUES (59, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-01 19:55:00');
INSERT INTO `sys_job_log` VALUES (60, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-01 20:00:00');
INSERT INTO `sys_job_log` VALUES (61, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：4毫秒', '0', '', '2025-07-01 20:00:00');
INSERT INTO `sys_job_log` VALUES (62, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 20:05:00');
INSERT INTO `sys_job_log` VALUES (63, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-01 20:10:00');
INSERT INTO `sys_job_log` VALUES (64, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：25毫秒', '0', '', '2025-07-01 20:15:00');
INSERT INTO `sys_job_log` VALUES (65, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：20毫秒', '0', '', '2025-07-01 20:20:00');
INSERT INTO `sys_job_log` VALUES (66, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：27毫秒', '0', '', '2025-07-01 20:25:00');
INSERT INTO `sys_job_log` VALUES (67, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-01 20:30:00');
INSERT INTO `sys_job_log` VALUES (68, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-01 20:35:00');
INSERT INTO `sys_job_log` VALUES (69, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：11毫秒', '0', '', '2025-07-01 20:40:00');
INSERT INTO `sys_job_log` VALUES (70, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-01 20:45:00');
INSERT INTO `sys_job_log` VALUES (71, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：24毫秒', '0', '', '2025-07-01 21:10:00');
INSERT INTO `sys_job_log` VALUES (72, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-01 21:15:00');
INSERT INTO `sys_job_log` VALUES (73, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-01 23:45:00');
INSERT INTO `sys_job_log` VALUES (74, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-01 23:50:00');
INSERT INTO `sys_job_log` VALUES (75, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-01 23:55:00');
INSERT INTO `sys_job_log` VALUES (76, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 00:00:00');
INSERT INTO `sys_job_log` VALUES (77, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：23毫秒', '0', '', '2025-07-02 00:00:00');
INSERT INTO `sys_job_log` VALUES (78, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：23毫秒', '0', '', '2025-07-02 00:05:00');
INSERT INTO `sys_job_log` VALUES (79, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-02 00:10:00');
INSERT INTO `sys_job_log` VALUES (80, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：20毫秒', '0', '', '2025-07-02 00:15:00');
INSERT INTO `sys_job_log` VALUES (81, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 00:20:00');
INSERT INTO `sys_job_log` VALUES (82, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 00:25:00');
INSERT INTO `sys_job_log` VALUES (83, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-02 00:30:00');
INSERT INTO `sys_job_log` VALUES (84, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 00:35:00');
INSERT INTO `sys_job_log` VALUES (85, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-02 00:40:00');
INSERT INTO `sys_job_log` VALUES (86, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：33毫秒', '0', '', '2025-07-02 00:45:00');
INSERT INTO `sys_job_log` VALUES (87, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：27毫秒', '0', '', '2025-07-02 00:50:00');
INSERT INTO `sys_job_log` VALUES (88, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-02 00:55:00');
INSERT INTO `sys_job_log` VALUES (89, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '拜访计划统计报告生成 总共耗时：13毫秒', '0', '', '2025-07-02 01:00:00');
INSERT INTO `sys_job_log` VALUES (90, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-02 01:00:00');
INSERT INTO `sys_job_log` VALUES (91, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：17毫秒', '0', '', '2025-07-02 01:00:00');
INSERT INTO `sys_job_log` VALUES (92, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 01:05:00');
INSERT INTO `sys_job_log` VALUES (93, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 01:10:00');
INSERT INTO `sys_job_log` VALUES (94, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：20毫秒', '0', '', '2025-07-02 01:15:00');
INSERT INTO `sys_job_log` VALUES (95, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：27毫秒', '0', '', '2025-07-02 01:20:00');
INSERT INTO `sys_job_log` VALUES (96, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-02 01:25:00');
INSERT INTO `sys_job_log` VALUES (97, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-02 01:30:00');
INSERT INTO `sys_job_log` VALUES (98, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-02 01:35:00');
INSERT INTO `sys_job_log` VALUES (99, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-02 01:40:00');
INSERT INTO `sys_job_log` VALUES (100, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-02 01:45:00');
INSERT INTO `sys_job_log` VALUES (101, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-02 01:50:00');
INSERT INTO `sys_job_log` VALUES (102, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-02 01:55:00');
INSERT INTO `sys_job_log` VALUES (103, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '拜访计划提醒记录清理 总共耗时：3毫秒', '0', '', '2025-07-02 02:00:00');
INSERT INTO `sys_job_log` VALUES (104, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-02 02:00:00');
INSERT INTO `sys_job_log` VALUES (105, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：12毫秒', '0', '', '2025-07-02 02:00:00');
INSERT INTO `sys_job_log` VALUES (106, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-02 02:05:00');
INSERT INTO `sys_job_log` VALUES (107, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-02 02:10:00');
INSERT INTO `sys_job_log` VALUES (108, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-02 02:15:00');
INSERT INTO `sys_job_log` VALUES (109, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：1毫秒', '0', '', '2025-07-02 02:20:00');
INSERT INTO `sys_job_log` VALUES (110, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-02 02:25:00');
INSERT INTO `sys_job_log` VALUES (111, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-02 02:30:00');
INSERT INTO `sys_job_log` VALUES (112, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-02 02:35:00');
INSERT INTO `sys_job_log` VALUES (113, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-02 02:40:00');
INSERT INTO `sys_job_log` VALUES (114, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-02 02:45:00');
INSERT INTO `sys_job_log` VALUES (115, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-02 02:50:00');
INSERT INTO `sys_job_log` VALUES (116, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-02 02:55:00');
INSERT INTO `sys_job_log` VALUES (117, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：5毫秒', '0', '', '2025-07-02 03:00:00');
INSERT INTO `sys_job_log` VALUES (118, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-02 03:00:00');
INSERT INTO `sys_job_log` VALUES (119, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-02 03:05:00');
INSERT INTO `sys_job_log` VALUES (120, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-02 03:10:00');
INSERT INTO `sys_job_log` VALUES (121, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-02 03:15:00');
INSERT INTO `sys_job_log` VALUES (122, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-02 03:20:00');
INSERT INTO `sys_job_log` VALUES (123, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-02 03:25:00');
INSERT INTO `sys_job_log` VALUES (124, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：50毫秒', '0', '', '2025-07-03 00:55:00');
INSERT INTO `sys_job_log` VALUES (125, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-03 01:00:00');
INSERT INTO `sys_job_log` VALUES (126, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '拜访计划统计报告生成 总共耗时：14毫秒', '0', '', '2025-07-03 01:00:00');
INSERT INTO `sys_job_log` VALUES (127, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：54毫秒', '0', '', '2025-07-03 01:00:00');
INSERT INTO `sys_job_log` VALUES (128, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：10毫秒', '0', '', '2025-07-03 01:05:00');
INSERT INTO `sys_job_log` VALUES (129, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-03 01:10:00');
INSERT INTO `sys_job_log` VALUES (130, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-03 01:15:00');
INSERT INTO `sys_job_log` VALUES (131, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-03 01:20:00');
INSERT INTO `sys_job_log` VALUES (132, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 01:25:00');
INSERT INTO `sys_job_log` VALUES (133, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-03 01:30:00');
INSERT INTO `sys_job_log` VALUES (134, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 01:35:00');
INSERT INTO `sys_job_log` VALUES (135, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-03 01:40:00');
INSERT INTO `sys_job_log` VALUES (136, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-03 01:45:00');
INSERT INTO `sys_job_log` VALUES (137, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 01:50:00');
INSERT INTO `sys_job_log` VALUES (138, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 01:55:00');
INSERT INTO `sys_job_log` VALUES (139, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '拜访计划提醒记录清理 总共耗时：4毫秒', '0', '', '2025-07-03 02:00:00');
INSERT INTO `sys_job_log` VALUES (140, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-03 02:00:00');
INSERT INTO `sys_job_log` VALUES (141, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：8毫秒', '0', '', '2025-07-03 02:00:00');
INSERT INTO `sys_job_log` VALUES (142, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-03 02:05:00');
INSERT INTO `sys_job_log` VALUES (143, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 02:10:00');
INSERT INTO `sys_job_log` VALUES (144, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-03 02:15:00');
INSERT INTO `sys_job_log` VALUES (145, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-03 02:20:00');
INSERT INTO `sys_job_log` VALUES (146, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 02:25:00');
INSERT INTO `sys_job_log` VALUES (147, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 02:30:00');
INSERT INTO `sys_job_log` VALUES (148, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-03 02:35:00');
INSERT INTO `sys_job_log` VALUES (149, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-03 02:40:00');
INSERT INTO `sys_job_log` VALUES (150, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-03 02:45:00');
INSERT INTO `sys_job_log` VALUES (151, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 02:50:00');
INSERT INTO `sys_job_log` VALUES (152, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 02:55:00');
INSERT INTO `sys_job_log` VALUES (153, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：8毫秒', '0', '', '2025-07-03 03:00:00');
INSERT INTO `sys_job_log` VALUES (154, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-03 03:00:00');
INSERT INTO `sys_job_log` VALUES (155, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-03 03:05:00');
INSERT INTO `sys_job_log` VALUES (156, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 03:10:00');
INSERT INTO `sys_job_log` VALUES (157, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-03 03:15:00');
INSERT INTO `sys_job_log` VALUES (158, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 03:20:00');
INSERT INTO `sys_job_log` VALUES (159, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-03 03:25:00');
INSERT INTO `sys_job_log` VALUES (160, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-03 03:30:00');
INSERT INTO `sys_job_log` VALUES (161, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：34毫秒', '0', '', '2025-07-04 21:10:00');
INSERT INTO `sys_job_log` VALUES (162, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-04 21:15:00');
INSERT INTO `sys_job_log` VALUES (163, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-04 21:20:00');
INSERT INTO `sys_job_log` VALUES (164, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 21:25:00');
INSERT INTO `sys_job_log` VALUES (165, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-04 21:30:00');
INSERT INTO `sys_job_log` VALUES (166, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-04 21:35:00');
INSERT INTO `sys_job_log` VALUES (167, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-04 21:40:00');
INSERT INTO `sys_job_log` VALUES (168, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-04 21:45:00');
INSERT INTO `sys_job_log` VALUES (169, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-04 21:50:00');
INSERT INTO `sys_job_log` VALUES (170, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-04 21:55:00');
INSERT INTO `sys_job_log` VALUES (171, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-04 22:00:00');
INSERT INTO `sys_job_log` VALUES (172, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：45毫秒', '0', '', '2025-07-04 22:00:00');
INSERT INTO `sys_job_log` VALUES (173, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-04 22:05:00');
INSERT INTO `sys_job_log` VALUES (174, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-04 22:10:00');
INSERT INTO `sys_job_log` VALUES (175, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-04 22:15:00');
INSERT INTO `sys_job_log` VALUES (176, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-04 22:20:00');
INSERT INTO `sys_job_log` VALUES (177, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-04 22:25:00');
INSERT INTO `sys_job_log` VALUES (178, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-04 22:30:00');
INSERT INTO `sys_job_log` VALUES (179, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 22:35:00');
INSERT INTO `sys_job_log` VALUES (180, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-04 22:40:00');
INSERT INTO `sys_job_log` VALUES (181, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-04 22:45:00');
INSERT INTO `sys_job_log` VALUES (182, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 22:50:00');
INSERT INTO `sys_job_log` VALUES (183, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 22:55:00');
INSERT INTO `sys_job_log` VALUES (184, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-04 23:00:00');
INSERT INTO `sys_job_log` VALUES (185, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：9毫秒', '0', '', '2025-07-04 23:00:00');
INSERT INTO `sys_job_log` VALUES (186, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-04 23:05:00');
INSERT INTO `sys_job_log` VALUES (187, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-04 23:10:00');
INSERT INTO `sys_job_log` VALUES (188, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:15:00');
INSERT INTO `sys_job_log` VALUES (189, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:20:00');
INSERT INTO `sys_job_log` VALUES (190, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:25:00');
INSERT INTO `sys_job_log` VALUES (191, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:30:00');
INSERT INTO `sys_job_log` VALUES (192, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:35:00');
INSERT INTO `sys_job_log` VALUES (193, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-04 23:40:00');
INSERT INTO `sys_job_log` VALUES (194, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:45:00');
INSERT INTO `sys_job_log` VALUES (195, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-04 23:50:00');
INSERT INTO `sys_job_log` VALUES (196, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-04 23:55:00');
INSERT INTO `sys_job_log` VALUES (197, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-05 00:00:00');
INSERT INTO `sys_job_log` VALUES (198, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：5毫秒', '0', '', '2025-07-05 00:00:00');
INSERT INTO `sys_job_log` VALUES (199, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：13毫秒', '0', '', '2025-07-05 00:05:00');
INSERT INTO `sys_job_log` VALUES (200, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 00:10:00');
INSERT INTO `sys_job_log` VALUES (201, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-05 00:15:00');
INSERT INTO `sys_job_log` VALUES (202, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-05 00:20:00');
INSERT INTO `sys_job_log` VALUES (203, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 00:25:00');
INSERT INTO `sys_job_log` VALUES (204, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 00:30:00');
INSERT INTO `sys_job_log` VALUES (205, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-05 00:35:00');
INSERT INTO `sys_job_log` VALUES (206, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-05 00:40:00');
INSERT INTO `sys_job_log` VALUES (207, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 00:45:00');
INSERT INTO `sys_job_log` VALUES (208, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 00:50:00');
INSERT INTO `sys_job_log` VALUES (209, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 00:55:00');
INSERT INTO `sys_job_log` VALUES (210, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '拜访计划统计报告生成 总共耗时：3毫秒', '0', '', '2025-07-05 01:00:00');
INSERT INTO `sys_job_log` VALUES (211, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-05 01:00:00');
INSERT INTO `sys_job_log` VALUES (212, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：7毫秒', '0', '', '2025-07-05 01:00:00');
INSERT INTO `sys_job_log` VALUES (213, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 01:05:00');
INSERT INTO `sys_job_log` VALUES (214, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-05 01:10:00');
INSERT INTO `sys_job_log` VALUES (215, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-05 01:15:00');
INSERT INTO `sys_job_log` VALUES (216, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 01:20:00');
INSERT INTO `sys_job_log` VALUES (217, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-05 01:25:00');
INSERT INTO `sys_job_log` VALUES (218, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 01:30:00');
INSERT INTO `sys_job_log` VALUES (219, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 01:35:00');
INSERT INTO `sys_job_log` VALUES (220, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 01:40:00');
INSERT INTO `sys_job_log` VALUES (221, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-05 01:45:00');
INSERT INTO `sys_job_log` VALUES (222, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 01:50:00');
INSERT INTO `sys_job_log` VALUES (223, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 01:55:00');
INSERT INTO `sys_job_log` VALUES (224, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '拜访计划提醒记录清理 总共耗时：1毫秒', '0', '', '2025-07-05 02:00:00');
INSERT INTO `sys_job_log` VALUES (225, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:00:00');
INSERT INTO `sys_job_log` VALUES (226, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：8毫秒', '0', '', '2025-07-05 02:00:00');
INSERT INTO `sys_job_log` VALUES (227, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:05:00');
INSERT INTO `sys_job_log` VALUES (228, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:10:00');
INSERT INTO `sys_job_log` VALUES (229, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:15:00');
INSERT INTO `sys_job_log` VALUES (230, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：1毫秒', '0', '', '2025-07-05 02:20:00');
INSERT INTO `sys_job_log` VALUES (231, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:25:00');
INSERT INTO `sys_job_log` VALUES (232, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 02:30:00');
INSERT INTO `sys_job_log` VALUES (233, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 02:35:00');
INSERT INTO `sys_job_log` VALUES (234, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 02:40:00');
INSERT INTO `sys_job_log` VALUES (235, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 02:45:00');
INSERT INTO `sys_job_log` VALUES (236, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 02:50:00');
INSERT INTO `sys_job_log` VALUES (237, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 02:55:00');
INSERT INTO `sys_job_log` VALUES (238, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 03:00:00');
INSERT INTO `sys_job_log` VALUES (239, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：4毫秒', '0', '', '2025-07-05 03:00:00');
INSERT INTO `sys_job_log` VALUES (240, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 03:05:00');
INSERT INTO `sys_job_log` VALUES (241, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 03:10:00');
INSERT INTO `sys_job_log` VALUES (242, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 03:15:00');
INSERT INTO `sys_job_log` VALUES (243, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 03:20:00');
INSERT INTO `sys_job_log` VALUES (244, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 03:25:00');
INSERT INTO `sys_job_log` VALUES (245, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-05 03:30:00');
INSERT INTO `sys_job_log` VALUES (246, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 03:35:00');
INSERT INTO `sys_job_log` VALUES (247, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-05 03:40:00');
INSERT INTO `sys_job_log` VALUES (248, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-05 03:45:00');
INSERT INTO `sys_job_log` VALUES (249, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-05 03:50:00');
INSERT INTO `sys_job_log` VALUES (250, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：52毫秒', '0', '', '2025-07-06 22:50:00');
INSERT INTO `sys_job_log` VALUES (251, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-06 22:55:00');
INSERT INTO `sys_job_log` VALUES (252, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-06 23:00:00');
INSERT INTO `sys_job_log` VALUES (253, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：56毫秒', '0', '', '2025-07-06 23:00:00');
INSERT INTO `sys_job_log` VALUES (254, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-06 23:05:00');
INSERT INTO `sys_job_log` VALUES (255, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-06 23:10:00');
INSERT INTO `sys_job_log` VALUES (256, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-06 23:15:00');
INSERT INTO `sys_job_log` VALUES (257, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-06 23:20:00');
INSERT INTO `sys_job_log` VALUES (258, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-06 23:25:00');
INSERT INTO `sys_job_log` VALUES (259, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-06 23:30:00');
INSERT INTO `sys_job_log` VALUES (260, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-06 23:35:00');
INSERT INTO `sys_job_log` VALUES (261, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-06 23:40:00');
INSERT INTO `sys_job_log` VALUES (262, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-06 23:45:00');
INSERT INTO `sys_job_log` VALUES (263, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-06 23:50:00');
INSERT INTO `sys_job_log` VALUES (264, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-06 23:55:00');
INSERT INTO `sys_job_log` VALUES (265, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 00:00:00');
INSERT INTO `sys_job_log` VALUES (266, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：8毫秒', '0', '', '2025-07-07 00:00:00');
INSERT INTO `sys_job_log` VALUES (267, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-07 00:05:00');
INSERT INTO `sys_job_log` VALUES (268, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 00:10:00');
INSERT INTO `sys_job_log` VALUES (269, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 00:15:00');
INSERT INTO `sys_job_log` VALUES (270, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 00:20:00');
INSERT INTO `sys_job_log` VALUES (271, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 00:25:00');
INSERT INTO `sys_job_log` VALUES (272, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 00:30:00');
INSERT INTO `sys_job_log` VALUES (273, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 00:35:00');
INSERT INTO `sys_job_log` VALUES (274, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-07 00:40:00');
INSERT INTO `sys_job_log` VALUES (275, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 00:45:00');
INSERT INTO `sys_job_log` VALUES (276, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 00:50:00');
INSERT INTO `sys_job_log` VALUES (277, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 00:55:00');
INSERT INTO `sys_job_log` VALUES (278, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 01:00:00');
INSERT INTO `sys_job_log` VALUES (279, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '拜访计划统计报告生成 总共耗时：0毫秒', '0', '', '2025-07-07 01:00:00');
INSERT INTO `sys_job_log` VALUES (280, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：4毫秒', '0', '', '2025-07-07 01:00:00');
INSERT INTO `sys_job_log` VALUES (281, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 01:05:00');
INSERT INTO `sys_job_log` VALUES (282, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 01:10:00');
INSERT INTO `sys_job_log` VALUES (283, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 01:15:00');
INSERT INTO `sys_job_log` VALUES (284, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 01:20:00');
INSERT INTO `sys_job_log` VALUES (285, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 01:25:00');
INSERT INTO `sys_job_log` VALUES (286, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 01:30:00');
INSERT INTO `sys_job_log` VALUES (287, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 01:35:00');
INSERT INTO `sys_job_log` VALUES (288, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 01:40:00');
INSERT INTO `sys_job_log` VALUES (289, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 01:45:00');
INSERT INTO `sys_job_log` VALUES (290, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 01:50:00');
INSERT INTO `sys_job_log` VALUES (291, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 01:55:00');
INSERT INTO `sys_job_log` VALUES (292, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '拜访计划提醒记录清理 总共耗时：2毫秒', '0', '', '2025-07-07 02:00:00');
INSERT INTO `sys_job_log` VALUES (293, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 02:00:00');
INSERT INTO `sys_job_log` VALUES (294, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：8毫秒', '0', '', '2025-07-07 02:00:00');
INSERT INTO `sys_job_log` VALUES (295, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 02:05:00');
INSERT INTO `sys_job_log` VALUES (296, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 02:10:00');
INSERT INTO `sys_job_log` VALUES (297, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 02:15:00');
INSERT INTO `sys_job_log` VALUES (298, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 02:20:00');
INSERT INTO `sys_job_log` VALUES (299, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 02:25:00');
INSERT INTO `sys_job_log` VALUES (300, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 02:30:00');
INSERT INTO `sys_job_log` VALUES (301, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 02:35:00');
INSERT INTO `sys_job_log` VALUES (302, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 02:40:00');
INSERT INTO `sys_job_log` VALUES (303, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 02:45:00');
INSERT INTO `sys_job_log` VALUES (304, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 02:50:00');
INSERT INTO `sys_job_log` VALUES (305, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：57毫秒', '0', '', '2025-07-07 17:35:00');
INSERT INTO `sys_job_log` VALUES (306, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-07 17:40:00');
INSERT INTO `sys_job_log` VALUES (307, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 17:45:00');
INSERT INTO `sys_job_log` VALUES (308, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 17:50:00');
INSERT INTO `sys_job_log` VALUES (309, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 17:55:00');
INSERT INTO `sys_job_log` VALUES (310, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-07 18:00:00');
INSERT INTO `sys_job_log` VALUES (311, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：30毫秒', '0', '', '2025-07-07 18:00:00');
INSERT INTO `sys_job_log` VALUES (312, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：11毫秒', '0', '', '2025-07-07 18:05:00');
INSERT INTO `sys_job_log` VALUES (313, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：11毫秒', '0', '', '2025-07-07 18:10:00');
INSERT INTO `sys_job_log` VALUES (314, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-07 18:15:00');
INSERT INTO `sys_job_log` VALUES (315, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：27毫秒', '0', '', '2025-07-07 18:20:00');
INSERT INTO `sys_job_log` VALUES (316, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-07 18:25:00');
INSERT INTO `sys_job_log` VALUES (317, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-07 18:30:00');
INSERT INTO `sys_job_log` VALUES (318, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：26毫秒', '0', '', '2025-07-07 18:35:00');
INSERT INTO `sys_job_log` VALUES (319, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：13毫秒', '0', '', '2025-07-07 18:40:00');
INSERT INTO `sys_job_log` VALUES (320, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 18:45:00');
INSERT INTO `sys_job_log` VALUES (321, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：20毫秒', '0', '', '2025-07-07 18:50:00');
INSERT INTO `sys_job_log` VALUES (322, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-07 18:55:00');
INSERT INTO `sys_job_log` VALUES (323, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：24毫秒', '0', '', '2025-07-07 19:00:00');
INSERT INTO `sys_job_log` VALUES (324, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：24毫秒', '0', '', '2025-07-07 19:00:00');
INSERT INTO `sys_job_log` VALUES (325, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-07 19:05:00');
INSERT INTO `sys_job_log` VALUES (326, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 19:10:00');
INSERT INTO `sys_job_log` VALUES (327, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-07 19:15:00');
INSERT INTO `sys_job_log` VALUES (328, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-07 19:20:00');
INSERT INTO `sys_job_log` VALUES (329, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 19:25:00');
INSERT INTO `sys_job_log` VALUES (330, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 19:30:00');
INSERT INTO `sys_job_log` VALUES (331, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-07 19:35:00');
INSERT INTO `sys_job_log` VALUES (332, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-07 19:40:00');
INSERT INTO `sys_job_log` VALUES (333, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-07 19:45:00');
INSERT INTO `sys_job_log` VALUES (334, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-07 19:50:00');
INSERT INTO `sys_job_log` VALUES (335, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：21毫秒', '0', '', '2025-07-07 19:55:00');
INSERT INTO `sys_job_log` VALUES (336, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：15毫秒', '0', '', '2025-07-07 20:00:00');
INSERT INTO `sys_job_log` VALUES (337, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-07 20:00:00');
INSERT INTO `sys_job_log` VALUES (338, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 20:05:00');
INSERT INTO `sys_job_log` VALUES (339, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-07 20:10:00');
INSERT INTO `sys_job_log` VALUES (340, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：19毫秒', '0', '', '2025-07-07 20:15:00');
INSERT INTO `sys_job_log` VALUES (341, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：13毫秒', '0', '', '2025-07-07 20:20:00');
INSERT INTO `sys_job_log` VALUES (342, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：11毫秒', '0', '', '2025-07-07 20:25:00');
INSERT INTO `sys_job_log` VALUES (343, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：14毫秒', '0', '', '2025-07-07 20:30:00');
INSERT INTO `sys_job_log` VALUES (344, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-07 20:35:00');
INSERT INTO `sys_job_log` VALUES (345, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-07 20:40:00');
INSERT INTO `sys_job_log` VALUES (346, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：14毫秒', '0', '', '2025-07-07 20:45:00');
INSERT INTO `sys_job_log` VALUES (347, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 20:50:00');
INSERT INTO `sys_job_log` VALUES (348, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 20:55:00');
INSERT INTO `sys_job_log` VALUES (349, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 21:00:00');
INSERT INTO `sys_job_log` VALUES (350, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：3毫秒', '0', '', '2025-07-07 21:00:00');
INSERT INTO `sys_job_log` VALUES (351, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 21:05:00');
INSERT INTO `sys_job_log` VALUES (352, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 21:10:00');
INSERT INTO `sys_job_log` VALUES (353, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 21:15:00');
INSERT INTO `sys_job_log` VALUES (354, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 21:20:00');
INSERT INTO `sys_job_log` VALUES (355, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 21:25:00');
INSERT INTO `sys_job_log` VALUES (356, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 21:30:00');
INSERT INTO `sys_job_log` VALUES (357, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-07 21:35:00');
INSERT INTO `sys_job_log` VALUES (358, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 21:40:00');
INSERT INTO `sys_job_log` VALUES (359, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 21:45:00');
INSERT INTO `sys_job_log` VALUES (360, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 21:50:00');
INSERT INTO `sys_job_log` VALUES (361, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 21:55:00');
INSERT INTO `sys_job_log` VALUES (362, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 22:00:00');
INSERT INTO `sys_job_log` VALUES (363, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：4毫秒', '0', '', '2025-07-07 22:00:00');
INSERT INTO `sys_job_log` VALUES (364, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 22:05:00');
INSERT INTO `sys_job_log` VALUES (365, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 22:10:00');
INSERT INTO `sys_job_log` VALUES (366, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 22:15:00');
INSERT INTO `sys_job_log` VALUES (367, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-07 22:20:00');
INSERT INTO `sys_job_log` VALUES (368, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 22:25:00');
INSERT INTO `sys_job_log` VALUES (369, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 22:30:00');
INSERT INTO `sys_job_log` VALUES (370, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 22:35:00');
INSERT INTO `sys_job_log` VALUES (371, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 22:40:00');
INSERT INTO `sys_job_log` VALUES (372, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 22:45:00');
INSERT INTO `sys_job_log` VALUES (373, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-07 22:50:00');
INSERT INTO `sys_job_log` VALUES (374, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 22:55:00');
INSERT INTO `sys_job_log` VALUES (375, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 23:00:00');
INSERT INTO `sys_job_log` VALUES (376, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：5毫秒', '0', '', '2025-07-07 23:00:00');
INSERT INTO `sys_job_log` VALUES (377, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 23:05:00');
INSERT INTO `sys_job_log` VALUES (378, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 23:10:00');
INSERT INTO `sys_job_log` VALUES (379, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 23:15:00');
INSERT INTO `sys_job_log` VALUES (380, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-07 23:20:00');
INSERT INTO `sys_job_log` VALUES (381, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 23:25:00');
INSERT INTO `sys_job_log` VALUES (382, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：1毫秒', '0', '', '2025-07-07 23:30:00');
INSERT INTO `sys_job_log` VALUES (383, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 23:35:00');
INSERT INTO `sys_job_log` VALUES (384, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 23:40:00');
INSERT INTO `sys_job_log` VALUES (385, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-07 23:45:00');
INSERT INTO `sys_job_log` VALUES (386, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-07 23:50:00');
INSERT INTO `sys_job_log` VALUES (387, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-07 23:55:00');
INSERT INTO `sys_job_log` VALUES (388, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-08 00:00:00');
INSERT INTO `sys_job_log` VALUES (389, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：9毫秒', '0', '', '2025-07-08 00:00:00');
INSERT INTO `sys_job_log` VALUES (390, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：13毫秒', '0', '', '2025-07-08 00:05:00');
INSERT INTO `sys_job_log` VALUES (391, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-08 00:10:00');
INSERT INTO `sys_job_log` VALUES (392, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-08 00:15:00');
INSERT INTO `sys_job_log` VALUES (393, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-08 00:20:00');
INSERT INTO `sys_job_log` VALUES (394, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-08 00:25:00');
INSERT INTO `sys_job_log` VALUES (395, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-08 00:30:00');
INSERT INTO `sys_job_log` VALUES (396, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：24毫秒', '0', '', '2025-07-08 22:50:00');
INSERT INTO `sys_job_log` VALUES (397, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-08 22:55:00');
INSERT INTO `sys_job_log` VALUES (398, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：6毫秒', '0', '', '2025-07-08 23:00:00');
INSERT INTO `sys_job_log` VALUES (399, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-08 23:00:00');
INSERT INTO `sys_job_log` VALUES (400, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-08 23:05:00');
INSERT INTO `sys_job_log` VALUES (401, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-08 23:10:00');
INSERT INTO `sys_job_log` VALUES (402, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-08 23:15:00');
INSERT INTO `sys_job_log` VALUES (403, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-08 23:20:00');
INSERT INTO `sys_job_log` VALUES (404, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-08 23:25:00');
INSERT INTO `sys_job_log` VALUES (405, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-08 23:30:00');
INSERT INTO `sys_job_log` VALUES (406, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-08 23:35:00');
INSERT INTO `sys_job_log` VALUES (407, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-08 23:40:00');
INSERT INTO `sys_job_log` VALUES (408, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-08 23:45:00');
INSERT INTO `sys_job_log` VALUES (409, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-08 23:50:00');
INSERT INTO `sys_job_log` VALUES (410, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-08 23:55:00');
INSERT INTO `sys_job_log` VALUES (411, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-09 00:00:00');
INSERT INTO `sys_job_log` VALUES (412, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：12毫秒', '0', '', '2025-07-09 00:00:00');
INSERT INTO `sys_job_log` VALUES (413, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：12毫秒', '0', '', '2025-07-09 00:05:00');
INSERT INTO `sys_job_log` VALUES (414, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 00:10:00');
INSERT INTO `sys_job_log` VALUES (415, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 00:15:00');
INSERT INTO `sys_job_log` VALUES (416, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 00:20:00');
INSERT INTO `sys_job_log` VALUES (417, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 00:25:00');
INSERT INTO `sys_job_log` VALUES (418, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 00:30:00');
INSERT INTO `sys_job_log` VALUES (419, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 00:35:00');
INSERT INTO `sys_job_log` VALUES (420, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 00:40:00');
INSERT INTO `sys_job_log` VALUES (421, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 00:45:00');
INSERT INTO `sys_job_log` VALUES (422, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 00:50:00');
INSERT INTO `sys_job_log` VALUES (423, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 00:55:00');
INSERT INTO `sys_job_log` VALUES (424, '拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '拜访计划统计报告生成 总共耗时：1毫秒', '0', '', '2025-07-09 01:00:00');
INSERT INTO `sys_job_log` VALUES (425, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 01:00:00');
INSERT INTO `sys_job_log` VALUES (426, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：4毫秒', '0', '', '2025-07-09 01:00:00');
INSERT INTO `sys_job_log` VALUES (427, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 01:05:00');
INSERT INTO `sys_job_log` VALUES (428, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 01:10:00');
INSERT INTO `sys_job_log` VALUES (429, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 01:15:00');
INSERT INTO `sys_job_log` VALUES (430, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-09 01:20:00');
INSERT INTO `sys_job_log` VALUES (431, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-09 01:25:00');
INSERT INTO `sys_job_log` VALUES (432, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-09 01:30:00');
INSERT INTO `sys_job_log` VALUES (433, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 01:35:00');
INSERT INTO `sys_job_log` VALUES (434, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 01:40:00');
INSERT INTO `sys_job_log` VALUES (435, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-09 01:45:00');
INSERT INTO `sys_job_log` VALUES (436, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 01:50:00');
INSERT INTO `sys_job_log` VALUES (437, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 01:55:00');
INSERT INTO `sys_job_log` VALUES (438, '拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '拜访计划提醒记录清理 总共耗时：1毫秒', '0', '', '2025-07-09 02:00:00');
INSERT INTO `sys_job_log` VALUES (439, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 02:00:00');
INSERT INTO `sys_job_log` VALUES (440, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：2毫秒', '0', '', '2025-07-09 02:00:00');
INSERT INTO `sys_job_log` VALUES (441, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 02:05:00');
INSERT INTO `sys_job_log` VALUES (442, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 02:10:00');
INSERT INTO `sys_job_log` VALUES (443, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 02:15:00');
INSERT INTO `sys_job_log` VALUES (444, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：3毫秒', '0', '', '2025-07-09 02:20:00');
INSERT INTO `sys_job_log` VALUES (445, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：2毫秒', '0', '', '2025-07-09 02:25:00');
INSERT INTO `sys_job_log` VALUES (446, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：47毫秒', '0', '', '2025-07-09 16:25:00');
INSERT INTO `sys_job_log` VALUES (447, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-09 16:30:00');
INSERT INTO `sys_job_log` VALUES (448, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：10毫秒', '0', '', '2025-07-09 16:35:00');
INSERT INTO `sys_job_log` VALUES (449, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 16:40:00');
INSERT INTO `sys_job_log` VALUES (450, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 16:45:00');
INSERT INTO `sys_job_log` VALUES (451, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 16:50:00');
INSERT INTO `sys_job_log` VALUES (452, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：23毫秒', '0', '', '2025-07-09 16:55:00');
INSERT INTO `sys_job_log` VALUES (453, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：8毫秒', '0', '', '2025-07-09 17:00:00');
INSERT INTO `sys_job_log` VALUES (454, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：11毫秒', '0', '', '2025-07-09 17:00:00');
INSERT INTO `sys_job_log` VALUES (455, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：25毫秒', '0', '', '2025-07-09 17:05:00');
INSERT INTO `sys_job_log` VALUES (456, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：22毫秒', '0', '', '2025-07-09 17:10:00');
INSERT INTO `sys_job_log` VALUES (457, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-09 17:15:00');
INSERT INTO `sys_job_log` VALUES (458, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 17:20:00');
INSERT INTO `sys_job_log` VALUES (459, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-09 17:25:00');
INSERT INTO `sys_job_log` VALUES (460, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-09 17:30:00');
INSERT INTO `sys_job_log` VALUES (461, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 17:35:00');
INSERT INTO `sys_job_log` VALUES (462, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 17:40:00');
INSERT INTO `sys_job_log` VALUES (463, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：4毫秒', '0', '', '2025-07-09 17:45:00');
INSERT INTO `sys_job_log` VALUES (464, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-09 17:50:00');
INSERT INTO `sys_job_log` VALUES (465, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：14毫秒', '0', '', '2025-07-09 17:55:00');
INSERT INTO `sys_job_log` VALUES (466, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：7毫秒', '0', '', '2025-07-09 18:00:00');
INSERT INTO `sys_job_log` VALUES (467, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：14毫秒', '0', '', '2025-07-09 18:00:00');
INSERT INTO `sys_job_log` VALUES (468, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：9毫秒', '0', '', '2025-07-09 18:05:00');
INSERT INTO `sys_job_log` VALUES (469, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：6毫秒', '0', '', '2025-07-09 18:10:00');
INSERT INTO `sys_job_log` VALUES (470, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：5毫秒', '0', '', '2025-07-09 18:15:00');
INSERT INTO `sys_job_log` VALUES (471, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-09 18:20:00');
INSERT INTO `sys_job_log` VALUES (472, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-09 18:25:00');
INSERT INTO `sys_job_log` VALUES (473, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-09 18:30:00');
INSERT INTO `sys_job_log` VALUES (474, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-09 18:35:00');
INSERT INTO `sys_job_log` VALUES (475, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-09 18:40:00');
INSERT INTO `sys_job_log` VALUES (476, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-09 18:45:00');
INSERT INTO `sys_job_log` VALUES (477, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：14毫秒', '0', '', '2025-07-09 18:50:00');
INSERT INTO `sys_job_log` VALUES (478, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-09 18:55:00');
INSERT INTO `sys_job_log` VALUES (479, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：15毫秒', '0', '', '2025-07-09 19:00:00');
INSERT INTO `sys_job_log` VALUES (480, '拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '拜访计划状态自动更新 总共耗时：16毫秒', '0', '', '2025-07-09 19:00:00');
INSERT INTO `sys_job_log` VALUES (481, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：17毫秒', '0', '', '2025-07-09 19:05:00');
INSERT INTO `sys_job_log` VALUES (482, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-09 19:10:00');
INSERT INTO `sys_job_log` VALUES (483, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：16毫秒', '0', '', '2025-07-09 19:15:00');
INSERT INTO `sys_job_log` VALUES (484, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-09 19:20:00');
INSERT INTO `sys_job_log` VALUES (485, '拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '拜访计划提醒处理 总共耗时：18毫秒', '0', '', '2025-07-09 19:25:00');

-- ----------------------------


-- ========================================
-- 15. sys_logininfor
-- ========================================
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 309 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-09-09 15:15:32');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-09-09 16:26:25');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-09-11 20:44:13');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-09-11 20:44:16');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-09-11 20:44:18');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-29 19:06:05');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-29 21:31:11');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-30 12:32:48');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-30 22:28:37');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-31 02:29:01');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 19:53:34');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 19:56:24');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 20:03:47');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 20:04:05');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 20:06:36');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 22:05:26');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-02-22 22:08:31');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-02-22 22:08:36');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 22:08:39');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-02-22 22:27:22');
INSERT INTO `sys_logininfor` VALUES (120, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-02-22 22:28:26');
INSERT INTO `sys_logininfor` VALUES (121, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-22 22:33:42');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 20:50:42');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:05:33');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:05:57');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:08:04');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:08:31');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:14:08');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:14:08');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-02-23 21:14:08');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:14:22');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:14:22');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:15:54');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:15:54');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:20:53');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:20:53');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:21:40');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:21:40');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:54:33');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:54:33');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:55:43');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:55:43');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:57:26');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:57:26');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:57:42');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:57:42');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 21:58:20');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 21:58:20');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 22:01:12');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-23 22:21:30');
INSERT INTO `sys_logininfor` VALUES (150, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-23 22:21:37');
INSERT INTO `sys_logininfor` VALUES (151, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-25 17:10:11');
INSERT INTO `sys_logininfor` VALUES (152, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-02-25 17:10:16');
INSERT INTO `sys_logininfor` VALUES (153, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-25 17:10:20');
INSERT INTO `sys_logininfor` VALUES (154, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-25 18:09:19');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-25 19:35:31');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-25 20:17:00');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-25 20:18:55');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-25 20:37:00');
INSERT INTO `sys_logininfor` VALUES (159, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-25 20:37:05');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-02-27 22:08:10');
INSERT INTO `sys_logininfor` VALUES (161, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-27 22:08:14');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-02-27 22:10:26');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-27 22:10:41');
INSERT INTO `sys_logininfor` VALUES (164, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-02-28 00:05:59');
INSERT INTO `sys_logininfor` VALUES (165, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-02-28 00:06:03');
INSERT INTO `sys_logininfor` VALUES (166, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-02-28 00:06:10');
INSERT INTO `sys_logininfor` VALUES (167, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-03 21:01:26');
INSERT INTO `sys_logininfor` VALUES (168, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '退出成功', '2025-03-03 21:19:43');
INSERT INTO `sys_logininfor` VALUES (169, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-15 23:10:54');
INSERT INTO `sys_logininfor` VALUES (170, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-17 21:53:37');
INSERT INTO `sys_logininfor` VALUES (171, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-20 17:26:59');
INSERT INTO `sys_logininfor` VALUES (172, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-20 19:27:18');
INSERT INTO `sys_logininfor` VALUES (173, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-21 21:44:14');
INSERT INTO `sys_logininfor` VALUES (174, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-03-22 17:12:52');
INSERT INTO `sys_logininfor` VALUES (175, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-22 17:12:59');
INSERT INTO `sys_logininfor` VALUES (176, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-22 17:56:28');
INSERT INTO `sys_logininfor` VALUES (177, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-22 20:23:35');
INSERT INTO `sys_logininfor` VALUES (178, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-22 22:01:43');
INSERT INTO `sys_logininfor` VALUES (179, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-23 22:44:46');
INSERT INTO `sys_logininfor` VALUES (180, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-25 12:36:32');
INSERT INTO `sys_logininfor` VALUES (181, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-25 20:57:08');
INSERT INTO `sys_logininfor` VALUES (182, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-25 21:53:21');
INSERT INTO `sys_logininfor` VALUES (183, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-25 22:28:35');
INSERT INTO `sys_logininfor` VALUES (184, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-26 11:59:09');
INSERT INTO `sys_logininfor` VALUES (185, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-03-26 22:49:36');
INSERT INTO `sys_logininfor` VALUES (186, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-26 22:49:39');
INSERT INTO `sys_logininfor` VALUES (187, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-03-28 13:31:24');
INSERT INTO `sys_logininfor` VALUES (188, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-28 13:31:27');
INSERT INTO `sys_logininfor` VALUES (189, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-28 15:14:38');
INSERT INTO `sys_logininfor` VALUES (190, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-03-28 21:18:24');
INSERT INTO `sys_logininfor` VALUES (191, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-28 21:18:27');
INSERT INTO `sys_logininfor` VALUES (192, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-28 22:39:16');
INSERT INTO `sys_logininfor` VALUES (193, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-29 17:17:35');
INSERT INTO `sys_logininfor` VALUES (194, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-29 20:35:23');
INSERT INTO `sys_logininfor` VALUES (195, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-03-31 20:32:18');
INSERT INTO `sys_logininfor` VALUES (196, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-02 16:59:43');
INSERT INTO `sys_logininfor` VALUES (197, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-03 22:20:07');
INSERT INTO `sys_logininfor` VALUES (198, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-03 23:04:25');
INSERT INTO `sys_logininfor` VALUES (199, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-04 16:10:55');
INSERT INTO `sys_logininfor` VALUES (200, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-04 19:56:38');
INSERT INTO `sys_logininfor` VALUES (201, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-04 21:27:16');
INSERT INTO `sys_logininfor` VALUES (202, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-04 22:10:59');
INSERT INTO `sys_logininfor` VALUES (203, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 00:34:24');
INSERT INTO `sys_logininfor` VALUES (204, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 10:53:26');
INSERT INTO `sys_logininfor` VALUES (205, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 14:14:13');
INSERT INTO `sys_logininfor` VALUES (206, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 15:53:19');
INSERT INTO `sys_logininfor` VALUES (207, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 16:32:49');
INSERT INTO `sys_logininfor` VALUES (208, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 17:49:25');
INSERT INTO `sys_logininfor` VALUES (209, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 18:45:30');
INSERT INTO `sys_logininfor` VALUES (210, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 19:54:11');
INSERT INTO `sys_logininfor` VALUES (211, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 22:32:16');
INSERT INTO `sys_logininfor` VALUES (212, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 23:38:22');
INSERT INTO `sys_logininfor` VALUES (213, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-04-05 23:39:43');
INSERT INTO `sys_logininfor` VALUES (214, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-05 23:39:46');
INSERT INTO `sys_logininfor` VALUES (215, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-06 17:22:50');
INSERT INTO `sys_logininfor` VALUES (216, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-07 10:05:51');
INSERT INTO `sys_logininfor` VALUES (217, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-07 10:57:08');
INSERT INTO `sys_logininfor` VALUES (218, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-07 14:50:52');
INSERT INTO `sys_logininfor` VALUES (219, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-07 21:49:49');
INSERT INTO `sys_logininfor` VALUES (220, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-08 22:02:37');
INSERT INTO `sys_logininfor` VALUES (221, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-09 23:30:30');
INSERT INTO `sys_logininfor` VALUES (222, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-12 23:52:11');
INSERT INTO `sys_logininfor` VALUES (223, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-13 00:41:29');
INSERT INTO `sys_logininfor` VALUES (224, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-14 23:51:59');
INSERT INTO `sys_logininfor` VALUES (225, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-15 22:23:54');
INSERT INTO `sys_logininfor` VALUES (226, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-16 00:17:25');
INSERT INTO `sys_logininfor` VALUES (227, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-16 01:25:23');
INSERT INTO `sys_logininfor` VALUES (228, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-04-17 21:58:27');
INSERT INTO `sys_logininfor` VALUES (229, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-17 21:58:33');
INSERT INTO `sys_logininfor` VALUES (230, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-17 22:35:38');
INSERT INTO `sys_logininfor` VALUES (231, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-04-25 23:56:40');
INSERT INTO `sys_logininfor` VALUES (232, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-01 22:49:35');
INSERT INTO `sys_logininfor` VALUES (233, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-05-06 20:32:10');
INSERT INTO `sys_logininfor` VALUES (234, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-06 20:37:25');
INSERT INTO `sys_logininfor` VALUES (235, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-06 20:37:41');
INSERT INTO `sys_logininfor` VALUES (236, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-06 21:53:50');
INSERT INTO `sys_logininfor` VALUES (237, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-06 22:37:44');
INSERT INTO `sys_logininfor` VALUES (238, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-06 23:26:58');
INSERT INTO `sys_logininfor` VALUES (239, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-20 21:33:22');
INSERT INTO `sys_logininfor` VALUES (240, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-09 21:16:36');
INSERT INTO `sys_logininfor` VALUES (241, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-09 21:16:41');
INSERT INTO `sys_logininfor` VALUES (242, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-10 12:23:43');
INSERT INTO `sys_logininfor` VALUES (243, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-10 14:10:53');
INSERT INTO `sys_logininfor` VALUES (244, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-10 15:17:46');
INSERT INTO `sys_logininfor` VALUES (245, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-10 18:20:24');
INSERT INTO `sys_logininfor` VALUES (246, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-15 16:43:43');
INSERT INTO `sys_logininfor` VALUES (247, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-15 17:56:13');
INSERT INTO `sys_logininfor` VALUES (248, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-18 13:55:21');
INSERT INTO `sys_logininfor` VALUES (249, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-18 21:13:45');
INSERT INTO `sys_logininfor` VALUES (250, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-19 14:25:10');
INSERT INTO `sys_logininfor` VALUES (251, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-19 15:21:47');
INSERT INTO `sys_logininfor` VALUES (252, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-19 15:21:50');
INSERT INTO `sys_logininfor` VALUES (253, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-19 19:48:27');
INSERT INTO `sys_logininfor` VALUES (254, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-21 13:59:49');
INSERT INTO `sys_logininfor` VALUES (255, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-23 10:01:29');
INSERT INTO `sys_logininfor` VALUES (256, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-23 12:10:22');
INSERT INTO `sys_logininfor` VALUES (257, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-23 13:04:02');
INSERT INTO `sys_logininfor` VALUES (258, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-23 20:56:51');
INSERT INTO `sys_logininfor` VALUES (259, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-23 22:30:13');
INSERT INTO `sys_logininfor` VALUES (260, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-25 22:10:54');
INSERT INTO `sys_logininfor` VALUES (261, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 10:46:50');
INSERT INTO `sys_logininfor` VALUES (262, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 11:46:46');
INSERT INTO `sys_logininfor` VALUES (263, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 15:35:16');
INSERT INTO `sys_logininfor` VALUES (264, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 17:04:43');
INSERT INTO `sys_logininfor` VALUES (265, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 18:57:22');
INSERT INTO `sys_logininfor` VALUES (266, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 08:41:50');
INSERT INTO `sys_logininfor` VALUES (267, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-27 12:06:34');
INSERT INTO `sys_logininfor` VALUES (268, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-27 12:06:38');
INSERT INTO `sys_logininfor` VALUES (269, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 12:06:43');
INSERT INTO `sys_logininfor` VALUES (270, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 12:12:54');
INSERT INTO `sys_logininfor` VALUES (271, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-06-27 12:18:42');
INSERT INTO `sys_logininfor` VALUES (272, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 12:18:48');
INSERT INTO `sys_logininfor` VALUES (273, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 13:24:46');
INSERT INTO `sys_logininfor` VALUES (274, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-06-27 13:39:19');
INSERT INTO `sys_logininfor` VALUES (275, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 13:39:24');
INSERT INTO `sys_logininfor` VALUES (276, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 15:21:37');
INSERT INTO `sys_logininfor` VALUES (277, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 16:46:12');
INSERT INTO `sys_logininfor` VALUES (278, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 18:45:34');
INSERT INTO `sys_logininfor` VALUES (279, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-27 19:32:25');
INSERT INTO `sys_logininfor` VALUES (280, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 19:32:28');
INSERT INTO `sys_logininfor` VALUES (281, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 20:18:26');
INSERT INTO `sys_logininfor` VALUES (282, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 09:49:42');
INSERT INTO `sys_logininfor` VALUES (283, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 16:10:48');
INSERT INTO `sys_logininfor` VALUES (284, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 17:31:17');
INSERT INTO `sys_logininfor` VALUES (285, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-29 19:12:37');
INSERT INTO `sys_logininfor` VALUES (286, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 19:12:40');
INSERT INTO `sys_logininfor` VALUES (287, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 20:24:45');
INSERT INTO `sys_logininfor` VALUES (288, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 22:03:15');
INSERT INTO `sys_logininfor` VALUES (289, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 22:35:37');
INSERT INTO `sys_logininfor` VALUES (290, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 00:55:24');
INSERT INTO `sys_logininfor` VALUES (291, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 20:11:05');
INSERT INTO `sys_logininfor` VALUES (292, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 21:41:19');
INSERT INTO `sys_logininfor` VALUES (293, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 22:35:07');
INSERT INTO `sys_logininfor` VALUES (294, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-01 01:25:04');
INSERT INTO `sys_logininfor` VALUES (295, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-01 02:18:16');
INSERT INTO `sys_logininfor` VALUES (296, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-01 17:20:29');
INSERT INTO `sys_logininfor` VALUES (297, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-01 18:20:32');
INSERT INTO `sys_logininfor` VALUES (298, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-01 23:44:30');
INSERT INTO `sys_logininfor` VALUES (299, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-02 00:17:01');
INSERT INTO `sys_logininfor` VALUES (300, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-04 21:08:55');
INSERT INTO `sys_logininfor` VALUES (301, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-06 22:47:17');
INSERT INTO `sys_logininfor` VALUES (302, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-07 17:34:21');
INSERT INTO `sys_logininfor` VALUES (303, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-07 20:45:33');
INSERT INTO `sys_logininfor` VALUES (304, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-08 22:45:46');
INSERT INTO `sys_logininfor` VALUES (305, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-08 23:19:00');
INSERT INTO `sys_logininfor` VALUES (306, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-08 23:19:02');
INSERT INTO `sys_logininfor` VALUES (307, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-09 16:21:38');
INSERT INTO `sys_logininfor` VALUES (308, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-09 16:21:40');

-- ----------------------------


-- ========================================
-- 16. sys_oper_log
-- ========================================
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(1) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 183 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-09-09 16:31:44', 14);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-09-09 15:12:01\",\"icon\":\"guide\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":4,\"menuName\":\"若依官网\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"http://ruoyi.vip\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-09 16:31:57', 47);
INSERT INTO `sys_oper_log` VALUES (102, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-09-09 16:31:59', 5);
INSERT INTO `sys_oper_log` VALUES (103, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-09-09 15:12:01\",\"icon\":\"guide\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":4,\"menuName\":\"若依官网\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"http://ruoyi.vip\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-09 16:32:08', 31);
INSERT INTO `sys_oper_log` VALUES (104, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-09-09 16:32:10', 4);
INSERT INTO `sys_oper_log` VALUES (105, '部门管理', 2, 'com.ruoyi.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"逗你玩科技\",\"email\":\"<EMAIL>\",\"leader\":\"逗你玩\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"***********\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-09 16:39:11', 28);
INSERT INTO `sys_oper_log` VALUES (106, '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"crm_business_leads,crm_business_lead_user_associations,crm_business_lead_assignment_history,crm_business_follow_up_records,crm_business_customers,crm_business_contacts\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:44:43', 331);
INSERT INTO `sys_oper_log` VALUES (107, '代码生成', 8, 'com.ruoyi.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"crm_business_contacts,crm_business_customers,crm_business_follow_up_records,crm_business_lead_assignment_history,crm_business_lead_user_associations,crm_business_leads\"}', NULL, 0, NULL, '2024-09-11 20:44:52', 583);
INSERT INTO `sys_oper_log` VALUES (108, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"contacts\",\"className\":\"CrmBusinessContacts\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":1,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"ResponsiblePerson\",\"columnComment\":\"负责人\",\"columnId\":2,\"columnName\":\"responsible_person\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"responsiblePerson\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Name\",\"columnComment\":\"姓名\",\"columnId\":3,\"columnName\":\"name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"name\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CustomerName\",\"columnComment\":\"客户名称\",\"columnId\":4,\"columnName\":\"customer_name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQ', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:46:52', 277);
INSERT INTO `sys_oper_log` VALUES (109, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"customers\",\"className\":\"CrmBusinessCustomers\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":19,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"ResponsiblePerson\",\"columnComment\":\"负责人\",\"columnId\":20,\"columnName\":\"responsible_person\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"responsiblePerson\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CustomerName\",\"columnComment\":\"客户名称\",\"columnId\":21,\"columnName\":\"customer_name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"customerName\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":2,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CustomerSource\",\"columnComment\":\"客户来源\",\"columnId\":22,\"columnName\":\"customer_source\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isIns', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:47:09', 53);
INSERT INTO `sys_oper_log` VALUES (110, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"records\",\"className\":\"CrmBusinessFollowUpRecords\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":37,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"FollowUpContent\",\"columnComment\":\"跟进内容\",\"columnId\":38,\"columnName\":\"follow_up_content\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"editor\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"followUpContent\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"NextContactMethod\",\"columnComment\":\"下次联系方式\",\"columnId\":39,\"columnName\":\"next_contact_method\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"nextContactMethod\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"FollowUpMethod\",\"columnComment\":\"跟进方式\",\"columnId\":40,\"columnName\":\"follow_up_method\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:47:18', 76);
INSERT INTO `sys_oper_log` VALUES (111, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"history\",\"className\":\"CrmBusinessLeadAssignmentHistory\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":54,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"LeadId\",\"columnComment\":\"线索ID，外键，指向 Leads 表的 id\",\"columnId\":55,\"columnName\":\"lead_id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"leadId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"OldOwnerId\",\"columnComment\":\"前任所属人的用户ID\",\"columnId\":56,\"columnName\":\"old_owner_id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"oldOwnerId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":4,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"NewOwnerId\",\"columnComment\":\"新任所属人的用户ID\",\"columnId\":57,\"columnName\":\"new_owner_id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"i', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:47:31', 56);
INSERT INTO `sys_oper_log` VALUES (112, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"associations\",\"className\":\"CrmBusinessLeadUserAssociations\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":62,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"LeadId\",\"columnComment\":\"线索ID，外键，指向 Leads 表的 id\",\"columnId\":63,\"columnName\":\"lead_id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"leadId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"用户ID，外键，指向用户表（例如 Users 表）\",\"columnId\":64,\"columnName\":\"user_id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":5,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Status\",\"columnComment\":\"线索的状态，例如 \\\"已经转化的\\\"、\\\"正在跟踪的\\\" 或 \\\"没有分配的\\\"\",\"columnId\":65,\"columnName\":\"status\",\"columnType\":\"varchar(50)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"radio\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:48:16', 39);
INSERT INTO `sys_oper_log` VALUES (113, '代码生成', 2, 'com.ruoyi.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', '研发部门', '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"leads\",\"className\":\"CrmBusinessLeads\",\"columns\":[{\"capJavaField\":\"Id\",\"columnComment\":\"主键，自增字段\",\"columnId\":70,\"columnName\":\"id\",\"columnType\":\"int(11)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"ResponsiblePersonId\",\"columnComment\":\"负责人\",\"columnId\":71,\"columnName\":\"responsible_person_id\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"responsiblePersonId\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"LeadName\",\"columnComment\":\"线索名称\",\"columnId\":72,\"columnName\":\"lead_name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"leadName\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":6,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"LeadSource\",\"columnComment\":\"线索来源，例如网络、推荐等\",\"columnId\":73,\"columnName\":\"lead_source\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-09-11 20:44:43\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"is', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:48:26', 63);
INSERT INTO `sys_oper_log` VALUES (114, '代码生成', 8, 'com.ruoyi.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"crm_business_contacts,crm_business_customers,crm_business_follow_up_records,crm_business_lead_assignment_history,crm_business_lead_user_associations,crm_business_leads\"}', NULL, 0, NULL, '2024-09-11 20:48:39', 446);
INSERT INTO `sys_oper_log` VALUES (115, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"clipboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"客户管理\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"crm\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:55:34', 85);
INSERT INTO `sys_oper_log` VALUES (116, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/associations/index\",\"createTime\":\"2024-09-11 20:49:46\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1061,\"menuName\":\"线索和用户关联\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"associations\",\"perms\":\"crm:associations:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:55:58', 22);
INSERT INTO `sys_oper_log` VALUES (117, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/customers/index\",\"createTime\":\"2024-09-11 20:49:58\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1073,\"menuName\":\"客户\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"customers\",\"perms\":\"crm:customers:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:56:04', 22);
INSERT INTO `sys_oper_log` VALUES (118, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/contacts/index\",\"createTime\":\"2024-09-11 20:49:53\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1067,\"menuName\":\"联系人\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"contacts\",\"perms\":\"crm:contacts:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:56:13', 26);
INSERT INTO `sys_oper_log` VALUES (119, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/leads/index\",\"createTime\":\"2024-09-11 20:50:09\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1085,\"menuName\":\"线索\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"leads\",\"perms\":\"crm:leads:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:56:18', 25);
INSERT INTO `sys_oper_log` VALUES (120, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/records/index\",\"createTime\":\"2024-09-11 20:50:15\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1091,\"menuName\":\"跟进记录\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"records\",\"perms\":\"crm:records:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:56:23', 24);
INSERT INTO `sys_oper_log` VALUES (121, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/history/index\",\"createTime\":\"2024-09-11 20:50:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1079,\"menuName\":\"线索分配历史记录\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"history\",\"perms\":\"crm:history:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:56:34', 22);
INSERT INTO `sys_oper_log` VALUES (122, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/associations/index\",\"createTime\":\"2024-09-11 20:49:46\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1061,\"menuName\":\"线索和用户关联\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"associations\",\"perms\":\"crm:associations:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:01', 23);
INSERT INTO `sys_oper_log` VALUES (123, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/contacts/index\",\"createTime\":\"2024-09-11 20:49:53\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1067,\"menuName\":\"联系人\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"contacts\",\"perms\":\"crm:contacts:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:10', 27);
INSERT INTO `sys_oper_log` VALUES (124, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/customers/index\",\"createTime\":\"2024-09-11 20:49:58\",\"icon\":\"checkbox\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1073,\"menuName\":\"客户\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"customers\",\"perms\":\"crm:customers:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:20', 36);
INSERT INTO `sys_oper_log` VALUES (125, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/history/index\",\"createTime\":\"2024-09-11 20:50:04\",\"icon\":\"search\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1079,\"menuName\":\"线索分配历史记录\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"history\",\"perms\":\"crm:history:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:34', 13);
INSERT INTO `sys_oper_log` VALUES (126, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/leads/index\",\"createTime\":\"2024-09-11 20:50:09\",\"icon\":\"link\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1085,\"menuName\":\"线索\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"leads\",\"perms\":\"crm:leads:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:44', 20);
INSERT INTO `sys_oper_log` VALUES (127, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"crm/records/index\",\"createTime\":\"2024-09-11 20:50:15\",\"icon\":\"druid\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1091,\"menuName\":\"跟进记录\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"records\",\"perms\":\"crm:records:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:57:50', 38);
INSERT INTO `sys_oper_log` VALUES (128, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2024-09-09 15:12:01\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":11,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-09-11 20:58:02', 20);
INSERT INTO `sys_oper_log` VALUES (129, '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"crm_business_payments,crm_business_payment_plans,crm_business_payment_details,crm_business_opportunities,crm_business_contract_watchlist,crm_business_contract_user_relations,crm_business,crm_business_contracts,crm_business_ap_process_instance,crm_business_ap_node_detail,crm_business_ap_my_application,crm_business_ap_approval_process,crm_business_ap_approval_history\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:48:20', 395);
INSERT INTO `sys_oper_log` VALUES (130, '代码生成', 3, 'com.ruoyi.generator.controller.GenController.remove()', 'DELETE', 1, 'admin', '研发部门', '/tool/gen/6,5,4,3,2,1', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:48:55', 19);
INSERT INTO `sys_oper_log` VALUES (131, '代码生成', 8, 'com.ruoyi.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"crm_business,crm_business_ap_approval_history,crm_business_ap_approval_process,crm_business_ap_my_application,crm_business_ap_node_detail,crm_business_ap_process_instance,crm_business_contract_user_relations,crm_business_contract_watchlist,crm_business_contracts,crm_business_opportunities,crm_business_payment_details,crm_business_payment_plans,crm_business_payments\"}', NULL, 0, NULL, '2024-10-29 21:50:01', 806);
INSERT INTO `sys_oper_log` VALUES (132, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-10-29 21:57:11', 37);
INSERT INTO `sys_oper_log` VALUES (133, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"合同\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"contract\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:57:50', 27);
INSERT INTO `sys_oper_log` VALUES (134, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/contracts/index\",\"createTime\":\"2024-10-29 21:54:28\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1110,\"menuName\":\"合同\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1182,\"path\":\"contracts\",\"perms\":\"system:contracts:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:58:21', 21);
INSERT INTO `sys_oper_log` VALUES (135, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/relations/index\",\"createTime\":\"2024-10-29 21:56:05\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1170,\"menuName\":\"合同用户关系\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1182,\"path\":\"relations\",\"perms\":\"system:relations:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:58:36', 19);
INSERT INTO `sys_oper_log` VALUES (136, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/watchlist/index\",\"createTime\":\"2024-10-29 21:56:12\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1176,\"menuName\":\"合同关注\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1182,\"path\":\"watchlist\",\"perms\":\"system:watchlist:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:58:43', 18);
INSERT INTO `sys_oper_log` VALUES (137, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"education\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"审批设置\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"approval\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 21:59:51', 41);
INSERT INTO `sys_oper_log` VALUES (138, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/application/index\",\"createTime\":\"2024-10-29 21:54:12\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1098,\"menuName\":\"我的申请\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1183,\"path\":\"application\",\"perms\":\"system:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:00:08', 38);
INSERT INTO `sys_oper_log` VALUES (139, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/detail/index\",\"createTime\":\"2024-10-29 21:54:35\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1116,\"menuName\":\"审批节点详情\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1183,\"path\":\"detail\",\"perms\":\"system:detail:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:00:17', 39);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/history/index\",\"createTime\":\"2024-10-29 21:55:12\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1134,\"menuName\":\"审批历史\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1183,\"path\":\"history\",\"perms\":\"system:history:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:00:26', 22);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/process/index\",\"createTime\":\"2024-10-29 21:55:58\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1164,\"menuName\":\"审批处理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1183,\"path\":\"process\",\"perms\":\"system:process:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:00:36', 22);
INSERT INTO `sys_oper_log` VALUES (142, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/instance/index\",\"createTime\":\"2024-10-29 21:55:21\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1140,\"menuName\":\"流程实例\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1183,\"path\":\"instance\",\"perms\":\"system:instance:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:00:48', 38);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"example\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"回款\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"payment\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:03:10', 21);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/details/index\",\"createTime\":\"2024-10-29 21:54:54\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1122,\"menuName\":\"回款明细\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1184,\"path\":\"details\",\"perms\":\"system:details:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:03:23', 21);
INSERT INTO `sys_oper_log` VALUES (145, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/details/index\",\"createTime\":\"2024-10-29 21:55:02\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1128,\"menuName\":\"回款明细\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1184,\"path\":\"details\",\"perms\":\"system:details:list\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"修改菜单\'回款明细\'失败，菜单名称已存在\",\"code\":500}', 0, NULL, '2024-10-29 22:03:29', 5);
INSERT INTO `sys_oper_log` VALUES (146, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/details/index\",\"createTime\":\"2024-10-29 21:55:02\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1128,\"menuName\":\"回款明细\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1184,\"path\":\"details\",\"perms\":\"system:details:list\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"修改菜单\'回款明细\'失败，菜单名称已存在\",\"code\":500}', 0, NULL, '2024-10-29 22:03:41', 9);
INSERT INTO `sys_oper_log` VALUES (147, '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1128', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2024-10-29 22:04:42', 4);
INSERT INTO `sys_oper_log` VALUES (148, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/details/index\",\"createTime\":\"2024-10-29 21:55:02\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1128,\"menuName\":\"回款明细\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3,\"path\":\"details\",\"perms\":\"system:details:list\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:04:51', 38);
INSERT INTO `sys_oper_log` VALUES (149, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/payments/index\",\"createTime\":\"2024-10-29 21:55:42\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1152,\"menuName\":\"回款\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1184,\"path\":\"payments\",\"perms\":\"system:payments:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:05:02', 38);
INSERT INTO `sys_oper_log` VALUES (150, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/plans/index\",\"createTime\":\"2024-10-29 21:55:49\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1158,\"menuName\":\"回款计划\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1184,\"path\":\"plans\",\"perms\":\"system:plans:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:05:07', 24);
INSERT INTO `sys_oper_log` VALUES (151, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/opportunities/index\",\"createTime\":\"2024-10-29 21:55:33\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1146,\"menuName\":\"商机\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1097,\"path\":\"opportunities\",\"perms\":\"system:opportunities:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:05:19', 20);
INSERT INTO `sys_oper_log` VALUES (152, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/business/index\",\"createTime\":\"2024-10-29 21:54:21\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1104,\"menuName\":\"业务\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1,\"path\":\"business\",\"perms\":\"system:business:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-29 22:05:28', 29);
INSERT INTO `sys_oper_log` VALUES (153, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"tool/gen/index\",\"createTime\":\"2024-09-09 15:12:01\",\"icon\":\"code\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":116,\"menuName\":\"代码生成\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3,\"path\":\"gen\",\"perms\":\"tool:gen:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-30 22:54:17', 30);
INSERT INTO `sys_oper_log` VALUES (154, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-02-23 22:26:00', 9);
INSERT INTO `sys_oper_log` VALUES (155, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-02-24 21:09:24', 29);
INSERT INTO `sys_oper_log` VALUES (156, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-02-24 21:09:34', 0);
INSERT INTO `sys_oper_log` VALUES (157, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-02-24 21:32:33', 9);
INSERT INTO `sys_oper_log` VALUES (158, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-02-24 21:32:38', 0);
INSERT INTO `sys_oper_log` VALUES (159, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:43:53', 11);
INSERT INTO `sys_oper_log` VALUES (160, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:50:09', 13);
INSERT INTO `sys_oper_log` VALUES (161, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:56:21', 4);
INSERT INTO `sys_oper_log` VALUES (162, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:56:29', 4);
INSERT INTO `sys_oper_log` VALUES (163, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:56:36', 3);
INSERT INTO `sys_oper_log` VALUES (164, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 21:56:58', 3);
INSERT INTO `sys_oper_log` VALUES (165, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:00:17', 6297);
INSERT INTO `sys_oper_log` VALUES (166, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊沙发上\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:02:02', 3);
INSERT INTO `sys_oper_log` VALUES (167, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊沙发上\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:04:36', 3);
INSERT INTO `sys_oper_log` VALUES (168, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:05:36', 9414);
INSERT INTO `sys_oper_log` VALUES (169, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:12:19', 5);
INSERT INTO `sys_oper_log` VALUES (170, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:13:22', 5);
INSERT INTO `sys_oper_log` VALUES (171, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:13:25', 3);
INSERT INTO `sys_oper_log` VALUES (172, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:16:32', 6);
INSERT INTO `sys_oper_log` VALUES (173, '线索', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/leads', '127.0.0.1', '内网IP', '{\"customerIndustry\":\"internet\",\"customerName\":\"啊舒服\",\"email\":\"<EMAIL>\",\"id\":1,\"leadName\":\"你好\",\"leadSource\":\"电话\",\"nextContactTime\":\"2025-02-22\",\"params\":{},\"phone\":\"***********\",\"responsiblePersonId\":\"4\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-02-24 22:17:55', 3);
INSERT INTO `sys_oper_log` VALUES (174, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-03-15 23:12:21', 10);
INSERT INTO `sys_oper_log` VALUES (175, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-03-15 23:12:25', 0);
INSERT INTO `sys_oper_log` VALUES (176, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-03-15 23:12:26', 0);
INSERT INTO `sys_oper_log` VALUES (177, '线索和用户关联', 1, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.add()', 'POST', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{}}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.insertCrmBusinessLeadUserAssociations', '2025-03-15 23:12:37', 1);
INSERT INTO `sys_oper_log` VALUES (178, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-03-15 23:12:43', 1);
INSERT INTO `sys_oper_log` VALUES (179, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-03-15 23:14:31', 0);
INSERT INTO `sys_oper_log` VALUES (180, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', NULL, 1, 'Invalid bound statement (not found): com.ruoyi.crmAdmin.mapper.CrmBusinessLeadUserAssociationsMapper.updateCrmBusinessLeadUserAssociations', '2025-03-17 22:04:37', 16);
INSERT INTO `sys_oper_log` VALUES (181, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-03-17 22:09:00', 43);
INSERT INTO `sys_oper_log` VALUES (182, '线索和用户关联', 2, 'com.ruoyi.crmAdmin.controller.CrmBusinessLeadUserAssociationsController.edit()', 'PUT', 1, 'admin', '研发部门', '/crm/associations', '127.0.0.1', '内网IP', '{\"leadId\":1,\"params\":{},\"status\":\"converted\"}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-03-20 19:32:19', 24);

-- ----------------------------


-- ========================================
-- 17. sys_notice
-- ========================================
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2024-09-09 15:12:02', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2024-09-09 15:12:02', '', NULL, '管理员');

-- ----------------------------


-- ========================================
-- 合并完成
-- 总计: 17 个sys系统表
-- 生成时间: 2025-07-23 19:12:58
-- ========================================
