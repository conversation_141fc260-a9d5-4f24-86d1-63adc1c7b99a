<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wechatwork.mapper.SysWecomConfigMapper">
    
    <resultMap type="com.ruoyi.wechatwork.domain.SysWecomConfig" id="SysWecomConfigResult">
        <id     property="id"              column="id"                />
        <result property="corpId"          column="corp_id"          />
        <result property="corpSecret"      column="corp_secret"      />
        <result property="agentId"         column="agent_id"         />
        <result property="token"           column="token"            />
        <result property="encodingAesKey"  column="encoding_aes_key" />
        <result property="status"          column="status"           />
        <result property="createdAt"       column="created_at"       />
        <result property="updatedAt"       column="updated_at"       />
    </resultMap>

    <sql id="selectSysWecomConfigVo">
        select id, corp_id, corp_secret, agent_id, token, encoding_aes_key, status, created_at, updated_at
        from sys_wecom_config
    </sql>

    <select id="selectSysWecomConfigList" parameterType="com.ruoyi.wechatwork.domain.SysWecomConfig" resultMap="SysWecomConfigResult">
        <include refid="selectSysWecomConfigVo"/>
        <where>  
            <if test="corpId != null  and corpId != ''"> and corp_id = #{corpId}</if>
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysWecomConfigById" parameterType="Long" resultMap="SysWecomConfigResult">
        <include refid="selectSysWecomConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectSysWecomConfigByCorpId" parameterType="String" resultMap="SysWecomConfigResult">
        <include refid="selectSysWecomConfigVo"/>
        where corp_id = #{corpId}
    </select>
        
    <insert id="insertSysWecomConfig" parameterType="com.ruoyi.wechatwork.domain.SysWecomConfig" useGeneratedKeys="true" keyProperty="id">
        insert into sys_wecom_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id,</if>
            <if test="corpSecret != null and corpSecret != ''">corp_secret,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="token != null">token,</if>
            <if test="encodingAesKey != null">encoding_aes_key,</if>
            <if test="status != null">status,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">#{corpId},</if>
            <if test="corpSecret != null and corpSecret != ''">#{corpSecret},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="token != null">#{token},</if>
            <if test="encodingAesKey != null">#{encodingAesKey},</if>
            <if test="status != null">#{status},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
        </trim>
    </insert>

    <update id="updateSysWecomConfig" parameterType="com.ruoyi.wechatwork.domain.SysWecomConfig">
        update sys_wecom_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id = #{corpId},</if>
            <if test="corpSecret != null and corpSecret != ''">corp_secret = #{corpSecret},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="token != null">token = #{token},</if>
            <if test="encodingAesKey != null">encoding_aes_key = #{encodingAesKey},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysWecomConfigById" parameterType="Long">
        delete from sys_wecom_config where id = #{id}
    </delete>

    <delete id="deleteSysWecomConfigByIds" parameterType="String">
        delete from sys_wecom_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>