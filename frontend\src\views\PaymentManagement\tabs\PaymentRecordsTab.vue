<template>
  <div class="payment-records-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchInput"
          placeholder="记录编号/客户名称"
          style="width: 240px"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        <el-select
          v-model="statusFilter"
          placeholder="记录状态"
          style="width: 120px; margin-left: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="待确认" value="待确认" />
          <el-option label="已确认" value="已确认" />
          <el-option label="已退回" value="已退回" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 10px"
          @change="handleSearch"
        />
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><plus /></el-icon>
          新建记录
        </el-button>
        <el-button @click="handleExport">
          <el-icon><download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="recordNumber" label="记录编号" width="150" />
      <el-table-column prop="planNumber" label="关联计划" width="150" />
      <el-table-column prop="customerName" label="客户名称" width="180" />
      <el-table-column prop="paymentAmount" label="回款金额" width="120">
        <template #default="scope">
          <span>{{ formatCurrency(scope.row.paymentAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentDate" label="回款日期" width="120" />
      <el-table-column prop="paymentMethod" label="回款方式" width="100" />
      <el-table-column prop="recordStatus" label="记录状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.recordStatus)">
            {{ scope.row.recordStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="confirmUserName" label="确认人" width="100" />
      <el-table-column prop="recordTime" label="记录时间" width="150" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleView(scope.row)">
            <el-icon><view /></el-icon>
            查看
          </el-button>
          <el-button 
            v-if="scope.row.recordStatus === '待确认'" 
            type="success" 
            link 
            @click="handleConfirm(scope.row)"
          >
            <el-icon><check /></el-icon>
            确认
          </el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">
            <el-icon><delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      draggable
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录编号" prop="recordNumber">
              <el-input v-model="formData.recordNumber" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联计划" prop="planId">
              <el-select v-model="formData.planId" placeholder="请选择回款计划" style="width: 100%">
                <el-option
                  v-for="plan in paymentPlans"
                  :key="plan.id"
                  :label="plan.planNumber"
                  :value="plan.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="formData.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款金额" prop="paymentAmount">
              <el-input-number
                v-model="formData.paymentAmount"
                :min="0"
                :precision="2"
                :step="1000"
                controls-position="right"
                style="width: 100%"
                placeholder="请输入回款金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款日期" prop="paymentDate">
              <el-date-picker
                v-model="formData.paymentDate"
                type="date"
                placeholder="请选择回款日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款方式" prop="paymentMethod">
              <el-select v-model="formData.paymentMethod" placeholder="请选择回款方式" style="width: 100%">
                <el-option label="银行转账" value="银行转账" />
                <el-option label="现金" value="现金" />
                <el-option label="支票" value="支票" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="formData.bankAccount" placeholder="请输入银行账号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行名称" prop="bankName">
              <el-input v-model="formData.bankName" placeholder="请输入银行名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 回款记录详情抽屉 -->
    <common-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      :data="currentRow"
      :config="drawerConfig"
      size="60%"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Download, View, Check, Delete } from '@element-plus/icons-vue'
import Pagination from '@/components/Pagination/index.vue'
import CommonDrawer from '@/components/CommonDrawer/index.vue'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const drawerVisible = ref(false)
const dialogTitle = ref('')
const drawerTitle = ref('')
const currentRow = ref({})
const formData = ref({})
const selectedRows = ref([])
const searchInput = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const paymentPlans = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  recordNumber: undefined,
  customerName: undefined,
  recordStatus: undefined,
  startDate: undefined,
  endDate: undefined
})

// 表单引用和规则
const formRef = ref()
const formRules = {
  planId: [
    { required: true, message: '请选择关联计划', trigger: 'change' }
  ],
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' }
  ],
  paymentAmount: [
    { required: true, message: '请输入回款金额', trigger: 'blur' }
  ],
  paymentDate: [
    { required: true, message: '请选择回款日期', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: '请选择回款方式', trigger: 'change' }
  ]
}

// 抽屉配置
const drawerConfig = {
  headerFields: [
    { label: '记录编号', field: 'recordNumber' },
    { label: '客户名称', field: 'customerName' },
    { label: '回款金额', field: 'paymentAmount' },
    { label: '记录时间', field: 'recordTime' }
  ],
  actions: [
    {
      label: '确认',
      type: 'success',
      icon: 'Check',
      handler: (data: any) => handleConfirm(data)
    }
  ],
  menuItems: [
    {
      key: 'details',
      label: '基本信息',
      icon: 'Document'
    },
    {
      key: 'attachments',
      label: '附件',
      icon: 'Folder'
    }
  ]
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = {
      rows: [
        {
          id: 1,
          recordNumber: 'REC20240721001',
          planNumber: 'PLN20240721001',
          customerName: '测试客户A',
          paymentAmount: 50000.00,
          paymentDate: '2024-07-21',
          paymentMethod: '银行转账',
          recordStatus: '已确认',
          confirmUserName: '张三',
          recordTime: '2024-07-21 09:30:00',
          bankAccount: '****************',
          bankName: '中国工商银行',
          remark: '首期回款'
        },
        {
          id: 2,
          recordNumber: 'REC20240721002',
          planNumber: 'PLN20240721002',
          customerName: '测试客户B',
          paymentAmount: 30000.00,
          paymentDate: '2024-07-21',
          paymentMethod: '银行转账',
          recordStatus: '待确认',
          confirmUserName: '',
          recordTime: '2024-07-21 10:15:00',
          bankAccount: '****************',
          bankName: '中国建设银行',
          remark: '进度款'
        }
      ],
      total: 2
    }
    
    tableData.value = mockData.rows
    total.value = mockData.total
  } catch (error) {
    console.error('获取回款记录列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.recordNumber = searchInput.value
  queryParams.customerName = searchInput.value
  queryParams.recordStatus = statusFilter.value
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startDate = dateRange.value[0]
    queryParams.endDate = dateRange.value[1]
  } else {
    queryParams.startDate = undefined
    queryParams.endDate = undefined
  }
  queryParams.pageNum = 1
  getList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  formData.value = {
    recordNumber: 'REC' + new Date().toISOString().slice(0, 10).replace(/-/g, '') + String(Math.floor(Math.random() * 1000)).padStart(3, '0'),
    paymentDate: new Date().toISOString().slice(0, 10)
  }
  dialogTitle.value = '新增回款记录'
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  currentRow.value = row
  drawerTitle.value = `回款记录详情 - ${row.recordNumber}`
  drawerVisible.value = true
}

// 确认
const handleConfirm = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认回款记录 "${row.recordNumber}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 模拟确认操作
    row.recordStatus = '已确认'
    row.confirmUserName = '当前用户'
    ElMessage.success('确认成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认失败:', error)
    }
  }
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除回款记录 "${row.recordNumber}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除操作
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    // 模拟导出操作
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // 模拟提交操作
    if (formData.value.id) {
      ElMessage.success('修改成功')
    } else {
      ElMessage.success('新增成功')
    }
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
  formData.value = {}
}

// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '已确认': 'success',
    '已退回': 'danger'
  }
  return statusMap[status] || 'info'
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
  // 模拟获取回款计划列表
  paymentPlans.value = [
    { id: 1, planNumber: 'PLN20240721001' },
    { id: 2, planNumber: 'PLN20240721002' }
  ]
})
</script>

<style scoped>
.payment-records-tab {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}
</style>