package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;

/**
 * 客户跟进记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Mapper
public interface CrmCustomerFollowupRecordMapper {
    
    /**
     * 查询客户跟进记录
     * 
     * @param id 客户跟进记录主键
     * @return 客户跟进记录
     */
    CrmCustomerFollowupRecord selectCrmCustomerFollowupRecordById(Long id);

    /**
     * 查询客户跟进记录列表
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 客户跟进记录集合
     */
    List<CrmCustomerFollowupRecord> selectCrmCustomerFollowupRecordList(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 根据客户ID查询跟进记录列表
     * 
     * @param customerId 客户ID
     * @return 跟进记录集合
     */
    List<CrmCustomerFollowupRecord> selectFollowupRecordsByCustomerId(@Param("customerId") Long customerId);

    /**
     * 新增客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    int insertCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 修改客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    int updateCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 删除客户跟进记录
     * 
     * @param id 客户跟进记录主键
     * @return 结果
     */
    int deleteCrmCustomerFollowupRecordById(Long id);

    /**
     * 批量删除客户跟进记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCrmCustomerFollowupRecordByIds(Long[] ids);

    /**
     * 统计客户的跟进记录数量
     * 
     * @param customerId 客户ID
     * @return 跟进记录数量
     */
    int countFollowupRecordsByCustomerId(@Param("customerId") Long customerId);

    /**
     * 查询最近的跟进记录
     * 
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 最近的跟进记录
     */
    List<CrmCustomerFollowupRecord> selectRecentFollowupRecords(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 查询需要跟进的记录（根据下次跟进时间）
     * 
     * @param creatorId 创建者ID
     * @return 需要跟进的记录
     */
    List<CrmCustomerFollowupRecord> selectPendingFollowupRecords(@Param("creatorId") Long creatorId);
}