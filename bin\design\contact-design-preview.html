<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人详细资料设计方案预览</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section {
            margin-bottom: 40px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }

        /* 方案一：参考截图的现代卡片式布局 */
        .design-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            padding: 0;
            overflow: hidden;
        }

        .design-1 .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .design-1 .company-name {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .design-1 .contact-name {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .design-1 .follow-star {
            color: #fbbf24;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .design-1 .follow-star:hover {
            transform: scale(1.2);
        }

        .design-1 .content {
            padding: 20px;
        }

        .design-1 .info-section {
            margin-bottom: 20px;
        }

        .design-1 .section-label {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #f8fafc;
        }

        .design-1 .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .design-1 .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }

        .design-1 .info-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .design-1 .info-value {
            font-size: 14px;
            font-weight: 500;
        }

        /* 方案二：现代化分栏卡片设计 */
        .design-2 {
            background: white;
        }

        .design-2 .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .design-2 .contact-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .design-2 .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .design-2 .contact-details h3 {
            font-size: 18px;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .design-2 .contact-details p {
            color: #6b7280;
            font-size: 14px;
        }

        .design-2 .actions {
            display: flex;
            gap: 8px;
        }

        .design-2 .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .design-2 .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .design-2 .btn-secondary {
            background: #e5e7eb;
            color: #374151;
        }

        .design-2 .content {
            padding: 20px;
        }

        .design-2 .cards-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .design-2 .info-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s;
        }

        .design-2 .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .design-2 .card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .design-2 .card-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .design-2 .card-item:last-child {
            margin-bottom: 0;
        }

        .design-2 .card-label {
            color: #6b7280;
        }

        .design-2 .card-value {
            color: #1f2937;
            font-weight: 500;
        }

        /* 方案三：时尚渐变卡片设计 */
        .design-3 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .design-3::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .design-3 .header {
            padding: 24px;
            position: relative;
            z-index: 1;
        }

        .design-3 .contact-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .design-3 .company-name {
            font-size: 16px;
            opacity: 0.9;
        }

        .design-3 .content {
            padding: 0 24px 24px;
            position: relative;
            z-index: 1;
        }

        .design-3 .info-sections {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .design-3 .info-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 16px;
        }

        .design-3 .section-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #f8fafc;
        }

        .design-3 .info-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .design-3 .info-row {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
        }

        .design-3 .info-icon {
            width: 20px;
            color: rgba(255, 255, 255, 0.8);
        }

        .design-3 .info-text {
            flex: 1;
        }

        /* 方案四：简约图标式设计 */
        .design-4 {
            background: white;
            border: 1px solid #e5e7eb;
        }

        .design-4 .header {
            background: #f9fafb;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .design-4 .contact-name {
            font-size: 22px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .design-4 .company-role {
            color: #6b7280;
            font-size: 14px;
        }

        .design-4 .content {
            padding: 20px;
        }

        .design-4 .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .design-4 .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .design-4 .info-item:hover {
            background: #e2e8f0;
        }

        .design-4 .info-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .design-4 .icon-phone { background: #10b981; }
        .design-4 .icon-email { background: #3b82f6; }
        .design-4 .icon-building { background: #8b5cf6; }
        .design-4 .icon-user { background: #f59e0b; }
        .design-4 .icon-location { background: #ef4444; }
        .design-4 .icon-star { background: #ec4899; }
        .design-4 .icon-calendar { background: #06b6d4; }
        .design-4 .icon-phone-call { background: #84cc16; }

        .design-4 .info-text {
            font-size: 14px;
            color: #374151;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .design-2 .cards-grid,
            .design-4 .info-grid {
                grid-template-columns: 1fr;
            }
            
            .design-1 .info-grid {
                grid-template-columns: 1fr;
            }
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .comparison-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .rating {
            color: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px; color: #1f2937;">联系人详细资料设计方案预览</h1>
        
        <!-- 方案一 -->
        <div class="section">
            <h2 class="section-title">方案一：参考截图的现代卡片式布局</h2>
            <div class="design-1">
                <div class="header">
                    <div class="company-name">深圳市万创达科技有限公司</div>
                    <div class="contact-name">
                        江文强
                        <i class="fas fa-star follow-star"></i>
                    </div>
                </div>
                <div class="content">
                    <div class="info-section">
                        <div class="section-label">基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">姓名</div>
                                <div class="info-value">江文强</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">客户名称</div>
                                <div class="info-value">深圳市万创达科技有限公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">手机</div>
                                <div class="info-value">18813933384</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">电话</div>
                                <div class="info-value">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">邮箱</div>
                                <div class="info-value">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">职务</div>
                                <div class="info-value">销售</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">直属上级</div>
                                <div class="info-value">周金火</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">下次联系时间</div>
                                <div class="info-value">-</div>
                            </div>
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="section-label">系统信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">2024-10-31 22:19:39</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">更新时间</div>
                                <div class="info-value">2024-10-31 22:19:39</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案二 -->
        <div class="section">
            <h2 class="section-title">方案二：现代化分栏卡片设计</h2>
            <div class="design-2">
                <div class="header">
                    <div class="contact-info">
                        <div class="avatar">江</div>
                        <div class="contact-details">
                            <h3>江文强</h3>
                            <p>深圳市万创达科技有限公司 · 销售</p>
                        </div>
                    </div>
                    <div class="actions">
                        <button class="btn btn-secondary"><i class="fas fa-star"></i></button>
                        <button class="btn btn-primary"><i class="fas fa-edit"></i> 编辑</button>
                    </div>
                </div>
                <div class="content">
                    <div class="cards-grid">
                        <div class="info-card">
                            <div class="card-title">
                                <i class="fas fa-user"></i>
                                个人信息
                            </div>
                            <div class="card-item">
                                <span class="card-label">姓名</span>
                                <span class="card-value">江文强</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">职位</span>
                                <span class="card-value">销售</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">性别</span>
                                <span class="card-value">-</span>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">
                                <i class="fas fa-phone"></i>
                                联系方式
                            </div>
                            <div class="card-item">
                                <span class="card-label">手机</span>
                                <span class="card-value">18813933384</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">邮箱</span>
                                <span class="card-value">-</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">地址</span>
                                <span class="card-value">-</span>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">
                                <i class="fas fa-building"></i>
                                客户关系
                            </div>
                            <div class="card-item">
                                <span class="card-label">所属企业</span>
                                <span class="card-value">深圳市万创达科技有限公司</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">直属上级</span>
                                <span class="card-value">周金火</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">负责人</span>
                                <span class="card-value">-</span>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">
                                <i class="fas fa-info-circle"></i>
                                其他信息
                            </div>
                            <div class="card-item">
                                <span class="card-label">关键决策人</span>
                                <span class="card-value">否</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">下次联系</span>
                                <span class="card-value">-</span>
                            </div>
                            <div class="card-item">
                                <span class="card-label">创建时间</span>
                                <span class="card-value">2024-10-31</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案三 -->
        <div class="section">
            <h2 class="section-title">方案三：时尚渐变卡片设计</h2>
            <div class="design-3">
                <div class="header">
                    <div class="contact-name">
                        <i class="fas fa-star" style="color: #fbbf24;"></i>
                        江文强 · 销售
                    </div>
                    <div class="company-name">深圳市万创达科技有限公司</div>
                </div>
                <div class="content">
                    <div class="info-sections">
                        <div class="info-section">
                            <div class="section-title">
                                <i class="fas fa-phone"></i>
                                联系方式
                            </div>
                            <div class="info-list">
                                <div class="info-row">
                                    <i class="fas fa-mobile-alt info-icon"></i>
                                    <span class="info-text">手机号码：18813933384</span>
                                </div>
                                <div class="info-row">
                                    <i class="fas fa-envelope info-icon"></i>
                                    <span class="info-text">电子邮箱：暂无</span>
                                </div>
                                <div class="info-row">
                                    <i class="fas fa-map-marker-alt info-icon"></i>
                                    <span class="info-text">工作地址：暂无</span>
                                </div>
                            </div>
                        </div>
                        <div class="info-section">
                            <div class="section-title">
                                <i class="fas fa-users"></i>
                                关系网络
                            </div>
                            <div class="info-list">
                                <div class="info-row">
                                    <i class="fas fa-user-tie info-icon"></i>
                                    <span class="info-text">直属上级：周金火</span>
                                </div>
                                <div class="info-row">
                                    <i class="fas fa-crown info-icon"></i>
                                    <span class="info-text">关键决策人：否</span>
                                </div>
                                <div class="info-row">
                                    <i class="fas fa-handshake info-icon"></i>
                                    <span class="info-text">负责销售：张三</span>
                                </div>
                            </div>
                        </div>
                        <div class="info-section">
                            <div class="section-title">
                                <i class="fas fa-clock"></i>
                                重要时间
                            </div>
                            <div class="info-list">
                                <div class="info-row">
                                    <i class="fas fa-calendar-plus info-icon"></i>
                                    <span class="info-text">创建时间：2024-10-31 22:19:39</span>
                                </div>
                                <div class="info-row">
                                    <i class="fas fa-calendar-check info-icon"></i>
                                    <span class="info-text">下次联系：待安排</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案四 -->
        <div class="section">
            <h2 class="section-title">方案四：简约图标式设计</h2>
            <div class="design-4">
                <div class="header">
                    <div class="contact-name">江文强</div>
                    <div class="company-role">深圳市万创达科技有限公司 · 销售</div>
                </div>
                <div class="content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon icon-phone">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="info-text">18813933384</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-email">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-text">暂无邮箱</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-building">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="info-text">销售职位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-user">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="info-text">周金火 (上级)</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-location">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="info-text">暂无地址</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-star">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="info-text">普通联系人</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-calendar">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="info-text">2024-10-31 创建</div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon icon-phone-call">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="info-text">待安排联系</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对比表格 -->
        <div class="section">
            <h2 class="section-title">方案特点对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>方案</th>
                        <th>视觉效果</th>
                        <th>实现复杂度</th>
                        <th>信息密度</th>
                        <th>用户体验</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>方案一</strong><br>现代卡片式</td>
                        <td><span class="rating">★★★★☆</span></td>
                        <td>中等</td>
                        <td>高</td>
                        <td>很好</td>
                        <td>参考原型，稳妥选择</td>
                    </tr>
                    <tr>
                        <td><strong>方案二</strong><br>分栏卡片</td>
                        <td><span class="rating">★★★★★</span></td>
                        <td>较高</td>
                        <td>中等</td>
                        <td>优秀</td>
                        <td>现代化企业应用</td>
                    </tr>
                    <tr>
                        <td><strong>方案三</strong><br>时尚渐变</td>
                        <td><span class="rating">★★★★★</span></td>
                        <td>高</td>
                        <td>高</td>
                        <td>优秀</td>
                        <td>时尚前卫应用</td>
                    </tr>
                    <tr>
                        <td><strong>方案四</strong><br>简约图标</td>
                        <td><span class="rating">★★★☆☆</span></td>
                        <td>低</td>
                        <td>中等</td>
                        <td>良好</td>
                        <td>快速实现，简洁风格</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2 class="section-title">推荐建议</h2>
            <div style="padding: 20px; background: #f0f9ff; border-left: 4px solid #3b82f6; border-radius: 4px;">
                <p><strong>最推荐：方案二 - 现代化分栏卡片设计</strong></p>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>视觉层次清晰，信息分组合理</li>
                    <li>响应式设计，适配各种屏幕</li>
                    <li>符合现代UI设计趋势</li>
                    <li>组件化程度高，易于维护和扩展</li>
                    <li>用户体验优秀，操作直观</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
