<template>
  <div class="pending-tasks">
    <div class="page-header">
      <h2>我的待办任务</h2>
      <p>处理待审批的回款计划</p>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" :inline="true" label-width="80px">
        <el-form-item label="计划编号">
          <el-input 
            v-model="queryParams.planNumber" 
            placeholder="请输入计划编号" 
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input 
            v-model="queryParams.customerName" 
            placeholder="请输入客户名称" 
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="任务名称">
          <el-input 
            v-model="queryParams.taskName" 
            placeholder="请输入任务名称" 
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ filteredTasks.length }}</div>
            <div class="stat-label">待处理任务</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value urgent">{{ urgentTasksCount }}</div>
            <div class="stat-label">紧急任务</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ todayTasksCount }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ overdueTasksCount }}</div>
            <div class="stat-label">超时任务</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card class="task-list-card">
      <template #header>
        <span>待办任务列表</span>
      </template>

      <el-table 
        v-loading="loading" 
        :data="filteredTasks" 
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="planNumber" label="计划编号" width="150">
          <template #default="scope">
            <el-button type="primary" link @click="handleViewPlan(scope.row)">
              {{ scope.row.planNumber }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="180" />
        <el-table-column prop="totalAmount" label="金额" width="120">
          <template #default="scope">
            <span class="amount">¥{{ parseFloat(scope.row.totalAmount || 0).toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taskName" label="当前任务" width="150">
          <template #default="scope">
            <el-tag :type="getTaskTypeByName(scope.row.taskName)">
              {{ scope.row.taskName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitterName" label="提交人" width="120" />
        <el-table-column prop="createTime" label="提交时间" width="150">
          <template #default="scope">
            {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
          </template>
        </el-table-column>
        <el-table-column label="等待时长" width="120">
          <template #default="scope">
            <span :class="{ 'text-danger': isOverdue(scope.row.createTime) }">
              {{ getWaitingTime(scope.row.createTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="紧急程度" width="100">
          <template #default="scope">
            <el-tag :type="getUrgencyType(scope.row)">
              {{ getUrgencyText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <el-icon><check /></el-icon>
              处理
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="handleViewDetail(scope.row)"
            >
              <el-icon><view /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务处理对话框 -->
    <el-dialog 
      v-model="approvalDialogVisible" 
      :title="currentTask ? `处理任务: ${currentTask.taskName}` : '处理任务'"
      width="600px"
      @close="handleCloseApprovalDialog"
    >
      <div v-if="currentTask" class="task-detail">
        <!-- 任务基本信息 -->
        <el-descriptions title="任务信息" :column="2" border>
          <el-descriptions-item label="计划编号">{{ currentTask.planNumber }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentTask.customerName }}</el-descriptions-item>
          <el-descriptions-item label="金额">
            ¥{{ parseFloat(currentTask.totalAmount || 0).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="提交人">{{ currentTask.submitterName }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ parseTime(currentTask.createTime, '{y}-{m}-{d} {h}:{i}') }}
          </el-descriptions-item>
          <el-descriptions-item label="等待时长">
            {{ getWaitingTime(currentTask.createTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 审批表单 -->
        <el-form 
          :model="approvalForm" 
          :rules="approvalRules" 
          ref="approvalFormRef" 
          label-width="100px"
          style="margin-top: 20px"
        >
          <el-form-item label="审批意见" prop="comment">
            <el-input 
              v-model="approvalForm.comment" 
              type="textarea" 
              :rows="4" 
              placeholder="请输入审批意见（必填）"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            type="success" 
            @click="handleApproveConfirm" 
            :loading="approving"
          >
            <el-icon><check /></el-icon>
            审批通过
          </el-button>
          <el-button 
            type="danger" 
            @click="handleRejectConfirm" 
            :loading="rejecting"
          >
            <el-icon><close /></el-icon>
            审批拒绝
          </el-button>
          <el-button @click="approvalDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 计划详情对话框 -->
    <el-dialog 
      v-model="planDetailDialogVisible" 
      title="回款计划详情"
      width="80%"
      @close="planDetailDialogVisible = false"
    >
      <div v-if="currentPlanDetail">
        <!-- 这里可以展示完整的计划详情，包括分期信息等 -->
        <el-descriptions title="计划详情" :column="2" border>
          <el-descriptions-item label="计划编号">{{ currentPlanDetail.planNumber }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentPlanDetail.customerName }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ currentPlanDetail.responsibleUserName }}</el-descriptions-item>
          <el-descriptions-item label="计划状态">{{ currentPlanDetail.planStatus }}</el-descriptions-item>
          <el-descriptions-item label="总金额">
            ¥{{ parseFloat(currentPlanDetail.totalAmount || 0).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="已回款">
            ¥{{ parseFloat(currentPlanDetail.receivedAmount || 0).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="剩余金额">
            ¥{{ parseFloat(currentPlanDetail.remainingAmount || 0).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentPlanDetail.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="PendingTasks">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Check, Close, View } from '@element-plus/icons-vue'
import { getPendingTasks, approveTask, rejectTask } from '@/api/crm/paymentPlanWorkflow'
import { getPaymentPlan } from '@/api/crm/paymentPlan'

const { proxy } = getCurrentInstance()
const { parseTime } = proxy.useParseTime

// 响应式数据
const loading = ref(false)
const approving = ref(false)
const rejecting = ref(false)
const taskList = ref([])
const total = ref(0)
const approvalDialogVisible = ref(false)
const planDetailDialogVisible = ref(false)
const currentTask = ref(null)
const currentPlanDetail = ref(null)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  planNumber: '',
  customerName: '',
  taskName: ''
})

// 审批表单
const approvalForm = ref({
  comment: ''
})

const approvalRules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
  ]
}

// 定时器
let refreshTimer = null

// 计算属性 - 过滤任务
const filteredTasks = computed(() => {
  let filtered = taskList.value
  
  if (queryParams.value.planNumber) {
    filtered = filtered.filter(task => 
      task.planNumber && task.planNumber.includes(queryParams.value.planNumber)
    )
  }
  
  if (queryParams.value.customerName) {
    filtered = filtered.filter(task => 
      task.customerName && task.customerName.includes(queryParams.value.customerName)
    )
  }
  
  if (queryParams.value.taskName) {
    filtered = filtered.filter(task => 
      task.taskName && task.taskName.includes(queryParams.value.taskName)
    )
  }
  
  return filtered
})

// 计算属性 - 紧急任务数量
const urgentTasksCount = computed(() => {
  return filteredTasks.value.filter(task => {
    const hours = getWaitingHours(task.createTime)
    return hours >= 24 || parseFloat(task.totalAmount || 0) >= 100000
  }).length
})

// 计算属性 - 今日新增任务数量
const todayTasksCount = computed(() => {
  const today = new Date().toDateString()
  return filteredTasks.value.filter(task => {
    const taskDate = new Date(task.createTime).toDateString()
    return taskDate === today
  }).length
})

// 计算属性 - 超时任务数量  
const overdueTasksCount = computed(() => {
  return filteredTasks.value.filter(task => isOverdue(task.createTime)).length
})

// 获取待办任务列表
const getList = async () => {
  loading.value = true
  try {
    const response = await getPendingTasks(queryParams.value)
    taskList.value = response.rows || response.data || []
    total.value = response.total || taskList.value.length
  } catch (error) {
    console.error('获取待办任务失败:', error)
    ElMessage.error('获取待办任务失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  // 过滤逻辑在计算属性中处理
}

// 重置搜索
const handleReset = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 20,
    planNumber: '',
    customerName: '',
    taskName: ''
  }
}

// 刷新
const handleRefresh = () => {
  getList()
}

// 分页处理
const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  getList()
}

// 行点击
const handleRowClick = (row) => {
  // 可以在这里处理行点击事件
}

// 查看计划
const handleViewPlan = async (task) => {
  try {
    const response = await getPaymentPlan(task.planId)
    currentPlanDetail.value = response.data
    planDetailDialogVisible.value = true
  } catch (error) {
    console.error('获取计划详情失败:', error)
    ElMessage.error('获取计划详情失败')
  }
}

// 处理任务
const handleApprove = (task) => {
  currentTask.value = task
  approvalForm.value.comment = ''
  approvalDialogVisible.value = true
}

// 查看详情
const handleViewDetail = (task) => {
  handleViewPlan(task)
}

// 审批通过确认
const handleApproveConfirm = async () => {
  if (!currentTask.value) return
  
  try {
    await proxy.$refs.approvalFormRef.validate()
    
    approving.value = true
    await approveTask({
      taskId: currentTask.value.taskId,
      comment: approvalForm.value.comment
    })
    
    ElMessage.success('审批通过成功')
    approvalDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批通过失败: ' + error.message)
    }
  } finally {
    approving.value = false
  }
}

// 审批拒绝确认
const handleRejectConfirm = async () => {
  if (!currentTask.value) return
  
  try {
    await proxy.$refs.approvalFormRef.validate()
    
    await ElMessageBox.confirm(
      '确定要拒绝该回款计划的审批吗？',
      '审批拒绝确认',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    rejecting.value = true
    await rejectTask({
      taskId: currentTask.value.taskId,
      comment: approvalForm.value.comment
    })
    
    ElMessage.success('审批拒绝成功')
    approvalDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批拒绝失败: ' + error.message)
    }
  } finally {
    rejecting.value = false
  }
}

// 关闭审批对话框
const handleCloseApprovalDialog = () => {
  currentTask.value = null
  approvalForm.value.comment = ''
}

// 获取等待时长
const getWaitingTime = (createTime) => {
  if (!createTime) return '-'
  
  const now = new Date()
  const create = new Date(createTime)
  const diffMs = now - create
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays}天${diffHours % 24}小时`
  } else {
    return `${diffHours}小时`
  }
}

// 获取等待小时数
const getWaitingHours = (createTime) => {
  if (!createTime) return 0
  
  const now = new Date()
  const create = new Date(createTime)
  const diffMs = now - create
  return Math.floor(diffMs / (1000 * 60 * 60))
}

// 判断是否超时
const isOverdue = (createTime) => {
  return getWaitingHours(createTime) >= 48 // 48小时算超时
}

// 根据任务名称获取类型
const getTaskTypeByName = (taskName) => {
  const typeMap = {
    '部门主管审批': 'primary',
    '财务审批': 'warning',
    '总经理审批': 'danger'
  }
  return typeMap[taskName] || 'info'
}

// 获取紧急程度类型
const getUrgencyType = (task) => {
  const hours = getWaitingHours(task.createTime)
  const amount = parseFloat(task.totalAmount || 0)
  
  if (hours >= 48 || amount >= 500000) {
    return 'danger'
  } else if (hours >= 24 || amount >= 100000) {
    return 'warning'
  } else {
    return 'success'
  }
}

// 获取紧急程度文本
const getUrgencyText = (task) => {
  const hours = getWaitingHours(task.createTime)
  const amount = parseFloat(task.totalAmount || 0)
  
  if (hours >= 48 || amount >= 500000) {
    return '紧急'
  } else if (hours >= 24 || amount >= 100000) {
    return '重要'
  } else {
    return '普通'
  }
}

// 组件挂载时
onMounted(() => {
  getList()
  
  // 设置定时刷新（5分钟）
  refreshTimer = setInterval(() => {
    getList()
  }, 5 * 60 * 1000)
})

// 组件卸载时
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.pending-tasks {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card,
.task-list-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-value.urgent {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.amount {
  color: #e6a23c;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
}

.task-detail {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>