import { performance } from 'node:perf_hooks';
import { crawlProductDetail, crawlProductList } from './crawler.js';
import { batchInsertProducts, checkTableExists, closeConnection } from './db.js';

// 测试配置
const config = {
  maxRetries: 3,
  batchSize: 10,
  reportPath: './test-report.json'
};

// 添加日志函数
function logWithTimestamp(message, error = null) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  if (error) {
    console.error(`[${timestamp}] 错误详情:`, error);
  }
}

// 测试数据
const urlTypeMap = [
  {
    url: 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/15.html',
    type: 'FDM工程塑料'
  },
  {
    url: 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/12.html',
    type: 'SLA光敏树脂'
  },
  {
    url: 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/14.html',
    type: 'SLS热塑性塑料'
  },
  {
    url: 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/11.html',
    type: 'SLM金属材料'
  },
  {
    url: 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/13.html',
    type: '复模材料'
  }
];

// 测试报告
const testReport = {
  startTime: null,
  endTime: null,
  totalProducts: 0,
  successCount: 0,
  failureCount: 0,
  performance: {
    listCrawlTime: 0,
    detailCrawlTime: 0,
    dbInsertTime: 0
  },
  errors: []
};

async function main() {
  logWithTimestamp('爬虫任务开始');
  testReport.startTime = new Date().toISOString();
  
  try {
    // 检查表是否存在
    logWithTimestamp('正在检查数据库表...');
    const tableExists = await checkTableExists('crm_products');
    if (!tableExists) {
      throw new Error('crm_products表不存在，请先创建表');
    }
    logWithTimestamp('数据库表检查完成');

    // 遍历所有URL和类型
    for (const { url, type } of urlTypeMap) {
      logWithTimestamp(`\n===== 开始处理类型: ${type} =====`);
      logWithTimestamp(`正在爬取URL: ${url}`);

      try {
        // 爬取产品列表
        const listStart = performance.now();
        logWithTimestamp('正在爬取产品列表...');
        const products = await crawlProductList(url);
        const listEnd = performance.now();
        testReport.performance.listCrawlTime += listEnd - listStart;
        
        logWithTimestamp(`成功获取产品列表，共找到 ${products.length} 个产品`);
        testReport.totalProducts += products.length;

        // 批量处理产品
        const batches = [];
        for (let i = 0; i < products.length; i += config.batchSize) {
          batches.push(products.slice(i, i + config.batchSize));
        }
        logWithTimestamp(`将分 ${batches.length} 批处理产品`);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
          const batch = batches[batchIndex];
          logWithTimestamp(`开始处理第 ${batchIndex + 1}/${batches.length} 批`);
          const batchProducts = [];
          
          for (const product of batch) {
            logWithTimestamp(`正在处理产品: ${product.name}`);
            
            try {
              // 爬取产品详情
              const detailStart = performance.now();
              logWithTimestamp(`正在爬取产品详情: ${product.link}`);
              const detail = await crawlProductDetail(product.link, url);
              const detailEnd = performance.now();
              testReport.performance.detailCrawlTime += detailEnd - detailStart;
              logWithTimestamp(`产品详情爬取成功: ${product.name}`);

              // 合并信息
              batchProducts.push({
                ...product,
                ...detail,
                type
              });
              
              testReport.successCount++;
            } catch (error) {
              logWithTimestamp(`产品 "${product.name}" 详情爬取失败`, error);
              testReport.failureCount++;
              testReport.errors.push({
                product: product.name,
                error: error.message
              });
            }
          }

          // 批量插入
          if (batchProducts.length > 0) {
            try {
              const insertStart = performance.now();
              logWithTimestamp(`正在批量插入 ${batchProducts.length} 条数据到数据库...`);
              await batchInsertProducts(batchProducts);
              const insertEnd = performance.now();
              testReport.performance.dbInsertTime += insertEnd - insertStart;
              logWithTimestamp(`数据库插入成功`);
            } catch (error) {
              logWithTimestamp('批量插入数据库失败', error);
              testReport.failureCount += batchProducts.length;
              testReport.errors.push({
                batch: batchProducts.map(p => p.name),
                error: error.message
              });
            }
          }
        }
      } catch (error) {
        logWithTimestamp(`处理类型 ${type} 时发生错误`, error);
        testReport.failureCount += products?.length || 0;
        testReport.errors.push({
          type: 'batch',
          error: error.message
        });
      }
    }
  } catch (err) {
    logWithTimestamp('爬虫任务执行失败', err);
    testReport.errors.push({
      type: 'global',
      error: err.message
    });
  } finally {
    testReport.endTime = new Date().toISOString();
    await closeConnection();
    await generateReport();
    logWithTimestamp('爬虫任务结束');
  }
}

// 生成测试报告
async function generateReport() {
  const fs = await import('fs');
  fs.writeFileSync(config.reportPath, JSON.stringify(testReport, null, 2));
  logWithTimestamp(`测试报告已生成: ${config.reportPath}`);
}

main();
