package com.ruoyi.crm.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.common.domain.entity.CrmLeadPool;

/**
 * 线索池Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface ICrmLeadPoolService {
    
    /**
     * 查询线索池
     * 
     * @param id 线索池主键
     * @return 线索池
     */
    CrmLeadPool selectCrmLeadPoolById(Long id);

    /**
     * 查询线索池列表
     * 
     * @param crmLeadPool 线索池
     * @return 线索池集合
     */
    List<CrmLeadPool> selectCrmLeadPoolList(CrmLeadPool crmLeadPool);

    /**
     * 查询可用的线索池列表
     * 
     * @param crmLeadPool 线索池查询条件
     * @return 线索池集合
     */
    List<CrmLeadPool> selectAvailableLeadPoolList(CrmLeadPool crmLeadPool);

    /**
     * 新增线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    int insertCrmLeadPool(CrmLeadPool crmLeadPool);

    /**
     * 修改线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    int updateCrmLeadPool(CrmLeadPool crmLeadPool);

    /**
     * 批量删除线索池
     * 
     * @param ids 需要删除的线索池主键集合
     * @return 结果
     */
    int deleteCrmLeadPoolByIds(Long[] ids);

    /**
     * 删除线索池信息
     * 
     * @param id 线索池主键
     * @return 结果
     */
    int deleteCrmLeadPoolById(Long id);

    /**
     * 手动分配线索
     * 
     * @param poolIds 线索池ID数组
     * @param toUserId 分配给的用户ID
     * @param reason 分配原因
     * @return 结果
     */
    int assignLeads(Long[] poolIds, Long toUserId, String reason);

    /**
     * 批量分配线索
     * 
     * @param poolIds 线索池ID数组
     * @param userIds 用户ID数组
     * @param reason 分配原因
     * @return 结果
     */
    int batchAssignLeads(Long[] poolIds, Long[] userIds, String reason);

    /**
     * 抢单 - 销售人员主动获取线索
     * 
     * @param poolId 线索池ID
     * @param userId 用户ID
     * @param reason 抢单原因
     * @return 结果
     */
    int grabLead(Long poolId, Long userId, String reason);

    /**
     * 回收线索到池中
     * 
     * @param leadId 线索ID
     * @param reason 回收原因
     * @return 结果
     */
    int recycleLead(Long leadId, String reason);

    /**
     * 批量回收线索到池中
     * 
     * @param leadIds 线索ID数组
     * @param reason 回收原因
     * @return 结果
     */
    int batchRecycleLeads(Long[] leadIds, String reason);

    /**
     * 添加线索到池中
     * 
     * @param leadId 线索ID（可为空，表示纯线索池数据）
     * @param qualityLevel 质量等级
     * @param priority 优先级
     * @param region 地区
     * @param industry 行业
     * @param estimatedValue 预估价值
     * @param remarks 备注
     * @return 结果
     */
    int addToPool(Long leadId, String qualityLevel, Integer priority, 
                  String region, String industry, String estimatedValue, String remarks);

    /**
     * 更新线索池状态
     * 
     * @param poolId 线索池ID
     * @param status 状态
     * @return 结果
     */
    int updatePoolStatus(Long poolId, String status);

    /**
     * 获取线索池统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getLeadPoolStats();

    /**
     * 根据质量等级获取线索池列表
     * 
     * @param qualityLevel 质量等级
     * @return 线索池集合
     */
    List<CrmLeadPool> getLeadPoolByQualityLevel(String qualityLevel);

    /**
     * 根据地区获取线索池列表
     * 
     * @param region 地区
     * @return 线索池集合
     */
    List<CrmLeadPool> getLeadPoolByRegion(String region);

    /**
     * 根据行业获取线索池列表
     * 
     * @param industry 行业
     * @return 线索池集合
     */
    List<CrmLeadPool> getLeadPoolByIndustry(String industry);

    /**
     * 检查线索是否在池中
     * 
     * @param leadId 线索ID
     * @return 是否在池中
     */
    boolean isLeadInPool(Long leadId);

    /**
     * 根据线索ID获取线索池信息
     * 
     * @param leadId 线索ID
     * @return 线索池信息
     */
    CrmLeadPool getLeadPoolByLeadId(Long leadId);

    /**
     * 清理过期记录（定时任务使用）
     * 
     * @return 清理数量
     */
    int cleanupExpiredRecords();
}
