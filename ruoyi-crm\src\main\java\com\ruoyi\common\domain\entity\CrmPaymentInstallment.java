package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 回款分期对象 crm_payment_installment
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmPaymentInstallment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 回款计划ID */
    @Excel(name = "回款计划ID")
    private Long planId;

    /** 分期序号 */
    @Excel(name = "分期序号")
    private Integer installmentNumber;

    /** 分期名称 */
    @Excel(name = "分期名称")
    private String installmentName;

    /** 分期金额 */
    @Excel(name = "分期金额")
    private BigDecimal installmentAmount;

    /** 分期比例 */
    @Excel(name = "分期比例")
    private BigDecimal installmentPercentage;

    /** 计划回款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划回款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date plannedDate;

    /** 实际回款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际回款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualDate;

    /** 实际回款金额 */
    @Excel(name = "实际回款金额")
    private BigDecimal actualAmount;

    /** 逾期天数 */
    @Excel(name = "逾期天数")
    private Integer overdueDays;

    /** 违约金 */
    @Excel(name = "违约金")
    private BigDecimal penaltyAmount;

    /** 付款凭证路径 */
    @Excel(name = "付款凭证路径")
    private String paymentVoucher;

    /** 分期状态 */
    @Excel(name = "分期状态")
    private String installmentStatus;

    /** 删除标志 */
    private String delFlag;
}