<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPaymentApprovalMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmPaymentApproval" id="CrmPaymentApprovalResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="approvalLevel"    column="approval_level"    />
        <result property="approverId"    column="approver_id"    />
        <result property="approverName"    column="approver_name"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="approvalComments"    column="approval_comments"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCrmPaymentApprovalVo">
        select id, plan_id, approval_level, approver_id, approver_name, approval_status, 
               approval_time, approval_comments, del_flag, create_by, create_time, update_by, update_time 
        from crm_payment_approval
    </sql>

    <select id="selectCrmPaymentApprovalList" parameterType="com.ruoyi.common.domain.entity.CrmPaymentApproval" resultMap="CrmPaymentApprovalResult">
        <include refid="selectCrmPaymentApprovalVo"/>
        <where>
            del_flag = '0'
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="approverId != null "> and approver_id = #{approverId}</if>
            <if test="approverName != null  and approverName != ''"> and approver_name like concat('%', #{approverName}, '%')</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
        </where>
        order by approval_level
    </select>
    
    <select id="selectCrmPaymentApprovalById" parameterType="Long" resultMap="CrmPaymentApprovalResult">
        <include refid="selectCrmPaymentApprovalVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectApprovalsByPlanId" parameterType="Long" resultMap="CrmPaymentApprovalResult">
        <include refid="selectCrmPaymentApprovalVo"/>
        where plan_id = #{planId} and del_flag = '0'
        order by approval_level
    </select>

    <select id="selectPendingApprovals" parameterType="Long" resultMap="CrmPaymentApprovalResult">
        select a.id, a.plan_id, a.approval_level, a.approver_id, a.approver_name, a.approval_status, 
               a.approval_time, a.approval_comments, a.del_flag, a.create_by, a.create_time, 
               a.update_by, a.update_time 
        from crm_payment_approval a
        inner join crm_payment_plan p on a.plan_id = p.id
        where a.approver_id = #{approverId}
        and a.approval_status = '待审批'
        and a.del_flag = '0'
        and p.del_flag = '0'
        and p.approval_status = '审批中'
        order by a.create_time desc
    </select>

    <select id="getCurrentApprovalLevel" parameterType="Long" resultType="java.lang.Integer">
        select max(approval_level)
        from crm_payment_approval
        where plan_id = #{planId}
        and approval_status = '已通过'
        and del_flag = '0'
    </select>
        
    <insert id="insertCrmPaymentApproval" parameterType="com.ruoyi.common.domain.entity.CrmPaymentApproval" useGeneratedKeys="true" keyProperty="id">
        insert into crm_payment_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="approvalLevel != null">approval_level,</if>
            <if test="approverId != null">approver_id,</if>
            <if test="approverName != null">approver_name,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="approvalComments != null">approval_comments,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="approvalLevel != null">#{approvalLevel},</if>
            <if test="approverId != null">#{approverId},</if>
            <if test="approverName != null">#{approverName},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="approvalComments != null">#{approvalComments},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <insert id="batchInsertApprovals" parameterType="java.util.List">
        insert into crm_payment_approval(plan_id, approval_level, approver_id, approver_name, 
                                       approval_status, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.approvalLevel}, #{item.approverId}, #{item.approverName}, 
             #{item.approvalStatus}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateCrmPaymentApproval" parameterType="com.ruoyi.common.domain.entity.CrmPaymentApproval">
        update crm_payment_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalComments != null">approval_comments = #{approvalComments},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmPaymentApprovalById" parameterType="Long">
        update crm_payment_approval set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmPaymentApprovalByIds" parameterType="String">
        update crm_payment_approval set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteApprovalsByPlanId" parameterType="Long">
        update crm_payment_approval set del_flag = '2' where plan_id = #{planId}
    </delete>
</mapper>