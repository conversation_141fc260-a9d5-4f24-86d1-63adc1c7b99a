package com.ruoyi.crm.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.BusinessLog;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.BusinessOperationLog;
import com.ruoyi.common.domain.entity.CrmContactFollowupRecords;
import com.ruoyi.common.domain.entity.CrmContactOperationLog;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.enums.BusinessClass;
import com.ruoyi.common.enums.OperationType;
import com.ruoyi.common.service.BusinessLogService;
import com.ruoyi.common.service.ICrmContactFollowService;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.common.service.ICrmCustomerContactRelationService;
import com.ruoyi.common.service.ICrmUserHierarchyService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.crm.service.ICrmContactFollowupRecordsService;

@RestController
@RequestMapping("/front/crm/contacts")
public class CrmContactsController extends BaseController {
    
    @Autowired
    private ICrmContactsService crmContactsService;
    
    @Autowired
    private ICrmUserHierarchyService crmUserHierarchyService;
    
    @Autowired
    private ICrmContactFollowService crmContactFollowService;
    
    
    @Autowired
    private ICrmContactFollowupRecordsService crmContactFollowupRecordsService;
    
    @Autowired
    private BusinessLogService businessLogService;
    
    @Autowired
    private ICrmCustomerContactRelationService customerContactRelationService;

    /**
     * 查询联系人列表
     * @param crmContacts 查询条件
     * @param filterType 筛选类型：all(全部)、mine(我负责的)、subordinate(下属负责的)、following(我关注的)
     */
    @GetMapping("/list")
    public TableDataInfo getContactsList(CrmContacts crmContacts, 
                                       @RequestParam(required = false) String filterType) {
        // 获取当前登录用户ID
        Long currentUserId = SecurityUtils.getUserId();
        
        // 根据筛选类型设置查询条件
        if (StringUtils.isNotEmpty(filterType)) {
            switch (filterType) {
                case "mine":
                    // 查询我负责的联系人
                    crmContacts.setResponsiblePersonId(String.valueOf(currentUserId));
                    break;
                case "subordinate":
                    // 查询下属负责的联系人
                    List<Long> subordinateIds = crmUserHierarchyService.getAllSubordinateIds(currentUserId, 5); // 默认查询5级下属
                    if (subordinateIds != null && !subordinateIds.isEmpty()) {
                        crmContacts.setSubordinateIds(subordinateIds);
                    } else {
                        // 如果没有下属，则设置一个无效的ID，确保不返回任何结果
                        // 使用 new ArrayList<>() 替代 List.of() 以避免反射问题
                        crmContacts.setSubordinateIds(new java.util.ArrayList<>(java.util.List.of(-1L)));
                    }
                    break;
                case "following":
                    // 查询我关注的联系人
                    crmContacts.setFollowerId(currentUserId);
                    break;
                case "all":
                default:
                    // 查询全部联系人（不设置额外条件）
                    break;
            }
        }
        
        startPage(); // 将startPage移动到主查询之前
        List<CrmContacts> list = crmContactsService.selectCrmContactsList(crmContacts);
        return getDataTable(list);
    }

    /**
     * 获取联系人详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmContactsService.selectCrmContactsById(id));
    }

    /**
     * 新增联系人
     */
    @PostMapping
    @BusinessLog(
        businessClass = BusinessClass.CONTACT,
        operationType = OperationType.CREATE,
        description = "创建联系人",
        entityClass = CrmContacts.class
    )
    public AjaxResult addWithRecord(@RequestBody CrmContacts crmContacts) {
        try {
            int rows = crmContactsService.insertCrmContacts(crmContacts);
            if (rows > 0) {
                return AjaxResult.success(crmContacts);
            }
            return AjaxResult.error();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改联系人
     */
    @PutMapping
    @BusinessLog(
        businessClass = BusinessClass.CONTACT,
        operationType = OperationType.UPDATE,
        description = "修改联系人",
        entityClass = CrmContacts.class
    )
    public AjaxResult editWithRecord(@RequestBody CrmContacts crmContacts) {
        try {
            int rows = crmContactsService.updateCrmContacts(crmContacts);
            if (rows > 0) {
                return AjaxResult.success(crmContacts);
            }
            return AjaxResult.error();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除联系人
     */
    @DeleteMapping("/{ids}")
    @BusinessLog(
        businessClass = BusinessClass.CONTACT,
        operationType = OperationType.DELETE,
        description = "删除联系人",
        entityClass = CrmContacts.class
    )
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmContactsService.deleteCrmContactsByIds(ids));
    }

    /**
     * 导出联系人
     */
    @PostMapping("/export")
    public AjaxResult export(CrmContacts crmContacts) {
        List<CrmContacts> list = crmContactsService.selectCrmContactsList(crmContacts);
        ExcelUtil<CrmContacts> util = new ExcelUtil<CrmContacts>(CrmContacts.class);
        return util.exportExcel(list, "联系人数据");
    }

    /**
     * 关注联系人
     */
    @PostMapping("/follow/{contactId}")
    public AjaxResult followContact(@PathVariable("contactId") Long contactId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            int result = crmContactFollowService.followContact(contactId, currentUserId);
            if (result > 0) {
                return AjaxResult.success("关注成功");
            } else {
                return AjaxResult.error("关注失败，可能已经关注过该联系人");
            }
        } catch (Exception e) {
            return AjaxResult.error("关注失败：" + e.getMessage());
        }
    }

    /**
     * 取消关注联系人
     */
    @DeleteMapping("/follow/{contactId}")
    public AjaxResult unfollowContact(@PathVariable("contactId") Long contactId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            int result = crmContactFollowService.unfollowContact(contactId, currentUserId);
            if (result > 0) {
                return AjaxResult.success("取消关注成功");
            } else {
                return AjaxResult.error("取消关注失败，可能未关注该联系人");
            }
        } catch (Exception e) {
            return AjaxResult.error("取消关注失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否关注联系人
     */
    @GetMapping("/follow/status/{contactId}")
    public AjaxResult getFollowStatus(@PathVariable("contactId") Long contactId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            boolean isFollowing = crmContactFollowService.isFollowing(contactId, currentUserId);
            return AjaxResult.success("查询成功", isFollowing);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量关注联系人
     */
    @PostMapping("/follow/batch")
    public AjaxResult batchFollowContacts(@RequestBody List<Long> contactIds) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            int successCount = crmContactFollowService.batchFollowContacts(contactIds, currentUserId);
            return AjaxResult.success("批量关注完成，成功关注 " + successCount + " 个联系人");
        } catch (Exception e) {
            return AjaxResult.error("批量关注失败：" + e.getMessage());
        }
    }

    /**
     * 批量取消关注联系人
     */
    @DeleteMapping("/follow/batch")
    public AjaxResult batchUnfollowContacts(@RequestBody List<Long> contactIds) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            int successCount = crmContactFollowService.batchUnfollowContacts(contactIds, currentUserId);
            return AjaxResult.success("批量取消关注完成，成功取消关注 " + successCount + " 个联系人");
        } catch (Exception e) {
            return AjaxResult.error("批量取消关注失败：" + e.getMessage());
        }
    }

    /**
     * 获取联系人活动记录
     */
    @GetMapping("/{contactId}/activities")
    public AjaxResult getContactActivities(@PathVariable("contactId") Long contactId) {
        try {
            // 查询联系人相关的跟进记录
            List<CrmContactFollowupRecords> activities = crmContactFollowupRecordsService.selectByContactId(contactId);
            return AjaxResult.success("查询成功", activities);
        } catch (Exception e) {
            logger.error("获取联系人活动记录失败", e);
            return AjaxResult.error("获取联系人活动记录失败：" + e.getMessage());
        }
    }

    /**
     * 创建联系人活动记录
     */
    @PostMapping("/activities")
    public AjaxResult createContactActivity(@RequestBody CrmContactFollowupRecords record) {
        try {
            int result = crmContactFollowupRecordsService.insertCrmContactFollowupRecords(record);
            if (result > 0) {
                return AjaxResult.success("创建活动记录成功");
            } else {
                return AjaxResult.error("创建活动记录失败");
            }
        } catch (Exception e) {
            logger.error("创建联系人活动记录失败", e);
            return AjaxResult.error("创建联系人活动记录失败：" + e.getMessage());
        }
    }

    /**
     * 编辑联系人活动记录
     */
    @PutMapping("/{contactId}/activities/{id}")
    public AjaxResult updateContactActivity(
            @PathVariable("contactId") Long contactId,
            @PathVariable("id") Long id, 
            @RequestBody CrmContactFollowupRecords record) {
        try {
            // 验证活动记录是否属于该联系人
            CrmContactFollowupRecords existingRecord = crmContactFollowupRecordsService.selectCrmContactFollowupRecordsById(id);
            if (existingRecord == null) {
                return AjaxResult.error("活动记录不存在");
            }
            if (!contactId.equals(existingRecord.getContactId())) {
                return AjaxResult.error("活动记录不属于该联系人");
            }
            
            // 更新记录
            record.setId(id);
            record.setContactId(contactId);
            
            int result = crmContactFollowupRecordsService.updateCrmContactFollowupRecords(record);
            if (result > 0) {
                return AjaxResult.success("更新活动记录成功");
            } else {
                return AjaxResult.error("更新活动记录失败");
            }
        } catch (Exception e) {
            logger.error("更新联系人活动记录失败", e);
            return AjaxResult.error("更新联系人活动记录失败：" + e.getMessage());
        }
    }

    /**
     * 删除联系人活动记录
     */
    @DeleteMapping("/{contactId}/activities/{id}")
    public AjaxResult deleteContactActivity(
            @PathVariable("contactId") Long contactId,
            @PathVariable("id") Long id) {
        try {
            // 验证活动记录是否属于该联系人
            CrmContactFollowupRecords existingRecord = crmContactFollowupRecordsService.selectCrmContactFollowupRecordsById(id);
            if (existingRecord == null) {
                return AjaxResult.error("活动记录不存在");
            }
            if (!contactId.equals(existingRecord.getContactId())) {
                return AjaxResult.error("活动记录不属于该联系人");
            }
            
            int result = crmContactFollowupRecordsService.deleteCrmContactFollowupRecordsById(id);
            if (result > 0) {
                return AjaxResult.success("删除活动记录成功");
            } else {
                return AjaxResult.error("删除活动记录失败");
            }
        } catch (Exception e) {
            logger.error("删除联系人活动记录失败", e);
            return AjaxResult.error("删除联系人活动记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取联系人活动记录统计
     */
    @GetMapping("/{contactId}/activities/stats")
    public AjaxResult getContactActivityStats(@PathVariable("contactId") Long contactId) {
        try {
            // 验证联系人是否存在
            CrmContacts contact = crmContactsService.selectCrmContactsById(contactId);
            if (contact == null) {
                return AjaxResult.error("联系人不存在");
            }
            
            // 统计活动记录数量和获取最新记录
            int totalCount = crmContactFollowupRecordsService.countByContactId(contactId);
            CrmContactFollowupRecords latestRecord = crmContactFollowupRecordsService.selectLatestByContactId(contactId);
            
            // 构建统计信息
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("lastActivityTime", latestRecord != null ? latestRecord.getCreatedAt() : null);
            
            return AjaxResult.success("获取统计信息成功", stats);
        } catch (Exception e) {
            logger.error("获取联系人活动记录统计失败", e);
            return AjaxResult.error("获取联系人活动记录统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取联系人操作日志
     */
    @GetMapping("/{contactId}/logs")
    public AjaxResult getContactLogs(@PathVariable("contactId") Long contactId,
                                    @RequestParam(required = false) String operationType,
                                    @RequestParam(required = false) Long operatorId,
                                    @RequestParam(required = false) String startTime,
                                    @RequestParam(required = false) String endTime,
                                    @RequestParam(defaultValue = "1") Integer current,
                                    @RequestParam(defaultValue = "20") Integer size) {
        logger.warn("========== 获取联系人操作日志接口被调用 ==========");
        logger.warn("联系人ID: {}", contactId);
        logger.warn("请求参数: operationType={}, operatorId={}, startTime={}, endTime={}, current={}, size={}", 
                   operationType, operatorId, startTime, endTime, current, size);
        try {
            // 验证联系人是否存在
            CrmContacts contact = crmContactsService.selectCrmContactsById(contactId);
            if (contact == null) {
                return AjaxResult.error("联系人不存在");
            }
            
            // 构建查询条件
            CrmContactOperationLog queryLog = new CrmContactOperationLog();
            queryLog.setContactId(contactId);
            if (operationType != null && !operationType.isEmpty()) {
                queryLog.setOperationType(operationType);
            }
            if (operatorId != null) {
                queryLog.setOperatorId(operatorId);
            }
            if (startTime != null && !startTime.isEmpty()) {
                queryLog.setBeginTime(startTime);
            }
            if (endTime != null && !endTime.isEmpty()) {
                queryLog.setEndTime(endTime);
            }
            
            // 使用带分页的日志服务查询联系人操作日志
            List<BusinessOperationLog> businessLogs = businessLogService.queryLogsPage("CONTACT", contactId, current, size);
            
            // 获取总数
            int totalCount = businessLogService.countLogs("CONTACT", contactId);
            
            logger.warn("查询到业务日志数量: {}, 总数: {}", businessLogs.size(), totalCount);
            
            // 如果没有日志，为测试目的添加一条模拟数据
            if (businessLogs.isEmpty()) {
                logger.warn("没有找到联系人 {} 的操作日志，添加测试数据", contactId);
                BusinessOperationLog testLog = new BusinessOperationLog();
                testLog.setId(999L);
                testLog.setBusinessType("CONTACT");
                testLog.setBusinessId(contactId);
                testLog.setOperationType("CREATE");
                testLog.setOperationDesc("测试日志记录");
                testLog.setOperatorName("系统");
                testLog.setOperationTime(new java.util.Date());
                testLog.setSuccess(true);
                businessLogs.add(testLog);
            }
            
            // 转换为前端期望的格式
            List<Map<String, Object>> logs = businessLogs.stream().map(log -> {
                Map<String, Object> logMap = new HashMap<>();
                logMap.put("id", log.getId());
                logMap.put("operationType", log.getOperationType());
                logMap.put("operationDesc", log.getOperationDesc());
                logMap.put("operatorName", log.getOperatorName());
                logMap.put("operationTime", log.getOperationTime());
                logMap.put("ipAddress", log.getIpAddress());
                logMap.put("userAgent", log.getUserAgent());
                logMap.put("success", log.getSuccess());
                logMap.put("errorMessage", log.getErrorMessage());
                logMap.put("requestParams", log.getRequestParams());
                logMap.put("responseResult", log.getResponseResult());
                logMap.put("fieldChanges", log.getFieldChanges());
                return logMap;
            }).collect(java.util.stream.Collectors.toList());
            
            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", logs);
            result.put("total", totalCount);  // 使用真实的总数而不是当前页数量
            result.put("current", current);
            result.put("size", size);
            
            return AjaxResult.success("获取操作日志成功", result);
        } catch (Exception e) {
            logger.error("获取联系人操作日志失败", e);
            return AjaxResult.error("获取联系人操作日志失败：" + e.getMessage());
        }
    }

    /**
     * 获取联系人操作记录（别名）
     */
    @GetMapping("/{contactId}/operations")
    public AjaxResult getContactOperations(@PathVariable("contactId") Long contactId,
                                          @RequestParam(required = false) String operationType,
                                          @RequestParam(required = false) Long operatorId,
                                          @RequestParam(required = false) String startTime,
                                          @RequestParam(required = false) String endTime,
                                          @RequestParam(defaultValue = "1") Integer pageNum,
                                          @RequestParam(defaultValue = "20") Integer pageSize) {
        // 调用日志接口，保持接口一致性
        return getContactLogs(contactId, operationType, operatorId, startTime, endTime, pageNum, pageSize);
    }

    /**
     * 根据客户ID查询联系人
     */
    @GetMapping("/customer/{customerId}")
    public TableDataInfo getContactsByCustomer(@PathVariable("customerId") Long customerId) {
        try {
            List<CrmContacts> contacts = customerContactRelationService.selectContactsByCustomerId(customerId);
            return getDataTable(contacts);
        } catch (Exception e) {
            logger.error("根据客户ID查询联系人失败", e);
            return new TableDataInfo(new ArrayList<>(), 0);
        }
    }

    /**
     * 搜索联系人
     */
    @GetMapping("/search")
    public TableDataInfo searchContacts(@RequestParam(required = false) String keyword,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "100") Integer pageSize) {
        try {
            CrmContacts searchCondition = new CrmContacts();
            if (keyword != null && !keyword.trim().isEmpty()) {
                searchCondition.setName(keyword);
            }
            
            startPage();
            List<CrmContacts> contacts = crmContactsService.selectCrmContactsList(searchCondition);
            return getDataTable(contacts);
        } catch (Exception e) {
            logger.error("搜索联系人失败", e);
            return new TableDataInfo(new ArrayList<>(), 0);
        }
    }
}