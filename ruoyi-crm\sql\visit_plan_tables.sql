-- ================================================
-- 拜访计划功能数据库建表脚本
-- 创建时间：2024-06-30
-- 说明：包含拜访计划主表、提醒记录表、状态变更日志表
-- ================================================

-- 1. 拜访计划主表
CREATE TABLE `crm_visit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_name` varchar(200) NOT NULL COMMENT '拜访计划名称',
  `visit_time` datetime NOT NULL COMMENT '预计拜访时间',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID，外键关联crm_business_customers.id',
  `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称（冗余）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID，外键关联crm_business_contacts.id',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余）',
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '商机ID，外键关联crm_business_opportunities.id',
  `opportunity_name` varchar(200) DEFAULT NULL COMMENT '商机名称（冗余）',
  `visit_purpose` text COMMENT '拜访目的',
  `remind_time` int(11) DEFAULT '30' COMMENT '提前提醒时间（分钟）',
  `remark` text COMMENT '备注',
  `postpone_reason` varchar(500) DEFAULT NULL COMMENT '延期原因',
  `postpone_remark` text COMMENT '延期备注',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
  `cancel_remark` text COMMENT '取消备注',
  `followup_content` text COMMENT '跟进记录内容',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID，外键关联sys_user.user_id',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `status` varchar(20) NOT NULL DEFAULT 'planned' COMMENT '状态：planned-计划中,ongoing-进行中,completed-已完成,postponed-已延期,cancelled-已取消',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称（冗余）',
  `actual_visit_time` datetime DEFAULT NULL COMMENT '实际拜访时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划表';

-- 2. 拜访计划提醒记录表
CREATE TABLE `crm_visit_plan_reminders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `remind_type` varchar(20) NOT NULL COMMENT '提醒类型：system-系统内,wechat-企业微信,sms-短信,email-邮件',
  `remind_time` datetime NOT NULL COMMENT '提醒时间',
  `remind_status` varchar(20) DEFAULT 'pending' COMMENT '提醒状态：pending-待发送,sent-已发送,failed-发送失败',
  `send_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `recipient_id` bigint(20) DEFAULT NULL COMMENT '接收人ID',
  `recipient_name` varchar(100) DEFAULT NULL COMMENT '接收人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_remind_time` (`remind_time`),
  KEY `idx_remind_status` (`remind_status`),
  KEY `idx_remind_type` (`remind_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划提醒记录表';

-- 3. 拜访计划状态变更日志表
CREATE TABLE `crm_visit_plan_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '变更原因',
  `change_remark` text COMMENT '变更备注',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型：create-创建,update-更新,postpone-延期,cancel-取消,complete-完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划状态变更日志表';

-- 4. 添加外键约束（可选，根据实际情况决定是否启用）
-- ALTER TABLE `crm_visit_plans` ADD CONSTRAINT `fk_visit_plan_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_business_customers` (`id`) ON DELETE RESTRICT;
-- ALTER TABLE `crm_visit_plans` ADD CONSTRAINT `fk_visit_plan_contact` FOREIGN KEY (`contact_id`) REFERENCES `crm_business_contacts` (`id`) ON DELETE SET NULL;
-- ALTER TABLE `crm_visit_plans` ADD CONSTRAINT `fk_visit_plan_opportunity` FOREIGN KEY (`opportunity_id`) REFERENCES `crm_business_opportunities` (`id`) ON DELETE SET NULL;
-- ALTER TABLE `crm_visit_plans` ADD CONSTRAINT `fk_visit_plan_owner` FOREIGN KEY (`owner_id`) REFERENCES `sys_user` (`user_id`) ON DELETE RESTRICT;

-- ALTER TABLE `crm_visit_plan_reminders` ADD CONSTRAINT `fk_reminder_visit_plan` FOREIGN KEY (`visit_plan_id`) REFERENCES `crm_visit_plans` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `crm_visit_plan_logs` ADD CONSTRAINT `fk_log_visit_plan` FOREIGN KEY (`visit_plan_id`) REFERENCES `crm_visit_plans` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `crm_visit_plan_logs` ADD CONSTRAINT `fk_log_operator` FOREIGN KEY (`operator_id`) REFERENCES `sys_user` (`user_id`) ON DELETE RESTRICT;

-- 5. 初始化字典数据
-- 拜访计划状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('拜访计划状态', 'visit_plan_status', '0', 'admin', NOW(), '拜访计划状态列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(1, '计划中', 'planned', 'visit_plan_status', '', 'primary', 'Y', '0', 'admin', NOW(), '拜访计划状态-计划中'),
(2, '进行中', 'ongoing', 'visit_plan_status', '', 'warning', 'N', '0', 'admin', NOW(), '拜访计划状态-进行中'),
(3, '已完成', 'completed', 'visit_plan_status', '', 'success', 'N', '0', 'admin', NOW(), '拜访计划状态-已完成'),
(4, '已延期', 'postponed', 'visit_plan_status', '', 'info', 'N', '0', 'admin', NOW(), '拜访计划状态-已延期'),
(5, '已取消', 'cancelled', 'visit_plan_status', '', 'danger', 'N', '0', 'admin', NOW(), '拜访计划状态-已取消');

-- 提醒类型字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('提醒类型', 'reminder_type', '0', 'admin', NOW(), '拜访计划提醒类型列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(1, '系统内提醒', 'system', 'reminder_type', '', 'primary', 'Y', '0', 'admin', NOW(), '系统内消息提醒'),
(2, '企业微信', 'wechat', 'reminder_type', '', 'success', 'N', '0', 'admin', NOW(), '企业微信提醒'),
(3, '短信提醒', 'sms', 'reminder_type', '', 'warning', 'N', '0', 'admin', NOW(), '短信提醒'),
(4, '邮件提醒', 'email', 'reminder_type', '', 'info', 'N', '0', 'admin', NOW(), '邮件提醒');

-- 提醒状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('提醒状态', 'reminder_status', '0', 'admin', NOW(), '拜访计划提醒状态列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(1, '待发送', 'pending', 'reminder_status', '', 'info', 'Y', '0', 'admin', NOW(), '提醒待发送'),
(2, '已发送', 'sent', 'reminder_status', '', 'success', 'N', '0', 'admin', NOW(), '提醒已发送'),
(3, '发送失败', 'failed', 'reminder_status', '', 'danger', 'N', '0', 'admin', NOW(), '提醒发送失败');

-- 6. 创建视图（可选）
-- 拜访计划统计视图
CREATE OR REPLACE VIEW `v_visit_plan_statistics` AS
SELECT 
    owner_id,
    owner_name,
    dept_id,
    dept_name,
    COUNT(*) as total_plans,
    SUM(CASE WHEN status = 'planned' THEN 1 ELSE 0 END) as planned_count,
    SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
    SUM(CASE WHEN status = 'postponed' THEN 1 ELSE 0 END) as postponed_count,
    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count,
    ROUND(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completion_rate,
    ROUND(SUM(CASE WHEN status = 'completed' AND actual_visit_time <= visit_time THEN 1 ELSE 0 END) * 100.0 / 
          NULLIF(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END), 0), 2) as on_time_rate
FROM `crm_visit_plans` 
WHERE del_flag = '0'
GROUP BY owner_id, owner_name, dept_id, dept_name;

-- 7. 测试数据（开发环境使用）
-- INSERT INTO `crm_visit_plans` (`visit_plan_name`, `visit_time`, `customer_id`, `customer_name`, `contact_id`, `contact_name`, `visit_purpose`, `remind_time`, `owner_id`, `owner_name`, `status`, `dept_id`, `create_by`) VALUES 
-- ('测试拜访计划1', '2024-07-01 10:00:00', 1, '测试客户A', 1, '张三', '产品演示和需求调研', 30, 1, 'admin', 'planned', 103, 'admin'),
-- ('测试拜访计划2', '2024-07-02 14:00:00', 2, '测试客户B', 2, '李四', '合同签署', 60, 1, 'admin', 'planned', 103, 'admin'),
-- ('测试拜访计划3', '2024-06-28 09:00:00', 1, '测试客户A', 1, '张三', '项目实施讨论', 15, 1, 'admin', 'completed', 103, 'admin');

COMMIT;
