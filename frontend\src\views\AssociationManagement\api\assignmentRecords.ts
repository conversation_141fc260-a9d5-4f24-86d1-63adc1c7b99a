import request from '@/utils/request';

interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

// 分配记录查询参数
export interface AssignmentRecordQuery {
    pageNum: number;
    pageSize: number;
    leadId?: number;
    poolId?: number;
    fromUserId?: number;
    toUserId?: number;
    assignmentType?: string;
    operatorId?: number;
    operatorName?: string;
    beginTime?: string;
    endTime?: string;
    [key: string]: any;
}

// 分配记录实体
export interface AssignmentRecord {
    id?: number;
    leadId: number;
    poolId?: number;
    fromUserId?: number;
    toUserId?: number;
    assignmentType: string;
    assignmentReason?: string;
    assignmentTime: string;
    operatorId: number;
    operatorName?: string;
    remarks?: string;
    // 扩展字段
    leadName?: string;
    customerName?: string;
    fromUserName?: string;
    toUserName?: string;
}

// 查询线索分配记录列表
export function listAssignmentRecords(query: AssignmentRecordQuery): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: '/front/crm/assignmentRecords/list',
        method: 'get',
        params: query
    });
}

// 查询线索分配记录详细
export function getAssignmentRecord(id: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/${id}`,
        method: 'get'
    });
}

// 新增线索分配记录
export function addAssignmentRecord(data: AssignmentRecord): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords',
        method: 'post',
        data: data
    });
}

// 修改线索分配记录
export function updateAssignmentRecord(data: AssignmentRecord): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords',
        method: 'put',
        data: data
    });
}

// 删除线索分配记录
export function deleteAssignmentRecord(ids: number | number[]): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/assignmentRecords/${typeof ids === 'number' ? ids : ids.join(',')}`,
        method: 'delete'
    });
}

// 根据线索ID查询分配记录列表
export function getRecordsByLeadId(leadId: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/lead/${leadId}`,
        method: 'get'
    });
}

// 根据用户ID查询分配记录列表（作为分配对象）
export function getRecordsByToUserId(userId: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/toUser/${userId}`,
        method: 'get'
    });
}

// 根据用户ID查询分配记录列表（作为原负责人）
export function getRecordsByFromUserId(userId: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/fromUser/${userId}`,
        method: 'get'
    });
}

// 根据操作人ID查询分配记录列表
export function getRecordsByOperatorId(operatorId: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/operator/${operatorId}`,
        method: 'get'
    });
}

// 根据分配类型查询分配记录列表
export function getRecordsByAssignmentType(assignmentType: string): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/type/${assignmentType}`,
        method: 'get'
    });
}

// 根据时间范围查询分配记录列表
export function getRecordsByTimeRange(startTime: string, endTime: string): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: '/front/crm/assignmentRecords/timeRange',
        method: 'get',
        params: {
            startTime,
            endTime
        }
    });
}

// 获取分配记录统计信息
export function getAssignmentRecordStats(): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords/stats',
        method: 'get'
    });
}

// 统计各分配类型的记录数量
export function countRecordsByAssignmentType(): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords/stats/type',
        method: 'get'
    });
}

// 统计各用户的分配数量（作为分配对象）
export function countRecordsByToUser(): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords/stats/user',
        method: 'get'
    });
}

// 统计各操作人的操作数量
export function countRecordsByOperator(): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords/stats/operator',
        method: 'get'
    });
}

// 统计指定时间范围内的分配数量
export function countRecordsByTimeRange(startTime: string, endTime: string): Promise<ApiResponse<number>> {
    return request({
        url: '/front/crm/assignmentRecords/stats/timeRange',
        method: 'get',
        params: {
            startTime,
            endTime
        }
    });
}

// 获取最近的分配记录
export function getLatestRecordByLeadId(leadId: number): Promise<ApiResponse<AssignmentRecord>> {
    return request({
        url: `/front/crm/assignmentRecords/latest/${leadId}`,
        method: 'get'
    });
}

// 导出线索分配记录列表
export function exportAssignmentRecords(query: AssignmentRecordQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/assignmentRecords/export',
        method: 'post',
        params: query
    });
}
