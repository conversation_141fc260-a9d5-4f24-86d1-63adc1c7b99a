<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.common.mapper.CrmProductMapper">

    <resultMap type="com.ruoyi.common.domain.entity.CrmProduct" id="CrmProductResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="price" column="price"/>
        <result property="imageUrl" column="image_url"/>
        <result property="productLink" column="product_link"/>
        <result property="materialProperties" column="material_properties"/>
        <result property="materialProcess" column="material_process"/>
        <result property="techSpecs" column="tech_specs"/>
        <result property="advantages" column="advantages"/>
        <result property="disadvantages" column="disadvantages"/>
        <result property="applicationAreas" column="application_areas"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="selectCrmProductVo">
        select id, name, price, image_url, product_link, material_properties, material_process,
        tech_specs, advantages, disadvantages, application_areas, created_at, updated_at, type
        from crm_products
    </sql>

    <select id="selectCrmProductList" parameterType="CrmProduct" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="price != null">
                AND price = #{price}
            </if>
            <if test="materialProperties != null and materialProperties != ''">
                AND material_properties like concat('%', #{materialProperties}, '%')
            </if>
            <if test="materialProcess != null and materialProcess != ''">
                AND material_process like concat('%', #{materialProcess}, '%')
            </if>
            <if test="applicationAreas != null and applicationAreas != ''">
                AND application_areas like concat('%', #{applicationAreas}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectCrmProductById" parameterType="Long" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmProduct" parameterType="CrmProduct" useGeneratedKeys="true" keyProperty="id">
        insert into crm_products (
            name, price, image_url, product_link, material_properties, material_process,
            tech_specs, advantages, disadvantages, application_areas, created_at, updated_at, type
        ) values (
            #{name}, #{price}, #{imageUrl}, #{productLink}, #{materialProperties}, #{materialProcess},
            #{techSpecs}, #{advantages}, #{disadvantages}, #{applicationAreas}, now(), now(), #{type}
        )
    </insert>

    <update id="updateCrmProduct" parameterType="CrmProduct">
        update crm_products
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="productLink != null">product_link = #{productLink},</if>
            <if test="materialProperties != null">material_properties = #{materialProperties},</if>
            <if test="materialProcess != null">material_process = #{materialProcess},</if>
            <if test="techSpecs != null">tech_specs = #{techSpecs},</if>
            <if test="advantages != null">advantages = #{advantages},</if>
            <if test="disadvantages != null">disadvantages = #{disadvantages},</if>
            <if test="applicationAreas != null">application_areas = #{applicationAreas},</if>
            <if test="type != null">type = #{type},</if>
                updated_at = now()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteCrmProductByIds" parameterType="Long">
        delete from crm_products where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProductTypes" resultType="String">
        SELECT DISTINCT type 
        FROM crm_products 
        WHERE 1=1
        AND type IS NOT NULL 
        AND type != ''
        ORDER BY type
    </select>

</mapper>
