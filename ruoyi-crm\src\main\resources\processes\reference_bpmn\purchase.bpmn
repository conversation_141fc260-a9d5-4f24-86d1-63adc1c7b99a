<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:bpsim="http://www.bpsim.org/schemas/1.0" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:drools="http://www.jboss.org/drools" id="_zl9isDGQED6Y3aO9eAPkBw" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd http://www.jboss.org/drools drools.xsd http://www.bpsim.org/schemas/1.0 bpsim.xsd http://www.omg.org/spec/DD/20100524/DC DC.xsd http://www.omg.org/spec/DD/20100524/DI DI.xsd " exporter="jBPM Process Modeler" exporterVersion="2.0" targetNamespace="http://www.omg.org/bpmn20">
  <bpmn2:itemDefinition id="_procurementExecution_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_procurementExecution_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:collaboration id="_575AED51-2200-4889-96B9-88A28DF12F43" name="Default Collaboration">
    <bpmn2:participant id="_A7D7E3E7-0E2B-4A9D-B78F-6AF721FF3C4C" name="Pool Participant" processRef="purchase"/>
  </bpmn2:collaboration>
  <bpmn2:process id="purchase" drools:packageName="com.example" drools:version="1.0" drools:adHoc="false" name="purchase" isExecutable="true" processType="Public">
    <bpmn2:documentation><![CDATA[这是一个参考文件，展示采购审批流程的设计]]></bpmn2:documentation>
    <bpmn2:sequenceFlow id="flow1" sourceRef="startEvent" targetRef="deptApproval"/>
    <bpmn2:sequenceFlow id="flow2" sourceRef="deptApproval" targetRef="amountGateway"/>
    <bpmn2:sequenceFlow id="flowHighAmount" name="大额采购(>=10000)" sourceRef="amountGateway" targetRef="ceoApproval">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[大额采购(>=10000)]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[${amount >= 10000}]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="flowLowAmount" name="小额采购(<10000)" sourceRef="amountGateway" targetRef="procurementExecution">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[小额采购(<10000)]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[${amount < 10000}]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="flow6" sourceRef="procurementExecution" targetRef="mergeGateway"/>
    <bpmn2:sequenceFlow id="flow5" sourceRef="ceoApproval" targetRef="mergeGateway"/>
    <bpmn2:sequenceFlow id="flow7" sourceRef="mergeGateway" targetRef="endEvent"/>
    <bpmn2:startEvent id="startEvent" name="提交采购申请">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[提交采购申请]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:outgoing>flow1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:userTask id="deptApproval" name="部门主管审批">
      <bpmn2:documentation><![CDATA[部门主管初步审批采购申请]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[部门主管审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow1</bpmn2:incoming>
      <bpmn2:outgoing>flow2</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="deptApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_deptApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="deptApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_deptApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>deptApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>deptApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>deptApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[deptApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>deptApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[deptApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:exclusiveGateway id="amountGateway" name="金额判断" gatewayDirection="Diverging">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[金额判断]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow2</bpmn2:incoming>
      <bpmn2:outgoing>flowLowAmount</bpmn2:outgoing>
      <bpmn2:outgoing>flowHighAmount</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:userTask id="ceoApproval" name="总经理审批">
      <bpmn2:documentation><![CDATA[大额采购需要总经理审批]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[总经理审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flowHighAmount</bpmn2:incoming>
      <bpmn2:outgoing>flow5</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="ceoApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_ceoApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="ceoApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_ceoApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>ceoApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>ceoApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>ceoApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[ceoApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>ceoApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[ceoApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:userTask id="procurementExecution" name="采购部门执行">
      <bpmn2:documentation><![CDATA[小额采购直接由采购部门执行]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[采购部门执行]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flowLowAmount</bpmn2:incoming>
      <bpmn2:outgoing>flow6</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="procurementExecution_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_procurementExecution_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="procurementExecution_SkippableInputX" drools:dtype="Object" itemSubjectRef="_procurementExecution_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>procurementExecution_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>procurementExecution_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>procurementExecution_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[procurementExecution_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>procurementExecution_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[procurementExecution_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:exclusiveGateway id="mergeGateway" name="流程汇聚" gatewayDirection="Converging">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[流程汇聚]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow5</bpmn2:incoming>
      <bpmn2:incoming>flow6</bpmn2:incoming>
      <bpmn2:outgoing>flow7</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:endEvent id="endEvent" name="采购完成">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[采购完成]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow7</bpmn2:incoming>
    </bpmn2:endEvent>
  </bpmn2:process>
  <bpmndi:BPMNDiagram>
    <bpmndi:BPMNPlane bpmnElement="purchase">
      <bpmndi:BPMNShape id="shape_endEvent" bpmnElement="endEvent">
        <dc:Bounds height="56" width="56" x="685" y="189"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_mergeGateway" bpmnElement="mergeGateway">
        <dc:Bounds height="56" width="56" x="585" y="182"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_procurementExecution" bpmnElement="procurementExecution">
        <dc:Bounds height="80" width="100" x="435" y="235"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_ceoApproval" bpmnElement="ceoApproval">
        <dc:Bounds height="80" width="100" x="435" y="115"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_amountGateway" bpmnElement="amountGateway">
        <dc:Bounds height="56" width="56" x="345" y="182"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_deptApproval" bpmnElement="deptApproval">
        <dc:Bounds height="80" width="100" x="195" y="165"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_startEvent" bpmnElement="startEvent">
        <dc:Bounds height="56" width="56" x="115" y="185"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge_shape_mergeGateway_to_shape_endEvent" bpmnElement="flow7">
        <di:waypoint x="635" y="207"/>
        <di:waypoint x="685" y="207"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_ceoApproval_to_shape_mergeGateway" bpmnElement="flow5">
        <di:waypoint x="535" y="155"/>
        <di:waypoint x="585" y="182"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_procurementExecution_to_shape_mergeGateway" bpmnElement="flow6">
        <di:waypoint x="535" y="275"/>
        <di:waypoint x="585" y="232"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_amountGateway_to_shape_procurementExecution" bpmnElement="flowLowAmount">
        <di:waypoint x="370" y="232"/>
        <di:waypoint x="370" y="275"/>
        <di:waypoint x="435" y="275"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_amountGateway_to_shape_ceoApproval" bpmnElement="flowHighAmount">
        <di:waypoint x="370" y="182"/>
        <di:waypoint x="370" y="155"/>
        <di:waypoint x="435" y="155"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_deptApproval_to_shape_amountGateway" bpmnElement="flow2">
        <di:waypoint x="295" y="205"/>
        <di:waypoint x="345" y="207"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_startEvent_to_shape_deptApproval" bpmnElement="flow1">
        <di:waypoint x="151" y="203"/>
        <di:waypoint x="195" y="205"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmn2:relationship type="BPSimData">
    <bpmn2:extensionElements>
      <bpsim:BPSimData>
        <bpsim:Scenario id="default" name="Simulationscenario">
          <bpsim:ScenarioParameters/>
          <bpsim:ElementParameters elementRef="procurementExecution">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="ceoApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="deptApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="startEvent">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
          </bpsim:ElementParameters>
        </bpsim:Scenario>
      </bpsim:BPSimData>
    </bpmn2:extensionElements>
    <bpmn2:source>_zl9isDGQED6Y3aO9eAPkBw</bpmn2:source>
    <bpmn2:target>_zl9isDGQED6Y3aO9eAPkBw</bpmn2:target>
  </bpmn2:relationship>
</bpmn2:definitions>