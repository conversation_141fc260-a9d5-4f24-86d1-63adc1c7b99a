package com.ruoyi.common.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户跟进记录实体类
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmCustomerFollowupRecord extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 跟进人ID */
    private Long creatorId;

    /** 跟进方式（call-电话，visit-拜访，email-邮件，wechat-微信，other-其他） */
    private String followupType;

    /** 跟进内容 */
    private String followupContent;

    // 关联信息（用于查询时的关联数据）
    /** 客户名称 */
    private String customerName;

    /** 跟进人姓名 */
    private String userName;

    /** 跟进人昵称 */
    private String userNickName;

    /** 附件URL列表 */
    private List<String> attachmentUrlList;

    /**
     * 获取跟进方式中文名称
     */
    public String getFollowupTypeName() {
        if (followupType == null) return "";
        switch (followupType) {
            case "call": return "电话";
            case "visit": return "拜访";
            case "email": return "邮件";
            case "wechat": return "微信";
            case "other": return "其他";
            default: return followupType;
        }
    }
}