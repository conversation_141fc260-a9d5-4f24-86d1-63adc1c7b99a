package com.ruoyi.common.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户跟进记录实体类
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmCustomerFollowupRecord extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 跟进人ID */
    private Long creatorId;

    /** 跟进方式（call-电话，visit-拜访，email-邮件，wechat-微信，other-其他） */
    private String followupType;

    /** 跟进内容 */
    private String followupContent;

    /** 跟进结果（interested-有兴趣，no_interest-无兴趣，pending-待定，deal-成交） */
    private String followupResult;

    /** 下次跟进时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextFollowupTime;

    /** 附件URL列表，JSON格式 */
    private String attachmentUrls;

    /** 是否重要跟进（0否 1是） */
    private Integer isImportant;

    /** 删除标志（0存在 1删除） */
    private String delFlag;

    // 关联信息（用于查询时的关联数据）
    /** 客户名称 */
    private String customerName;

    /** 跟进人姓名 */
    private String userName;

    /** 跟进人昵称 */
    private String userNickName;

    /** 附件URL列表 */
    private List<String> attachmentUrlList;

    /**
     * 获取跟进方式中文名称
     */
    public String getFollowupTypeName() {
        if (followupType == null) return "";
        switch (followupType) {
            case "call": return "电话";
            case "visit": return "拜访";
            case "email": return "邮件";
            case "wechat": return "微信";
            case "other": return "其他";
            default: return followupType;
        }
    }

    /**
     * 获取跟进结果中文名称
     */
    public String getFollowupResultName() {
        if (followupResult == null) return "";
        switch (followupResult) {
            case "interested": return "有兴趣";
            case "no_interest": return "无兴趣";
            case "pending": return "待定";
            case "deal": return "成交";
            default: return followupResult;
        }
    }
}