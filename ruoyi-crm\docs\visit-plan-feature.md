# 拜访计划功能使用说明

## 功能概述

拜访计划功能是CRM系统中的核心功能之一，用于帮助销售人员管理和跟踪客户拜访活动。该功能提供了完整的拜访计划创建、管理、提醒和统计分析能力。

## 主要特性

### 1. 拜访计划管理
- **创建拜访计划**：可以为客户、联系人或商机创建拜访计划
- **编辑拜访计划**：支持修改计划中的拜访信息
- **删除拜访计划**：可删除不需要的拜访计划
- **查询拜访计划**：支持按客户、时间、状态等条件查询

### 2. 状态管理
- **计划中** (planned)：新创建的拜访计划默认状态
- **进行中** (ongoing)：当前正在进行的拜访
- **已完成** (completed)：已完成的拜访，需要填写跟进记录
- **已延期** (postponed)：因故延期的拜访，需要填写延期原因
- **已取消** (cancelled)：取消的拜访，需要填写取消原因

### 3. 提醒功能
- 支持设置提前提醒时间（默认30分钟）
- 系统内消息提醒
- 支持扩展企业微信、短信、邮件等提醒方式
- 定时任务每5分钟检查一次即将到期的拜访计划

### 4. 统计分析
- 查看拜访计划总数、各状态数量
- 计算拜访完成率
- 支持按时间范围筛选统计数据
- 管理员可查看所有人的统计，普通用户只能查看自己的

## API接口说明

### 基础接口

| 接口地址 | 请求方式 | 说明 | 权限要求 |
|---------|---------|------|---------|
| /crm/visitPlan/list | GET | 查询拜访计划列表 | crm:visitPlan:list |
| /crm/visitPlan/{id} | GET | 获取拜访计划详情 | crm:visitPlan:query |
| /crm/visitPlan | POST | 新增拜访计划 | crm:visitPlan:add |
| /crm/visitPlan | PUT | 修改拜访计划 | crm:visitPlan:edit |
| /crm/visitPlan/{ids} | DELETE | 删除拜访计划 | crm:visitPlan:remove |

### 业务接口

| 接口地址 | 请求方式 | 说明 | 权限要求 |
|---------|---------|------|---------|
| /crm/visitPlan/listByObject | GET | 根据关联对象查询 | crm:visitPlan:query |
| /crm/visitPlan/postpone/{id} | POST | 延期拜访计划 | crm:visitPlan:postpone |
| /crm/visitPlan/cancel/{id} | POST | 取消拜访计划 | crm:visitPlan:cancel |
| /crm/visitPlan/complete/{id} | POST | 完成拜访计划 | crm:visitPlan:complete |
| /crm/visitPlan/statistics | GET | 获取统计信息 | crm:visitPlan:statistics |
| /crm/visitPlan/export | POST | 导出拜访计划 | crm:visitPlan:export |

## 数据库设计

### 主表：crm_visit_plans
存储拜访计划的主要信息，包括：
- 基本信息：计划名称、拜访时间、拜访目的
- 关联信息：客户、联系人、商机
- 状态信息：当前状态、实际拜访时间、完成时间
- 业务信息：延期原因、取消原因、跟进记录

### 提醒表：crm_visit_plan_reminders
记录提醒发送情况，包括：
- 提醒类型、提醒时间
- 发送状态、实际发送时间
- 错误信息（如果发送失败）

### 日志表：crm_visit_plan_logs
记录状态变更历史，包括：
- 原状态、新状态
- 变更原因、变更备注
- 操作人信息、操作时间

## 使用示例

### 1. 创建拜访计划
```json
POST /crm/visitPlan
{
  "visitPlanName": "XX公司产品演示",
  "visitTime": "2025-02-01 14:00:00",
  "customerId": 100,
  "customerName": "XX科技有限公司",
  "contactId": 200,
  "contactName": "张经理",
  "visitPurpose": "演示新产品功能，了解客户需求",
  "remindTime": 30,
  "remark": "记得带产品演示资料"
}
```

### 2. 延期拜访计划
```json
POST /crm/visitPlan/postpone/1
{
  "reason": "客户临时有重要会议",
  "remark": "已与客户沟通，改期到下周",
  "newVisitTime": "2025-02-08 14:00:00"
}
```

### 3. 完成拜访计划
```json
POST /crm/visitPlan/complete/1
{
  "followupContent": "拜访顺利完成。客户对产品很感兴趣，特别是数据分析功能。下一步：准备详细的技术方案，预计下周再次拜访进行技术交流。"
}
```

### 4. 查询统计信息
```
GET /crm/visitPlan/statistics?dateRange=month
```

返回示例：
```json
{
  "code": 200,
  "data": {
    "total": 25,
    "planned": 10,
    "ongoing": 3,
    "completed": 8,
    "postponed": 2,
    "cancelled": 2,
    "completionRate": "32.00",
    "onTimeRate": "75.00"
  }
}
```

## 最佳实践

### 1. 拜访计划创建
- 提前规划拜访时间，建议至少提前1天创建
- 填写清晰的拜访目的，便于后续跟踪
- 关联相关的客户、联系人或商机信息

### 2. 状态管理
- 及时更新拜访状态，保持数据准确性
- 延期或取消时填写详细原因，便于分析
- 完成拜访后及时填写跟进记录

### 3. 提醒设置
- 根据拜访重要性设置合适的提醒时间
- 重要拜访建议设置60分钟或更长的提醒时间
- 确保接收提醒的渠道畅通

### 4. 数据分析
- 定期查看拜访统计，了解工作情况
- 分析延期和取消原因，优化拜访安排
- 关注完成率和准时率，提升拜访效率

## 扩展功能

### 1. 批量操作
- 支持批量创建拜访计划
- 支持批量更新状态
- 支持批量导出数据

### 2. 高级查询
- 支持复杂条件组合查询
- 支持自定义字段排序
- 支持保存常用查询条件

### 3. 集成功能
- 与日程管理系统集成
- 与地图导航集成
- 与视频会议系统集成

## 常见问题

### Q1: 为什么不能编辑已完成的拜访计划？
A: 已完成的拜访计划代表历史记录，不应被修改。如需调整，可以创建新的拜访计划。

### Q2: 提醒没有收到怎么办？
A: 请检查：
1. 提醒时间设置是否正确
2. 系统定时任务是否正常运行
3. 消息接收设置是否正确

### Q3: 如何查看团队成员的拜访计划？
A: 需要具有相应的数据权限，管理员默认可以查看所有人的拜访计划。

### Q4: 删除的拜访计划能否恢复？
A: 系统采用逻辑删除，管理员可以通过数据库恢复已删除的记录。

## 技术支持

如有问题或建议，请联系系统管理员或提交工单。

---

更新日期：2025-01-29
版本：v1.0.0
