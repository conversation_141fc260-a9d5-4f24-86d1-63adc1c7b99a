package com.ruoyi.common.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;
import com.ruoyi.common.mapper.CrmCustomerFollowupRecordMapper;
import com.ruoyi.common.service.ICrmCustomerFollowupRecordService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 客户跟进记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class CrmCustomerFollowupRecordServiceImpl implements ICrmCustomerFollowupRecordService {
    
    @Autowired
    private CrmCustomerFollowupRecordMapper crmCustomerFollowupRecordMapper;

    /**
     * 查询客户跟进记录
     * 
     * @param id 客户跟进记录主键
     * @return 客户跟进记录
     */
    @Override
    public CrmCustomerFollowupRecord selectCrmCustomerFollowupRecordById(Long id) {
        return crmCustomerFollowupRecordMapper.selectCrmCustomerFollowupRecordById(id);
    }

    /**
     * 查询客户跟进记录列表
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 客户跟进记录
     */
    @Override
    public List<CrmCustomerFollowupRecord> selectCrmCustomerFollowupRecordList(CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        return crmCustomerFollowupRecordMapper.selectCrmCustomerFollowupRecordList(crmCustomerFollowupRecord);
    }

    /**
     * 根据客户ID查询跟进记录列表
     * 
     * @param customerId 客户ID
     * @return 跟进记录集合
     */
    @Override
    public List<CrmCustomerFollowupRecord> selectFollowupRecordsByCustomerId(Long customerId) {
        return crmCustomerFollowupRecordMapper.selectFollowupRecordsByCustomerId(customerId);
    }

    /**
     * 新增客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    @Override
    public int insertCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        // 设置默认值
        if (crmCustomerFollowupRecord.getCreatorId() == null) {
            try {
                Long currentUserId = SecurityUtils.getUserId();
                crmCustomerFollowupRecord.setCreatorId(currentUserId);
            } catch (Exception e) {
                // 如果获取用户ID失败，设置为1（默认管理员）
                crmCustomerFollowupRecord.setCreatorId(1L);
            }
        }
        
        crmCustomerFollowupRecord.setCreateTime(new Date());
        crmCustomerFollowupRecord.setUpdateTime(new Date());
        
        return crmCustomerFollowupRecordMapper.insertCrmCustomerFollowupRecord(crmCustomerFollowupRecord);
    }

    /**
     * 修改客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    @Override
    public int updateCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord) {
        crmCustomerFollowupRecord.setUpdateTime(new Date());
        return crmCustomerFollowupRecordMapper.updateCrmCustomerFollowupRecord(crmCustomerFollowupRecord);
    }

    /**
     * 批量删除客户跟进记录
     * 
     * @param ids 需要删除的客户跟进记录主键
     * @return 结果
     */
    @Override
    public int deleteCrmCustomerFollowupRecordByIds(Long[] ids) {
        return crmCustomerFollowupRecordMapper.deleteCrmCustomerFollowupRecordByIds(ids);
    }

    /**
     * 删除客户跟进记录信息
     * 
     * @param id 客户跟进记录主键
     * @return 结果
     */
    @Override
    public int deleteCrmCustomerFollowupRecordById(Long id) {
        return crmCustomerFollowupRecordMapper.deleteCrmCustomerFollowupRecordById(id);
    }

    /**
     * 统计客户的跟进记录数量
     * 
     * @param customerId 客户ID
     * @return 跟进记录数量
     */
    @Override
    public int countFollowupRecordsByCustomerId(Long customerId) {
        return crmCustomerFollowupRecordMapper.countFollowupRecordsByCustomerId(customerId);
    }

    /**
     * 查询最近的跟进记录
     * 
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 最近的跟进记录
     */
    @Override
    public List<CrmCustomerFollowupRecord> selectRecentFollowupRecords(Long customerId, Integer limit) {
        return crmCustomerFollowupRecordMapper.selectRecentFollowupRecords(customerId, limit);
    }

    /**
     * 查询需要跟进的记录（根据下次跟进时间）
     * 
     * @param creatorId 创建者ID
     * @return 需要跟进的记录
     */
    @Override
    public List<CrmCustomerFollowupRecord> selectPendingFollowupRecords(Long creatorId) {
        return crmCustomerFollowupRecordMapper.selectPendingFollowupRecords(creatorId);
    }

    /**
     * 创建跟进记录
     * 
     * @param customerId 客户ID
     * @param followupType 跟进方式
     * @param content 跟进内容
     * @param result 跟进结果（已废弃，保留参数兼容性）
     * @param nextFollowupTime 下次跟进时间（已废弃，保留参数兼容性）
     * @param isImportant 是否重要（已废弃，保留参数兼容性）
     * @return 结果
     */
    @Override
    public int createFollowupRecord(Long customerId, String followupType, String content, 
                                  String result, Date nextFollowupTime, Boolean isImportant) {
        CrmCustomerFollowupRecord record = new CrmCustomerFollowupRecord();
        record.setCustomerId(customerId);
        record.setFollowupType(followupType);
        record.setFollowupContent(content);
        // 注意：result、nextFollowupTime、isImportant字段在数据库中不存在，已移除设置
        
        return insertCrmCustomerFollowupRecord(record);
    }
}