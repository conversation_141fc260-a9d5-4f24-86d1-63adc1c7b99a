// Radio Button 样式变量 - 简化版本
:root {
  // Radio Button 基础样式
  --ep-radio-button-font-weight: 500;
  --ep-radio-button-border-radius: 4px;
  --ep-radio-button-padding-vertical: 8px;
  --ep-radio-button-padding-horizontal: 16px;
  --ep-radio-button-height: 32px;
  --ep-radio-button-font-size: 14px;

  // 默认状态
  --ep-radio-button-text-color: #606266;
  --ep-radio-button-bg-color: #fff;
  --ep-radio-button-border-color: #dcdfe6;

  // 悬停状态
  --ep-radio-button-hover-text-color: #409eff;
  --ep-radio-button-hover-bg-color: #fff;
  --ep-radio-button-hover-border-color: #409eff;

  // 激活状态（is-active）
  --ep-radio-button-active-text-color: #fff;
  --ep-radio-button-active-bg-color: #409eff;
  --ep-radio-button-active-border-color: #409eff;

  // 禁用状态
  --ep-radio-button-disabled-text-color: #c0c4cc;
  --ep-radio-button-disabled-bg-color: #f5f7fa;
  --ep-radio-button-disabled-border-color: #e4e7ed;
}