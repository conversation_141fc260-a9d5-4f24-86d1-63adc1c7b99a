<template>
    <div class="chart-container">
        <div ref="followupChart" class="followup-chart"></div>
        <el-table :data="tableData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="name" label="员工姓名" width="120"></el-table-column>
            <el-table-column prop="followUpCount" label="更进次数" width="120"></el-table-column>
            <el-table-column prop="effectiveFollowUp" label="有效跟进" width="120"></el-table-column>
            <el-table-column prop="ineffectiveFollowUp" label="无效跟进" width="120"></el-table-column>
            <el-table-column prop="fieldSignIns" label="外勤签到次数" width="140"></el-table-column>
            <el-table-column prop="customerFollowups" label="跟进客户数" width="120"></el-table-column>
            <el-table-column prop="followUpRatio" label="客户跟进占比" width="140" :formatter="formatRatio"></el-table-column>
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    name: 'customerFollowupAnalysis',
    data() {
        return {
            chart: null,
            tableData: [
                { name: '张三', followUpCount: 15, effectiveFollowUp: 8, ineffectiveFollowUp: 7, fieldSignIns: 5, customerFollowups: 10, followUpRatio: 0.8 },
                { name: '李四', followUpCount: 20, effectiveFollowUp: 12, ineffectiveFollowUp: 8, fieldSignIns: 7, customerFollowups: 15, followUpRatio: 0.75 },
                // more data ...
            ],
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.followupChart;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '员工跟进活动分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['更进次数', '有效跟进', '无效跟进'],
                    bottom: 0
                },
                xAxis: {
                    type: 'category',
                    data: this.tableData.map(item => item.name)
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '更进次数',
                        type: 'bar',
                        data: this.tableData.map(item => item.followUpCount)
                    },
                    {
                        name: '有效跟进',
                        type: 'bar',
                        data: this.tableData.map(item => item.effectiveFollowUp)
                    },
                    {
                        name: '无效跟进',
                        type: 'bar',
                        data: this.tableData.map(item => item.ineffectiveFollowUp)
                    }
                ]
            };
            this.chart.setOption(option);
        },
        formatRatio(row) {
            return (row.followUpRatio * 100).toFixed(2) + '%';
        }
    }
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.followup-chart {
    width: 100%;
    height: 400px;
}
</style>