@host = http://localhost:8080
@token = Bearer ey...

###
# 1. 查询对账单列表 (无参数)
# 功能：获取所有对账单的分页列表
GET {{host}}/front/crm/reconciliation/list
Authorization: {{token}}
Content-Type: application/json

###
# 2. 查询对账单列表 (带参数)
# 功能：根据客户名称和状态筛选对账单
GET {{host}}/front/crm/reconciliation/list?customerName=测试客户&status=draft&pageNum=1&pageSize=10
Authorization: {{token}}
Content-Type: application/json

###
# 3. 新增对账单
# 功能：创建一个新的对账单草稿
# 注意：需要提供有效的 customerId 和 details 数组
POST {{host}}/front/crm/reconciliation
Authorization: {{token}}
Content-Type: application/json

{
  "customerId": 1,
  "customerName": "测试客户",
  "reconciliationDate": "2025-07-03",
  "reconciliationPeriod": "2025年6月",
  "remark": "API测试 - 新增对账单",
  "details": [
    {
      "orderId": 101,
      "orderNo": "ORD20250601",
      "amount": 1500.00,
      "detailType": "order"
    },
    {
      "orderId": 102,
      "orderNo": "ORD20250605",
      "amount": 2500.50,
      "detailType": "order"
    }
  ]
}

###
# 4. 查询对账单详细信息
# 功能：获取单个对账单的完整信息
# 注意：将 @reconciliationId 替换为实际存在的对账单ID
@reconciliationId = 1
GET {{host}}/front/crm/reconciliation/{{reconciliationId}}
Authorization: {{token}}
Content-Type: application/json

###
# 5. 修改对账单
# 功能：更新一个已存在的对账单草稿
# 注意：将 @reconciliationId 替换为实际存在的对账单ID
PUT {{host}}/front/crm/reconciliation
Authorization: {{token}}
Content-Type: application/json

{
  "reconciliationId": {{reconciliationId}},
  "customerId": 1,
  "customerName": "测试客户（已修改）",
  "reconciliationDate": "2025-07-03",
  "reconciliationPeriod": "2025年6月",
  "remark": "API测试 - 这是修改后的备注",
  "details": [
    {
      "orderId": 101,
      "orderNo": "ORD20250601",
      "amount": 1500.00,
      "detailType": "order"
    }
  ]
}

###
# 6. 提交对账单以供审核
# 功能：将对账单状态从“草稿”更新为“待审核”
# 注意：将 @reconciliationId 替换为实际存在的对账单ID
POST {{host}}/front/crm/reconciliation/submit/{{reconciliationId}}
Authorization: {{token}}
Content-Type: application/json

###
# 7. 查询指定客户的未对账订单
# 功能：获取某个客户下所有未被任何对账单引用的订单
# 注意：将 @customerId 替换为实际存在的客户ID
@customerId = 1
GET {{host}}/front/crm/reconciliation/unreconciled-orders/{{customerId}}?pageNum=1&pageSize=10
Authorization: {{token}}
Content-Type: application/json

###
# 8. 删除对账单
# 功能：删除一个对账单
# 注意：将 @reconciliationId 替换为实际存在的对账单ID
DELETE {{host}}/front/crm/reconciliation/{{reconciliationId}}
Authorization: {{token}}
Content-Type: application/json

###
# 9. 导出对账单 (示例)
# 功能：根据筛选条件导出对账单为Excel文件
# 注意：此请求通常在浏览器或支持文件下载的客户端中执行
GET {{host}}/front/crm/reconciliation/export?status=approved
Authorization: {{token}}
