<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线索池功能实现计划</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .table-container {
            overflow-x: auto;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .feature-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .step-box {
            background-color: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>线索池功能实现计划</h1>
        
        <div class="feature-box">
            <h2>📋 项目概述</h2>
            <p>基于现有线索管理模块，新增线索池功能，实现线索的统一管理、自动分配、回收机制和团队协作，提升线索管理效率和转化率。</p>
        </div>

        <h2>🎯 功能需求分析</h2>
        
        <h3>1. 核心功能</h3>
        <ul>
            <li><strong>线索池管理</strong>：统一存储和管理未分配的线索</li>
            <li><strong>自动分配机制</strong>：根据规则自动分配线索给销售人员</li>
            <li><strong>手动分配功能</strong>：管理员可手动分配线索</li>
            <li><strong>线索回收机制</strong>：超时未跟进的线索自动回收到线索池</li>
            <li><strong>线索抢单功能</strong>：销售人员可从线索池中主动获取线索</li>
            <li><strong>线索质量评级</strong>：对线索进行质量评估和分级</li>
        </ul>

        <h3>2. 高级功能</h3>
        <ul>
            <li><strong>分配规则配置</strong>：支持多种分配策略（轮询、负载均衡、地区优先等）</li>
            <li><strong>线索池统计</strong>：实时统计线索池状态和分配情况</li>
            <li><strong>权限控制</strong>：不同角色对线索池的不同操作权限</li>
            <li><strong>线索跟踪</strong>：完整的线索流转记录和状态追踪</li>
        </ul>

        <h2>🗄️ 数据库设计</h2>
        
        <h3>1. 线索池表 (crm_lead_pool)</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>id</td><td>BIGINT</td><td>主键ID</td><td>自增</td></tr>
                    <tr><td>lead_id</td><td>BIGINT</td><td>线索ID</td><td>关联crm_business_leads表</td></tr>
                    <tr><td>pool_status</td><td>VARCHAR(50)</td><td>池状态</td><td>available/assigned/locked</td></tr>
                    <tr><td>quality_level</td><td>VARCHAR(20)</td><td>质量等级</td><td>A/B/C/D</td></tr>
                    <tr><td>priority</td><td>INT</td><td>优先级</td><td>1-10，数字越大优先级越高</td></tr>
                    <tr><td>source_type</td><td>VARCHAR(50)</td><td>来源类型</td><td>new/recycled/imported</td></tr>
                    <tr><td>enter_pool_time</td><td>DATETIME</td><td>进入池时间</td><td></td></tr>
                    <tr><td>last_assign_time</td><td>DATETIME</td><td>最后分配时间</td><td></td></tr>
                    <tr><td>assign_count</td><td>INT</td><td>分配次数</td><td>默认0</td></tr>
                    <tr><td>region</td><td>VARCHAR(100)</td><td>地区</td><td>用于地区优先分配</td></tr>
                    <tr><td>industry</td><td>VARCHAR(100)</td><td>行业</td><td>用于行业专业分配</td></tr>
                    <tr><td>estimated_value</td><td>DECIMAL(15,2)</td><td>预估价值</td><td></td></tr>
                    <tr><td>remarks</td><td>TEXT</td><td>备注</td><td></td></tr>
                    <tr><td>create_time</td><td>DATETIME</td><td>创建时间</td><td></td></tr>
                    <tr><td>update_time</td><td>DATETIME</td><td>更新时间</td><td></td></tr>
                </tbody>
            </table>
        </div>

        <h3>2. 线索分配规则表 (crm_lead_assignment_rules)</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>id</td><td>BIGINT</td><td>主键ID</td><td>自增</td></tr>
                    <tr><td>rule_name</td><td>VARCHAR(100)</td><td>规则名称</td><td></td></tr>
                    <tr><td>rule_type</td><td>VARCHAR(50)</td><td>规则类型</td><td>round_robin/load_balance/region/industry</td></tr>
                    <tr><td>is_active</td><td>TINYINT(1)</td><td>是否启用</td><td>0/1</td></tr>
                    <tr><td>priority</td><td>INT</td><td>规则优先级</td><td>数字越大优先级越高</td></tr>
                    <tr><td>conditions</td><td>JSON</td><td>分配条件</td><td>JSON格式存储条件</td></tr>
                    <tr><td>target_users</td><td>JSON</td><td>目标用户</td><td>JSON格式存储用户ID列表</td></tr>
                    <tr><td>max_leads_per_user</td><td>INT</td><td>每人最大线索数</td><td>负载均衡用</td></tr>
                    <tr><td>create_time</td><td>DATETIME</td><td>创建时间</td><td></td></tr>
                    <tr><td>update_time</td><td>DATETIME</td><td>更新时间</td><td></td></tr>
                </tbody>
            </table>
        </div>

        <h3>3. 线索分配记录表 (crm_lead_assignment_records)</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>id</td><td>BIGINT</td><td>主键ID</td><td>自增</td></tr>
                    <tr><td>lead_id</td><td>BIGINT</td><td>线索ID</td><td></td></tr>
                    <tr><td>from_user_id</td><td>BIGINT</td><td>原负责人ID</td><td>可为空（从池中分配）</td></tr>
                    <tr><td>to_user_id</td><td>BIGINT</td><td>新负责人ID</td><td></td></tr>
                    <tr><td>assignment_type</td><td>VARCHAR(50)</td><td>分配类型</td><td>auto/manual/grab/recycle</td></tr>
                    <tr><td>assignment_rule_id</td><td>BIGINT</td><td>分配规则ID</td><td>自动分配时记录使用的规则</td></tr>
                    <tr><td>assignment_reason</td><td>VARCHAR(200)</td><td>分配原因</td><td></td></tr>
                    <tr><td>assignment_time</td><td>DATETIME</td><td>分配时间</td><td></td></tr>
                    <tr><td>operator_id</td><td>BIGINT</td><td>操作人ID</td><td></td></tr>
                    <tr><td>remarks</td><td>TEXT</td><td>备注</td><td></td></tr>
                </tbody>
            </table>
        </div>

        <h2>🎨 前端实现方案</h2>
        
        <h3>1. 导航配置更新</h3>
        <div class="code-block">
// frontend/src/views/AssociationManagement/config/index.ts
export const navConfig: NavConfig = {
    title: '线索管理',
    menuItems: [
        {
            key: 'leads',
            label: '线索',
            icon: 'Edit'
        },
        {
            key: 'leadPool',
            label: '线索池',
            icon: 'Box'
        },
        {
            key: 'assignmentRules',
            label: '分配规则',
            icon: 'Setting'
        }
    ]
};
        </div>

        <h3>2. 线索池页面组件</h3>
        <div class="step-box">
            <h4>创建线索池管理页面</h4>
            <p><strong>文件路径：</strong> frontend/src/views/AssociationManagement/LeadPool.vue</p>
            <ul>
                <li>线索池列表展示</li>
                <li>线索质量等级显示</li>
                <li>批量分配功能</li>
                <li>线索抢单功能</li>
                <li>筛选和搜索功能</li>
            </ul>
        </div>

        <h3>3. 分配规则配置页面</h3>
        <div class="step-box">
            <h4>创建分配规则管理页面</h4>
            <p><strong>文件路径：</strong> frontend/src/views/AssociationManagement/AssignmentRules.vue</p>
            <ul>
                <li>规则列表管理</li>
                <li>规则创建和编辑</li>
                <li>规则启用/禁用</li>
                <li>规则优先级设置</li>
            </ul>
        </div>

        <h3>4. 线索池统计面板</h3>
        <div class="step-box">
            <h4>创建统计组件</h4>
            <p><strong>文件路径：</strong> frontend/src/components/LeadPoolStats/index.vue</p>
            <ul>
                <li>线索池总数统计</li>
                <li>各质量等级分布</li>
                <li>分配效率统计</li>
                <li>转化率统计</li>
            </ul>
        </div>

        <h2>⚙️ 后端实现方案</h2>
        
        <h3>1. 控制器层</h3>
        <div class="step-box">
            <h4>创建线索池控制器</h4>
            <p><strong>文件路径：</strong> ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmLeadPoolController.java</p>
            <div class="code-block">
@RestController
@RequestMapping("/crm/leadPool")
public class CrmLeadPoolController {
    
    @GetMapping("/list")
    public AjaxResult getLeadPoolList(LeadPoolQuery query) {
        // 获取线索池列表
    }
    
    @PostMapping("/assign")
    public AjaxResult assignLeads(@RequestBody AssignLeadsRequest request) {
        // 批量分配线索
    }
    
    @PostMapping("/grab/{leadId}")
    public AjaxResult grabLead(@PathVariable Long leadId) {
        // 抢单功能
    }
    
    @PostMapping("/recycle")
    public AjaxResult recycleLeads(@RequestBody RecycleLeadsRequest request) {
        // 回收线索到池中
    }
    
    @GetMapping("/stats")
    public AjaxResult getLeadPoolStats() {
        // 获取线索池统计信息
    }
}
            </div>
        </div>

        <h3>2. 服务层</h3>
        <div class="step-box">
            <h4>创建线索池服务</h4>
            <p><strong>文件路径：</strong> ruoyi-crm/src/main/java/com/ruoyi/crm/service/ICrmLeadPoolService.java</p>
            <ul>
                <li>线索入池逻辑</li>
                <li>自动分配算法</li>
                <li>线索回收机制</li>
                <li>质量评级算法</li>
                <li>统计数据计算</li>
            </ul>
        </div>

        <h3>3. 分配规则引擎</h3>
        <div class="step-box">
            <h4>创建分配规则引擎</h4>
            <p><strong>文件路径：</strong> ruoyi-crm/src/main/java/com/ruoyi/crm/service/impl/LeadAssignmentEngine.java</p>
            <div class="code-block">
public class LeadAssignmentEngine {
    
    public AssignmentResult assignLead(CrmLeads lead, List&lt;AssignmentRule&gt; rules) {
        // 根据规则分配线索
        for (AssignmentRule rule : rules) {
            if (rule.matches(lead)) {
                return executeRule(lead, rule);
            }
        }
        return defaultAssignment(lead);
    }
    
    private AssignmentResult executeRule(CrmLeads lead, AssignmentRule rule) {
        switch (rule.getType()) {
            case ROUND_ROBIN:
                return roundRobinAssignment(lead, rule);
            case LOAD_BALANCE:
                return loadBalanceAssignment(lead, rule);
            case REGION_BASED:
                return regionBasedAssignment(lead, rule);
            default:
                return defaultAssignment(lead);
        }
    }
}
            </div>
        </div>

        <h3>4. 定时任务</h3>
        <div class="step-box">
            <h4>创建线索回收定时任务</h4>
            <p><strong>文件路径：</strong> ruoyi-crm/src/main/java/com/ruoyi/crm/task/LeadRecycleTask.java</p>
            <div class="code-block">
@Component
public class LeadRecycleTask {
    
    @Scheduled(cron = "0 0 2 * * ?")
    public void recycleTimeoutLeads() {
        // 每天凌晨2点执行线索回收
        List&lt;CrmLeads&gt; timeoutLeads = leadService.getTimeoutLeads();
        for (CrmLeads lead : timeoutLeads) {
            leadPoolService.recycleLead(lead.getId(), "超时未跟进自动回收");
        }
    }
    
    @Scheduled(cron = "0 */30 * * * ?")
    public void autoAssignLeads() {
        // 每30分钟执行一次自动分配
        leadPoolService.autoAssignAvailableLeads();
    }
}
            </div>
        </div>

        <h2>🔧 API接口设计</h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>接口名称</th>
                        <th>请求方法</th>
                        <th>路径</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>获取线索池列表</td><td>GET</td><td>/crm/leadPool/list</td><td>分页查询线索池数据</td></tr>
                    <tr><td>批量分配线索</td><td>POST</td><td>/crm/leadPool/assign</td><td>将线索分配给指定用户</td></tr>
                    <tr><td>抢单</td><td>POST</td><td>/crm/leadPool/grab/{leadId}</td><td>销售人员主动获取线索</td></tr>
                    <tr><td>回收线索</td><td>POST</td><td>/crm/leadPool/recycle</td><td>将线索回收到池中</td></tr>
                    <tr><td>线索池统计</td><td>GET</td><td>/crm/leadPool/stats</td><td>获取统计数据</td></tr>
                    <tr><td>获取分配规则</td><td>GET</td><td>/crm/assignmentRules/list</td><td>查询分配规则列表</td></tr>
                    <tr><td>创建分配规则</td><td>POST</td><td>/crm/assignmentRules</td><td>新建分配规则</td></tr>
                    <tr><td>更新分配规则</td><td>PUT</td><td>/crm/assignmentRules/{id}</td><td>修改分配规则</td></tr>
                    <tr><td>删除分配规则</td><td>DELETE</td><td>/crm/assignmentRules/{id}</td><td>删除分配规则</td></tr>
                    <tr><td>获取分配记录</td><td>GET</td><td>/crm/assignmentRecords/list</td><td>查询分配历史记录</td></tr>
                </tbody>
            </table>
        </div>

        <h2>📋 实施步骤</h2>
        
        <h3>第一阶段：数据库设计和基础架构</h3>
        <ol>
            <li>创建线索池相关数据表</li>
            <li>设计实体类和Mapper接口</li>
            <li>创建基础的Service接口</li>
        </ol>

        <h3>第二阶段：核心功能开发</h3>
        <ol>
            <li>实现线索入池逻辑</li>
            <li>开发分配规则引擎</li>
            <li>实现自动分配功能</li>
            <li>开发线索回收机制</li>
        </ol>

        <h3>第三阶段：前端界面开发</h3>
        <ol>
            <li>创建线索池管理页面</li>
            <li>开发分配规则配置界面</li>
            <li>实现统计面板</li>
            <li>集成到现有导航系统</li>
        </ol>

        <h3>第四阶段：高级功能和优化</h3>
        <ol>
            <li>实现线索质量评级</li>
            <li>开发抢单功能</li>
            <li>添加权限控制</li>
            <li>性能优化和测试</li>
        </ol>

        <h2>🔒 权限设计</h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>角色</th>
                        <th>权限</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>系统管理员</td><td>全部权限</td><td>可以管理所有线索池功能</td></tr>
                    <tr><td>销售经理</td><td>查看、分配、回收、配置规则</td><td>可以管理团队的线索分配</td></tr>
                    <tr><td>销售人员</td><td>查看、抢单</td><td>只能查看和获取线索</td></tr>
                    <tr><td>普通用户</td><td>查看</td><td>只能查看基本信息</td></tr>
                </tbody>
            </table>
        </div>

        <h2>📊 技术要点</h2>
        
        <h3>前端技术要点</h3>
        <ul>
            <li><strong>Vue 3 + TypeScript</strong>：确保类型安全和代码质量</li>
            <li><strong>Element Plus</strong>：使用统一的UI组件库</li>
            <li><strong>状态管理</strong>：使用Pinia管理线索池状态</li>
            <li><strong>实时更新</strong>：使用WebSocket实现线索状态实时同步</li>
        </ul>

        <h3>后端技术要点</h3>
        <ul>
            <li><strong>Spring Boot</strong>：基于现有框架扩展</li>
            <li><strong>MyBatis</strong>：数据库操作</li>
            <li><strong>定时任务</strong>：使用@Scheduled实现自动化处理</li>
            <li><strong>事务管理</strong>：确保数据一致性</li>
            <li><strong>缓存优化</strong>：使用Redis缓存热点数据</li>
        </ul>

        <h3>安全要点</h3>
        <ul>
            <li><strong>权限控制</strong>：基于角色的访问控制</li>
            <li><strong>操作日志</strong>：记录所有关键操作</li>
            <li><strong>数据验证</strong>：前后端双重验证</li>
            <li><strong>并发控制</strong>：防止线索重复分配</li>
        </ul>

        <h2>🎯 预期效果</h2>
        
        <div class="highlight">
            <h3>业务价值</h3>
            <ul>
                <li><strong>提升效率</strong>：自动化分配减少人工操作，提升30%的分配效率</li>
                <li><strong>公平分配</strong>：基于规则的分配确保线索分配的公平性</li>
                <li><strong>减少流失</strong>：及时回收机制减少线索流失率</li>
                <li><strong>数据驱动</strong>：详细的统计数据支持管理决策</li>
            </ul>
        </div>

        <div class="info">
            <h3>技术价值</h3>
            <ul>
                <li><strong>模块化设计</strong>：可复用的分配规则引擎</li>
                <li><strong>扩展性强</strong>：支持多种分配策略的扩展</li>
                <li><strong>性能优化</strong>：缓存和异步处理提升系统性能</li>
                <li><strong>监控完善</strong>：全面的日志和统计监控</li>
            </ul>
        </div>

        <h2>📝 总结</h2>
        
        <p>线索池功能是对现有线索管理模块的重要扩展，通过引入统一的线索池管理、智能分配规则、自动回收机制等功能，将大幅提升线索管理的效率和质量。该功能的实现将采用模块化设计，确保与现有系统的良好集成，同时为未来的功能扩展预留空间。</p>
        
        <div class="warning">
            <strong>注意事项：</strong>
            <ul>
                <li>实施过程中需要与现有线索管理功能保持兼容</li>
                <li>分配规则的设计需要考虑业务的复杂性和灵活性</li>
                <li>需要充分测试并发场景下的数据一致性</li>
                <li>建议分阶段实施，确保每个阶段的稳定性</li>
            </ul>
        </div>
    </div>
</body>
</html>