<template>
  <div class="payment-plans-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchInput"
          placeholder="计划编号/客户名称"
          style="width: 240px"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        <el-select
          v-model="statusFilter"
          placeholder="计划状态"
          style="width: 120px; margin-left: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="草稿" value="草稿" />
          <el-option label="待审批" value="待审批" />
          <el-option label="审批中" value="审批中" />
          <el-option label="已通过" value="已通过" />
          <el-option label="执行中" value="执行中" />
          <el-option label="已完成" value="已完成" />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><plus /></el-icon>
          新建计划
        </el-button>
        <el-button @click="handleExport">
          <el-icon><download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="planNumber" label="计划编号" width="150" />
      <el-table-column prop="customerName" label="客户名称" width="180" />
      <el-table-column prop="responsibleUserName" label="负责人" width="120" />
      <el-table-column prop="totalAmount" label="总金额" width="120">
        <template #default="scope">
          <span>{{ formatCurrency(scope.row.totalAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="receivedAmount" label="已回款" width="120">
        <template #default="scope">
          <span>{{ formatCurrency(scope.row.receivedAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remainingAmount" label="剩余金额" width="120">
        <template #default="scope">
          <span>{{ formatCurrency(scope.row.remainingAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="planStatus" label="计划状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.planStatus)">
            {{ scope.row.planStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="approvalStatus" label="审批状态" width="100">
        <template #default="scope">
          <el-tag :type="getApprovalStatusType(scope.row.approvalStatus)">
            {{ scope.row.approvalStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150" />
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleView(scope.row)">
            <el-icon><view /></el-icon>
            查看
          </el-button>
          <el-button type="warning" link @click="handleEdit(scope.row)">
            <el-icon><edit /></el-icon>
            编辑
          </el-button>
          <el-button 
            v-if="scope.row.planStatus === '草稿'" 
            type="success" 
            link 
            @click="handleSubmitApproval(scope.row)"
          >
            <el-icon><check /></el-icon>
            提交审批
          </el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">
            <el-icon><delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      draggable
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划编号" prop="planNumber">
              <el-input v-model="formData.planNumber" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerId">
              <el-select 
                v-model="formData.customerId" 
                placeholder="请选择客户" 
                style="width: 100%"
                filterable
                remote
                :loading="customersLoading"
                :remote-method="handleCustomerSearch"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="customer.customerName"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsibleUserId">
              <el-select 
                v-model="formData.responsibleUserId" 
                placeholder="请选择负责人" 
                style="width: 100%"
                filterable
                :loading="usersLoading"
                @change="handleUserChange"
              >
                <el-option
                  v-for="user in users"
                  :key="user.userId"
                  :label="user.nickName || user.userName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总金额" prop="totalAmount">
              <el-input-number
                v-model="formData.totalAmount"
                :min="0"
                :precision="2"
                :step="1000"
                controls-position="right"
                style="width: 100%"
                placeholder="请输入总金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联商机" prop="opportunityId">
              <el-select 
                v-model="formData.opportunityId" 
                placeholder="请选择关联商机" 
                style="width: 100%"
                filterable
                clearable
                :loading="opportunitiesLoading"
              >
                <el-option
                  v-for="opportunity in opportunities"
                  :key="opportunity.id"
                  :label="opportunity.opportunityName"
                  :value="opportunity.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联联系人" prop="contactId">
              <el-select 
                v-model="formData.contactId" 
                placeholder="请选择关联联系人" 
                style="width: 100%"
                filterable
                clearable
                :loading="contactsLoading"
              >
                <el-option
                  v-for="contact in contacts"
                  :key="contact.id"
                  :label="contact.contactName"
                  :value="contact.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractId">
              <el-select 
                v-model="formData.contractId" 
                placeholder="请选择合同" 
                style="width: 100%"
                filterable
                clearable
                :loading="contractsLoading"
              >
                <el-option
                  v-for="contract in contracts"
                  :key="contract.id"
                  :label="contract.contractNumber"
                  :value="contract.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款方式" prop="paymentMethod">
              <el-select v-model="formData.paymentMethod" placeholder="请选择回款方式" style="width: 100%">
                <el-option label="银行转账" value="银行转账" />
                <el-option label="现金" value="现金" />
                <el-option label="支票" value="支票" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="币种" prop="currency">
              <el-select v-model="formData.currency" placeholder="请选择币种" style="width: 100%">
                <el-option label="人民币" value="CNY" />
                <el-option label="美元" value="USD" />
                <el-option label="欧元" value="EUR" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划类型" prop="planType">
              <el-select v-model="formData.planType" placeholder="请选择计划类型" style="width: 100%">
                <el-option label="普通" value="普通" />
                <el-option label="紧急" value="紧急" />
                <el-option label="特殊" value="特殊" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 回款计划详情抽屉 -->
    <common-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      :data="currentRow"
      :config="drawerConfig"
      size="60%"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Download, View, Edit, Check, Delete } from '@element-plus/icons-vue'
import Pagination from '@/components/Pagination/index.vue'
import CommonFormDialog from '@/components/CommonFormDialog/index.vue'
import CommonDrawer from '@/components/CommonDrawer/index.vue'
import PaymentInstallmentsTab from './PaymentInstallmentsTab.vue'
import PaymentApprovalsTab from './PaymentApprovalsTab.vue'
import { newPlanFormConfig } from '../config'
import {
  listPaymentPlan,
  addPaymentPlan,
  updatePaymentPlan,
  delPaymentPlan,
  getPaymentPlan,
  submitForApproval,
  exportPaymentPlan,
  generatePlanNumber
} from '@/api/crm/paymentPlan'
import { searchCustomers } from '@/api/crm/customers'
import { getContactsByCustomer, searchContacts } from '@/api/crm/contacts'
import { searchOpportunity, getOpportunityByCustomer } from '@/api/crm/opportunity'
import { searchContract, getContractByCustomer } from '@/api/crm/contract'
import { listUser } from '@/api/system/user'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const drawerVisible = ref(false)
const dialogTitle = ref('')
const drawerTitle = ref('')
const currentRow = ref({})
const formData = ref({})
const selectedRows = ref([])
const searchInput = ref('')
const statusFilter = ref('')

// 选择器数据源
const customers = ref([])
const users = ref([])
const opportunities = ref([])
const contacts = ref([])
const contracts = ref([])

// 加载状态
const customersLoading = ref(false)
const usersLoading = ref(false)
const opportunitiesLoading = ref(false)
const contactsLoading = ref(false)
const contractsLoading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  planNumber: undefined,
  customerName: undefined,
  planStatus: undefined
})

// 表单配置
const planFormConfig = newPlanFormConfig

// 抽屉配置
const drawerConfig = {
  headerFields: [
    { label: '计划编号', field: 'planNumber' },
    { label: '客户名称', field: 'customerName' },
    { label: '总金额', field: 'totalAmount' },
    { label: '创建时间', field: 'createTime' }
  ],
  actions: [
    {
      label: '编辑',
      type: 'primary',
      icon: 'Edit',
      handler: (data: any) => handleEdit(data)
    },
    {
      label: '提交审批',
      type: 'success',
      icon: 'Check',
      handler: (data: any) => handleSubmitApproval(data)
    }
  ],
  menuItems: [
    {
      key: 'details',
      label: '基本信息',
      icon: 'Document'
    },
    {
      key: 'installments',
      label: '分期计划',
      icon: 'Calendar',
      component: PaymentInstallmentsTab
    },
    {
      key: 'approvals',
      label: '审批记录',
      icon: 'List',
      component: PaymentApprovalsTab
    }
  ]
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const response = await listPaymentPlan(queryParams)
    tableData.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取回款计划列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.planNumber = searchInput.value
  queryParams.customerName = searchInput.value
  queryParams.planStatus = statusFilter.value
  queryParams.pageNum = 1
  getList()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = async () => {
  try {
    const response = await generatePlanNumber()
    formData.value = {
      planNumber: response.data,
      currency: 'CNY',
      planType: '普通',
      riskLevel: '低',
      customerId: undefined,
      responsibleUserId: undefined,
      opportunityId: undefined,
      contactId: undefined,
      contractId: undefined
    }
    dialogTitle.value = '新增回款计划'
    dialogVisible.value = true
  } catch (error) {
    console.error('生成计划编号失败:', error)
  }
}

// 编辑
const handleEdit = async (row: any) => {
  try {
    const response = await getPaymentPlan(row.id)
    formData.value = response.data
    dialogTitle.value = '编辑回款计划'
    dialogVisible.value = true
  } catch (error) {
    console.error('获取回款计划详情失败:', error)
  }
}

// 查看
const handleView = (row: any) => {
  currentRow.value = row
  drawerTitle.value = `回款计划详情 - ${row.planNumber}`
  drawerVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除回款计划 "${row.planNumber}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await delPaymentPlan(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 提交审批
const handleSubmitApproval = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交回款计划 "${row.planNumber}" 审批吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    await submitForApproval(row.id)
    ElMessage.success('提交审批成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交审批失败:', error)
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    await exportPaymentPlan(queryParams)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 表单引用和规则
const formRef = ref()
const formRules = {
  customerId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  responsibleUserId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  totalAmount: [
    { required: true, message: '请输入总金额', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择回款方式', trigger: 'change' }
  ],
  currency: [
    { required: true, message: '请选择币种', trigger: 'change' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (formData.value.id) {
      await updatePaymentPlan(formData.value)
      ElMessage.success('修改成功')
    } else {
      await addPaymentPlan(formData.value)
      ElMessage.success('新增成功')
    }
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
  formData.value = {}
}

// 格式化金额
const formatCurrency = (amount: number) => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '草稿': 'info',
    '待审批': 'warning',
    '审批中': 'warning',
    '已通过': 'success',
    '执行中': 'primary',
    '已完成': 'success',
    '已终止': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取审批状态类型
const getApprovalStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待提交': 'info',
    '审批中': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取客户列表
const getCustomers = async (keyword = '') => {
  customersLoading.value = true
  try {
    const response = await searchCustomers(keyword)
    customers.value = response.rows || response.data || []
  } catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
  } finally {
    customersLoading.value = false
  }
}

// 获取用户列表
const getUsers = async () => {
  usersLoading.value = true
  try {
    const response = await listUser({ status: '0' }) // 只获取正常状态的用户
    users.value = response.rows || response.data || []
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

// 获取商机列表
const getOpportunities = async (keyword = '', customerId = null) => {
  opportunitiesLoading.value = true
  try {
    let response
    if (customerId) {
      response = await getOpportunityByCustomer(customerId)
    } else {
      response = await searchOpportunity(keyword)
    }
    opportunities.value = response.rows || response.data || []
  } catch (error) {
    console.error('获取商机列表失败:', error)
    ElMessage.error('获取商机列表失败')
  } finally {
    opportunitiesLoading.value = false
  }
}

// 获取联系人列表
const getContacts = async (customerId?: number, keyword = '') => {
  contactsLoading.value = true
  try {
    let response
    if (customerId) {
      response = await getContactsByCustomer(customerId)
    } else {
      response = await searchContacts(keyword)
    }
    contacts.value = response.rows || response.data || []
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    ElMessage.error('获取联系人列表失败')
  } finally {
    contactsLoading.value = false
  }
}

// 获取合同列表
const getContracts = async (customerId?: number, keyword = '') => {
  contractsLoading.value = true
  try {
    let response
    if (customerId) {
      response = await getContractByCustomer(customerId)
    } else {
      response = await searchContract(keyword)
    }
    contracts.value = response.rows || response.data || []
  } catch (error) {
    console.error('获取合同列表失败:', error)
    ElMessage.error('获取合同列表失败')
  } finally {
    contractsLoading.value = false
  }
}

// 客户选择变化处理
const handleCustomerChange = (customerId: number) => {
  // 清空相关选择
  formData.value.contactId = undefined
  formData.value.opportunityId = undefined
  formData.value.contractId = undefined
  
  // 根据选择的客户重新获取联系人、商机和合同列表
  if (customerId) {
    getContacts(customerId)
    getOpportunities('', customerId)
    getContracts(customerId)
  }
}

// 用户选择变化处理
const handleUserChange = (userId: number) => {
  // 可以在这里处理用户选择变化的逻辑
  console.log('选择用户:', userId)
}

// 客户远程搜索
const handleCustomerSearch = (keyword: string) => {
  if (keyword) {
    getCustomers(keyword)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
  getUsers() // 预加载用户列表
  getCustomers() // 预加载客户列表，便于选择
})
</script>

<style scoped>
.payment-plans-tab {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}
</style>