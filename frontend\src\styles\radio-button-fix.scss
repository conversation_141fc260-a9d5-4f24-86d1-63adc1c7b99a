/* 强制修复 radio-button 样式 */

/* 重置所有可能的默认样式 */
.ep-radio-group {
  display: inline-flex !important;
  align-items: center !important;
  font-size: 0 !important;
  gap: 0 !important;
  
  /* 确保按钮紧密连接 */
  .ep-radio-button {
    position: relative !important;
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 32px !important;
    border: 1px solid #dcdfe6 !important;
    border-radius: 0 !important;
    background: #fff !important;
    color: #606266 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
    box-sizing: border-box !important;
    vertical-align: top !important;
    float: none !important;
    flex: none !important;

    /* 移除可能的间距 */
    &:not(:first-child) {
      margin-left: -1px !important;
      border-left: 0 !important;
    }

    /* 第一个按钮的左边圆角 */
    &:first-child {
      border-radius: 4px 0 0 4px !important;
      border-left: 1px solid #dcdfe6 !important;
    }

    /* 最后一个按钮的右边圆角 */
    &:last-child {
      border-radius: 0 4px 4px 0 !important;
    }

    /* 如果只有一个按钮 */
    &:first-child:last-child {
      border-radius: 4px !important;
    }

    /* 悬停状态 */
    &:hover:not(.is-disabled) {
      color: #409eff !important;
      border-color: #409eff !important;
      z-index: 1 !important;
      background: #fff !important;
    }

    /* 激活状态 */
    &.is-active {
      color: #fff !important;
      background: #409eff !important;
      border-color: #409eff !important;
      z-index: 2 !important;
    }

    /* 禁用状态 */
    &.is-disabled {
      cursor: not-allowed !important;
      color: #c0c4cc !important;
      background-color: #f5f7fa !important;
      border-color: #e4e7ed !important;
    }

    /* 内部元素 */
    .ep-radio-button__inner {
      display: inline-block !important;
      line-height: 1 !important;
      white-space: nowrap !important;
      padding: 8px 16px !important;
      background: transparent !important;
      transition: all 0.3s ease !important;
      border: none !important;
      outline: none !important;
      font-size: 14px !important;
      color: inherit !important;
      margin: 0 !important;
      width: 100% !important;
      height: 100% !important;
      box-sizing: border-box !important;
    }

    /* 隐藏的radio输入 */
    .ep-radio-button__original-radio {
      position: absolute !important;
      opacity: 0 !important;
      outline: none !important;
      z-index: -1 !important;
      margin: 0 !important;
      width: 0 !important;
      height: 0 !important;
    }
  }
}

/* 小尺寸特殊处理 */
.ep-radio-group.ep-radio-group--small {
  .ep-radio-button {
    height: 28px !important;
    font-size: 12px !important;
    
    .ep-radio-button__inner {
      padding: 6px 12px !important;
      font-size: 12px !important;
    }
  }
}

/* 大尺寸特殊处理 */
.ep-radio-group.ep-radio-group--large {
  .ep-radio-button {
    height: 36px !important;
    font-size: 16px !important;
    
    .ep-radio-button__inner {
      padding: 10px 20px !important;
      font-size: 16px !important;
    }
  }
}

/* 确保在卡片头部的样式正确 */
.card-header .ep-radio-group {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.header-actions .ep-radio-group {
  margin: 0 !important;
}
