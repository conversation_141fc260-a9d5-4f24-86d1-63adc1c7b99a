import { TableRowData } from '@/components/TableOperations/types';

// 合同数据类型定义
export interface ContractData extends TableRowData {
    contract_number: string;
    contract_name: string;
    customer_name: string;
    quotation_number: string;
    opportunity_name: string;
    contract_amount: number;
    order_date: string;
    start_date: string;
    end_date: string;
    customer_signatory: string;
    company_signatory: string;
    remarks: string;
    contract_type: string;
    profit: number;
}

// 筛选类型定义
export type FilterType = 'all' | 'mine' | 'subordinate' | 'following';

// 查询参数类型
export interface QueryParams {
    pageNum: number;
    pageSize: number;
    searchInput?: string;
    filterType?: FilterType;
}

// 新建合同表单数据类型
export interface NewContractFormData {
    contract_number: string;
    contract_name: string;
    customer_name: string;
    quotation_number: string;
    opportunity_name: string;
    contract_amount: number;
    order_date: string;
    start_date: string;
    end_date: string;
    customer_signatory: string;
    company_signatory: string;
    remarks: string;
    contract_type: string;
    profit: number;
} 