<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM客户管理控制器接口文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .overview {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .api-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .put { background-color: #ffc107; color: #212529; }
        .delete { background-color: #dc3545; }
        .endpoint {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
        }
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .params-table th,
        .params-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        .params-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            background: #e8f5e8;
            margin: 8px 0;
            padding: 10px 15px;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRM客户管理控制器接口文档</h1>
        
        <div class="overview">
            <h2>📋 概述</h2>
            <p><strong>控制器类名：</strong> CrmCustomerController</p>
            <p><strong>基础路径：</strong> /front/crm/customer</p>
            <p><strong>功能描述：</strong> 提供客户管理的完整REST API接口，包括客户的增删改查、搜索、导出、关注管理等功能</p>
            <p><strong>权限控制：</strong> 使用@Anonymous注解，支持匿名访问</p>
            <p><strong>操作日志：</strong> 集成了操作日志记录功能，自动记录客户相关操作</p>
        </div>

        <div class="toc">
            <h2>📑 目录</h2>
            <ul>
                <li><a href="#basic-operations">1. 基础操作接口</a></li>
                <li><a href="#search-export">2. 搜索与导出接口</a></li>
                <li><a href="#follow-management">3. 关注管理接口</a></li>
                <li><a href="#data-structures">4. 数据结构说明</a></li>
                <li><a href="#error-handling">5. 错误处理</a></li>
                <li><a href="#usage-examples">6. 使用示例</a></li>
            </ul>
        </div>

        <h2 id="basic-operations">🔧 基础操作接口</h2>
        
        <div class="api-item">
            <h3><span class="method get">GET</span><span class="endpoint">/front/crm/customer/list</span></h3>
            <p><strong>功能：</strong> 查询客户列表（分页）</p>
            <p><strong>描述：</strong> 获取客户列表，支持分页查询和条件筛选</p>
            
            <h4>请求参数：</h4>
            <table class="params-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>pageNum</td>
                    <td>Integer</td>
                    <td>否</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td>pageSize</td>
                    <td>Integer</td>
                    <td>否</td>
                    <td>每页大小，默认10</td>
                </tr>
                <tr>
                    <td>customerName</td>
                    <td>String</td>
                    <td>否</td>
                    <td>客户名称（模糊查询）</td>
                </tr>
                <tr>
                    <td>mobile</td>
                    <td>String</td>
                    <td>否</td>
                    <td>手机号码</td>
                </tr>
                <tr>
                    <td>industry</td>
                    <td>String</td>
                    <td>否</td>
                    <td>所属行业</td>
                </tr>
                <tr>
                    <td>level</td>
                    <td>String</td>
                    <td>否</td>
                    <td>客户级别</td>
                </tr>
            </table>
            
            <h4>返回数据：</h4>
            <div class="code-block">
{
  "total": 100,
  "rows": [
    {
      "id": 1,
      "customerName": "示例客户",
      "mobile": "13800138000",
      "email": "<EMAIL>",
      "industry": "制造业",
      "level": "A级",
      "address": "北京市朝阳区",
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
            </div>
        </div>

        <div class="api-item">
            <h3><span class="method get">GET</span><span class="endpoint">/front/crm/customer/{id}</span></h3>
            <p><strong>功能：</strong> 获取客户详细信息</p>
            <p><strong>描述：</strong> 根据客户ID获取客户的详细信息</p>
            
            <h4>路径参数：</h4>
            <table class="params-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>id</td>
                    <td>Long</td>
                    <td>是</td>
                    <td>客户ID</td>
                </tr>
            </table>
        </div>

        <div class="api-item">
            <h3><span class="method post">POST</span><span class="endpoint">/front/crm/customer</span></h3>
            <p><strong>功能：</strong> 新增客户</p>
            <p><strong>描述：</strong> 创建新的客户记录，自动记录操作日志</p>
            
            <h4>请求体：</h4>
            <div class="code-block">
{
  "customerName": "新客户名称",
  "mobile": "13800138000",
  "phone": "010-12345678",
  "email": "<EMAIL>",
  "website": "https://www.example.com",
  "industry": "制造业",
  "level": "A级",
  "address": "详细地址",
  "primaryContact": "主要联系人",
  "nextContactTime": "2024-12-31 10:00:00",
  "remark": "备注信息"
}
            </div>
            
            <div class="note">
                <strong>注意：</strong> 新增成功后，返回的数据中会包含自动生成的客户ID
            </div>
        </div>

        <div class="api-item">
            <h3><span class="method put">PUT</span><span class="endpoint">/front/crm/customer</span></h3>
            <p><strong>功能：</strong> 修改客户信息</p>
            <p><strong>描述：</strong> 更新现有客户的信息，自动记录操作日志</p>
            
            <h4>请求体：</h4>
            <p>与新增客户相同，但必须包含客户ID</p>
        </div>

        <div class="api-item">
            <h3><span class="method delete">DELETE</span><span class="endpoint">/front/crm/customer/{ids}</span></h3>
            <p><strong>功能：</strong> 删除客户</p>
            <p><strong>描述：</strong> 批量删除客户记录，支持删除多个客户</p>
            
            <h4>路径参数：</h4>
            <table class="params-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>ids</td>
                    <td>Long[]</td>
                    <td>是</td>
                    <td>客户ID数组，用逗号分隔</td>
                </tr>
            </table>
        </div>

        <h2 id="search-export">🔍 搜索与导出接口</h2>
        
        <div class="api-item">
            <h3><span class="method get">GET</span><span class="endpoint">/front/crm/customer/search</span></h3>
            <p><strong>功能：</strong> 高级搜索客户</p>
            <p><strong>描述：</strong> 使用CustomerSearchDTO进行复杂条件搜索</p>
            
            <h4>请求参数：</h4>
            <table class="params-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>keyword</td>
                    <td>String</td>
                    <td>否</td>
                    <td>关键词搜索（客户名称、联系方式等）</td>
                </tr>
                <tr>
                    <td>industry</td>
                    <td>String</td>
                    <td>否</td>
                    <td>行业筛选</td>
                </tr>
                <tr>
                    <td>level</td>
                    <td>String</td>
                    <td>否</td>
                    <td>客户级别筛选</td>
                </tr>
                <tr>
                    <td>createTimeStart</td>
                    <td>Date</td>
                    <td>否</td>
                    <td>创建时间开始</td>
                </tr>
                <tr>
                    <td>createTimeEnd</td>
                    <td>Date</td>
                    <td>否</td>
                    <td>创建时间结束</td>
                </tr>
            </table>
        </div>

        <div class="api-item">
            <h3><span class="method post">POST</span><span class="endpoint">/front/crm/customer/export</span></h3>
            <p><strong>功能：</strong> 导出客户列表</p>
            <p><strong>描述：</strong> 将客户数据导出为Excel文件</p>
            
            <h4>请求体：</h4>
            <p>与查询客户列表的参数相同，用于筛选要导出的数据</p>
            
            <h4>返回：</h4>
            <p>Excel文件下载</p>
        </div>

        <h2 id="follow-management">⭐ 关注管理接口</h2>
        
        <div class="api-item">
            <h3><span class="method post">POST</span><span class="endpoint">/front/crm/customer/follow/{customerId}</span></h3>
            <p><strong>功能：</strong> 关注客户</p>
            <p><strong>描述：</strong> 当前用户关注指定客户</p>
            
            <h4>路径参数：</h4>
            <table class="params-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必填</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>customerId</td>
                    <td>Long</td>
                    <td>是</td>
                    <td>客户ID</td>
                </tr>
            </table>
        </div>

        <div class="api-item">
            <h3><span class="method delete">DELETE</span><span class="endpoint">/front/crm/customer/follow/{customerId}</span></h3>
            <p><strong>功能：</strong> 取消关注客户</p>
            <p><strong>描述：</strong> 当前用户取消关注指定客户</p>
        </div>

        <div class="api-item">
            <h3><span class="method get">GET</span><span class="endpoint">/front/crm/customer/follow/status/{customerId}</span></h3>
            <p><strong>功能：</strong> 查询关注状态</p>
            <p><strong>描述：</strong> 查询当前用户是否关注了指定客户</p>
            
            <h4>返回数据：</h4>
            <div class="code-block">
{
  "code": 200,
  "msg": "查询成功",
  "data": true  // true表示已关注，false表示未关注
}
            </div>
        </div>

        <div class="api-item">
            <h3><span class="method post">POST</span><span class="endpoint">/front/crm/customer/follow/batch</span></h3>
            <p><strong>功能：</strong> 批量关注客户</p>
            <p><strong>描述：</strong> 当前用户批量关注多个客户</p>
            
            <h4>请求体：</h4>
            <div class="code-block">
[1, 2, 3, 4, 5]  // 客户ID数组
            </div>
        </div>

        <div class="api-item">
            <h3><span class="method delete">DELETE</span><span class="endpoint">/front/crm/customer/follow/batch</span></h3>
            <p><strong>功能：</strong> 批量取消关注客户</p>
            <p><strong>描述：</strong> 当前用户批量取消关注多个客户</p>
        </div>

        <h2 id="data-structures">📊 数据结构说明</h2>
        
        <div class="api-item">
            <h3>CrmCustomer 客户实体</h3>
            <table class="params-table">
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>id</td>
                    <td>Long</td>
                    <td>客户ID（主键）</td>
                </tr>
                <tr>
                    <td>ownerId</td>
                    <td>Long</td>
                    <td>负责人ID</td>
                </tr>
                <tr>
                    <td>customerName</td>
                    <td>String</td>
                    <td>客户名称</td>
                </tr>
                <tr>
                    <td>source</td>
                    <td>String</td>
                    <td>客户来源</td>
                </tr>
                <tr>
                    <td>mobile</td>
                    <td>String</td>
                    <td>手机号码</td>
                </tr>
                <tr>
                    <td>phone</td>
                    <td>String</td>
                    <td>固定电话</td>
                </tr>
                <tr>
                    <td>email</td>
                    <td>String</td>
                    <td>邮箱地址</td>
                </tr>
                <tr>
                    <td>website</td>
                    <td>String</td>
                    <td>网站地址</td>
                </tr>
                <tr>
                    <td>industry</td>
                    <td>String</td>
                    <td>所属行业</td>
                </tr>
                <tr>
                    <td>level</td>
                    <td>String</td>
                    <td>客户级别</td>
                </tr>
                <tr>
                    <td>address</td>
                    <td>String</td>
                    <td>详细地址</td>
                </tr>
                <tr>
                    <td>primaryContact</td>
                    <td>String</td>
                    <td>首要联系人</td>
                </tr>
                <tr>
                    <td>dealStatus</td>
                    <td>String</td>
                    <td>成交状态</td>
                </tr>
                <tr>
                    <td>nextContactTime</td>
                    <td>Date</td>
                    <td>下次联系时间</td>
                </tr>
                <tr>
                    <td>remark</td>
                    <td>String</td>
                    <td>备注信息</td>
                </tr>
                <tr>
                    <td>status</td>
                    <td>String</td>
                    <td>状态</td>
                </tr>
                <tr>
                    <td>createTime</td>
                    <td>Date</td>
                    <td>创建时间</td>
                </tr>
                <tr>
                    <td>updateTime</td>
                    <td>Date</td>
                    <td>更新时间</td>
                </tr>
            </table>
        </div>

        <h2 id="error-handling">⚠️ 错误处理</h2>
        
        <div class="warning">
            <h3>常见错误码</h3>
            <ul>
                <li><strong>200：</strong> 操作成功</li>
                <li><strong>400：</strong> 请求参数错误</li>
                <li><strong>401：</strong> 用户未登录</li>
                <li><strong>403：</strong> 权限不足</li>
                <li><strong>404：</strong> 资源不存在</li>
                <li><strong>500：</strong> 服务器内部错误</li>
            </ul>
        </div>

        <div class="note">
            <h3>错误响应格式</h3>
            <div class="code-block">
{
  "code": 400,
  "msg": "请求参数错误",
  "data": null
}
            </div>
        </div>

        <h2 id="usage-examples">💡 使用示例</h2>
        
        <div class="api-item">
            <h3>JavaScript 调用示例</h3>
            <div class="code-block">
// 查询客户列表
fetch('/front/crm/customer/list?pageNum=1&pageSize=10')
  .then(response => response.json())
  .then(data => {
    console.log('客户列表:', data.rows);
  });

// 新增客户
fetch('/front/crm/customer', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    customerName: '测试客户',
    mobile: '13800138000',
    email: '<EMAIL>',
    industry: '制造业',
    level: 'A级'
  })
})
.then(response => response.json())
.then(data => {
  console.log('新增结果:', data);
});

// 关注客户
fetch('/front/crm/customer/follow/1', {
  method: 'POST'
})
.then(response => response.json())
.then(data => {
  console.log('关注结果:', data.msg);
});
            </div>
        </div>

        <div class="note">
            <h3>🔧 技术特性</h3>
            <ul class="feature-list">
                <li>✅ 支持分页查询，提高大数据量处理性能</li>
                <li>✅ 集成操作日志，自动记录所有客户操作</li>
                <li>✅ 支持批量操作，提高操作效率</li>
                <li>✅ 完善的错误处理和异常捕获</li>
                <li>✅ 支持Excel导出功能</li>
                <li>✅ 客户关注功能，便于重点客户管理</li>
                <li>✅ 高级搜索功能，支持多条件组合查询</li>
                <li>✅ RESTful API设计，符合标准规范</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>所有关注相关操作都需要用户登录状态</li>
                <li>删除操作不可逆，请谨慎使用</li>
                <li>批量操作时建议控制数据量，避免超时</li>
                <li>导出功能可能受到数据量限制</li>
                <li>部分接口的权限控制已被注释，实际使用时请根据需要启用</li>
            </ul>
        </div>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
            <p>© 2024 CRM客户管理系统 - 接口文档</p>
            <p>最后更新时间: 2024年12月19日</p>
        </footer>
    </div>
</body>
</html>