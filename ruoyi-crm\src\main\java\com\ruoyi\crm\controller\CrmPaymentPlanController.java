package com.ruoyi.crm.controller;

import java.util.List;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.service.ICrmPaymentInstallmentService;
import com.ruoyi.crm.service.ICrmPaymentApprovalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 回款计划Controller
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@RestController
@RequestMapping("/crm/paymentPlan")
public class CrmPaymentPlanController extends BaseController {
    
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;
    
    @Autowired
    private ICrmPaymentInstallmentService installmentService;
    
    @Autowired
    private ICrmPaymentApprovalService approvalService;

    /**
     * 查询回款计划列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CrmPaymentPlan plan) {
        startPage();
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanList(plan);
        return getDataTable(list);
    }

    /**
     * 导出回款计划列表
     */
    @Log(title = "回款计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmPaymentPlan plan) {
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanList(plan);
        ExcelUtil<CrmPaymentPlan> util = new ExcelUtil<CrmPaymentPlan>(CrmPaymentPlan.class);
        util.exportExcel(response, list, "回款计划数据");
    }

    /**
     * 获取回款计划详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(id);
        return success(plan);
    }

    /**
     * 获取回款计划详细信息（包含分期和审批）
     */
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetailsInfo(@PathVariable("id") Long id) {
        CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanWithDetailsById(id);
        return success(plan);
    }

    /**
     * 新增回款计划
     */
    @Log(title = "回款计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmPaymentPlan plan) {
        return toAjax(paymentPlanService.insertPaymentPlan(plan));
    }

    /**
     * 修改回款计划
     */
    @Log(title = "回款计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmPaymentPlan plan) {
        return toAjax(paymentPlanService.updatePaymentPlan(plan));
    }

    /**
     * 删除回款计划
     */
    @Log(title = "回款计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(paymentPlanService.deletePaymentPlanByIds(ids));
    }

    /**
     * 根据合同ID查询回款计划
     */
    @GetMapping("/contract/{contractId}")
    public AjaxResult getByContractId(@PathVariable Long contractId) {
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanByContractId(contractId);
        return success(list);
    }

    /**
     * 生成回款计划编号
     */
    @GetMapping("/generateNumber")
    public AjaxResult generatePlanNumber() {
        String planNumber = paymentPlanService.generatePlanNumber();
        return success(planNumber);
    }

    /**
     * 提交审批
     */
    @Log(title = "回款计划审批", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApproval/{id}")
    public AjaxResult submitForApproval(@PathVariable Long id) {
        return toAjax(paymentPlanService.submitForApproval(id));
    }

    /**
     * 审批处理
     */
    @Log(title = "回款计划审批", businessType = BusinessType.UPDATE)
    @PostMapping("/processApproval")
    public AjaxResult processApproval(@RequestParam Long approvalId, 
                                    @RequestParam boolean approved, 
                                    @RequestParam(required = false) String comments) {
        return toAjax(paymentPlanService.processApproval(approvalId, approved, comments));
    }

    /**
     * 获取客户回款计划统计
     */
    @GetMapping("/statistics/customer/{customerId}")
    public AjaxResult getCustomerStatistics(@PathVariable Long customerId) {
        int count = paymentPlanService.getPaymentPlanCount(customerId);
        return success(count);
    }

    /**
     * 更新回款金额统计
     */
    @PostMapping("/updateAmounts/{id}")
    public AjaxResult updatePaymentAmounts(@PathVariable Long id) {
        return toAjax(paymentPlanService.updatePaymentAmounts(id));
    }

    // ========================= 分期管理相关接口 =========================

    /**
     * 查询分期列表
     */
    @GetMapping("/installments/{planId}")
    public AjaxResult getInstallments(@PathVariable Long planId) {
        List<CrmPaymentInstallment> list = installmentService.selectInstallmentsByPlanId(planId);
        return success(list);
    }

    /**
     * 新增分期
     */
    @Log(title = "回款分期", businessType = BusinessType.INSERT)
    @PostMapping("/installments")
    public AjaxResult addInstallment(@RequestBody CrmPaymentInstallment installment) {
        return toAjax(installmentService.insertCrmPaymentInstallment(installment));
    }

    /**
     * 修改分期
     */
    @Log(title = "回款分期", businessType = BusinessType.UPDATE)
    @PutMapping("/installments")
    public AjaxResult editInstallment(@RequestBody CrmPaymentInstallment installment) {
        return toAjax(installmentService.updateCrmPaymentInstallment(installment));
    }

    /**
     * 删除分期
     */
    @Log(title = "回款分期", businessType = BusinessType.DELETE)
    @DeleteMapping("/installments/{ids}")
    public AjaxResult removeInstallments(@PathVariable Long[] ids) {
        return toAjax(installmentService.deleteCrmPaymentInstallmentByIds(ids));
    }

    /**
     * 记录实际回款
     */
    @Log(title = "回款记录", businessType = BusinessType.UPDATE)
    @PostMapping("/installments/payment")
    public AjaxResult recordPayment(@RequestParam Long installmentId,
                                   @RequestParam BigDecimal actualAmount,
                                   @RequestParam(required = false) String paymentVoucher) {
        return toAjax(installmentService.recordActualPayment(installmentId, actualAmount, paymentVoucher));
    }

    /**
     * 更新逾期天数
     */
    @PostMapping("/installments/updateOverdue")
    public AjaxResult updateOverdueDays() {
        return toAjax(installmentService.updateOverdueDays());
    }

    // ========================= 审批管理相关接口 =========================

    /**
     * 查询审批列表
     */
    @GetMapping("/approvals/{planId}")
    public AjaxResult getApprovals(@PathVariable Long planId) {
        List<CrmPaymentApproval> list = approvalService.selectApprovalsByPlanId(planId);
        return success(list);
    }

    /**
     * 查询待我审批的记录
     */
    @GetMapping("/approvals/pending")
    public AjaxResult getPendingApprovals() {
        Long userId = getUserId();
        List<CrmPaymentApproval> list = approvalService.selectPendingApprovals(userId);
        return success(list);
    }

    /**
     * 获取当前审批级别
     */
    @GetMapping("/approvals/currentLevel/{planId}")
    public AjaxResult getCurrentApprovalLevel(@PathVariable Long planId) {
        Integer level = approvalService.getCurrentApprovalLevel(planId);
        return success(level);
    }

    /**
     * 获取下一个审批人
     */
    @GetMapping("/approvals/nextApprover/{planId}")
    public AjaxResult getNextApprover(@PathVariable Long planId) {
        CrmPaymentApproval nextApprover = approvalService.getNextApprover(planId);
        return success(nextApprover);
    }
}