// 线索查询参数
export interface LeadQueryParams {
    pageNum?: number;
    pageSize?: number;
    searchKeyword?: string;
    filterType?: string;
}

// 线索数据
export interface LeadData {
    id: number;
    name: string;
    customerName: string;
    source: string;
    status: string;
    phone: string;
    email: string;
    industry: string;
    remarks: string;
    createTime: string;
    nextFollowUpTime: string;
    owner: number;
    customerId?: number;
    contactId?: number;
    convertTime?: string;
}

// 线索转化数据
export interface LeadConvertData {
    leadId: number;
    convertType: 'existing' | 'new';
    customerId?: number;
    customerName?: string;
    industry?: string;
    contact: {
        name: string;
        position: string;
        phone: string;
        email: string;
    };
}

// 分配表单数据
export interface AssignForm {
    leadId: number;
    newOwnerId: number;
} 