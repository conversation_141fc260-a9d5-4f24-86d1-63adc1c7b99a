@echo off
chcp 65001 >nul
echo =============================================
echo 深化团队管理实施方案-V2 数据库初始化脚本
echo =============================================
echo.

:: 设置数据库连接参数
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_NAME=crm41

:: 提示用户输入数据库密码
set /p DB_PASSWORD=请输入数据库密码: 

echo.
echo 开始执行团队关联表创建脚本...
echo.

:: 检查MySQL是否可用
echo [1/4] 检查数据库连接...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，请检查连接参数
    pause
    exit /b 1
)
echo ✓ 数据库连接成功

:: 检查数据库是否存在
echo.
echo [2/4] 检查数据库 %DB_NAME% 是否存在...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 数据库 %DB_NAME% 不存在，请先创建数据库
    echo 可以执行: CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    pause
    exit /b 1
)
echo ✓ 数据库 %DB_NAME% 存在

echo.
echo [2.5/4] 检查现有表结构...
echo 当前 crm_teams 表结构:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE crm_teams;" 2>nul
echo.
echo 当前 crm_team_members 表结构:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE crm_team_members;" 2>nul

:: 执行SQL脚本
echo.
echo [3/4] 执行团队关联表升级脚本...
echo 注意：此脚本会升级现有的 crm_teams 和 crm_team_members 表结构
echo 并创建新的 crm_team_relations 通用关联表
echo.
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < sql\team_relations_v2.sql
if %errorlevel% neq 0 (
    echo ❌ SQL脚本执行失败
    pause
    exit /b 1
)
echo ✓ 团队关联表升级脚本执行成功

:: 验证表结构
echo.
echo [4/4] 验证表结构和数据...
echo.
echo 验证表是否创建成功:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SHOW TABLES LIKE 'crm_team%';"

echo.
echo 验证表结构:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "DESCRIBE crm_team_relations;"

echo.
echo 验证初始数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT id, team_name, team_code, team_type FROM crm_teams;"

echo.
echo =============================================
echo 深化团队管理实施方案-V2 数据库初始化完成！
echo =============================================
echo.
echo 完成的任务：
echo ✓ 升级 crm_teams 表结构（添加 team_code, leader_name, team_type, status, remark 字段）
echo ✓ 升级 crm_team_members 表结构（添加 user_name, nick_name, status 等字段，role_in_team 改为 role_type）
echo ✓ 创建 crm_team_relations 通用关联表
echo ✓ 插入初始团队数据
echo ✓ 验证表结构和索引
echo.
echo 升级后的表结构：
echo - crm_teams: 支持团队编码、类型、状态等完整字段
echo - crm_team_members: 支持用户信息缓存、状态管理等
echo - crm_team_relations: 通用业务关联表，支持多种业务类型
echo.
echo 支持的业务类型：
echo - CONTACT: 联系人
echo - LEAD: 线索
echo - CUSTOMER: 客户
echo - OPPORTUNITY: 商机
echo - CONTRACT: 合同
echo - VISIT_PLAN: 拜访计划
echo.
echo 接下来可以执行：
echo 1. 后端实体类和Mapper开发
echo 2. Service层通用关联逻辑实现
echo 3. Controller API接口开发
echo 4. 前端组件重构和集成
echo.
pause
