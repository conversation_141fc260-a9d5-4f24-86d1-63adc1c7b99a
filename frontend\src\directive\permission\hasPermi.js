/**
 * v-hasPermi 操作权限处理
 * Copyright (c) 2019 ruoyi
 */

import { useUserStore } from '@/stores/user'

// 权限检查函数
function checkPermission(el, binding) {
  const { value } = binding
  const all_permission = "*:*:*"
  
  // 获取用户权限 - 兼容不同的状态管理方式
  let permissions = []
  try {
    // 尝试使用 Pinia store
    const userStore = useUserStore()
    permissions = userStore.permissions || []
  } catch (error) {
    // 如果 Pinia store 不可用，尝试从 localStorage 或其他方式获取
    console.warn('无法获取用户权限，元素将被隐藏')
    permissions = []
  }

  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value

    const hasPermissions = permissions.some(permission => {
      return all_permission === permission || permissionFlag.includes(permission)
    })

    if (!hasPermissions) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    console.error(`请设置操作权限标签值，如 v-hasPermi="['system:user:add']"`)
  }
}

export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    // Vue 3 中可能需要在更新时重新检查权限
    checkPermission(el, binding)
  }
}