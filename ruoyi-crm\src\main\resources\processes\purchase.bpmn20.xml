<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.activiti.org/test">

    <process id="purchase" name="采购审批流程" isExecutable="true">
        <!-- 开始事件 -->
        <startEvent id="startEvent" name="提交采购申请"></startEvent>
        
        <!-- 部门主管审批 -->
        <userTask id="deptApproval" name="部门主管审批" 
                  activiti:assignee="${deptManager}">
            <documentation>部门主管初步审批采购申请</documentation>
        </userTask>
        
        <!-- 排他网关 - 根据金额判断 -->
        <exclusiveGateway id="amountGateway" name="金额判断"></exclusiveGateway>
        
        <!-- 总经理审批（大额采购） -->
        <userTask id="ceoApproval" name="总经理审批" 
                  activiti:assignee="${ceo}">
            <documentation>大额采购需要总经理审批</documentation>
        </userTask>
        
        <!-- 采购部门执行（小额采购） -->
        <userTask id="procurementExecution" name="采购部门执行" 
                  activiti:candidateGroups="procurement">
            <documentation>小额采购直接由采购部门执行</documentation>
        </userTask>
        
        <!-- 汇聚网关 -->
        <exclusiveGateway id="mergeGateway" name="流程汇聚"></exclusiveGateway>
        
        <!-- 结束事件 -->
        <endEvent id="endEvent" name="采购完成"></endEvent>
        
        <!-- 序列流 -->
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="deptApproval"></sequenceFlow>
        <sequenceFlow id="flow2" sourceRef="deptApproval" targetRef="amountGateway"></sequenceFlow>
        
        <!-- 条件分支 -->
        <sequenceFlow id="flowHighAmount" name="大额采购(>=10000)" 
                      sourceRef="amountGateway" targetRef="ceoApproval">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount >= 10000}]]></conditionExpression>
        </sequenceFlow>
        
        <sequenceFlow id="flowLowAmount" name="小额采购(<10000)" 
                      sourceRef="amountGateway" targetRef="procurementExecution">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount < 10000}]]></conditionExpression>
        </sequenceFlow>
        
        <!-- 汇聚流程 -->
        <sequenceFlow id="flow5" sourceRef="ceoApproval" targetRef="mergeGateway"></sequenceFlow>
        <sequenceFlow id="flow6" sourceRef="procurementExecution" targetRef="mergeGateway"></sequenceFlow>
        <sequenceFlow id="flow7" sourceRef="mergeGateway" targetRef="endEvent"></sequenceFlow>
    </process>
</definitions>