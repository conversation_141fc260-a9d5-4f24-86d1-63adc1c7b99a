# 📊 表协作关系和无入侵设计详解

## 🎯 无入侵设计核心理念

### 完全无入侵的设计原则
```
现有系统 (完全不变)          新增无密码系统 (扩展)
┌─────────────────┐         ┌─────────────────────┐
│   原有功能      │         │   无密码认证        │
│   ✅ 管理员登录  │         │   📱 手机验证码     │
│   ✅ 员工权限    │    +    │   🔐 企业微信OAuth  │
│   ✅ 客户数据    │         │   🚫 完全无密码     │
│   ✅ 业务流程    │         │   🔗 专属访问链接   │
└─────────────────┘         └─────────────────────┘
```

## 🔄 表协作流程详解

### 1. 客户首次无密码注册流程协作

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端
    participant B as 后端
    participant T1 as crm_user_registration
    participant T2 as sys_user
    participant T3 as crm_user_auth_methods
    participant T4 as crm_customer_leads
    participant SMS as 短信服务
    
    Note over C,SMS: 表协作：客户无密码注册
    
    C->>F: 输入手机号
    F->>B: 请求验证码
    B->>SMS: 发送短信
    B->>T1: 创建注册记录
    Note right of T1: status='pending'<br/>phone_number='138xxxx'<br/>registration_type='sms'
    
    C->>F: 输入验证码
    F->>B: 提交验证码
    B->>T1: 验证码校验
    
    alt 验证成功
        B->>T2: 创建sys_user
        Note right of T2: user_name='user_138xxxx'<br/>password=自动生成(用户不知道)<br/>phonenumber='138xxxx'
        
        T2->>B: 返回user_id
        B->>T1: 更新注册记录
        Note right of T1: user_id=新用户ID<br/>status='completed'<br/>is_verified=true
        
        B->>T3: 创建认证方式
        Note right of T3: user_id=用户ID<br/>auth_type='phone'<br/>auth_identifier='138xxxx'<br/>is_primary=true
        
        B->>T4: 创建客户线索
        Note right of T4: user_id=用户ID<br/>phone_number='138xxxx'<br/>source_type='website'<br/>access_token=专用令牌
        
        B->>F: 返回成功和专区链接
    end
```

### 2. 企业微信员工登录流程协作

```mermaid
sequenceDiagram
    participant E as 员工
    participant F as 前端
    participant B as 后端
    participant W as 企业微信API
    participant T1 as crm_thirdparty_wechat
    participant T2 as sys_user
    participant T3 as crm_user_auth_methods
    participant T4 as crm_user_registration
    
    Note over E,T4: 表协作：企业微信无密码登录
    
    E->>F: 扫码登录
    F->>W: 获取授权
    W->>B: 返回用户信息
    B->>T1: 查询企业微信绑定
    
    alt 已有用户
        T1->>B: 返回user_id
        B->>T2: 查询用户详情
        T2->>B: 返回用户信息
        B->>T3: 更新最后使用时间
        Note right of T3: last_used_time=NOW()
        B->>F: 登录成功
    else 新用户
        B->>T4: 创建注册记录
        Note right of T4: registration_type='wechat'<br/>wechat_union_id='wx123'<br/>status='pending'
        
        B->>T2: 创建sys_user
        Note right of T2: password=自动生成<br/>user_type='employee'
        
        B->>T1: 创建微信绑定
        B->>T3: 创建认证方式
        Note right of T3: auth_type='wechat'<br/>auth_identifier='wx123'
        
        B->>T4: 完成注册
        Note right of T4: status='completed'<br/>user_id=新用户ID
    end
```

### 3. 客户专区访问流程协作

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端
    participant B as 后端
    participant T1 as crm_customer_leads
    participant T2 as crm_lead_files
    participant T3 as crm_lead_communications
    participant T4 as sys_user
    
    Note over C,T4: 表协作：客户专区访问
    
    C->>F: 点击专属链接(含token)
    F->>B: 发送访问令牌
    B->>T1: 验证访问令牌
    
    alt 令牌有效
        T1->>B: 返回线索信息
        Note right of T1: user_id, status, assigned_to<br/>evaluation_result, contact_notes
        
        B->>T2: 查询相关文件
        T2->>B: 返回文件列表
        Note right of T2: file_name, file_url<br/>file_type, file_size
        
        B->>T3: 查询沟通记录
        T3->>B: 返回沟通历史
        Note right of T3: communication_type, content<br/>communication_time, sales_user_id
        
        B->>T4: 查询销售人员信息
        T4->>B: 返回销售联系方式
        Note right of T4: nick_name, phonenumber, email
        
        B->>F: 组装专区数据
        F->>C: 显示客户专区
    else 令牌无效
        B->>F: 要求重新验证
    end
```

## 🛡️ 无入侵密码管理策略

### 自动密码生成机制（用户无感知）

```java
/**
 * 无感知密码生成器
 * 用户永远不知道这个密码，但系统内部需要
 */
public class PasswordlessPasswordGenerator {
    
    public String generateSecurePassword(String phoneNumber) {
        // 生成复杂密码：手机号+时间戳+随机字符串的MD5
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomStr = UUID.randomUUID().toString().substring(0, 8);
        String rawPassword = phoneNumber + timestamp + randomStr;
        
        // 使用BCrypt加密，符合系统安全要求
        return BCrypt.hashpw(rawPassword, BCrypt.gensalt());
    }
    
    public void createUserWithAutoPassword(String phoneNumber, String userType) {
        SysUser user = new SysUser();
        user.setUserName(generateUsername(phoneNumber));
        user.setPhonenumber(phoneNumber);
        
        // 🔑 关键：自动生成安全密码，用户永远不知道
        user.setPassword(generateSecurePassword(phoneNumber));
        
        // 设置用户类型和默认信息
        user.setUserType(userType);
        user.setStatus("0"); // 正常状态
        user.setNickName("客户" + phoneNumber.substring(7));
        
        // 保存到数据库
        userService.insertUser(user);
    }
}
```

### 双重认证机制：密码保留 + 无密码并存

```java
/**
 * 混合认证服务：传统密码 + 无密码并存
 */
@Service
public class HybridAuthService {
    
    /**
     * 🔐 管理员仍然可以使用密码登录（原有功能保持不变）
     */
    public LoginResult adminPasswordLogin(String username, String password) {
        // 走原有的密码验证逻辑，完全不变
        return traditionalLoginService.login(username, password);
    }
    
    /**
     * 📱 前端用户使用无密码登录（新增功能）
     */
    public LoginResult passwordlessLogin(String phoneNumber, String verificationCode) {
        // 1. 验证短信验证码
        if (!smsService.verifyCode(phoneNumber, verificationCode)) {
            return LoginResult.failure("验证码错误");
        }
        
        // 2. 查找用户认证记录
        CrmUserAuthMethods authMethod = authMethodsService.findByPhone(phoneNumber);
        if (authMethod == null) {
            // 新用户，自动创建（含自动生成的密码）
            SysUser newUser = createUserWithAutoPassword(phoneNumber, "customer");
            authMethod = createAuthMethod(newUser.getUserId(), phoneNumber);
        }
        
        // 3. 生成JWT令牌（不包含密码信息）
        String token = jwtService.generatePasswordlessToken(authMethod.getUserId());
        
        // 4. 更新最后使用时间
        authMethod.setLastUsedTime(LocalDateTime.now());
        authMethodsService.updateById(authMethod);
        
        return LoginResult.success(token);
    }
}
```

## 📋 无入侵实施方案

### 阶段1：新表创建（完全独立）

```sql
-- ✅ 完全独立的新表，不影响现有系统
CREATE TABLE `crm_user_registration` (...);
CREATE TABLE `crm_user_auth_methods` (...);
CREATE TABLE `crm_customer_leads` (...);

-- ✅ 只有外键关联到sys_user，但不修改sys_user结构
-- ✅ 原有表结构和数据完全保持不变
```

### 阶段2：服务层扩展（并行开发）

```java
// ✅ 新增认证服务，与现有LoginService并行
@Service
public class PasswordlessAuthService {
    // 无密码认证逻辑
}

// ✅ 现有服务保持不变
@Service 
public class SysLoginService {
    // 原有密码登录逻辑保持完全不变
}
```

### 阶段3：前端路由分离

```typescript
// ✅ 新增无密码登录路由
const passwordlessRoutes = [
  { path: '/passwordless-login', component: PasswordlessLogin },
  { path: '/customer-zone', component: CustomerZone }
];

// ✅ 原有管理后台路由保持不变
const adminRoutes = [
  { path: '/login', component: AdminLogin }, // 仍然使用密码
  { path: '/dashboard', component: Dashboard }
];
```

## 🔄 系统兼容性保证

### 登录入口分离

```
管理后台 (原有) → /admin/login → 密码登录 → 管理系统
客户前台 (新增) → /login → 无密码登录 → CRM功能  
员工企微 (新增) → /wechat/login → 企微扫码 → CRM功能
```

### 权限体系兼容

```java
@PreAuthorize("hasRole('ADMIN')") // ✅ 原有管理员权限
public void adminFunction() {}

@PreAuthorize("hasRole('EMPLOYEE')") // ✅ 新增员工权限
public void employeeFunction() {}

@PreAuthorize("hasRole('CUSTOMER')") // ✅ 新增客户权限  
public void customerFunction() {}
```

## 🔗 表关系图详解

```mermaid
erDiagram
    sys_user {
        bigint user_id PK "原有主键"
        varchar user_name "原有字段"
        varchar password "管理员保留,客户自动生成"
        varchar phonenumber "原有字段"
        varchar email "原有字段"
        datetime create_time "原有字段"
    }
    
    crm_user_registration {
        bigint id PK "新增主键"
        varchar phone_number "手机号"
        varchar registration_type "注册类型"
        varchar verification_code "验证码"
        bigint user_id FK "关联sys_user"
        varchar status "注册状态"
        varchar user_type "用户类型"
        varchar passwordless_token "无密码令牌"
    }
    
    crm_user_auth_methods {
        bigint id PK "新增主键"
        bigint user_id FK "关联sys_user"
        varchar auth_type "认证类型"
        varchar auth_identifier "认证标识"
        tinyint is_primary "是否主要"
        datetime last_used_time "最后使用"
        varchar auth_token "认证令牌"
    }
    
    crm_customer_leads {
        bigint id PK "新增主键"
        bigint user_id FK "关联sys_user"
        varchar phone_number "客户手机"
        varchar source_type "来源类型"
        varchar status "线索状态"
        bigint assigned_to FK "分配销售"
        varchar access_token "专用令牌"
    }
    
    sys_user ||--o{ crm_user_registration : "无入侵外键关联"
    sys_user ||--o{ crm_user_auth_methods : "无入侵外键关联"
    sys_user ||--o{ crm_customer_leads : "无入侵外键关联"
```

## 🛠️ 实施建议

### 1. 渐进式部署
- **第一周**：创建新表，不影响现有功能
- **第二周**：开发无密码认证API，与现有系统并行
- **第三周**：前端页面开发，独立入口
- **第四周**：内测和逐步开放

### 2. 回滚保障
- **新功能完全独立**：随时可以关闭
- **原有系统功能完全不受影响**：管理员仍可正常登录
- **数据库变更可逆**：支持快速回滚

### 3. 监控和观察
- **新旧系统使用情况对比**
- **用户体验反馈收集**
- **系统性能影响评估**

## ✅ 设计确保

这样的设计确保了：

✅ **完全无入侵**：不修改任何现有表和功能  
✅ **密码保留**：管理员仍可正常使用密码登录  
✅ **用户无感**：前端用户完全无密码体验  
✅ **渐进部署**：可以分阶段实施，风险可控  
✅ **完全可逆**：任何时候都可以回到原有状态
