# Activiti工作流引擎模块业务文档

## 一、功能介绍

本模块集成了 Activiti 工作流引擎，提供了对业务流程（如请假、报销、审批等）进行定义、部署、执行和监控的核心API。通过这些接口，开发者可以快速地将复杂的业务流程自动化，实现流程的标准化和效率提升。

---

## 二、API

| 接口名称 | 路径 | 方法 | 描述 | 主要参数 | 返回值说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 部署流程定义 | `/crm/activiti/deployments` | POST | 部署一个新的流程定义文件（.bpmn20.xml） | `file` (multipart/form-data) | 部署信息 |
| 查询流程部署 | `/crm/activiti/deployments` | GET | 分页查询所有已部署的流程 | `page`, `size` | 部署列表 |
| 删除流程部署 | `/crm/activiti/deployments/{deploymentId}` | DELETE | 删除一个已部署的流程（级联删除） | `deploymentId` (路径变量) | 成功或失败信息 |
| 启动流程实例 | `/crm/activiti/process-instances/start/{processDefinitionKey}` | POST | 根据流程定义Key启动一个流程实例 | `processDefinitionKey` (路径变量), `variables` (请求体, Map) | 启动的流程实例信息 |
| 查询用户待办任务 | `/crm/activiti/tasks` | GET | 查询指定用户的待办任务列表 | `assignee` (请求参数) | 任务列表 |
| 完成任务 | `/crm/activiti/tasks/{taskId}/complete` | POST | 完成一个任务，并可以传递流程变量 | `taskId` (路径变量), `variables` (请求体, Map) | 成功或失败信息 |
| 查询流程实例历史任务 | `/crm/activiti/history/tasks` | GET | 查询指定流程实例的历史任务 | `processInstanceId` (请求参数) | 历史任务列表 |
| 获取流程图 | `/crm/activiti/process-diagram/{processInstanceId}` | GET | 获取正在运行或已结束的流程实例的图表 | `processInstanceId` (路径变量) | 图片流 (image/png) |
| 挂起流程实例 | `/crm/activiti/process-instances/{processInstanceId}/suspend` | PUT | 挂起一个流程实例 | `processInstanceId` (路径变量) | 成功或失败信息 |
| 激活流程实例 | `/crm/activiti/process-instances/{processInstanceId}/activate` | PUT | 激活一个流程实例 | `processInstanceId` (路径变量) | 成功或失败信息 |

---

## 三、流程图

以一个简单的请假流程为例，展示核心操作流程。

```mermaid
flowchart TD
    A[用户上传 a leave.bpmn20.xml] --> B{调用 “部署流程定义” API};
    B --> C[流程部署成功];
    C --> D{用户调用 “启动流程实例” API};
    D --> E[流程实例已启动, 等待审批];
    E --> F{审批人调用 “查询用户待办任务” API};
    F --> G[获取到待办任务];
    G --> H{审批人调用 “完成任务” API};
    H --> I[任务完成, 流程结束];
```

---

## 四、结构图

```mermaid
graph TD
    subgraph "用户/客户端"
        User([外部调用方])
    end

    subgraph "应用层"
        ActivitiController[ActivitiController]
    end

    subgraph "服务层 (Activiti Engine)"
        RepositoryService
        RuntimeService
        TaskService
        HistoryService
        ManagementService
    end

    subgraph "数据层"
        DB[(Activiti 数据库表)]
    end

    User --> ActivitiController
    ActivitiController --> RepositoryService
    ActivitiController --> RuntimeService
    ActivitiController --> TaskService
    ActivitiController --> HistoryService

    RepositoryService --> DB
    RuntimeService --> DB
    TaskService --> DB
    HistoryService --> DB
```

---

## 五、实体图 (主要表)

```mermaid
erDiagram
    ACT_RE_DEPLOYMENT {
        string ID_ PK
        string NAME_
        datetime DEPLOY_TIME_
    }
    ACT_RE_PROCDEF {
        string ID_ PK
        string KEY_
        int VERSION_
        string DEPLOYMENT_ID_ FK
    }
    ACT_RU_EXECUTION {
        string ID_ PK
        string PROC_INST_ID_
        string PROC_DEF_ID_ FK
    }
    ACT_RU_TASK {
        string ID_ PK
        string PROC_INST_ID_
        string PROC_DEF_ID_ FK
        string NAME_
        string ASSIGNEE_
    }
    ACT_HI_PROCINST {
        string ID_ PK
        string PROC_INST_ID_
        string PROC_DEF_ID_ FK
        datetime START_TIME_
        datetime END_TIME_
    }

    ACT_RE_DEPLOYMENT ||--|{ ACT_RE_PROCDEF : "包含"
    ACT_RE_PROCDEF ||--|{ ACT_RU_EXECUTION : "启动"
    ACT_RE_PROCDEF ||--|{ ACT_RU_TASK : "定义"
    ACT_RE_PROCDEF ||--|{ ACT_HI_PROCINST : "定义"
    ACT_RU_EXECUTION }|--|| ACT_RU_TASK : "关联"
    ACT_RU_EXECUTION }|--|| ACT_HI_PROCINST : "记录"

```

---

## 六、其他说明

- **安全**: 所有接口应进行权限校验，确保只有授权用户才能操作工作流。
- **事务**: 核心操作（如启动、完成任务）是事务性的，Activiti引擎已内置支持。
- **BPMN规范**: 流程定义文件需遵循 BPMN 2.0 规范。 