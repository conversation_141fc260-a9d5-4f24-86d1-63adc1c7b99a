# 菜单后端集成 TODO

## 当前状态
- ✅ 已屏蔽后端菜单请求
- ✅ 使用硬编码菜单数据进行开发
- ✅ 补全了所有router中的菜单模块

## 需要完成的任务

### 1. 恢复后端菜单请求
**文件位置**: `frontend/src/components/layouts/BaseHeader.vue`

**修改步骤**:
1. 在 `loadMenuItems` 函数中，取消注释后端请求代码
2. 删除或注释掉硬编码菜单逻辑
3. 恢复 `visibleMenuItems` 计算属性中的 `parentId !== 0` 过滤条件

**具体代码修改**:
```typescript
// 删除这部分硬编码逻辑
// 暂时屏蔽后端请求，直接使用硬编码菜单
console.log('使用硬编码菜单数据，跳过后端请求');
menuItems.value = defaultMenuItems;
console.log('处理后的可见菜单项:', visibleMenuItems.value);
isMenuLoading.value = false;

// 取消注释这部分后端请求代码
try {
  const { data } = await getHeaderMenu();
  // ... 其余后端请求逻辑
} catch (error) {
  // ... 错误处理
}
```

### 2. 后端菜单数据准备
**需要确保后端提供的菜单数据包含以下模块**:
- 客户管理 (`/crm/customer`)
- 联系人管理 (`/crm/contact`)
- 关联管理 (`/crm/association`)
- 合同管理 (`/crm/contract`)
- 付款管理 (`/crm/payment`)
- 商机管理 (`/crm/opportunity`)
- 报价单管理 (`/crm/quotation`)
- 发票管理 (`/crm/invoice`)
- 团队管理 (`/crm/team`)
- 3D打印报价 (`/3d-printing/quote`)
- 模型管理 (`/3d-printing/model`)
- 订单导出 (`/3d-printing/export`)

### 3. 后端API接口
**接口地址**: `/crm/menu/header`
**方法**: GET
**返回格式**: 需要返回符合 `MenuItem` 接口的数组

**MenuItem 接口定义**:
```typescript
interface MenuItem {
  menuId: number;
  menuName: string;
  parentId: number;
  orderNum: number;
  path: string;
  component: string;
  query: string;
  isFrame: string;
  isCache: string;
  menuType: string;
  visible: string;
  status: string;
  perms: string;
  icon: string;
  children: MenuItem[];
  enabled: string;
}
```

### 4. 测试清单
- [ ] 后端菜单API正常返回数据
- [ ] 菜单项正确显示在页面头部
- [ ] 菜单点击跳转功能正常
- [ ] 菜单权限控制正常
- [ ] 菜单图标显示正常

### 5. 注意事项
- 后端返回的菜单数据中，`parentId !== 0` 的才会显示在头部菜单
- 确保菜单的 `visible` 和 `status` 字段为 `'0'` 才会显示
- 菜单类型 `menuType` 需要为 `'C'` (菜单类型)
- 图标名称需要与Element Plus图标库匹配

---

**创建时间**: 2024年12月19日  
**状态**: 待完成  
**优先级**: 高  
**预计完成时间**: 数据库配置完成后立即处理