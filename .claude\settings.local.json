{"permissions": {"allow": ["<PERSON><PERSON>(mvn test:*)", "<PERSON><PERSON>(mvn clean:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(mvn compile:*)", "<PERSON><PERSON>(javac:*)", "<PERSON><PERSON>(mvn:*)", "<PERSON><PERSON>(mysql:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(Stop-Process -Id 7304 -Force)", "<PERSON><PERSON>(powershell:*)", "Bash(git restore ruoyi-admin/src/main/resources/application.yml)", "Bash(cd \"D:\\work\\CRM412024-08-16\\CRM4\")", "Bash(git checkout -- frontend/src/views/ThreeDPrintingQuote/index.vue)", "Bash(cd \"D:\\work\\CRM412024-08-16\\CRM4\\frontend\")", "Bash(npm install:*)", "Bash(del \"D:\\work\\CRM412024-08-16\\CRM4\\ruoyi-crm\\src\\main\\java\\com\\ruoyi\\common\\domain\\CrmPaymentPlan.java\")", "<PERSON><PERSON>(mv:*)"], "deny": []}}