<!DOCTYPE html>
<html lang="zh-C<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拜访计划功能详细实施方案</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; max-width: 960px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3, h4, h5, h6 { color: #111; font-weight: 600; margin-top: 2.4em; margin-bottom: 1em; }
        h1 { font-size: 2.2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em;}
        h2 { font-size: 1.75em; border-bottom: 1px solid #eee; padding-bottom: 0.3em;}
        h3 { font-size: 1.4em; }
        h4 { font-size: 1.2em; }
        code, pre { font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace; }
        pre { background-color: #f6f8fa; border-radius: 3px; padding: 16px; overflow: auto; }
        code { background-color: rgba(27,31,35,.05); border-radius: 3px; padding: .2em .4em; }
        pre > code { background-color: transparent; padding: 0; }
        table { border-collapse: collapse; width: 100%; margin-top: 1em; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        ul { padding-left: 20px; }
        li { margin-bottom: 0.5em; }
        .mermaid { text-align: center; margin-bottom: 1em; }
        strong { font-weight: 600; }
    </style>
</head>
<body>
    <h1>拜访计划功能详细实施方案</h1>
    <h2>1. 项目概述</h2>
    <h3>1.1 功能背景</h3>
    <p>拜访计划是CRM系统中的核心功能之一，帮助销售人员系统化管理客户拜访活动。通过制定拜访计划，销售团队可以：</p>
    <ul>
        <li>提前规划客户拜访行程</li>
        <li>设置提醒避免遗漏重要拜访</li>
        <li>跟踪拜访执行情况</li>
        <li>记录拜访结果和后续行动</li>
    </ul>
    <h3>1.2 功能定位</h3>
    <ul>
        <li><strong>展现形式</strong>：作为联系人、客户、商机详情页的独立Tab页签</li>
        <li><strong>使用场景</strong>：销售人员在查看客户信息时，可直接创建和管理拜访计划</li>
        <li><strong>核心价值</strong>：提升销售效率，确保重要客户拜访不遗漏</li>
    </ul>
    <h3>1.3 技术架构</h3>
    <ul>
        <li><strong>前端</strong>：Vue 3 + TypeScript + Element Plus</li>
        <li><strong>后端</strong>：Spring Boot + MyBatis</li>
        <li><strong>数据库</strong>：MySQL</li>
        <li><strong>定时任务</strong>：Quartz</li>
        <li><strong>通知渠道</strong>：预留企业微信、短信接口</li>
    </ul>
    <h2>2. 功能需求分析</h2>
    <h3>2.1 业务流程图</h3>
    <div class="mermaid">
stateDiagram-v2
    [*] --> 计划中: 创建拜访计划
    计划中 --> 进行中: 到达计划时间
    计划中 --> 已延期: 延期操作
    计划中 --> 已取消: 取消操作
    已延期 --> 计划中: 重新安排
    进行中 --> 已完成: 完成拜访
    进行中 --> 已延期: 临时延期
    进行中 --> 已取消: 临时取消
    已完成 --> [*]: 结束
    已取消 --> [*]: 结束
    </div>
    <h3>2.2 功能清单</h3>
    <h4>2.2.1 拜访计划管理</h4>
    <ul>
        <li>✅ 创建拜访计划</li>
        <li>✅ 查看拜访计划列表</li>
        <li>✅ 编辑拜访计划（仅计划中状态）</li>
        <li>✅ 删除拜访计划（仅计划中状态）</li>
        <li>✅ 延期拜访计划（需填写延期原因）</li>
        <li>✅ 取消拜访计划（需填写取消原因）</li>
        <li>✅ 完成拜访计划（添加跟进记录）</li>
    </ul>
    <h4>2.2.2 提醒功能</h4>
    <ul>
        <li>✅ 自定义提前提醒时间</li>
        <li>✅ 系统内消息提醒</li>
        <li>🔲 企业微信提醒（预留接口）</li>
        <li>🔲 短信提醒（预留接口）</li>
        <li>🔲 邮件提醒（预留接口）</li>
    </ul>
    <h4>2.2.3 数据统计</h4>
    <ul>
        <li>✅ 拜访计划执行率统计</li>
        <li>✅ 延期/取消原因分析</li>
        <li>✅ 拜访效果跟踪</li>
    </ul>
    <h3>2.3 字段设计</h3>
    <table>
        <thead>
            <tr>
                <th>字段名</th>
                <th>字段标识</th>
                <th>数据类型</th>
                <th>是否必填</th>
                <th>说明</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>拜访计划名称</td>
                <td>visit_plan_name</td>
                <td>VARCHAR(200)</td>
                <td>是</td>
                <td>计划的标题或主题</td>
            </tr>
            <tr>
                <td>预计拜访时间</td>
                <td>visit_time</td>
                <td>DATETIME</td>
                <td>是</td>
                <td>计划的拜访时间</td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>customer_id</td>
                <td>BIGINT</td>
                <td>是</td>
                <td>关联客户ID</td>
            </tr>
            <tr>
                <td>联系人</td>
                <td>contact_id</td>
                <td>BIGINT</td>
                <td>否</td>
                <td>关联联系人ID</td>
            </tr>
            <tr>
                <td>商机名称</td>
                <td>opportunity_id</td>
                <td>BIGINT</td>
                <td>否</td>
                <td>关联商机ID</td>
            </tr>
            <tr>
                <td>拜访目的</td>
                <td>visit_purpose</td>
                <td>TEXT</td>
                <td>是</td>
                <td>详细描述拜访目的</td>
            </tr>
            <tr>
                <td>提前提醒时间</td>
                <td>remind_time</td>
                <td>INT</td>
                <td>是</td>
                <td>提前多少分钟提醒，默认30</td>
            </tr>
            <tr>
                <td>备注</td>
                <td>remark</td>
                <td>TEXT</td>
                <td>否</td>
                <td>其他补充信息</td>
            </tr>
            <tr>
                <td>延期原因</td>
                <td>postpone_reason</td>
                <td>VARCHAR(500)</td>
                <td>否</td>
                <td>延期时必填</td>
            </tr>
            <tr>
                <td>延期备注</td>
                <td>postpone_remark</td>
                <td>TEXT</td>
                <td>否</td>
                <td>延期的详细说明</td>
            </tr>
            <tr>
                <td>取消原因</td>
                <td>cancel_reason</td>
                <td>VARCHAR(500)</td>
                <td>否</td>
                <td>取消时必填</td>
            </tr>
            <tr>
                <td>取消备注</td>
                <td>cancel_remark</td>
                <td>TEXT</td>
                <td>否</td>
                <td>取消的详细说明</td>
            </tr>
            <tr>
                <td>跟进记录内容</td>
                <td>followup_content</td>
                <td>TEXT</td>
                <td>否</td>
                <td>完成拜访后的记录</td>
            </tr>
            <tr>
                <td>负责人</td>
                <td>owner_id</td>
                <td>BIGINT</td>
                <td>是</td>
                <td>负责人用户ID</td>
            </tr>
            <tr>
                <td>状态</td>
                <td>status</td>
                <td>VARCHAR(20)</td>
                <td>是</td>
                <td>planned/ongoing/completed/postponed/cancelled</td>
            </tr>
            <tr>
                <td>所属部门</td>
                <td>dept_id</td>
                <td>BIGINT</td>
                <td>是</td>
                <td>负责人所属部门</td>
            </tr>
            <tr>
                <td>更新时间</td>
                <td>update_time</td>
                <td>DATETIME</td>
                <td>是</td>
                <td>最后更新时间</td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td>create_time</td>
                <td>DATETIME</td>
                <td>是</td>
                <td>创建时间</td>
            </tr>
            <tr>
                <td>创建人</td>
                <td>create_by</td>
                <td>VARCHAR(64)</td>
                <td>是</td>
                <td>创建人用户名</td>
            </tr>
        </tbody>
    </table>
    <h2>3. 数据库设计</h2>
    <h3>3.1 主表结构</h3>
    <pre><code class="language-sql">CREATE TABLE `crm_visit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_name` varchar(200) NOT NULL COMMENT '拜访计划名称',
  `visit_time` datetime NOT NULL COMMENT '预计拜访时间',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称（冗余）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余）',
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '商机ID',
  `opportunity_name` varchar(200) DEFAULT NULL COMMENT '商机名称（冗余）',
  `visit_purpose` text COMMENT '拜访目的',
  `remind_time` int(11) DEFAULT '30' COMMENT '提前提醒时间（分钟）',
  `remark` text COMMENT '备注',
  `postpone_reason` varchar(500) DEFAULT NULL COMMENT '延期原因',
  `postpone_remark` text COMMENT '延期备注',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
  `cancel_remark` text COMMENT '取消备注',
  `followup_content` text COMMENT '跟进记录内容',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `status` varchar(20) NOT NULL DEFAULT 'planned' COMMENT '状态：planned-计划中,ongoing-进行中,completed-已完成,postponed-已延期,cancelled-已取消',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称（冗余）',
  `actual_visit_time` datetime DEFAULT NULL COMMENT '实际拜访时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划表';
</code></pre>
    <h3>3.2 提醒记录表</h3>
    <pre><code class="language-sql">CREATE TABLE `crm_visit_plan_reminders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `remind_type` varchar(20) NOT NULL COMMENT '提醒类型：system-系统内,wechat-企业微信,sms-短信,email-邮件',
  `remind_time` datetime NOT NULL COMMENT '提醒时间',
  `remind_status` varchar(20) DEFAULT 'pending' COMMENT '提醒状态：pending-待发送,sent-已发送,failed-发送失败',
  `send_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_remind_time` (`remind_time`),
  KEY `idx_remind_status` (`remind_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划提醒记录表';
</code></pre>
    <h3>3.3 状态变更日志表</h3>
    <pre><code class="language-sql">CREATE TABLE `crm_visit_plan_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '变更原因',
  `change_remark` text COMMENT '变更备注',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划状态变更日志表';
</code></pre>
    <h3>3.4 ER图</h3>
    <div class="mermaid">
---
title: CRM拜访计划ER图
---
erDiagram
    CRM_VISIT_PLAN ||--o{ CRM_VISIT_PLAN_REMINDER : has
    CRM_VISIT_PLAN ||--o{ CRM_VISIT_PLAN_LOG : has
    
    CRM_VISIT_PLAN {
        bigint id PK "主键ID"
        varchar(200) visit_plan_name "拜访计划名称"
        datetime visit_time "预计拜访时间"
        bigint customer_id FK "客户ID"
        varchar(200) customer_name "客户名称（冗余）"
        bigint contact_id FK "联系人ID"
        varchar(100) contact_name "联系人姓名（冗余）"
        bigint opportunity_id FK "商机ID"
        varchar(200) opportunity_name "商机名称（冗余）"
        text visit_purpose "拜访目的"
        int remind_time "提前提醒时间（分钟）"
        text remark "备注"
        varchar(500) postpone_reason "延期原因"
        text postpone_remark "延期备注"
        varchar(500) cancel_reason "取消原因"
        text cancel_remark "取消备注"
        text followup_content "跟进记录内容"
        bigint owner_id FK "负责人ID"
        varchar(100) owner_name "负责人姓名（冗余）"
        varchar(20) status "状态"
        bigint dept_id FK "所属部门ID"
        varchar(100) dept_name "部门名称（冗余）"
        datetime actual_visit_time "实际拜访时间"
        datetime complete_time "完成时间"
        char(1) del_flag "删除标志"
        varchar(64) create_by "创建者"
        datetime create_time "创建时间"
        varchar(64) update_by "更新者"
        datetime update_time "更新时间"
    }
    CRM_VISIT_PLAN_REMINDER {
        bigint id PK "主键ID"
        bigint visit_plan_id FK "拜访计划ID"
        varchar(20) remind_type "提醒类型"
        datetime remind_time "提醒时间"
        varchar(20) remind_status "提醒状态"
        datetime send_time "实际发送时间"
        varchar(500) error_msg "错误信息"
        datetime create_time "创建时间"
    }
    CRM_VISIT_PLAN_LOG {
        bigint id PK "主键ID"
        bigint visit_plan_id FK "拜访计划ID"
        varchar(20) from_status "原状态"
        varchar(20) to_status "新状态"
        varchar(500) change_reason "变更原因"
        text change_remark "变更备注"
        bigint operator_id FK "操作人ID"
        varchar(100) operator_name "操作人姓名"
        datetime create_time "创建时间"
    }
    CUSTOMER {
        bigint id PK
        varchar(200) name
    }
    CONTACT {
        bigint id PK
        varchar(100) name
    }
    OPPORTUNITY {
        bigint id PK
        varchar(200) name
    }
    DEPARTMENT {
        bigint id PK
        varchar(100) name
    }
    USER {
        bigint id PK
        varchar(100) username
    }
    
    CRM_VISIT_PLAN }o--|| CUSTOMER : "属于"
    CRM_VISIT_PLAN }o--|| CONTACT : "联系人"
    CRM_VISIT_PLAN }o--|| OPPORTUNITY : "商机"
    CRM_VISIT_PLAN }o--|| DEPARTMENT : "部门"
    CRM_VISIT_PLAN }o--|| USER : "负责人"
    CRM_VISIT_PLAN_LOG }o--|| USER : "操作人"
    </div>
    <h2>4. 后端开发</h2>
    
    <h3>4.0 架构设计（方案一：独立Service和Mapper）</h3>
    <h4>4.0.1 实体类设计</h4>
    <ul>
        <li><code>CrmVisitPlan.java</code> - 拜访计划主实体</li>
        <li><code>CrmVisitPlanReminder.java</code> - 提醒记录实体</li>
        <li><code>CrmVisitPlanLog.java</code> - 状态变更日志实体</li>
    </ul>

    <h4>4.0.2 Mapper层开发</h4>
    <ul>
        <li><code>CrmVisitPlanMapper.java</code> + <code>CrmVisitPlanMapper.xml</code> - 主表操作</li>
        <li><code>CrmVisitPlanReminderMapper.java</code> + <code>CrmVisitPlanReminderMapper.xml</code> - 提醒表操作</li>
        <li><code>CrmVisitPlanLogMapper.java</code> + <code>CrmVisitPlanLogMapper.xml</code> - 日志表操作</li>
    </ul>

    <h4>4.0.3 Service层开发</h4>
    <ul>
        <li><code>ICrmVisitPlanService.java</code> + <code>CrmVisitPlanServiceImpl.java</code> - 主业务逻辑</li>
        <li><code>ICrmVisitPlanReminderService.java</code> + <code>CrmVisitPlanReminderServiceImpl.java</code> - 提醒业务</li>
        <li><code>ICrmVisitPlanLogService.java</code> + <code>CrmVisitPlanLogServiceImpl.java</code> - 日志业务</li>
    </ul>

    <h4>4.0.4 Controller层开发</h4>
    <ul>
        <li><code>CrmVisitPlanController.java</code> - 主要的REST API</li>
        <li><code>CrmVisitPlanReminderController.java</code> - 提醒相关API（如果需要独立管理）</li>
        <li><code>CrmVisitPlanLogController.java</code> - 日志查询API（如果需要独立查询）</li>
    </ul>

    <h4>4.0.5 定时任务开发</h4>
    <ul>
        <li><code>VisitPlanReminderJob.java</code> - 提醒处理定时任务</li>
        <li><code>VisitPlanStatusJob.java</code> - 状态自动更新定时任务</li>
    </ul>

    <p><strong>设计优势：</strong></p>
    <ul>
        <li>职责分离，每个Service专注于自己的业务</li>
        <li>便于单独测试和维护</li>
        <li>符合单一职责原则</li>
        <li>便于后续扩展</li>
    </ul>

    <h3>4.1 实体类设计</h3>
    <pre><code class="language-java">// CrmVisitPlan.java
package com.ruoyi.crm.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

public class CrmVisitPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;
    
    private Long id;
    
    @Excel(name = "拜访计划名称")
    private String visitPlanName;
    
    @Excel(name = "预计拜访时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitTime;
    
    @Excel(name = "客户ID")
    private Long customerId;
    
    @Excel(name = "客户名称")
    private String customerName;
    
    private Long contactId;
    
    @Excel(name = "联系人姓名")
    private String contactName;
    
    private Long opportunityId;
    
    @Excel(name = "商机名称")
    private String opportunityName;
    
    @Excel(name = "拜访目的")
    private String visitPurpose;
    
    @Excel(name = "提前提醒时间(分钟)")
    private Integer remindTime;
    
    @Excel(name = "备注")
    private String remark;
    
    private String postponeReason;
    private String postponeRemark;
    private String cancelReason;
    private String cancelRemark;
    
    @Excel(name = "跟进记录内容")
    private String followupContent;
    
    @Excel(name = "负责人ID")
    private Long ownerId;
    
    @Excel(name = "负责人姓名")
    private String ownerName;
    
    @Excel(name = "状态", dictType = "visit_plan_status")
    private String status;
    
    private Long deptId;
    
    @Excel(name = "部门名称")
    private String deptName;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualVisitTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    
    // getter/setter methods...
}
</code></pre>
    <h3>4.2 Mapper接口</h3>
    <pre><code class="language-java">// CrmVisitPlanMapper.java
package com.ruoyi.crm.mapper;

import java.util.List;
import com.ruoyi.crm.domain.CrmVisitPlan;
import org.apache.ibatis.annotations.Param;

public interface CrmVisitPlanMapper {
    
    /**
     * 查询拜访计划
     */
    CrmVisitPlan selectCrmVisitPlanById(Long id);
    
    /**
     * 查询拜访计划列表
     */
    List<CrmVisitPlan> selectCrmVisitPlanList(CrmVisitPlan crmVisitPlan);
    
    /**
     * 根据客户ID查询拜访计划
     */
    List<CrmVisitPlan> selectByCustomerId(@Param("customerId") Long customerId);
    
    /**
     * 根据联系人ID查询拜访计划
     */
    List<CrmVisitPlan> selectByContactId(@Param("contactId") Long contactId);
    
    /**
     * 根据商机ID查询拜访计划
     */
    List<CrmVisitPlan> selectByOpportunityId(@Param("opportunityId") Long opportunityId);
    
    /**
     * 查询即将到期的拜访计划（用于提醒）
     */
    List<CrmVisitPlan> selectUpcomingPlans(@Param("minutes") Integer minutes);
    
    /**
     * 新增拜访计划
     */
    int insertCrmVisitPlan(CrmVisitPlan crmVisitPlan);
    
    /**
     * 修改拜访计划
     */
    int updateCrmVisitPlan(CrmVisitPlan crmVisitPlan);
    
    /**
     * 删除拜访计划
     */
    int deleteCrmVisitPlanById(Long id);
    
    /**
     * 批量删除拜访计划
     */
    int deleteCrmVisitPlanByIds(Long[] ids);
    
    /**
     * 更新拜访计划状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);
}
</code></pre>
    <h3>4.3 Service层</h3>
    <pre><code class="language-java">// ICrmVisitPlanService.java
package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.crm.domain.CrmVisitPlan;
import com.ruoyi.crm.domain.vo.VisitPlanStatisticsVO;

public interface ICrmVisitPlanService {
    
    /**
     * 查询拜访计划
     */
    CrmVisitPlan selectCrmVisitPlanById(Long id);
    
    /**
     * 查询拜访计划列表
     */
    List<CrmVisitPlan> selectCrmVisitPlanList(CrmVisitPlan crmVisitPlan);
    
    /**
     * 根据关联对象查询拜访计划
     */
    List<CrmVisitPlan> selectByRelatedObject(String objectType, Long objectId);
    
    /**
     * 新增拜访计划
     */
    int insertCrmVisitPlan(CrmVisitPlan crmVisitPlan);
    
    /**
     * 修改拜访计划
     */
    int updateCrmVisitPlan(CrmVisitPlan crmVisitPlan);
    
    /**
     * 批量删除拜访计划
     */
    int deleteCrmVisitPlanByIds(Long[] ids);
    
    /**
     * 延期拜访计划
     */
    int postponeVisitPlan(Long id, String reason, String remark, Date newVisitTime);
    
    /**
     * 取消拜访计划
     */
    int cancelVisitPlan(Long id, String reason, String remark);
    
    /**
     * 完成拜访计划
     */
    int completeVisitPlan(Long id, String followupContent);
    
    /**
     * 获取拜访计划统计信息
     */
    VisitPlanStatisticsVO getStatistics(Long userId, String dateRange);
    
    /**
     * 处理即将到期的提醒
     */
    void processUpcomingReminders();
}
</code></pre>
    <h3>4.4 Controller层</h3>
    <pre><code class="language-java">// CrmVisitPlanController.java
package com.ruoyi.crm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.crm.domain.CrmVisitPlan;
import com.ruoyi.crm.service.ICrmVisitPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

@RestController
@RequestMapping("/crm/visitPlan")
public class CrmVisitPlanController extends BaseController {
    
    @Autowired
    private ICrmVisitPlanService crmVisitPlanService;
    
    /**
     * 查询拜访计划列表
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmVisitPlan crmVisitPlan) {
        startPage();
        List<CrmVisitPlan> list = crmVisitPlanService.selectCrmVisitPlanList(crmVisitPlan);
        return getDataTable(list);
    }
    
    /**
     * 根据关联对象查询拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:query')")
    @GetMapping("/listByObject")
    public AjaxResult listByObject(@RequestParam String objectType, @RequestParam Long objectId) {
        List<CrmVisitPlan> list = crmVisitPlanService.selectByRelatedObject(objectType, objectId);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取拜访计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(crmVisitPlanService.selectCrmVisitPlanById(id));
    }
    
    /**
     * 新增拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:add')")
    @Log(title = "拜访计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmVisitPlan crmVisitPlan) {
        crmVisitPlan.setOwnerId(getUserId());
        crmVisitPlan.setOwnerName(getUsername());
        crmVisitPlan.setDeptId(getDeptId());
        return toAjax(crmVisitPlanService.insertCrmVisitPlan(crmVisitPlan));
    }
    
    /**
     * 修改拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:edit')")
    @Log(title = "拜访计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmVisitPlan crmVisitPlan) {
        return toAjax(crmVisitPlanService.updateCrmVisitPlan(crmVisitPlan));
    }
    
    /**
     * 删除拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:remove')")
    @Log(title = "拜访计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmVisitPlanService.deleteCrmVisitPlanByIds(ids));
    }
    
    /**
     * 延期拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:postpone')")
    @Log(title = "拜访计划延期", businessType = BusinessType.UPDATE)
    @PostMapping("/postpone/{id}")
    public AjaxResult postpone(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        String reason = (String) params.get("reason");
        String remark = (String) params.get("remark");
        Date newVisitTime = DateUtils.parseDate(params.get("newVisitTime"));
        return toAjax(crmVisitPlanService.postponeVisitPlan(id, reason, remark, newVisitTime));
    }
    
    /**
     * 取消拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:cancel')")
    @Log(title = "拜访计划取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        String reason = (String) params.get("reason");
        String remark = (String) params.get("remark");
        return toAjax(crmVisitPlanService.cancelVisitPlan(id, reason, remark));
    }
    
    /**
     * 完成拜访计划
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:complete')")
    @Log(title = "拜访计划完成", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{id}")
    public AjaxResult complete(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        String followupContent = (String) params.get("followupContent");
        return toAjax(crmVisitPlanService.completeVisitPlan(id, followupContent));
    }
    
    /**
     * 获取统计信息
     */
    @PreAuthorize("@ss.hasPermi('crm:visitPlan:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(required = false) String dateRange) {
        return AjaxResult.success(crmVisitPlanService.getStatistics(getUserId(), dateRange));
    }
}
</code></pre>
    <h2>5. 前端开发</h2>
    <h3>5.1 类型定义</h3>
    <pre><code class="language-typescript">// types/visit-plan.ts
export interface VisitPlan {
  id?: number;
  visitPlanName: string;
  visitTime: string | Date;
  customerId: number;
  customerName?: string;
  contactId?: number;
  contactName?: string;
  opportunityId?: number;
  opportunityName?: string;
  visitPurpose: string;
  remindTime: number;
  remark?: string;
  postponeReason?: string;
  postponeRemark?: string;
  cancelReason?: string;
  cancelRemark?: string;
  followupContent?: string;
  ownerId: number;
  ownerName?: string;
  status: VisitPlanStatus;
  deptId?: number;
  deptName?: string;
  actualVisitTime?: string | Date;
  completeTime?: string | Date;
  createTime?: string;
  updateTime?: string;
}

export type VisitPlanStatus = 'planned' | 'ongoing' | 'completed' | 'postponed' | 'cancelled';

export interface VisitPlanStatistics {
  total: number;
  planned: number;
  completed: number;
  postponed: number;
  cancelled: number;
  completionRate: number;
  onTimeRate: number;
}

export const VISIT_STATUS_MAP = {
  planned: { label: '计划中', color: 'primary' },
  ongoing: { label: '进行中', color: 'warning' },
  completed: { label: '已完成', color: 'success' },
  postponed: { label: '已延期', color: 'info' },
  cancelled: { label: '已取消', color: 'danger' }
};

export const REMIND_TIME_OPTIONS = [
  { label: '提前15分钟', value: 15 },
  { label: '提前30分钟', value: 30 },
  { label: '提前1小时', value: 60 },
  { label: '提前2小时', value: 120 },
  { label: '提前1天', value: 1440 },
];
</code></pre>
    <h3>5.2 API接口</h3>
    <pre><code class="language-typescript">// api/visit-plan.ts
import request from '@/utils/request';
import type { VisitPlan, VisitPlanStatistics } from '@/types/visit-plan';

// 查询拜访计划列表
export function getVisitPlanList(params: any) {
  return request({
    url: '/crm/visitPlan/list',
    method: 'get',
    params
  });
}

// 根据关联对象查询拜访计划
export function getVisitPlanByObject(objectType: string, objectId: number) {
  return request({
    url: '/crm/visitPlan/listByObject',
    method: 'get',
    params: { objectType, objectId }
  });
}

// 查询拜访计划详情
export function getVisitPlan(id: number) {
  return request({
    url: `/crm/visitPlan/${id}`,
    method: 'get'
  });
}

// 新增拜访计划
export function addVisitPlan(data: VisitPlan) {
  return request({
    url: '/crm/visitPlan',
    method: 'post',
    data
  });
}

// 修改拜访计划
export function updateVisitPlan(data: VisitPlan) {
  return request({
    url: '/crm/visitPlan',
    method: 'put',
    data
  });
}

// 删除拜访计划
export function deleteVisitPlan(id: number | number[]) {
  return request({
    url: `/crm/visitPlan/${id}`,
    method: 'delete'
  });
}

// 延期拜访计划
export function postponeVisitPlan(id: number, data: {
  reason: string;
  remark?: string;
  newVisitTime: string;
}) {
  return request({
    url: `/crm/visitPlan/postpone/${id}`,
    method: 'post',
    data
  });
}

// 取消拜访计划
export function cancelVisitPlan(id: number, data: {
  reason: string;
  remark?: string;
}) {
  return request({
    url: `/crm/visitPlan/cancel/${id}`,
    method: 'post',
    data
  });
}

// 完成拜访计划
export function completeVisitPlan(id: number, data: {
  followupContent: string;
}) {
  return request({
    url: `/crm/visitPlan/complete/${id}`,
    method: 'post',
    data
  });
}

// 获取统计信息
export function getVisitPlanStatistics(dateRange?: string): Promise<VisitPlanStatistics> {
  return request({
    url: '/crm/visitPlan/statistics',
    method: 'get',
    params: { dateRange }
  });
}
</code></pre>
    <h3>5.3 Tab组件实现</h3>
    <pre><code class="language-vue"><!-- ContactVisitPlanTab.vue -->
<template>
  <div class="visit-plan-tab">
    <!-- 统计卡片 -->
    <div class="statistics-row">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总计划数" :value="statistics.total" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="statistics.completed" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="完成率" :value="statistics.completionRate" suffix="%" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="准时率" :value="statistics.onTimeRate" suffix="%" />
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新建拜访计划
      </el-button>
      <div class="toolbar-right">
        <el-radio-group v-model="filterStatus" @change="loadVisitPlans">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="planned">计划中</el-radio-button>
          <el-radio-button label="ongoing">进行中</el-radio-button>
          <el-radio-button label="completed">已完成</el-radio按钮>
        </el-radio-group>
      </div>
    </div>

    <!-- 拜访计划列表 -->
    <el-table
      v-loading="loading"
      :data="visitPlanList"
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="visitPlanName" label="计划名称" min-width="200" />
      <el-table-column prop="visitTime" label="拜访时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.visitTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户" width="150" />
      <el-table-column prop="contactName" label="联系人" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="VISIT_STATUS_MAP[row.status].color">
            {{ VISIT_STATUS_MAP[row.status].label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button v-if="row.status === 'planned'" size="small" @click.stop="handleEdit(row)">
              编辑
            </el-button>
            <el-button v-if="row.status === 'planned'" size="small" @click.stop="handlePostpone(row)">
              延期
            </el-button>
            <el-button v-if="['planned', 'ongoing'].includes(row.status)" size="small" @click.stop="handleCancel(row)">
              取消
            </el-button>
            <el-button v-if="row.status === 'ongoing'" size="small" type="success" @click.stop="handleComplete(row)">
              完成
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingPlan ? '编辑拜访计划' : '新建拜访计划'"
      width="600px"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="计划名称" prop="visitPlanName">
          <el-input v-model="formData.visitPlanName" placeholder="请输入拜访计划名称" />
        </el-form-item>
        <el-form-item label="拜访时间" prop="visitTime">
          <el-date-picker
            v-model="formData.visitTime"
            type="datetime"
            placeholder="选择拜访时间"
            :disabledDate="disabledDate"
          />
        </el-form-item>
        <el-form-item label="拜访目的" prop="visitPurpose">
          <el-input
            v-model="formData.visitPurpose"
            type="textarea"
            :rows="3"
            placeholder="请输入拜访目的"
          />
        </el-form-item>
        <el-form-item label="提醒时间" prop="remindTime">
          <el-select v-model="formData.remindTime" placeholder="请选择提醒时间">
            <el-option
              v-for="item in REMIND_TIME_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 其他对话框（延期、取消、完成）... -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { VisitPlan, VisitPlanStatistics } from '@/types/visit-plan';
import { VISIT_STATUS_MAP, REMIND_TIME_OPTIONS } from '@/types/visit-plan';
import * as visitPlanApi from '@/api/visit-plan';
import { formatDateTime } from '@/utils/date';

const props = defineProps<{
  entityData: {
    id: number;
    type: 'customer' | 'contact' | 'opportunity';
  };
}>();

// 状态
const loading = ref(false);
const visitPlanList = ref<VisitPlan[]>([]);
const statistics = ref<VisitPlanStatistics>({
  total: 0,
  planned: 0,
  completed: 0,
  postponed: 0,
  cancelled: 0,
  completionRate: 0,
  onTimeRate: 0
});
const filterStatus = ref('');
const showAddDialog = ref(false);
const editingPlan = ref<VisitPlan | null>(null);

// 表单数据
const formData = reactive({
  visitPlanName: '',
  visitTime: '',
  visitPurpose: '',
  remindTime: 30,
  remark: ''
});

// 表单验证规则
const formRules = {
  visitPlanName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  visitTime: [
    { required: true, message: '请选择拜访时间', trigger: 'change' }
  ],
  visitPurpose: [
    { required: true, message: '请输入拜访目的', trigger: 'blur' }
  ],
  remindTime: [
    { required: true, message: '请选择提醒时间', trigger: 'change' }
  ]
};

// 加载拜访计划列表
const loadVisitPlans = async () => {
  loading.value = true;
  try {
    const res = await visitPlanApi.getVisitPlanByObject(props.entityData.type, props.entityData.id);
    visitPlanList.value = res.data || [];
    
    // 过滤状态
    if (filterStatus.value) {
      visitPlanList.value = visitPlanList.value.filter(item => item.status === filterStatus.value);
    }
  } catch (error) {
    console.error('加载拜访计划失败:', error);
    ElMessage.error('加载拜访计划失败');
  } finally {
    loading.value = false;
  }
};

// 加载统计信息
const loadStatistics = async () => {
  try {
    const res = await visitPlanApi.getVisitPlanStatistics();
    statistics.value = res.data;
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  const valid = await formRef.value?.validate();
  if (!valid) return;
  
  try {
    const data = {
      ...formData,
      [`${props.entityData.type}Id`]: props.entityData.id
    };
    
    if (editingPlan.value) {
      await visitPlanApi.updateVisitPlan({ ...editingPlan.value, ...data });
      ElMessage.success('修改成功');
    } else {
      await visitPlanApi.addVisitPlan(data);
      ElMessage.success('创建成功');
    }
    
    showAddDialog.value = false;
    loadVisitPlans();
    loadStatistics();
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  }
};

// 其他方法实现...

onMounted(() => {
  loadVisitPlans();
  loadStatistics();
});
</script>

<style scoped>
.visit-plan-tab {
  padding: 20px;
}

.statistics-row {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}
</style>
</code></pre>
    <h2>6. 定时任务设计</h2>
    <h3>6.1 Quartz配置</h3>
    <pre><code class="language-java">// VisitPlanReminderJob.java
package com.ruoyi.quartz.task;

import org.springframework.stereotype.Component;
import com.ruoyi.crm.service.ICrmVisitPlanService;
import org.springframework.beans.factory.annotation.Autowired;

@Component("visitPlanReminder")
public class VisitPlanReminderJob {
    
    @Autowired
    private ICrmVisitPlanService visitPlanService;
    
    /**
     * 处理拜访计划提醒
     * 每5分钟执行一次
     */
    public void processReminders() {
        visitPlanService.processUpcomingReminders();
    }
}
</code></pre>
    <h3>6.2 提醒服务实现</h3>
    <pre><code class="language-java">// VisitPlanReminderService.java
@Service
public class VisitPlanReminderService {
    
    @Autowired
    private CrmVisitPlanMapper visitPlanMapper;
    
    @Autowired
    private ISysNoticeService noticeService;
    
    @Autowired
    private IWeChatService weChatService;
    
    @Autowired
    private ISmsService smsService;
    
    public void processUpcomingReminders() {
        // 查询即将到期的拜访计划
        List<CrmVisitPlan> upcomingPlans = visitPlanMapper.selectUpcomingPlans(60);
        
        for (CrmVisitPlan plan : upcomingPlans) {
            // 检查是否已发送提醒
            if (!hasReminderSent(plan.getId())) {
                // 发送系统内提醒
                sendSystemNotice(plan);
                
                // 预留：发送企业微信提醒
                // sendWeChatReminder(plan);
                
                // 预留：发送短信提醒
                // sendSmsReminder(plan);
                
                // 记录提醒发送状态
                recordReminderSent(plan.getId());
            }
        }
    }
    
    private void sendSystemNotice(CrmVisitPlan plan) {
        SysNotice notice = new SysNotice();
        notice.setNoticeTitle("拜访计划提醒");
        notice.setNoticeContent(String.format(
            "您有一个拜访计划即将开始：\n计划名称：%s\n客户：%s\n时间：%s",
            plan.getVisitPlanName(),
            plan.getCustomerName(),
            DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", plan.getVisitTime())
        ));
        notice.setNoticeType("2"); // 通知类型
        notice.setStatus("0"); // 正常状态
        notice.setCreateBy("system");
        
        // 发送给负责人
        noticeService.insertNotice(notice);
    }
}
</code></pre>
    <h2>7. 测试计划</h2>
    <h3>7.1 单元测试</h3>
    <pre><code class="language-java">@SpringBootTest
class CrmVisitPlanServiceTest {
    
    @Autowired
    private ICrmVisitPlanService visitPlanService;
    
    @Test
    void testCreateVisitPlan() {
        CrmVisitPlan plan = new CrmVisitPlan();
        plan.setVisitPlanName("测试拜访计划");
        plan.setVisitTime(new Date());
        plan.setCustomerId(1L);
        plan.setVisitPurpose("产品演示");
        plan.setRemindTime(30);
        plan.setOwnerId(1L);
        
        int result = visitPlanService.insertCrmVisitPlan(plan);
        assertEquals(1, result);
    }
    
    @Test
    void testPostponeVisitPlan() {
        Long planId = 1L;
        String reason = "客户临时有事";
        String remark = "需要重新安排时间";
        Date newTime = DateUtils.addDays(new Date(), 1);
        
        int result = visitPlanService.postponeVisitPlan(planId, reason, remark, newTime);
        assertEquals(1, result);
        
        CrmVisitPlan plan = visitPlanService.selectCrmVisitPlanById(planId);
        assertEquals("postponed", plan.getStatus());
    }
}
</code></pre>
    <h3>7.2 集成测试</h3>
    <ol>
        <li><strong>创建拜访计划流程测试</strong>
            <ul>
                <li>从客户详情页创建拜访计划</li>
                <li>验证必填字段</li>
                <li>验证时间选择限制</li>
            </ul>
        </li>
        <li><strong>状态流转测试</strong>
            <ul>
                <li>计划中 → 进行中</li>
                <li>进行中 → 已完成</li>
                <li>计划中 → 已延期</li>
                <li>计划中 → 已取消</li>
            </ul>
        </li>
        <li><strong>提醒功能测试</strong>
            <ul>
                <li>设置不同提醒时间</li>
                <li>验证提醒触发</li>
                <li>验证提醒内容</li>
            </ul>
        </li>
        <li><strong>权限测试</strong>
            <ul>
                <li>只能查看自己的拜访计划</li>
                <li>只能编辑计划中的拜访计划</li>
                <li>管理员可查看所有拜访计划</li>
            </ul>
        </li>
    </ol>
    <h2>8. 部署与监控</h2>
    <h3>8.1 部署清单</h3>
    <ol>
        <li><strong>数据库变更</strong>
            <ul>
                <li>执行建表SQL脚本</li>
                <li>添加必要的索引</li>
                <li>初始化字典数据</li>
            </ul>
        </li>
        <li><strong>后端部署</strong>
            <ul>
                <li>部署新的Java类</li>
                <li>配置Quartz定时任务</li>
                <li>更新权限配置</li>
            </ul>
        </li>
        <li><strong>前端部署</strong>
            <ul>
                <li>部署Vue组件</li>
                <li>更新路由配置</li>
                <li>更新菜单权限</li>
            </ul>
        </li>
    </ol>
    <h3>8.2 监控指标</h3>
    <ol>
        <li><strong>业务指标</strong>
            <ul>
                <li>拜访计划创建数量</li>
                <li>拜访计划完成率</li>
                <li>平均延期次数</li>
                <li>准时完成率</li>
            </ul>
        </li>
        <li><strong>系统指标</strong>
            <ul>
                <li>提醒发送成功率</li>
                <li>接口响应时间</li>
                <li>定时任务执行情况</li>
            </ul>
        </li>
    </ol>
    <h2>9. 后续优化建议</h2>
    <ol>
        <li><strong>功能扩展</strong>
            <ul>
                <li>拜访计划模板功能</li>
                <li>批量创建拜访计划</li>
                <li>拜访路线规划</li>
                <li>拜访报告生成</li>
            </ul>
        </li>
        <li><strong>智能化提升</strong>
            <ul>
                <li>基于历史数据的最佳拜访时间推荐</li>
                <li>客户拜访频率分析</li>
                <li>拜访效果评估</li>
            </ul>
        </li>
        <li><strong>移动端支持</strong>
            <ul>
                <li>移动端拜访签到</li>
                <li>地理位置记录</li>
                <li>拍照上传功能</li>
            </ul>
        </li>
        <li><strong>集成扩展</strong>
            <ul>
                <li>日历同步（Outlook、Google Calendar）</li>
                <li>地图导航集成</li>
                <li>视频会议集成</li>
            </ul>
        </li>
    </ol>
    <h2>10. 风险与应对</h2>
    <table>
        <thead>
            <tr>
                <th>风险项</th>
                <th>影响</th>
                <th>概率</th>
                <th>应对措施</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>提醒发送失败</td>
                <td>高</td>
                <td>中</td>
                <td>1. 实现重试机制<br>2. 多渠道提醒<br>3. 异常监控告警</td>
            </tr>
            <tr>
                <td>数据量过大导致性能问题</td>
                <td>中</td>
                <td>低</td>
                <td>1. 分页查询<br>2. 定期归档历史数据<br>3. 优化查询索引</td>
            </tr>
            <tr>
                <td>用户操作复杂</td>
                <td>中</td>
                <td>中</td>
                <td>1. 优化UI交互<br>2. 提供操作引导<br>3. 快捷操作入口</td>
            </tr>
        </tbody>
    </table>
    <h2>总结</h2>
    <p>拜访计划功能是CRM系统中提升销售效率的重要工具。通过系统化的计划管理、智能化的提醒机制、完善的执行跟踪，能够帮助销售团队更好地管理客户关系，提高拜访效率和成功率。</p>
    <p>本方案从数据库设计、后端开发、前端实现、定时任务等多个维度提供了完整的实施指导，预留了扩展接口，为后续的功能升级和集成提供了良好的基础。</p>
    <h2>11. 主要业务时序图</h2>
    <div class="mermaid">
---
title: 拜访计划主要业务时序图
---
sequenceDiagram
    participant 用户
    participant 前端
    participant 后端
    participant 数据库

    %% 新建拜访计划
    用户->>前端: 新建拜访计划表单填写并提交
    前端->>后端: POST /crm/visitPlan
    后端->>数据库: 插入crm_visit_plans记录
    数据库-->>后端: 返回插入结果
    后端-->>前端: 返回创建成功/失败
    前端-->>用户: 显示操作结果

    %% 查看拜访计划列表
    用户->>前端: 查看拜访计划列表
    前端->>后端: GET /crm/visitPlan/listByObject
    后端->>数据库: 查询crm_visit_plans
    数据库-->>后端: 返回计划列表
    后端-->>前端: 返回数据
    前端-->>用户: 展示拜访计划列表

    %% 计划操作（延期/取消/完成）
    用户->>前端: 对计划操作（延期/取消/完成）
    前端->>后端: POST /crm/visitPlan/(postpone|cancel|complete)/{id}
    后端->>数据库: 更新crm_visit_plans状态
    后端->>数据库: 插入crm_visit_plan_logs日志
    数据库-->>后端: 返回操作结果
    后端-->>前端: 返回操作结果
    前端-->>用户: 显示操作反馈

    %% 定时任务提醒
    Note over 后端,数据库: Quartz定时任务定期扫描即将到期计划，写入提醒记录并触发通知
    后端->>数据库: 查询即将到期计划
    后端->>数据库: 插入crm_visit_plan_reminders
    后端->>通知服务: 发送系统/微信/短信提醒
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>mermaid.initialize({startOnLoad:true});</script>
</body>
</html>
