# MessagePushController

基础路径：`/wecom/message`

## 接口列表

### 发送消息
- **方法**：POST
- **路径**：`/send`
- **参数**：
  - `request` (请求体)：消息请求对象
- **功能**：发送消息

### 发送文本消息
- **方法**：POST
- **路径**：`/send/text`
- **参数**：
  - `touser`：接收用户
  - `content`：消息内容
- **功能**：发送文本消息

### 发送图片消息
- **方法**：POST
- **路径**：`/send/image`
- **参数**：
  - `touser`：接收用户
  - `mediaId`：媒体文件ID
- **功能**：发送图片消息

### 发送视频消息
- **方法**：POST
- **路径**：`/send/video`
- **参数**：
  - `touser`：接收用户
  - `mediaId`：媒体文件ID
  - `title`：视频标题
  - `description`：视频描述
- **功能**：发送视频消息

### 发送文件消息
- **方法**：POST
- **路径**：`/send/file`
- **参数**：
  - `touser`：接收用户
  - `mediaId`：媒体文件ID
- **功能**：发送文件消息

### 发送图文消息
- **方法**：POST
- **路径**：`/send/news`
- **参数**：
  - `touser`：接收用户
  - `title`：标题
  - `description`：描述
  - `url`：链接地址
  - `picurl`：图片地址
- **功能**：发送图文消息
