// 表单字段选项接口
export interface FieldOption {
  label: string;
  value: any;
  disabled?: boolean;
}

// 响应式布局配置接口
export interface ResponsiveConfig {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}

// 表单字段配置接口
export interface FormField {
  field: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'textarea' | 'number' | 'switch' | 'radio' | 'checkbox';
  colSpan: number;
  required?: boolean;
  rules?: any[];
  placeholder?: string;
  clearable?: boolean;
  props?: Record<string, any>;
  responsive?: ResponsiveConfig;
  prefixIcon?: string;
  suffixIcon?: string;
  maxLength?: number;
  showWordLimit?: boolean;
  options?: FieldOption[];
  multiple?: boolean;
  filterable?: boolean;
  dateType?: 'date' | 'year' | 'month' | 'dates' | 'week' | 'datetime' | 'datetimerange' | 'daterange' | 'monthrange';
  format?: string;
  valueFormat?: string;
  rows?: number;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  controls?: boolean;
  activeText?: string;
  inactiveText?: string;
  buttonStyle?: boolean;
}

// 表单布局配置接口
export interface FormLayout {
  labelPosition?: 'top' | 'left' | 'right';
  labelWidth?: string | number;
  size?: 'default' | 'small' | 'large';
  gutter?: number;
}

// 表单配置接口
export interface FormConfig {
  layout?: FormLayout;
  fields: FormField[];
} 