// src/router/index.ts
import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true , showHeader: false}
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true , showHeader: false}
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    meta: { hidden: true , showHeader: false}
  },
  // 企业微信登录回调页面
  {
    path: '/wecom-callback',
    component: () => import('@/views/login/wecom-callback.vue'),
    meta: {
      hidden: true,
      showHeader: false // 不需要验证登录状态
    }
  },
  // Radio Button 测试页面
  {
    path: '/radio-button-test',
    component: () => import('@/views/RadioButtonTest.vue'),
    meta: {
      hidden: true,
      title: 'Radio Button 测试'
    }
  },
  {
    path: '/system',
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'setting'
    },
    children: [
      {
        path: 'wecom-config',
        name: 'WecomConfig',
        component: () => import('@/views/system/wecom-config/index.vue'),
        meta: { 
          title: '企业微信配置',
          icon: 'ChatDotRound'
        }
      }
    ]
  },
  {
    path: '/',
    // component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'DataAnalysis', affix: true }
      }
    ]
  },
  // MessageBox 测试页面
  {
    path: '/test-messagebox',
    name: 'TestMessageBox',
    component: () => import('@/views/TestMessageBox.vue'),
    meta: { 
      title: 'MessageBox 测试',
      icon: 'Tools',
      showHeader: false
    }
  },
  // MessageBox 调试页面
  {
    path: '/debug-messagebox',
    name: 'MessageBoxDebug',
    component: () => import('@/views/MessageBoxDebug.vue'),
    meta: { 
      title: 'MessageBox 调试',
      icon: 'BugFilled',
      showHeader: false
    }
  },
  // 用户个人中心路由
  {
    path: '/user',
    name: 'User',
    children: [
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/profile/index.vue'),
        meta: { title: '个人中心' }
      }
    ]
  },
  {
    path: '/crm',
    name: 'CRM',
    meta: {
      title: '客户关系管理',
      icon: 'User'
    },
    children: [
      {
        path: 'customer',
        name: 'CustomerManagement',
        component: () => import('@/views/CustomerManagement/index.vue'),
        meta: {
          title: '客户管理',
          icon: 'User'
        }
      },
      {
        path: 'contact',
        name: 'ContactManagement',
        component: () => import('@/views/ContactManagement/index.vue'),
        meta: {
          title: '联系人管理',
          icon: 'Phone'
        }
      },
      {
        path: 'association',
        name: 'AssociationManagement',
        component: () => import('@/views/AssociationManagement/index.vue'),
        meta: {
          title: '关联管理',
          icon: 'Connection'
        }
      },
      {
        path: 'contract',
        name: 'ContractManagement',
        component: () => import('@/views/ContractManagement/index.vue'),
        meta: {
          title: '合同管理',
          icon: 'Document'
        }
      },
      {
        path: 'payment',
        name: 'PaymentManagement',
        component: () => import('@/views/PaymentManagement/index.vue'),
        meta: {
          title: '付款管理',
          icon: 'Money'
        }
      },
      {
        path: 'opportunity',
        name: 'BusinessOpportunity',
        component: () => import('@/views/BusinessOpportunityManagement/index.vue'),
        meta: {
          title: '商机管理',
          icon: 'TrendCharts'
        }
      },
      {
        path: 'quotation',
        name: 'QuotationManagement',
        component: () => import('@/views/QuotationManagement/index.vue'),
        meta: {
          title: '报价单管理',
          icon: 'Document'
        }
      },
      {
        path: 'invoice',
        name: 'InvoiceManagement',
        component: () => import('@/views/InvoiceManagement/index.vue'),
        meta: {
          title: '发票管理',
          icon: 'Receipt'
        }
      },
      {
        path: 'team',
        name: 'TeamManagement',
        component: () => import('@/views/TeamManagement/index.vue'),
        meta: {
          title: '团队管理',
          icon: 'UserFilled'
        }
      }
    ]
  },
  {
    path: '/3d-printing',
    name: '3DPrinting',
    meta: {
      title: '3D打印服务',
      icon: 'Printer'
    },
    children: [
      {
        path: 'quote',
        name: 'ThreeDPrintingQuote',
        component: () => import('@/views/ThreeDPrintingQuote/index.vue'),
        meta: {
          title: '报价管理',
          icon: 'Document',
          showHeader: false,
          requiresAuth: false
        }
      },
      {
        path: 'model',
        name: 'ThreeDModels',
        component: () => import('@/views/ThreeDModels/index.vue'),
        meta: {
          title: '模型管理',
          icon: 'Box'
        }
      },
      {
        path: 'export',
        name: 'QuoteExport',
        component: () => import('@/views/QuoteExport/index.vue'),
        meta: { 
          title: '3D打印订单导出',
          icon: 'Download'
        }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),  // 使用的是历史模式
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
});

export default router;
