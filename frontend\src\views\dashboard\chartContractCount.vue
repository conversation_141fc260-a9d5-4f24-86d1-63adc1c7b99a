<template>
    <div>
        <div ref="chartContractCount" style="width: 100%; height: 400px; margin-top: 20px;"></div>

        <el-table :data="contractAnalysisData" style="width:800px; margin-top: 20px;">
            <el-table-column prop="month" label="月份" width="100" />
            <el-table-column prop="contractCount" label="当月合同个数" width="150" />
            <el-table-column prop="monthOnMonthGrowth" label="环比增长" width="150" />
            <el-table-column prop="yearOnYearGrowth" label="同比增长" width="150" />
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            contractAnalysisData: [
                { month: '1月', contractCount: 20, monthOnMonthGrowth: '10%', yearOnYearGrowth: '15%' },
                { month: '2月', contractCount: 25, monthOnMonthGrowth: '25%', yearOnYearGrowth: '20%' },
                // 可以根据需要填充更多月份的数据
            ],
            chartData: {
                months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                contractCounts: Array.from({ length: 12 }, () => Math.floor(Math.random() * 50)),
                monthOnMonthGrowth: Array.from({ length: 12 }, () => (Math.random() * 100).toFixed(2)),
                yearOnYearGrowth: Array.from({ length: 12 }, () => (Math.random() * 100).toFixed(2)),
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
        });
    },
    methods: {
        initChart() {
            this.chartInstance = echarts.init(this.$refs.chartContractCount);
            this.updateChart();
        },
        updateChart() {
            const options = {
                tooltip: {
                    trigger: 'axis',
                },
                legend: {
                    data: ['当月合同个数', '环比增长', '同比增长'],
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.months,
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}',
                    },
                },
                series: [
                    {
                        name: '当月合同个数',
                        data: this.chartData.contractCounts,
                        type: 'line',
                    },
                    {
                        name: '环比增长',
                        data: this.chartData.monthOnMonthGrowth,
                        type: 'line',
                        yAxisIndex: 1,
                    },
                    {
                        name: '同比增长',
                        data: this.chartData.yearOnYearGrowth,
                        type: 'line',
                        yAxisIndex: 1,
                    },
                ],
            };
            this.chartInstance.setOption(options);
        },
    },
};
</script>

<style scoped>
/* 在此处添加样式以调整组件的视觉效果 */
</style>