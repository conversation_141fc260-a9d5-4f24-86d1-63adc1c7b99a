server {
    listen 8090;
    server_name cc.jolinmind.com;  # 替换为你的域名或IP地址
    charset utf-8;

    # 前端静态文件服务
    location / {
        root /var/www/html/ruoyi;
        try_files $uri $uri/ /index.html;
        index index.html index.htm;
    }

    # 后端 API 代理
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root html;
    }

    # 日志
    access_log /www/wwwlogs/ruoyi-access.log;
    error_log /www/wwwlogs/ruoyi-error.log;
}