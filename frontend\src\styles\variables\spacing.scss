// 间距和布局变量
:root {
  // 基础间距
  --ep-spacing-mini: 4px;
  --ep-spacing-small: 8px;
  --ep-spacing-medium: 16px;
  --ep-spacing-large: 24px;
  --ep-spacing-extra-large: 32px;

  // 边框圆角
  --ep-border-radius-small: 2px;
  --ep-border-radius-base: 4px;
  --ep-border-radius-medium: 6px;
  --ep-border-radius-large: 8px;
  --ep-border-radius-round: 20px;
  --ep-border-radius-circle: 50%;

  // 阴影
  --ep-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --ep-box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
  --ep-box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.2);

  // 过渡
  --ep-transition-duration: 0.3s;
  --ep-transition-timing-function: ease-in-out;
} 