/*
Navicat MySQL Data Transfer

Source Server         : 本地数据库
Source Server Version : 50726
Source Host           : localhost:3306
Source Database       : crm41

Target Server Type    : MYSQL
Target Server Version : 50726
File Encoding         : 65001

Date: 2025-07-23 02:16:06
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for act_evt_log
-- ----------------------------
DROP TABLE IF EXISTS `act_evt_log`;
CREATE TABLE `act_evt_log` (
  `LOG_NR_` bigint(20) NOT NULL AUTO_INCREMENT,
  `TYPE_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `TASK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `TIME_STAMP_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `USER_ID_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `DATA_` longblob,
  `LOCK_OWNER_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL,
  `IS_PROCESSED_` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`LOG_NR_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ge_bytearray
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_bytearray`;
CREATE TABLE `act_ge_bytearray` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `BYTES_` longblob,
  `GENERATED_` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_BYTEARR_DEPL` (`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_BYTEARR_DEPL` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_property`;
CREATE TABLE `act_ge_property` (
  `NAME_` varchar(64) COLLATE utf8_bin NOT NULL,
  `VALUE_` varchar(300) COLLATE utf8_bin DEFAULT NULL,
  `REV_` int(11) DEFAULT NULL,
  PRIMARY KEY (`NAME_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_procdef_info
-- ----------------------------
DROP TABLE IF EXISTS `act_procdef_info`;
CREATE TABLE `act_procdef_info` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `INFO_JSON_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE KEY `ACT_UNIQ_INFO_PROCDEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_IDX_INFO_PROCDEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_INFO_JSON_BA` (`INFO_JSON_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_INFO_JSON_BA` FOREIGN KEY (`INFO_JSON_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_INFO_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_re_deployment
-- ----------------------------
DROP TABLE IF EXISTS `act_re_deployment`;
CREATE TABLE `act_re_deployment` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `CATEGORY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  `DEPLOY_TIME_` timestamp(3) NULL DEFAULT NULL,
  `ENGINE_VERSION_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `VERSION_` int(11) DEFAULT '1',
  `PROJECT_RELEASE_VERSION_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_re_model
-- ----------------------------
DROP TABLE IF EXISTS `act_re_model`;
CREATE TABLE `act_re_model` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `CATEGORY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LAST_UPDATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `VERSION_` int(11) DEFAULT NULL,
  `META_INFO_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EDITOR_SOURCE_VALUE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EDITOR_SOURCE_EXTRA_VALUE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_MODEL_SOURCE` (`EDITOR_SOURCE_VALUE_ID_`) USING BTREE,
  KEY `ACT_FK_MODEL_SOURCE_EXTRA` (`EDITOR_SOURCE_EXTRA_VALUE_ID_`) USING BTREE,
  KEY `ACT_FK_MODEL_DEPLOYMENT` (`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_MODEL_DEPLOYMENT` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`),
  CONSTRAINT `ACT_FK_MODEL_SOURCE` FOREIGN KEY (`EDITOR_SOURCE_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_MODEL_SOURCE_EXTRA` FOREIGN KEY (`EDITOR_SOURCE_EXTRA_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_re_procdef
-- ----------------------------
DROP TABLE IF EXISTS `act_re_procdef`;
CREATE TABLE `act_re_procdef` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `CATEGORY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `KEY_` varchar(255) COLLATE utf8_bin NOT NULL,
  `VERSION_` int(11) NOT NULL,
  `DEPLOYMENT_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `RESOURCE_NAME_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DGRM_RESOURCE_NAME_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `HAS_START_FORM_KEY_` tinyint(4) DEFAULT NULL,
  `HAS_GRAPHICAL_NOTATION_` tinyint(4) DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  `ENGINE_VERSION_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `APP_VERSION_` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE KEY `ACT_UNIQ_PROCDEF` (`KEY_`,`VERSION_`,`TENANT_ID_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_deadletter_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_deadletter_job`;
CREATE TABLE `act_ru_deadletter_job` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `EXCLUSIVE_` tinyint(1) DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_DEADLETTER_JOB_EXECUTION` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_DEADLETTER_JOB_PROCESS_INSTANCE` (`PROCESS_INSTANCE_ID_`) USING BTREE,
  KEY `ACT_FK_DEADLETTER_JOB_PROC_DEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_DEADLETTER_JOB_EXCEPTION` (`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_DEADLETTER_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_DEADLETTER_JOB_EXECUTION` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_DEADLETTER_JOB_PROCESS_INSTANCE` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_DEADLETTER_JOB_PROC_DEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_event_subscr
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_event_subscr`;
CREATE TABLE `act_ru_event_subscr` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `EVENT_TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `EVENT_NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `ACTIVITY_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `CONFIGURATION_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `CREATED_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_IDX_EVENT_SUBSCR_CONFIG_` (`CONFIGURATION_`) USING BTREE,
  KEY `ACT_FK_EVENT_EXEC` (`EXECUTION_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_EVENT_EXEC` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_execution
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_execution`;
CREATE TABLE `act_ru_execution` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `BUSINESS_KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `PARENT_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `SUPER_EXEC_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `ROOT_PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `ACT_ID_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `IS_ACTIVE_` tinyint(4) DEFAULT NULL,
  `IS_CONCURRENT_` tinyint(4) DEFAULT NULL,
  `IS_SCOPE_` tinyint(4) DEFAULT NULL,
  `IS_EVENT_SCOPE_` tinyint(4) DEFAULT NULL,
  `IS_MI_ROOT_` tinyint(4) DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) DEFAULT NULL,
  `CACHED_ENT_STATE_` int(11) DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `START_TIME_` datetime(3) DEFAULT NULL,
  `START_USER_ID_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL,
  `IS_COUNT_ENABLED_` tinyint(4) DEFAULT NULL,
  `EVT_SUBSCR_COUNT_` int(11) DEFAULT NULL,
  `TASK_COUNT_` int(11) DEFAULT NULL,
  `JOB_COUNT_` int(11) DEFAULT NULL,
  `TIMER_JOB_COUNT_` int(11) DEFAULT NULL,
  `SUSP_JOB_COUNT_` int(11) DEFAULT NULL,
  `DEADLETTER_JOB_COUNT_` int(11) DEFAULT NULL,
  `VAR_COUNT_` int(11) DEFAULT NULL,
  `ID_LINK_COUNT_` int(11) DEFAULT NULL,
  `APP_VERSION_` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_IDX_EXEC_BUSKEY` (`BUSINESS_KEY_`) USING BTREE,
  KEY `ACT_IDC_EXEC_ROOT` (`ROOT_PROC_INST_ID_`) USING BTREE,
  KEY `ACT_FK_EXE_PROCINST` (`PROC_INST_ID_`) USING BTREE,
  KEY `ACT_FK_EXE_PARENT` (`PARENT_ID_`) USING BTREE,
  KEY `ACT_FK_EXE_SUPER` (`SUPER_EXEC_`) USING BTREE,
  KEY `ACT_FK_EXE_PROCDEF` (`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_EXE_PARENT` FOREIGN KEY (`PARENT_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE,
  CONSTRAINT `ACT_FK_EXE_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`),
  CONSTRAINT `ACT_FK_EXE_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `ACT_FK_EXE_SUPER` FOREIGN KEY (`SUPER_EXEC_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_identitylink
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_identitylink`;
CREATE TABLE `act_ru_identitylink` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `GROUP_ID_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `USER_ID_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `TASK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_IDX_IDENT_LNK_USER` (`USER_ID_`) USING BTREE,
  KEY `ACT_IDX_IDENT_LNK_GROUP` (`GROUP_ID_`) USING BTREE,
  KEY `ACT_IDX_ATHRZ_PROCEDEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_TSKASS_TASK` (`TASK_ID_`) USING BTREE,
  KEY `ACT_FK_IDL_PROCINST` (`PROC_INST_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_ATHRZ_PROCEDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`),
  CONSTRAINT `ACT_FK_IDL_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_TSKASS_TASK` FOREIGN KEY (`TASK_ID_`) REFERENCES `act_ru_task` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_integration
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_integration`;
CREATE TABLE `act_ru_integration` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `FLOW_NODE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `CREATED_DATE_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_INT_EXECUTION` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_INT_PROC_INST` (`PROCESS_INSTANCE_ID_`) USING BTREE,
  KEY `ACT_FK_INT_PROC_DEF` (`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_INT_EXECUTION` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE,
  CONSTRAINT `ACT_FK_INT_PROC_DEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`),
  CONSTRAINT `ACT_FK_INT_PROC_INST` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_job`;
CREATE TABLE `act_ru_job` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `LOCK_EXP_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LOCK_OWNER_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `EXCLUSIVE_` tinyint(1) DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `RETRIES_` int(11) DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_JOB_EXECUTION` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_JOB_PROCESS_INSTANCE` (`PROCESS_INSTANCE_ID_`) USING BTREE,
  KEY `ACT_FK_JOB_PROC_DEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_JOB_EXCEPTION` (`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_JOB_EXECUTION` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_JOB_PROCESS_INSTANCE` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_JOB_PROC_DEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_suspended_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_suspended_job`;
CREATE TABLE `act_ru_suspended_job` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `EXCLUSIVE_` tinyint(1) DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `RETRIES_` int(11) DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_SUSPENDED_JOB_EXECUTION` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_SUSPENDED_JOB_PROCESS_INSTANCE` (`PROCESS_INSTANCE_ID_`) USING BTREE,
  KEY `ACT_FK_SUSPENDED_JOB_PROC_DEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_SUSPENDED_JOB_EXCEPTION` (`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_SUSPENDED_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_SUSPENDED_JOB_EXECUTION` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_SUSPENDED_JOB_PROCESS_INSTANCE` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_SUSPENDED_JOB_PROC_DEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_task`;
CREATE TABLE `act_ru_task` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `BUSINESS_KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `PARENT_TASK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TASK_DEF_KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `OWNER_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `ASSIGNEE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `DELEGATION_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PRIORITY_` int(11) DEFAULT NULL,
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `DUE_DATE_` datetime(3) DEFAULT NULL,
  `CATEGORY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  `FORM_KEY_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `CLAIM_TIME_` datetime(3) DEFAULT NULL,
  `APP_VERSION_` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_IDX_TASK_CREATE` (`CREATE_TIME_`) USING BTREE,
  KEY `ACT_FK_TASK_EXE` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_TASK_PROCINST` (`PROC_INST_ID_`) USING BTREE,
  KEY `ACT_FK_TASK_PROCDEF` (`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_TASK_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_TASK_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`),
  CONSTRAINT `ACT_FK_TASK_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_timer_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_timer_job`;
CREATE TABLE `act_ru_timer_job` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `LOCK_EXP_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LOCK_OWNER_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `EXCLUSIVE_` tinyint(1) DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `RETRIES_` int(11) DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TENANT_ID_` varchar(255) COLLATE utf8_bin DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_FK_TIMER_JOB_EXECUTION` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_TIMER_JOB_PROCESS_INSTANCE` (`PROCESS_INSTANCE_ID_`) USING BTREE,
  KEY `ACT_FK_TIMER_JOB_PROC_DEF` (`PROC_DEF_ID_`) USING BTREE,
  KEY `ACT_FK_TIMER_JOB_EXCEPTION` (`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_TIMER_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_TIMER_JOB_EXECUTION` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_TIMER_JOB_PROCESS_INSTANCE` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_TIMER_JOB_PROC_DEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for act_ru_variable
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_variable`;
CREATE TABLE `act_ru_variable` (
  `ID_` varchar(64) COLLATE utf8_bin NOT NULL,
  `REV_` int(11) DEFAULT NULL,
  `TYPE_` varchar(255) COLLATE utf8_bin NOT NULL,
  `NAME_` varchar(255) COLLATE utf8_bin NOT NULL,
  `EXECUTION_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `TASK_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `BYTEARRAY_ID_` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `DOUBLE_` double DEFAULT NULL,
  `LONG_` bigint(20) DEFAULT NULL,
  `TEXT_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  `TEXT2_` varchar(4000) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `ACT_IDX_VARIABLE_TASK_ID` (`TASK_ID_`) USING BTREE,
  KEY `ACT_FK_VAR_EXE` (`EXECUTION_ID_`) USING BTREE,
  KEY `ACT_FK_VAR_PROCINST` (`PROC_INST_ID_`) USING BTREE,
  KEY `ACT_FK_VAR_BYTEARRAY` (`BYTEARRAY_ID_`) USING BTREE,
  CONSTRAINT `ACT_FK_VAR_BYTEARRAY` FOREIGN KEY (`BYTEARRAY_ID_`) REFERENCES `act_ge_bytearray` (`ID_`),
  CONSTRAINT `ACT_FK_VAR_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`),
  CONSTRAINT `ACT_FK_VAR_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for business_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `business_operation_log`;
CREATE TABLE `business_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务名称',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `field_changes` json DEFAULT NULL COMMENT '字段变更详情',
  `before_data` json DEFAULT NULL COMMENT '变更前数据',
  `after_data` json DEFAULT NULL COMMENT '变更后数据',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `extra_data` text COMMENT '扩展数据',
  `success` tinyint(1) DEFAULT '1' COMMENT '是否成功',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `method_name` varchar(200) DEFAULT NULL COMMENT '方法名',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间（毫秒）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`,`business_id`),
  KEY `idx_operation` (`operation_type`,`operation_time`),
  KEY `idx_operator` (`operator_id`,`operation_time`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_business_type` (`business_type`)
) ENGINE=InnoDB AUTO_INCREMENT=17555 DEFAULT CHARSET=utf8mb4 COMMENT='业务操作日志表';

-- ----------------------------
-- Table structure for crm_attachments
-- ----------------------------
DROP TABLE IF EXISTS `crm_attachments`;
CREATE TABLE `crm_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型：contact/customer/opportunity/contract/lead/product',
  `entity_id` bigint(20) NOT NULL COMMENT '关联实体的主键ID',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名(唯一)',
  `original_name` varchar(255) NOT NULL COMMENT '用户上传的原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件在服务器上的完整路径',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型(image/jpeg)',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名(.jpg)',
  `category` varchar(50) DEFAULT NULL COMMENT '附件分类：contract/image/document/spreadsheet/presentation/video/audio/other',
  `description` varchar(500) DEFAULT NULL COMMENT '附件描述信息',
  `upload_by` varchar(64) DEFAULT NULL COMMENT '上传用户',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `download_count` int(11) DEFAULT '0' COMMENT '下载次数统计',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开访问(0私有 1公开)',
  `sort_order` int(11) DEFAULT '0' COMMENT '显示排序(数字越小越靠前)',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_file_name` (`file_name`) USING BTREE,
  KEY `idx_entity` (`entity_type`,`entity_id`,`del_flag`) USING BTREE,
  KEY `idx_upload_time` (`upload_time`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_upload_by` (`upload_by`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='CRM通用附件表';

-- ----------------------------
-- Table structure for crm_business
-- ----------------------------
DROP TABLE IF EXISTS `crm_business`;
CREATE TABLE `crm_business` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `business_name` varchar(255) NOT NULL COMMENT '业务名称',
  `business_type` varchar(255) NOT NULL COMMENT '业务类型',
  `related_table` varchar(255) DEFAULT NULL COMMENT '关联表名',
  `primary_field1` varchar(255) DEFAULT NULL COMMENT '主要字段1',
  `primary_field2` varchar(255) DEFAULT NULL COMMENT '主要字段2',
  `primary_field3` varchar(255) DEFAULT NULL COMMENT '主要字段3',
  `function_description` text COMMENT '表功能描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务表';

-- ----------------------------
-- Table structure for crm_business_ap_approval_history
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_ap_approval_history`;
CREATE TABLE `crm_business_ap_approval_history` (
  `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `instance_node_id` bigint(20) DEFAULT NULL COMMENT '节点实例ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `action_type` varchar(50) DEFAULT '' COMMENT '审批动作',
  `action_timestamp` datetime DEFAULT NULL COMMENT '操作时间',
  `action_comments` text COMMENT '审批意见或备注',
  `application_id` bigint(20) DEFAULT NULL COMMENT '我的申请',
  `approval_id` bigint(20) DEFAULT NULL COMMENT '审批处理',
  `previous_history_id` bigint(20) DEFAULT NULL COMMENT '上一个历史记录ID',
  `previous_instance_node_id` bigint(20) DEFAULT NULL COMMENT '上一个节点',
  `previous_applicant` bigint(20) DEFAULT NULL COMMENT '上一个节点的申请人',
  `previous_approver_type` varchar(10) DEFAULT NULL COMMENT '上一个节点的申请处理人类型（1个人2团队）',
  `selected_team` varchar(100) DEFAULT NULL COMMENT '选择的团队',
  `selected_team_member` varchar(100) DEFAULT NULL COMMENT '选择的团队的人',
  `select_user` varchar(255) DEFAULT NULL COMMENT '选择的个人',
  PRIMARY KEY (`history_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='审批历史表';

-- ----------------------------
-- Table structure for crm_business_ap_approval_process
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_ap_approval_process`;
CREATE TABLE `crm_business_ap_approval_process` (
  `approval_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
  `instance_node_id` bigint(20) NOT NULL COMMENT '节点实例ID',
  `assignee` varchar(255) DEFAULT NULL COMMENT '审批人',
  `start_time` datetime DEFAULT NULL COMMENT '开始审批的时间',
  `end_time` datetime DEFAULT NULL COMMENT '审批结束的时间',
  `status` varchar(50) DEFAULT NULL COMMENT '审批状态（如待审批、已批准、已拒绝）',
  `content_id` bigint(20) DEFAULT NULL COMMENT '关联的文章ID',
  `comment` varchar(500) DEFAULT NULL COMMENT '审批建议或评论',
  `is_processed` tinyint(1) DEFAULT NULL COMMENT '是否已经处理',
  `application_id` bigint(20) DEFAULT NULL COMMENT '关联的申请ID',
  `previous_history_id` bigint(20) DEFAULT NULL COMMENT '上一个历史记录ID',
  `previous_instance_node_id` bigint(20) DEFAULT NULL COMMENT '上一个节点的实例ID',
  `previous_applicant` bigint(20) DEFAULT NULL COMMENT '上一个节点的申请人ID',
  `previous_approver_type` varchar(10) DEFAULT NULL COMMENT '上一个节点的申请处理人类型（1个人2团队）',
  PRIMARY KEY (`approval_id`) USING BTREE,
  KEY `idx_instance_node_id` (`instance_node_id`) USING BTREE,
  CONSTRAINT `crm_business_ap_approval_process_ibfk_1` FOREIGN KEY (`instance_node_id`) REFERENCES `content_publish_ap_node_instance` (`instance_node_id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='审批处理表';

-- ----------------------------
-- Table structure for crm_business_ap_my_application
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_ap_my_application`;
CREATE TABLE `crm_business_ap_my_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `content_id` bigint(20) NOT NULL COMMENT '文章ID',
  `instance_id` bigint(20) NOT NULL COMMENT '流程实例ID',
  `current_node_id` bigint(20) DEFAULT NULL COMMENT '当前审批的节点ID',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `status` varchar(50) DEFAULT NULL COMMENT '当前审批状态',
  PRIMARY KEY (`application_id`) USING BTREE,
  KEY `idx_content_id` (`content_id`) USING BTREE,
  KEY `idx_instance_id` (`instance_id`) USING BTREE,
  KEY `idx_current_node_id` (`current_node_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='我的申请表';

-- ----------------------------
-- Table structure for crm_business_ap_node_detail
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_ap_node_detail`;
CREATE TABLE `crm_business_ap_node_detail` (
  `detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点详情ID，自增长主键',
  `node_id` bigint(20) NOT NULL COMMENT '关联的节点ID',
  `display_x` int(11) NOT NULL COMMENT '节点在前端界面上的X坐标',
  `display_y` int(11) NOT NULL COMMENT '节点在前端界面上的Y坐标',
  `width` int(11) NOT NULL COMMENT '节点的宽度',
  `height` int(11) NOT NULL COMMENT '节点的高度',
  `node_type` varchar(50) NOT NULL COMMENT '节点的类型',
  `is_recursive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '节点是否循环连接到自己',
  `additional_info` text COMMENT '节点的附加信息',
  `create_time` datetime DEFAULT NULL COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '记录最后更新时间',
  `dept_ids` text COMMENT '可选部门列表',
  `user_ids` text COMMENT '可选用户列表',
  `can_edit_article` tinyint(1) NOT NULL DEFAULT '0' COMMENT '能否编辑文章',
  `can_edit_fee` tinyint(1) NOT NULL DEFAULT '0' COMMENT '能否修改稿费',
  PRIMARY KEY (`detail_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='审批节点详情表';

-- ----------------------------
-- Table structure for crm_business_ap_process_instance
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_ap_process_instance`;
CREATE TABLE `crm_business_ap_process_instance` (
  `instance_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '实例ID',
  `definition_id` bigint(20) DEFAULT NULL COMMENT '流程定义ID',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `instance_status` varchar(50) DEFAULT '' COMMENT '状态',
  `instance_name` varchar(255) DEFAULT NULL COMMENT '实例名称',
  PRIMARY KEY (`instance_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='流程实例表';

-- ----------------------------
-- Table structure for crm_business_contacts
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_contacts`;
CREATE TABLE `crm_business_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `responsible_person_id` varchar(255) NOT NULL COMMENT '负责人ID',
  `name` varchar(255) NOT NULL COMMENT '联系人姓名',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `phone` varchar(20) DEFAULT NULL COMMENT '办公电话',
  `telephone` varchar(20) DEFAULT NULL COMMENT '固定电话',
  `email` varchar(255) DEFAULT NULL COMMENT '电子邮件地址',
  `position` varchar(255) DEFAULT NULL COMMENT '职务',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `decision_role` varchar(50) DEFAULT NULL COMMENT '决策角色(决策者/影响者/使用者/其他)',
  `contact_level` varchar(10) DEFAULT NULL COMMENT '联系人级别(A/B/C/D)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0有效 1无效)',
  `is_key_decision_maker` varchar(50) DEFAULT NULL COMMENT '是否关键决策人',
  `direct_superior` varchar(255) DEFAULT NULL COMMENT '直属上级',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `detailed_address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `next_contact_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `selected_date` date DEFAULT NULL COMMENT '选择日期',
  `gender` varchar(50) DEFAULT NULL COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `remarks` text COMMENT '备注信息',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `customer_name_backup` varchar(255) DEFAULT NULL COMMENT '客户名称备份',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_responsible_person_del` (`responsible_person_id`,`del_flag`) USING BTREE,
  KEY `idx_name_mobile` (`name`,`mobile`) USING BTREE,
  KEY `idx_create_time_desc` (`create_time`) USING BTREE,
  KEY `idx_telephone` (`telephone`) USING BTREE,
  KEY `idx_department` (`department`) USING BTREE,
  KEY `idx_decision_role` (`decision_role`) USING BTREE,
  KEY `idx_contact_level` (`contact_level`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_birthday` (`birthday`) USING BTREE,
  KEY `idx_status_del_flag` (`status`,`del_flag`) USING BTREE,
  KEY `idx_level_role` (`contact_level`,`decision_role`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=621 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='联系人表';

-- ----------------------------
-- Table structure for crm_business_contracts
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_contracts`;
CREATE TABLE `crm_business_contracts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `manager_id` int(11) NOT NULL COMMENT '负责人ID',
  `contract_number` varchar(255) NOT NULL COMMENT '合同编号',
  `contract_name` varchar(255) NOT NULL COMMENT '合同名称',
  `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
  `quotation_number` varchar(255) DEFAULT NULL COMMENT '报价单编号',
  `opportunity_name` varchar(255) DEFAULT NULL COMMENT '商机名称',
  `contract_amount` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
  `order_date` date DEFAULT NULL COMMENT '下单时间',
  `start_date` date DEFAULT NULL COMMENT '合同开始时间',
  `end_date` date DEFAULT NULL COMMENT '合同结束时间',
  `customer_signatory` varchar(255) DEFAULT NULL COMMENT '客户签约人',
  `company_signatory` varchar(255) DEFAULT NULL COMMENT '公司签约人',
  `remarks` text COMMENT '备注',
  `contract_type` varchar(255) DEFAULT NULL COMMENT '合同类型',
  `contract_image` blob COMMENT '合同影像',
  `attachments` blob COMMENT '附件',
  `profit` decimal(15,2) DEFAULT NULL COMMENT '利润',
  `total_product_cost` decimal(15,2) DEFAULT NULL COMMENT '产品成本总金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `status` varchar(255) DEFAULT NULL COMMENT '状态',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='合同表';

-- ----------------------------
-- Table structure for crm_business_contract_user_relations
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_contract_user_relations`;
CREATE TABLE `crm_business_contract_user_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `relation_type` enum('负责','下属负责','关注') DEFAULT NULL COMMENT '关系类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='合同用户关系表';

-- ----------------------------
-- Table structure for crm_business_contract_watchlist
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_contract_watchlist`;
CREATE TABLE `crm_business_contract_watchlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `person_id` int(11) NOT NULL COMMENT '人物ID',
  `contract_id` int(11) NOT NULL COMMENT '合同ID',
  `watch_status` enum('关注','取消关注') NOT NULL COMMENT '关注状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `contract_id` (`contract_id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='合同关注表';

-- ----------------------------
-- Table structure for crm_business_customers
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_customers`;
CREATE TABLE `crm_business_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `responsible_person_id` varchar(255) NOT NULL COMMENT '负责人ID',
  `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
  `customer_source` varchar(255) DEFAULT NULL COMMENT '客户来源',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话号码',
  `email` varchar(255) DEFAULT NULL COMMENT '电子邮件地址',
  `website` varchar(255) DEFAULT NULL COMMENT '网址',
  `customer_industry` varchar(255) DEFAULT NULL COMMENT '客户所属的行业',
  `customer_level` varchar(50) DEFAULT NULL COMMENT '客户的级别，例如潜在客户、高级客户等',
  `customer_address` varchar(255) DEFAULT NULL COMMENT '客户地址',
  `primary_contact` varchar(255) DEFAULT NULL COMMENT '首要联系人',
  `deal_status` varchar(50) DEFAULT NULL COMMENT '成交状态',
  `next_contact_time` datetime DEFAULT NULL COMMENT '下次联系的时间',
  `selected_date` date DEFAULT NULL COMMENT '选择日期',
  `remarks` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，默认为当前时间戳',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，默认为当前时间戳，并在更新记录时自动更新',
  `status` varchar(50) DEFAULT NULL COMMENT '状态',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=89 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='客户表';

-- ----------------------------
-- Table structure for crm_business_follow_up_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_follow_up_records`;
CREATE TABLE `crm_business_follow_up_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `module_type` varchar(50) NOT NULL COMMENT '所属模块（customer:客户,lead:线索,opportunity:商机,contract:合同）',
  `follow_up_content` text COMMENT '跟进内容',
  `next_contact_method` varchar(255) DEFAULT NULL COMMENT '下次联系方式',
  `follow_up_method` varchar(255) DEFAULT NULL COMMENT '跟进方式',
  `visit_plan_id` int(11) DEFAULT NULL COMMENT '拜访计划主键ID',
  `related_files` json DEFAULT NULL COMMENT '相关的文件资源（用JSON表达）',
  `related_customer_id` int(11) DEFAULT NULL COMMENT '关联客户ID',
  `related_contact_id` int(11) DEFAULT NULL COMMENT '相关联系人ID',
  `related_opportunity_id` int(11) DEFAULT NULL COMMENT '相关商机ID',
  `related_contract_id` int(11) DEFAULT NULL COMMENT '相关合同ID',
  `related_payment_id` int(11) DEFAULT NULL COMMENT '相关汇款ID',
  `related_product_id` int(11) DEFAULT NULL COMMENT '相关产品ID',
  `follow_up_type` varchar(255) DEFAULT NULL COMMENT '跟进类型',
  `effective_follow_up_person` varchar(255) DEFAULT NULL COMMENT '有效跟进人',
  `activity_time` datetime DEFAULT NULL COMMENT '活动时间',
  `contact_result` varchar(100) DEFAULT NULL COMMENT '联系结果',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `activity_type` varchar(50) DEFAULT NULL COMMENT '活动类型(phone,email,meeting,visit,demo,other)',
  `content` text COMMENT '活动内容',
  `result` varchar(100) DEFAULT NULL COMMENT '活动结果(interested,considering,no_need,interested_deep,refused)',
  `participants` varchar(500) DEFAULT NULL COMMENT '参与者',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，默认为当前时间戳，并在更新记录时自动更新',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，默认为当前时间戳',
  `creator_id` varchar(255) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='跟进记录表';

-- ----------------------------
-- Table structure for crm_business_leads
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_leads`;
CREATE TABLE `crm_business_leads` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `responsible_person_id` varchar(255) NOT NULL COMMENT '负责人',
  `lead_name` varchar(255) NOT NULL COMMENT '线索名称',
  `lead_source` varchar(255) DEFAULT NULL COMMENT '线索来源，例如网络、推荐等',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话号码',
  `email` varchar(255) DEFAULT NULL COMMENT '电子邮件地址',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `detailed_address` varchar(255) DEFAULT NULL COMMENT '详细的地址信息',
  `customer_industry` varchar(255) DEFAULT NULL COMMENT '客户所属的行业',
  `customer_level` varchar(50) DEFAULT NULL COMMENT '客户的级别，例如潜在客户、高级客户等',
  `next_contact_time` datetime DEFAULT NULL COMMENT '下次联系的时间',
  `selected_date` date DEFAULT NULL COMMENT '选择日期',
  `remarks` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，默认为当前时间戳',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，默认为当前时间戳，并在更新记录时自动更新',
  `customer_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `status` varchar(50) DEFAULT NULL COMMENT '状态',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=1875 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='线索表';

-- ----------------------------
-- Table structure for crm_business_lead_assignment_history
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_lead_assignment_history`;
CREATE TABLE `crm_business_lead_assignment_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `lead_id` int(11) NOT NULL COMMENT '线索ID，外键，指向 Leads 表的 id',
  `old_owner_id` int(11) DEFAULT NULL COMMENT '前任所属人的用户ID',
  `new_owner_id` int(11) NOT NULL COMMENT '新任所属人的用户ID',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '指派时间，默认为当前时间戳',
  `status` varchar(255) DEFAULT NULL COMMENT '线索状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，默认为当前时间戳',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，默认为当前时间戳，并在更新记录时自动更新',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='线索分配历史记录表';

-- ----------------------------
-- Table structure for crm_business_lead_followers
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_lead_followers`;
CREATE TABLE `crm_business_lead_followers` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `lead_id` int(11) NOT NULL COMMENT '线索ID，外键，指向 Leads 表的 id',
  `follower_id` int(11) NOT NULL COMMENT '关注者ID，外键，指向用户表',
  `status` varchar(50) DEFAULT NULL COMMENT '关注状态',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lead_id` (`lead_id`) USING BTREE,
  KEY `idx_follower_id` (`follower_id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='线索关注者关联表';

-- ----------------------------
-- Table structure for crm_business_lead_user_associations
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_lead_user_associations`;
CREATE TABLE `crm_business_lead_user_associations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `lead_id` int(11) NOT NULL COMMENT '线索ID，外键，指向 Leads 表的 id',
  `user_id` int(11) NOT NULL COMMENT '用户ID，外键，指向用户表（例如 Users 表）',
  `status` varchar(50) DEFAULT NULL COMMENT '线索的状态，例如 "已经转化的"、"正在跟踪的" 或 "没有分配的"',
  `current_owner_id` int(11) NOT NULL COMMENT '当前所属人的用户ID',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='线索和用户关联表';

-- ----------------------------
-- Table structure for crm_business_opportunities
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_opportunities`;
CREATE TABLE `crm_business_opportunities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `manager_id` int(11) NOT NULL COMMENT '负责人ID',
  `opportunity_name` varchar(255) NOT NULL COMMENT '商机名称',
  `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
  `opportunity_amount` decimal(15,2) DEFAULT NULL COMMENT '商机金额',
  `opportunity_stage` varchar(255) DEFAULT NULL COMMENT '商机阶段',
  `win_rate` decimal(15,2) DEFAULT NULL COMMENT '赢单率',
  `expected_close_date` date DEFAULT NULL COMMENT '预计关闭日期',
  `opportunity_source` varchar(255) DEFAULT NULL COMMENT '商机来源',
  `opportunity_type` varchar(255) DEFAULT NULL COMMENT '商机类型',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=63 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商机表';

-- ----------------------------
-- Table structure for crm_business_payments
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_payments`;
CREATE TABLE `crm_business_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `manager_id` int(11) NOT NULL COMMENT '负责人ID',
  `payment_number` varchar(255) NOT NULL COMMENT '回款编号',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
  `payment_details` text COMMENT '回款明细',
  `payment_date` date NOT NULL COMMENT '回款日期',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '回款金额',
  `payment_method` varchar(255) DEFAULT NULL COMMENT '回款方式',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='回款表';

-- ----------------------------
-- Table structure for crm_business_payment_details
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_payment_details`;
CREATE TABLE `crm_business_payment_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `payment_id` int(11) NOT NULL COMMENT '回款ID',
  `payment_plan_id` int(11) NOT NULL COMMENT '回款计划ID',
  `payment_date` date NOT NULL COMMENT '回款日期',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '回款金额',
  `payment_method` varchar(255) DEFAULT NULL COMMENT '回款方式',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='回款明细表';

-- ----------------------------
-- Table structure for crm_business_payment_plans
-- ----------------------------
DROP TABLE IF EXISTS `crm_business_payment_plans`;
CREATE TABLE `crm_business_payment_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增字段',
  `manager_id` int(11) NOT NULL COMMENT '负责人ID',
  `payment_plan_name` varchar(255) NOT NULL COMMENT '回款计划名称',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
  `payment_plan_details` text COMMENT '回款计划明细',
  `payment_plan_date` date NOT NULL COMMENT '回款计划日期',
  `payment_plan_amount` decimal(15,2) NOT NULL COMMENT '回款计划金额',
  `payment_plan_method` varchar(255) DEFAULT NULL COMMENT '回款计划方式',
  `remarks` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='回款计划表';

-- ----------------------------
-- Table structure for crm_contact_followers
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_followers`;
CREATE TABLE `crm_contact_followers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `follower_id` bigint(20) NOT NULL COMMENT '关注者用户ID',
  `follow_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效关注（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_contact_follower` (`contact_id`,`follower_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_follower_id` (`follower_id`) USING BTREE,
  KEY `idx_follow_time` (`follow_time`) USING BTREE,
  KEY `idx_is_active` (`is_active`) USING BTREE,
  KEY `idx_follower_active` (`follower_id`,`is_active`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='联系人关注表';

-- ----------------------------
-- Table structure for crm_contact_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_followup_records`;
CREATE TABLE `crm_contact_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `follow_up_content` text COMMENT '跟进内容',
  `next_contact_method` varchar(100) DEFAULT NULL COMMENT '下次联系方式',
  `follow_up_method` varchar(100) DEFAULT NULL COMMENT '跟进方式',
  `next_contact_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `communication_result` varchar(200) DEFAULT NULL COMMENT '沟通结果',
  `meeting_summary` text COMMENT '会议纪要',
  `contact_quality` varchar(50) DEFAULT NULL COMMENT '联系质量评级',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_next_contact` (`next_contact_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='联系人跟进记录表';

-- ----------------------------
-- Table structure for crm_contact_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_operation_log`;
CREATE TABLE `crm_contact_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型(CREATE,UPDATE,DELETE,FOLLOW,UNFOLLOW等)',
  `operation_content` text COMMENT '操作内容',
  `operation_details` json DEFAULT NULL COMMENT '操作细则，存储变更前后的值',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名称',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `extra_data` text COMMENT '附加数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_business_operation` (`business_type`,`operation_type`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE,
  KEY `idx_operator_id` (`operator_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='联系人操作日志表';

-- ----------------------------
-- Table structure for crm_contact_team_members
-- ----------------------------
DROP TABLE IF EXISTS `crm_contact_team_members`;
CREATE TABLE `crm_contact_team_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID，关联crm_contacts表',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID，关联sys_user表',
  `role_type` varchar(20) NOT NULL DEFAULT 'member' COMMENT '角色类型：owner-负责人，admin-管理员，member-成员',
  `permissions` json DEFAULT NULL COMMENT '权限列表：["view","edit","delete","assign"]',
  `assigned_by` bigint(20) DEFAULT NULL COMMENT '分配人ID',
  `assigned_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `status` char(1) DEFAULT '0' COMMENT '状态：0-正常，1-停用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_contact_user` (`contact_id`,`user_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_role_type` (`role_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='联系人团队成员关系表';

-- ----------------------------
-- Table structure for crm_contract_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_contract_followup_records`;
CREATE TABLE `crm_contract_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
  `follow_up_content` text COMMENT '跟进内容',
  `execution_status` varchar(100) DEFAULT NULL COMMENT '执行状态',
  `milestone_progress` varchar(200) DEFAULT NULL COMMENT '里程碑进度',
  `risk_assessment` text COMMENT '风险评估',
  `payment_status` varchar(100) DEFAULT NULL COMMENT '付款状态',
  `next_review_date` datetime DEFAULT NULL COMMENT '下次审查时间',
  `delivery_progress` varchar(200) DEFAULT NULL COMMENT '交付进度',
  `customer_satisfaction` varchar(50) DEFAULT NULL COMMENT '客户满意度',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_contract_id` (`contract_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_execution_status` (`execution_status`) USING BTREE,
  KEY `idx_review_date` (`next_review_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='合同跟进记录表';

-- ----------------------------
-- Table structure for crm_customer_contact_relations
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_contact_relations`;
CREATE TABLE `crm_customer_contact_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `contact_id` int(11) NOT NULL COMMENT '联系人ID',
  `relation_type` varchar(50) DEFAULT NULL COMMENT '关系类型（主要联系人、次要联系人、决策人、财务联系人等）',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否主要联系人（0否 1是）',
  `start_date` date DEFAULT NULL COMMENT '关系开始日期',
  `end_date` date DEFAULT NULL COMMENT '关系结束日期',
  `status` varchar(20) DEFAULT 'active' COMMENT '关系状态（active有效 inactive无效）',
  `remarks` text COMMENT '关系备注',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_customer_contact` (`customer_id`,`contact_id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_is_primary` (`is_primary`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=894 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='客户联系人关联表';

-- ----------------------------
-- Table structure for crm_customer_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_followup_records`;
CREATE TABLE `crm_customer_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `follow_up_content` text COMMENT '跟进内容',
  `follow_up_type` varchar(100) DEFAULT NULL COMMENT '跟进类型',
  `business_stage` varchar(100) DEFAULT NULL COMMENT '业务阶段',
  `potential_value` decimal(15,2) DEFAULT NULL COMMENT '潜在价值',
  `cooperation_intention` varchar(200) DEFAULT NULL COMMENT '合作意向',
  `competitor_analysis` text COMMENT '竞争对手分析',
  `decision_maker_info` text COMMENT '决策人信息',
  `next_action_plan` text COMMENT '下一步行动计划',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_business_stage` (`business_stage`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='客户跟进记录表';

-- ----------------------------
-- Table structure for crm_customer_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_operation_log`;
CREATE TABLE `crm_customer_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型(CREATE,UPDATE,DELETE等)',
  `operation_content` text COMMENT '操作内容',
  `operation_details` json DEFAULT NULL COMMENT '操作细则，存储变更前后的值',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名称',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `extra_data` text COMMENT '附加数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_business_operation` (`business_type`,`operation_type`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='客户操作日志表';

-- ----------------------------
-- Table structure for crm_customer_followers
-- ----------------------------
DROP TABLE IF EXISTS `crm_customer_followers`;
CREATE TABLE `crm_customer_followers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `follower_id` bigint(20) NOT NULL COMMENT '关注者用户ID',
  `follow_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效关注（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_customer_follower` (`customer_id`,`follower_id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_follower_id` (`follower_id`) USING BTREE,
  KEY `idx_follow_time` (`follow_time`) USING BTREE,
  KEY `idx_is_active` (`is_active`) USING BTREE,
  KEY `idx_follower_active` (`follower_id`,`is_active`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='客户关注表';

-- ----------------------------
-- Table structure for crm_invoice_application
-- ----------------------------
DROP TABLE IF EXISTS `crm_invoice_application`;
CREATE TABLE `crm_invoice_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `application_no` varchar(50) NOT NULL COMMENT '申请编号',
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `reconciliation_ids` text COMMENT '关联对账单ID列表,逗号分隔',
  `invoice_type` varchar(20) NOT NULL COMMENT '发票类型:special-专用发票,ordinary-普通发票,electronic-电子发票',
  `invoice_amount` decimal(15,2) NOT NULL COMMENT '开票金额',
  `tax_rate` decimal(5,2) NOT NULL COMMENT '税率',
  `tax_amount` decimal(15,2) NOT NULL COMMENT '税额',
  `total_amount` decimal(15,2) NOT NULL COMMENT '价税合计',
  `invoice_date` date NOT NULL COMMENT '开票日期',
  `invoice_title` varchar(200) NOT NULL COMMENT '发票抬头',
  `tax_no` varchar(50) DEFAULT NULL COMMENT '纳税人识别号',
  `bank_account` varchar(100) DEFAULT NULL COMMENT '开户行及账号',
  `address_phone` varchar(200) DEFAULT NULL COMMENT '地址电话',
  `application_status` varchar(20) DEFAULT 'draft' COMMENT '申请状态:draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,invoiced-已开票,cancelled-已取消',
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'invoice-application-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `approval_notes` text COMMENT '审批备注',
  `approved_by` bigint(20) DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `invoice_no` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `invoice_code` varchar(20) DEFAULT NULL COMMENT '发票代码',
  `invoice_time` datetime DEFAULT NULL COMMENT '开票时间',
  `invoice_user_id` bigint(20) DEFAULT NULL COMMENT '开票人ID',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`application_id`) USING BTREE,
  UNIQUE KEY `uk_application_no` (`application_no`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_application_status` (`application_status`) USING BTREE,
  KEY `idx_process_instance_id` (`process_instance_id`) USING BTREE,
  KEY `idx_current_assignee` (`current_assignee`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='开票申请表';

-- ----------------------------
-- Table structure for crm_lead_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_followup_records`;
CREATE TABLE `crm_lead_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `follow_up_content` text COMMENT '跟进内容',
  `lead_status` varchar(100) DEFAULT NULL COMMENT '线索状态',
  `qualification_level` varchar(50) DEFAULT NULL COMMENT '资质等级',
  `estimated_value` decimal(15,2) DEFAULT NULL COMMENT '预估价值',
  `conversion_probability` varchar(50) DEFAULT NULL COMMENT '转化概率',
  `expected_conversion_date` datetime DEFAULT NULL COMMENT '预期转化时间',
  `contact_preference` varchar(100) DEFAULT NULL COMMENT '联系偏好',
  `pain_points` text COMMENT '痛点分析',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lead_id` (`lead_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_lead_status` (`lead_status`) USING BTREE,
  KEY `idx_conversion_date` (`expected_conversion_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='线索跟进记录表';

-- ----------------------------
-- Table structure for crm_lead_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_operation_log`;
CREATE TABLE `crm_lead_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型(CREATE,UPDATE,DELETE,CONVERT等)',
  `operation_content` text COMMENT '操作内容',
  `operation_details` json DEFAULT NULL COMMENT '操作细则，存储变更前后的值',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人名称',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `extra_data` text COMMENT '附加数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lead_id` (`lead_id`) USING BTREE,
  KEY `idx_business_operation` (`business_type`,`operation_type`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='线索操作日志表';

-- ----------------------------
-- Table structure for crm_menu
-- ----------------------------
DROP TABLE IF EXISTS `crm_menu`;
CREATE TABLE `crm_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `is_frame` char(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` char(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `enabled` char(1) DEFAULT '0' COMMENT '是否启用（0启用 1禁用）',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2009 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CRM菜单权限表';

-- ----------------------------
-- Table structure for crm_opportunity_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_opportunity_followup_records`;
CREATE TABLE `crm_opportunity_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `opportunity_id` bigint(20) NOT NULL COMMENT '商机ID',
  `follow_up_content` text COMMENT '跟进内容',
  `opportunity_stage` varchar(100) DEFAULT NULL COMMENT '商机阶段',
  `win_probability` decimal(5,2) DEFAULT NULL COMMENT '赢单概率',
  `competitor_info` varchar(500) DEFAULT NULL COMMENT '竞争对手信息',
  `negotiation_points` text COMMENT '谈判要点',
  `expected_close_date` datetime DEFAULT NULL COMMENT '预期成交时间',
  `budget_confirmed` tinyint(1) DEFAULT '0' COMMENT '预算是否确认',
  `decision_timeline` varchar(200) DEFAULT NULL COMMENT '决策时间线',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_opportunity_id` (`opportunity_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_opportunity_stage` (`opportunity_stage`) USING BTREE,
  KEY `idx_close_date` (`expected_close_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='商机跟进记录表';

-- ----------------------------
-- Table structure for crm_order
-- ----------------------------
DROP TABLE IF EXISTS `crm_order`;
CREATE TABLE `crm_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号',
  `quote_no` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '询价单号',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户名称',
  `contact_person` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `delivery_address` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货地址',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '订单总金额',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '订单状态:pending-待处理,confirmed-已确认,producing-生产中,completed-已完成,cancelled-已取消',
  `estimated_delivery_date` datetime DEFAULT NULL COMMENT '预计发货日期',
  `actual_delivery_date` datetime DEFAULT NULL COMMENT '实际发货日期',
  `remarks` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_crm_order_customer_name` (`customer_name`),
  KEY `idx_crm_order_contact_person` (`contact_person`),
  KEY `idx_crm_order_estimated_delivery` (`estimated_delivery_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='3D打印订单表';

-- ----------------------------
-- Table structure for crm_order_item
-- ----------------------------
DROP TABLE IF EXISTS `crm_order_item`;
CREATE TABLE `crm_order_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `model_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型名称',
  `model_file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型文件URL',
  `dimensions` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型尺寸',
  `volume` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型体积',
  `surface_area` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型表面积',
  `material_id` bigint(20) DEFAULT NULL COMMENT '材料ID',
  `material_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '材料名称',
  `quantity` int(11) DEFAULT '1' COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '总价',
  `process_options` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '后处理选项',
  `remarks` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_crm_order_item_model_name` (`model_name`),
  KEY `idx_crm_order_item_material_name` (`material_name`),
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `crm_order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='3D打印订单项表';

-- ----------------------------
-- Table structure for crm_payment_followup_records
-- ----------------------------
DROP TABLE IF EXISTS `crm_payment_followup_records`;
CREATE TABLE `crm_payment_followup_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `payment_id` bigint(20) NOT NULL COMMENT '回款ID',
  `follow_up_content` text COMMENT '跟进内容',
  `payment_status` varchar(100) DEFAULT NULL COMMENT '回款状态',
  `outstanding_amount` decimal(15,2) DEFAULT NULL COMMENT '未回款金额',
  `collection_method` varchar(100) DEFAULT NULL COMMENT '催收方式',
  `delay_reason` text COMMENT '延迟原因',
  `expected_payment_date` datetime DEFAULT NULL COMMENT '预期回款时间',
  `collection_difficulty` varchar(50) DEFAULT NULL COMMENT '催收难度',
  `legal_action_required` tinyint(1) DEFAULT '0' COMMENT '是否需要法律行动',
  `related_files` json DEFAULT NULL COMMENT '相关文件',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_payment_id` (`payment_id`) USING BTREE,
  KEY `idx_creator_time` (`creator_id`,`created_at`) USING BTREE,
  KEY `idx_payment_status` (`payment_status`) USING BTREE,
  KEY `idx_expected_date` (`expected_payment_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='回款跟进记录表';

-- ----------------------------
-- Table structure for crm_payment_record
-- ----------------------------
DROP TABLE IF EXISTS `crm_payment_record`;
CREATE TABLE `crm_payment_record` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '回款ID',
  `payment_no` varchar(50) NOT NULL COMMENT '回款编号',
  `reconciliation_id` bigint(20) DEFAULT NULL COMMENT '对账单ID（可为空，支持独立回款）',
  `reconciliation_no` varchar(50) DEFAULT NULL COMMENT '对账单编号',
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '回款金额',
  `payment_method` varchar(20) NOT NULL COMMENT '回款方式:bank_transfer-银行转账,check-支票,cash-现金,draft-承兑汇票',
  `payment_date` date NOT NULL COMMENT '回款日期',
  `bank_info` varchar(200) DEFAULT NULL COMMENT '银行信息',
  `voucher_no` varchar(50) DEFAULT NULL COMMENT '凭证号码',
  `payment_status` varchar(20) DEFAULT 'draft' COMMENT '回款状态:draft-草稿,submitted-已提交,confirmed-已确认,cancelled-已取消',
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'payment-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `allocation_details` text COMMENT '分配明细JSON',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`payment_id`) USING BTREE,
  UNIQUE KEY `uk_payment_no` (`payment_no`) USING BTREE,
  KEY `idx_reconciliation_id` (`reconciliation_id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_payment_date` (`payment_date`) USING BTREE,
  KEY `idx_process_instance_id` (`process_instance_id`) USING BTREE,
  KEY `idx_current_assignee` (`current_assignee`) USING BTREE,
  CONSTRAINT `fk_payment_reconciliation` FOREIGN KEY (`reconciliation_id`) REFERENCES `crm_reconciliation` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='回款记录表';

-- ----------------------------
-- Table structure for crm_product
-- ----------------------------
DROP TABLE IF EXISTS `crm_product`;
CREATE TABLE `crm_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '产品价格',
  `image_url` text COLLATE utf8mb4_unicode_ci COMMENT '产品图片URL(JSON格式)',
  `product_link` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品链接',
  `material_properties` text COLLATE utf8mb4_unicode_ci COMMENT '材料属性',
  `material_process` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '材料工艺',
  `tech_specs` text COLLATE utf8mb4_unicode_ci COMMENT '技术规格(JSON格式)',
  `advantages` text COLLATE utf8mb4_unicode_ci COMMENT '优势',
  `disadvantages` text COLLATE utf8mb4_unicode_ci COMMENT '劣势',
  `application_areas` text COLLATE utf8mb4_unicode_ci COMMENT '应用领域',
  `type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- ----------------------------
-- Table structure for crm_products
-- ----------------------------
DROP TABLE IF EXISTS `crm_products`;
CREATE TABLE `crm_products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(255) NOT NULL COMMENT '产品名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '产品价格',
  `image_url` varchar(4096) DEFAULT NULL COMMENT '产品图片URL',
  `product_link` varchar(500) DEFAULT NULL COMMENT '产品链接',
  `material_properties` text COMMENT '材料特性',
  `material_process` text COMMENT '材料工艺',
  `tech_specs` text COMMENT '技术参数(JSON格式)',
  `advantages` text COMMENT '产品优点',
  `disadvantages` text COMMENT '产品缺点',
  `application_areas` text COMMENT '应用领域(逗号分隔)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` varchar(50) DEFAULT NULL COMMENT '产品类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name` (`name`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品表';

-- ----------------------------
-- Table structure for crm_reconciliation
-- ----------------------------
DROP TABLE IF EXISTS `crm_reconciliation`;
CREATE TABLE `crm_reconciliation` (
  `reconciliation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '对账单ID',
  `reconciliation_no` varchar(50) NOT NULL COMMENT '对账单编号',
  `relation_type` varchar(10) NOT NULL DEFAULT 'customer' COMMENT '关联类型:customer-客户,contact-联系人',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID（当relation_type=customer时使用）',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID（当relation_type=contact时使用）',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
  `contact_customer_id` bigint(20) DEFAULT NULL COMMENT '联系人所属客户ID（当relation_type=contact时）',
  `contact_customer_name` varchar(100) DEFAULT NULL COMMENT '联系人所属客户名称',
  `reconciliation_period` varchar(20) DEFAULT NULL COMMENT '对账周期',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '订单总金额',
  `manual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '手动添加金额',
  `subtotal_amount` decimal(15,2) DEFAULT '0.00' COMMENT '小计金额',
  `prepayment_amount` decimal(15,2) DEFAULT '0.00' COMMENT '预收款抵扣金额',
  `net_amount` decimal(15,2) DEFAULT '0.00' COMMENT '净对账金额',
  `paid_amount` decimal(15,2) DEFAULT '0.00' COMMENT '已回款金额',
  `outstanding_amount` decimal(15,2) DEFAULT '0.00' COMMENT '待回款金额',
  `status` varchar(20) DEFAULT 'draft' COMMENT '业务状态:draft-草稿,submitted-已提交,approved-已审核,rejected-已驳回,invoiced-已开票,paid-已回款,completed-已完成,cancelled-已取消',
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT 'Activiti流程实例ID',
  `process_definition_key` varchar(64) DEFAULT 'reconciliation-approval' COMMENT '流程定义Key',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态:not_started-未启动,running-运行中,suspended-已挂起,completed-已完成,terminated-已终止',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `process_start_time` datetime DEFAULT NULL COMMENT '流程启动时间',
  `process_end_time` datetime DEFAULT NULL COMMENT '流程结束时间',
  `approval_status` varchar(20) DEFAULT 'pending' COMMENT '审批状态:pending-待审批,approved-已通过,rejected-已驳回',
  `approval_notes` text COMMENT '审批备注',
  `approved_by` bigint(20) DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `responsible_user_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(50) NOT NULL COMMENT '负责人姓名',
  `team_id` bigint(20) DEFAULT NULL COMMENT '团队ID',
  `team_name` varchar(50) DEFAULT NULL COMMENT '团队名称',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`reconciliation_id`) USING BTREE,
  UNIQUE KEY `uk_reconciliation_no` (`reconciliation_no`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_process_instance_id` (`process_instance_id`) USING BTREE,
  KEY `idx_process_status` (`process_status`) USING BTREE,
  KEY `idx_current_assignee` (`current_assignee`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_responsible_user` (`responsible_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='对账单主表';

-- ----------------------------
-- Table structure for crm_reconciliation_detail
-- ----------------------------
DROP TABLE IF EXISTS `crm_reconciliation_detail`;
CREATE TABLE `crm_reconciliation_detail` (
  `detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `reconciliation_id` bigint(20) NOT NULL COMMENT '对账单ID',
  `detail_type` varchar(20) NOT NULL COMMENT '明细类型:order-订单,manual-手动添加',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `item_description` text COMMENT '项目描述',
  `quantity` decimal(10,2) DEFAULT '1.00' COMMENT '数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(15,2) DEFAULT '0.00' COMMENT '单价',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率',
  `tax_amount` decimal(15,2) DEFAULT '0.00' COMMENT '税额',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '含税总额',
  `remark` text COMMENT '备注',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`detail_id`) USING BTREE,
  KEY `idx_reconciliation_id` (`reconciliation_id`) USING BTREE,
  KEY `idx_order_id` (`order_id`) USING BTREE,
  CONSTRAINT `fk_reconciliation_detail_main` FOREIGN KEY (`reconciliation_id`) REFERENCES `crm_reconciliation` (`reconciliation_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='对账单明细表';

-- ----------------------------
-- Table structure for crm_reconciliation_process_history
-- ----------------------------
DROP TABLE IF EXISTS `crm_reconciliation_process_history`;
CREATE TABLE `crm_reconciliation_process_history` (
  `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型:reconciliation-对账单,invoice-开票申请,payment-回款记录',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `business_no` varchar(50) NOT NULL COMMENT '业务编号',
  `process_instance_id` varchar(64) NOT NULL COMMENT 'Activiti流程实例ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
  `task_name` varchar(100) DEFAULT NULL COMMENT '任务名称',
  `action_type` varchar(20) NOT NULL COMMENT '操作类型:submit-提交,approve-通过,reject-驳回,cancel-取消,complete-完成',
  `action_user_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `action_user_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `action_comment` text COMMENT '操作意见',
  `from_status` varchar(20) DEFAULT NULL COMMENT '变更前状态',
  `to_status` varchar(20) DEFAULT NULL COMMENT '变更后状态',
  `form_data` text COMMENT '表单数据JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`history_id`) USING BTREE,
  KEY `idx_business_type_id` (`business_type`,`business_id`) USING BTREE,
  KEY `idx_process_instance_id` (`process_instance_id`) USING BTREE,
  KEY `idx_action_user` (`action_user_id`) USING BTREE,
  KEY `idx_action_time` (`action_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='流程操作历史表';

-- ----------------------------
-- Table structure for crm_reconciliation_task_todo
-- ----------------------------
DROP TABLE IF EXISTS `crm_reconciliation_task_todo`;
CREATE TABLE `crm_reconciliation_task_todo` (
  `todo_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '待办ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型:reconciliation-对账单,invoice-开票申请,payment-回款记录',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `business_no` varchar(50) NOT NULL COMMENT '业务编号',
  `business_title` varchar(200) NOT NULL COMMENT '业务标题',
  `process_instance_id` varchar(64) NOT NULL COMMENT 'Activiti流程实例ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_definition_key` varchar(64) NOT NULL COMMENT '任务定义Key',
  `assignee` varchar(50) NOT NULL COMMENT '处理人',
  `candidate_users` text COMMENT '候选人列表',
  `candidate_groups` text COMMENT '候选组列表',
  `priority` int(11) DEFAULT '50' COMMENT '优先级',
  `task_create_time` datetime NOT NULL COMMENT '任务创建时间',
  `due_date` datetime DEFAULT NULL COMMENT '到期时间',
  `form_key` varchar(100) DEFAULT NULL COMMENT '表单Key',
  `is_read` char(1) DEFAULT '0' COMMENT '是否已读:0-未读,1-已读',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`todo_id`) USING BTREE,
  UNIQUE KEY `uk_task_id` (`task_id`) USING BTREE,
  KEY `idx_business_type_id` (`business_type`,`business_id`) USING BTREE,
  KEY `idx_assignee` (`assignee`) USING BTREE,
  KEY `idx_task_create_time` (`task_create_time`) USING BTREE,
  KEY `idx_is_read` (`is_read`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='流程任务待办表';

-- ----------------------------
-- Table structure for crm_static_files
-- ----------------------------
DROP TABLE IF EXISTS `crm_static_files`;
CREATE TABLE `crm_static_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `module_type` varchar(50) NOT NULL COMMENT '所属模块（如：customer/lead/contract等）',
  `module_id` bigint(20) NOT NULL COMMENT '关联模块记录ID',
  `storage_type` varchar(20) NOT NULL COMMENT '存储方式（oss/local）',
  `file_url` varchar(500) NOT NULL COMMENT '文件访问链接',
  `file_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_extension` varchar(20) NOT NULL COMMENT '文件扩展名',
  `md5_hash` varchar(32) NOT NULL COMMENT '文件MD5值',
  `metadata` json DEFAULT NULL COMMENT '文件元数据(JSON格式)',
  `description` varchar(500) DEFAULT NULL COMMENT '文件描述',
  `tags` varchar(255) DEFAULT NULL COMMENT '文件标签(逗号分隔)',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开(0私有 1公开)',
  `download_count` int(11) DEFAULT '0' COMMENT '下载次数',
  `view_count` int(11) DEFAULT '0' COMMENT '查看次数',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_module_type` (`module_type`) USING BTREE,
  KEY `idx_module_id` (`module_id`) USING BTREE,
  KEY `idx_storage_type` (`storage_type`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='静态文件记录表';

-- ----------------------------
-- Table structure for crm_teams
-- ----------------------------
DROP TABLE IF EXISTS `crm_teams`;
CREATE TABLE `crm_teams` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `team_name` varchar(100) NOT NULL COMMENT '团队名称',
  `team_code` varchar(255) DEFAULT NULL COMMENT '团队编码',
  `leader_id` bigint(20) DEFAULT NULL COMMENT '团队负责人ID (关联sys_user.user_id)',
  `description` text COMMENT '团队描述',
  `team_type` varchar(255) DEFAULT NULL COMMENT '团队类型',
  `status` char(1) DEFAULT '0' COMMENT '状态：0-正常，1-停用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_team_name` (`team_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CRM团队表';

-- ----------------------------
-- Table structure for crm_team_members
-- ----------------------------
DROP TABLE IF EXISTS `crm_team_members`;
CREATE TABLE `crm_team_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID (关联sys_user.user_id)',
  `role_type` varchar(20) DEFAULT NULL COMMENT '角色类型：owner-负责人，admin-管理员，member-成员',
  `role_in_team` varchar(50) DEFAULT 'member' COMMENT '在团队中的角色 (如: member, admin)',
  `join_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` char(1) DEFAULT '0' COMMENT '状态：0-正常，1-停用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_team_user` (`team_id`,`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CRM团队成员关系表';

-- ----------------------------
-- Table structure for crm_team_relation
-- ----------------------------
DROP TABLE IF EXISTS `crm_team_relation`;
CREATE TABLE `crm_team_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `relation_type` varchar(50) NOT NULL COMMENT '关联对象类型 (例如: CONTACT, OPPORTUNITY, CUSTOMER)',
  `relation_id` bigint(20) NOT NULL COMMENT '关联对象ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_team_relation` (`team_id`,`relation_type`,`relation_id`) USING BTREE COMMENT '团队与业务对象的唯一关联'
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='CRM团队与业务对象关联表';

-- ----------------------------
-- Table structure for crm_team_relations
-- ----------------------------
DROP TABLE IF EXISTS `crm_team_relations`;
CREATE TABLE `crm_team_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `biz_id` bigint(20) NOT NULL COMMENT '业务主键ID (例如: crm_contacts.id)',
  `biz_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务类型 (例如: CONTACT, LEAD, CUSTOMER, OPPORTUNITY)',
  `relation_status` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '关联状态：0-正常，1-停用',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_biz` (`team_id`,`biz_id`,`biz_type`) COMMENT '团队-业务唯一约束',
  KEY `idx_biz` (`biz_id`,`biz_type`) COMMENT '业务查询索引',
  KEY `idx_team_id` (`team_id`) COMMENT '团队查询索引',
  KEY `idx_biz_type` (`biz_type`) COMMENT '业务类型索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用团队业务关联表';

-- ----------------------------
-- Table structure for crm_thirdparty_wechat
-- ----------------------------
DROP TABLE IF EXISTS `crm_thirdparty_wechat`;
CREATE TABLE `crm_thirdparty_wechat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '系统用户ID',
  `wecom_user_id` varchar(100) NOT NULL COMMENT '企业微信用户唯一标识',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_wecom_user_id` (`wecom_user_id`) USING BTREE,
  UNIQUE KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='企业微信第三方登录关联表';

-- ----------------------------
-- Table structure for crm_user_hierarchy
-- ----------------------------
DROP TABLE IF EXISTS `crm_user_hierarchy`;
CREATE TABLE `crm_user_hierarchy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `superior_id` bigint(20) NOT NULL COMMENT '上级用户ID',
  `hierarchy_level` int(11) DEFAULT '1' COMMENT '层级深度',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_superior` (`user_id`,`superior_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_superior_id` (`superior_id`) USING BTREE,
  KEY `idx_hierarchy_level` (`hierarchy_level`) USING BTREE,
  KEY `idx_del_flag` (`del_flag`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='CRM用户层级关系表';

-- ----------------------------
-- Table structure for crm_visit_plans
-- ----------------------------
DROP TABLE IF EXISTS `crm_visit_plans`;
CREATE TABLE `crm_visit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_name` varchar(200) NOT NULL COMMENT '拜访计划名称',
  `visit_time` datetime NOT NULL COMMENT '预计拜访时间',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID，外键关联crm_business_customers.id',
  `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称（冗余）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID，外键关联crm_business_contacts.id',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余）',
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '商机ID，外键关联crm_business_opportunities.id',
  `opportunity_name` varchar(200) DEFAULT NULL COMMENT '商机名称（冗余）',
  `visit_purpose` text COMMENT '拜访目的',
  `remind_time` int(11) DEFAULT '30' COMMENT '提前提醒时间（分钟）',
  `remark` text COMMENT '备注',
  `postpone_reason` varchar(500) DEFAULT NULL COMMENT '延期原因',
  `postpone_remark` text COMMENT '延期备注',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
  `cancel_remark` text COMMENT '取消备注',
  `followup_content` text COMMENT '跟进记录内容',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID，外键关联sys_user.user_id',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `status` varchar(20) NOT NULL DEFAULT 'planned' COMMENT '状态：planned-计划中,ongoing-进行中,completed-已完成,postponed-已延期,cancelled-已取消',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称（冗余）',
  `actual_visit_time` datetime DEFAULT NULL COMMENT '实际拜访时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_visit_time` (`visit_time`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_contact_id` (`contact_id`) USING BTREE,
  KEY `idx_opportunity_id` (`opportunity_id`) USING BTREE,
  KEY `idx_owner_id` (`owner_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_del_flag` (`del_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拜访计划表';

-- ----------------------------
-- Table structure for crm_visit_plan_logs
-- ----------------------------
DROP TABLE IF EXISTS `crm_visit_plan_logs`;
CREATE TABLE `crm_visit_plan_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '变更原因',
  `change_remark` text COMMENT '变更备注',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型：create-创建,update-更新,postpone-延期,cancel-取消,complete-完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_visit_plan_id` (`visit_plan_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_operator_id` (`operator_id`) USING BTREE,
  KEY `idx_operation_type` (`operation_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拜访计划状态变更日志表';

-- ----------------------------
-- Table structure for crm_visit_plan_reminders
-- ----------------------------
DROP TABLE IF EXISTS `crm_visit_plan_reminders`;
CREATE TABLE `crm_visit_plan_reminders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `remind_type` varchar(20) NOT NULL COMMENT '提醒类型：system-系统内,wechat-企业微信,sms-短信,email-邮件',
  `remind_time` datetime NOT NULL COMMENT '提醒时间',
  `remind_status` varchar(20) DEFAULT 'pending' COMMENT '提醒状态：pending-待发送,sent-已发送,failed-发送失败',
  `send_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `recipient_id` bigint(20) DEFAULT NULL COMMENT '接收人ID',
  `recipient_name` varchar(100) DEFAULT NULL COMMENT '接收人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_visit_plan_id` (`visit_plan_id`) USING BTREE,
  KEY `idx_remind_time` (`remind_time`) USING BTREE,
  KEY `idx_remind_status` (`remind_status`) USING BTREE,
  KEY `idx_remind_type` (`remind_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拜访计划提醒记录表';

-- ----------------------------
-- Table structure for crm_wecom_config
-- ----------------------------
DROP TABLE IF EXISTS `crm_wecom_config`;
CREATE TABLE `crm_wecom_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `corp_id` varchar(100) NOT NULL COMMENT '企业ID',
  `corp_secret` varchar(100) NOT NULL COMMENT '应用密钥',
  `agent_id` varchar(100) NOT NULL COMMENT '应用ID',
  `api_base_url` varchar(255) DEFAULT NULL COMMENT 'API基础URL',
  `redirect_uri` varchar(255) DEFAULT NULL COMMENT '回调URI',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='企业微信配置表';

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table` (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='代码生成业务表';

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column` (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=231 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='代码生成业务表字段';

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='Blob类型的触发器表';

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='日历信息表';

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='Cron类型的触发器表';

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint(13) NOT NULL COMMENT '触发的时间',
  `sched_time` bigint(13) NOT NULL COMMENT '定时器制定的时间',
  `priority` int(11) NOT NULL COMMENT '优先级',
  `state` varchar(16) NOT NULL COMMENT '状态',
  `job_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='已触发的触发器表';

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) NOT NULL COMMENT '任务组名',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务详细信息表';

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='存储的悲观锁信息表';

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='暂停的触发器表';

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint(13) NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint(13) NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='调度器状态表';

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint(7) NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint(12) NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint(10) NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='简单触发器的信息表';

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int(11) DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int(11) DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint(20) DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint(20) DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='同步机制的行锁表';

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint(13) DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint(13) DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int(11) DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) NOT NULL COMMENT '触发器的类型',
  `start_time` bigint(13) NOT NULL COMMENT '开始时间',
  `end_time` bigint(13) DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint(2) DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='触发器详细信息表';

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='参数配置表';

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='部门表';

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='字典数据表';

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE KEY `dict_type` (`dict_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='字典类型表';

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定时任务调度表';

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
  `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=675 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定时任务调度日志表';

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  KEY `idx_sys_logininfor_s` (`status`) USING BTREE,
  KEY `idx_sys_logininfor_lt` (`login_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=323 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统访问记录';

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1204 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice` (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_type` char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='通知公告表';

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
  `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  KEY `idx_sys_oper_log_bt` (`business_type`) USING BTREE,
  KEY `idx_sys_oper_log_s` (`status`) USING BTREE,
  KEY `idx_sys_oper_log_ot` (`oper_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=228 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='操作日志记录';

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post` (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='岗位信息表';

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色信息表';

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色和部门关联表';

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色和菜单关联表';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户与岗位关联表';

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户和角色关联表';

-- ----------------------------
-- View structure for v_customer_contacts
-- ----------------------------
DROP VIEW IF EXISTS `v_customer_contacts`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_customer_contacts` AS select `c`.`id` AS `customer_id`,`c`.`customer_name` AS `customer_name`,`ct`.`id` AS `contact_id`,`ct`.`name` AS `contact_name`,`ct`.`mobile` AS `mobile`,`ct`.`email` AS `email`,`ct`.`position` AS `position`,`r`.`relation_type` AS `relation_type`,`r`.`is_primary` AS `is_primary`,`r`.`status` AS `relation_status`,`r`.`start_date` AS `start_date`,`r`.`end_date` AS `end_date` from ((`crm_business_customers` `c` join `crm_customer_contact_relations` `r` on((`c`.`id` = `r`.`customer_id`))) join `crm_business_contacts` `ct` on((`r`.`contact_id` = `ct`.`id`))) where ((`r`.`del_flag` = '0') and (`c`.`del_flag` = '0') and (`ct`.`del_flag` = '0')) ;

-- ----------------------------
-- View structure for v_user_followed_contacts_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_user_followed_contacts_stats`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_user_followed_contacts_stats` AS select `crm_contact_followers`.`follower_id` AS `follower_id`,count(0) AS `followed_count`,count((case when (`crm_contact_followers`.`is_active` = 1) then 1 end)) AS `active_followed_count`,max(`crm_contact_followers`.`follow_time`) AS `latest_follow_time` from `crm_contact_followers` group by `crm_contact_followers`.`follower_id` ;

-- ----------------------------
-- View structure for v_user_hierarchy_relations
-- ----------------------------
DROP VIEW IF EXISTS `v_user_hierarchy_relations`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_user_hierarchy_relations` AS select `h`.`user_id` AS `user_id`,`h`.`superior_id` AS `superior_id`,`u1`.`user_name` AS `user_name`,`u2`.`user_name` AS `superior_name`,`h`.`hierarchy_level` AS `hierarchy_level`,`h`.`create_time` AS `create_time` from ((`crm_user_hierarchy` `h` left join `sys_user` `u1` on((`h`.`user_id` = `u1`.`user_id`))) left join `sys_user` `u2` on((`h`.`superior_id` = `u2`.`user_id`))) where ((`h`.`del_flag` = '0') and (`u1`.`del_flag` = '0') and (`u2`.`del_flag` = '0')) ;

-- ----------------------------
-- View structure for v_visit_plan_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_visit_plan_statistics`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_visit_plan_statistics` AS select `crm_visit_plans`.`owner_id` AS `owner_id`,`crm_visit_plans`.`owner_name` AS `owner_name`,`crm_visit_plans`.`dept_id` AS `dept_id`,`crm_visit_plans`.`dept_name` AS `dept_name`,count(0) AS `total_plans`,sum((case when (`crm_visit_plans`.`status` = 'planned') then 1 else 0 end)) AS `planned_count`,sum((case when (`crm_visit_plans`.`status` = 'ongoing') then 1 else 0 end)) AS `ongoing_count`,sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)) AS `completed_count`,sum((case when (`crm_visit_plans`.`status` = 'postponed') then 1 else 0 end)) AS `postponed_count`,sum((case when (`crm_visit_plans`.`status` = 'cancelled') then 1 else 0 end)) AS `cancelled_count`,round(((sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)) * 100.0) / count(0)),2) AS `completion_rate`,round(((sum((case when ((`crm_visit_plans`.`status` = 'completed') and (`crm_visit_plans`.`actual_visit_time` <= `crm_visit_plans`.`visit_time`)) then 1 else 0 end)) * 100.0) / nullif(sum((case when (`crm_visit_plans`.`status` = 'completed') then 1 else 0 end)),0)),2) AS `on_time_rate` from `crm_visit_plans` where (`crm_visit_plans`.`del_flag` = '0') group by `crm_visit_plans`.`owner_id`,`crm_visit_plans`.`owner_name`,`crm_visit_plans`.`dept_id`,`crm_visit_plans`.`dept_name` ;
