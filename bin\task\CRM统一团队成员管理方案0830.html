<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM统一团队成员管理方案</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        /* 代码高亮样式 */
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .sql-keyword { color: #63b3ed; font-weight: bold; }
        .sql-string { color: #68d391; }
        .sql-comment { color: #a0aec0; font-style: italic; }
        .sql-number { color: #f6ad55; }
        .java-keyword { color: #63b3ed; font-weight: bold; }
        .java-annotation { color: #f6ad55; }
        .java-string { color: #68d391; }
        .java-comment { color: #a0aec0; font-style: italic; }
        .java-number { color: #f6ad55; }
        .java-type { color: #81c784; }
        .java-identifier { color: #e2e8f0; }
        .yaml-key { color: #63b3ed; font-weight: bold; }
        .yaml-value { color: #68d391; }
        .yaml-comment { color: #a0aec0; font-style: italic; }
        .bash-keyword { color: #63b3ed; font-weight: bold; }
        .bash-string { color: #68d391; }
        .bash-comment { color: #a0aec0; font-style: italic; }
        .bash-variable { color: #f6ad55; }
        /* Mermaid图表容器 */
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        .status-completed {
            color: #27ae60;
            font-weight: bold;
        }
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        .priority-high {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-medium {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-low {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .risk-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .phase-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .architecture-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        /* 任务清单样式 */
        .phase-box ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .phase-box ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        .phase-box ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        .phase-box ul li label:hover {
            color: #007bff;
        }
        
        /* 验收标准样式 */
        h3 + ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        h3 + ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        h3 + ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        h3 + ul li label:hover {
            color: #007bff;
        }
        
        .update-note {
            background: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th {
            background-color: #3498db;
            color: white;
            text-align: center;
            padding: 12px;
            border: 1px solid #ddd;
        }
        
        .comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .comparison-table .feature {
            font-weight: bold;
            width: 25%;
        }
        
        .comparison-table .current {
            width: 35%;
            background-color: #fff3cd;
        }
        
        .comparison-table .proposed {
            width: 40%;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRM统一团队成员管理方案</h1>
        
        <h2>项目背景</h2>
        <p>随着CRM系统的不断发展，我们发现当前的团队成员管理方式存在一些问题。目前，联系人模块已经实现了团队成员管理功能，但其他实体（如客户、商机等）仍然使用单一负责人模式，这导致了数据结构不一致、权限管理有限、代码重复等问题。为了解决这些问题，我们提出了统一团队成员管理方案，旨在为所有CRM实体提供一致的团队协作管理能力。</p>
        
        <h2>现状分析</h2>
        
        <h3>联系人团队成员管理（现有）</h3>
        <ul>
            <li>已实现<code>crm_contact_team_members</code>表，存储联系人-用户关系</li>
            <li>支持多种角色类型：owner（负责人）、admin（管理员）、member（成员）</li>
            <li>支持JSON格式的权限配置，如查看、编辑、删除、分配等</li>
            <li>实现了<code>v_contact_team_permissions</code>视图，用于权限判断</li>
            <li>提供了完整的Service和Controller层实现</li>
        </ul>
        
        <h3>其他实体的负责人管理（现有）</h3>
        <ul>
            <li>客户（<code>crm_business_customers</code>）：使用<code>responsible_person_id</code>字段存储单一负责人</li>
            <li>商机（<code>crm_business_opportunities</code>）：使用<code>manager_id</code>字段存储单一负责人</li>
            <li>线索（<code>crm_business_leads</code>）：使用<code>responsible_person_id</code>字段存储单一负责人</li>
            <li>没有团队成员的概念，只有单一负责人</li>
            <li>权限控制相对简单，主要依赖于系统角色和数据范围</li>
        </ul>
        
        <h3>存在的问题</h3>
        <div class="risk-box">
            <ol>
                <li><strong>数据结构不一致</strong>：联系人使用团队成员表，其他实体使用单一负责人字段，导致数据结构不一致</li>
                <li><strong>权限管理有限</strong>：单一负责人模式无法满足复杂的协作场景，如多人协作、不同权限分配等</li>
                <li><strong>代码重复</strong>：如果为每个实体单独实现团队成员管理，将导致大量代码重复</li>
                <li><strong>扩展性差</strong>：当需要为新实体添加团队协作功能时，需要重复实现相似的逻辑</li>
                <li><strong>维护成本高</strong>：多套团队管理逻辑会增加系统维护成本</li>
                <li><strong>用户体验不一致</strong>：不同实体的团队管理方式不同，导致用户体验不一致</li>
            </ol>
        </div>
        
        <h2>解决方案</h2>
        
        <div class="success-box">
            <h3>核心思路</h3>
            <p>建立统一的团队成员管理框架，适用于所有需要团队协作的CRM实体（联系人、客户、商机等），提供一致的数据结构、API接口和权限控制机制。</p>
        </div>
        
        <h3>数据库设计</h3>
        
        <div class="mermaid">
erDiagram
    crm_team_members {
        bigint id PK "主键ID"
        varchar entity_type "实体类型"
        bigint entity_id "实体ID"
        bigint user_id FK "用户ID"
        varchar role_type "角色类型"
        json permissions "权限列表"
        bigint assigned_by FK "分配人ID"
        datetime assigned_at "分配时间"
        char status "状态"
        varchar create_by "创建者"
        datetime create_time "创建时间"
        varchar update_by "更新者"
        datetime update_time "更新时间"
        varchar remark "备注"
    }
    
    sys_user {{
        bigint user_id PK
        bigint dept_id FK
        varchar user_name
        varchar nick_name
        varchar email
        varchar phonenumber
        char sex
        varchar avatar
        varchar password
        char status
        char del_flag
        varchar login_ip
        datetime login_date
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
        varchar remark
    }}
    
    sys_role {{
        bigint role_id PK
        varchar role_name
        varchar role_key
        int role_sort
        char data_scope
        char status
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
        varchar remark
    }}
    
    sys_user_role {{
        bigint user_id FK
        bigint role_id FK
    }}
    
    crm_business_contacts {{
        bigint id PK
        varchar name
        varchar mobile
        varchar email
        varchar position
        tinyint is_key_decision_maker
        varchar direct_superior
        varchar address
        varchar detailed_address
        datetime next_contact_time
        date selected_date
        char gender
        text remarks
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
    }}
    
    crm_business_customers {{
        bigint id PK
        varchar name
        varchar industry
        varchar address
        varchar website
        varchar phone
        varchar email
        text description
        char status
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
    }}
    
    crm_business_opportunities {{
        bigint id PK
        varchar name
        bigint customer_id FK
        decimal amount
        varchar stage
        date expected_closing_date
        varchar source
        text description
        char status
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
    }}
    
    sys_user ||--o{ crm_team_members : "成员"
    sys_user ||--o{ crm_team_members : "分配人"
    sys_user ||--o{ sys_user_role : "拥有"
    sys_role ||--o{ sys_user_role : "分配给"
    crm_team_members }|--|| crm_business_contacts : "关联联系人"
    crm_team_members }|--|| crm_business_customers : "关联客户"
    crm_team_members }|--|| crm_business_opportunities : "关联商机"
        </div>
        
        <h4>1. 统一团队成员表</h4>
        <pre><code class="sql"><span class="sql-comment">-- 创建统一的团队成员表</span>
<span class="sql-keyword">CREATE TABLE</span> crm_team_members (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
    entity_type <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">50</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'实体类型：contact-联系人，customer-客户，opportunity-商机'</span>,
    entity_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'实体ID'</span>,
    user_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'用户ID，关联sys_user表'</span>,
    role_type <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL DEFAULT</span> <span class="sql-string">'member'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'角色类型：owner-负责人，admin-管理员，member-成员'</span>,
    permissions <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'权限列表：["view","edit","delete","assign"]'</span>,
    assigned_by <span class="sql-keyword">BIGINT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'分配人ID'</span>,
    assigned_at <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'分配时间'</span>,
    status <span class="sql-keyword">CHAR</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'0'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'状态：0-正常，1-停用'</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
    update_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新者'</span>,
    update_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
    remark <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">500</span>) <span class="sql-keyword">DEFAULT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'备注'</span>,
    
    <span class="sql-keyword">INDEX</span> idx_entity_type_id (entity_type, entity_id),
    <span class="sql-keyword">INDEX</span> idx_user_id (user_id),
    <span class="sql-keyword">INDEX</span> idx_role_type (role_type),
    <span class="sql-keyword">INDEX</span> idx_status (status),
    <span class="sql-keyword">UNIQUE KEY</span> uk_entity_user (entity_type, entity_id, user_id),
    
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (assigned_by) <span class="sql-keyword">REFERENCES</span> sys_user(user_id) <span class="sql-keyword">ON DELETE SET NULL</span>
) <span class="sql-keyword">ENGINE</span>=InnoDB <span class="sql-keyword">DEFAULT CHARSET</span>=utf8mb4 <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM团队成员关系表'</span>;</code></pre>
        
        <h4>2. 统一的权限查询视图</h4>
        <pre><code class="sql"><span class="sql-comment">-- 创建统一的权限查询视图</span>
<span class="sql-keyword">CREATE OR REPLACE VIEW</span> v_team_permissions <span class="sql-keyword">AS</span>
<span class="sql-keyword">SELECT</span> 
    tm.entity_type,
    tm.entity_id,
    tm.user_id,
    u.user_name,
    u.nick_name,
    tm.role_type,
    tm.permissions,
    tm.status,
    sr.role_name <span class="sql-keyword">as</span> system_role,
    sr.data_scope,
    <span class="sql-keyword">CASE</span> 
        <span class="sql-keyword">WHEN</span> sr.data_scope = <span class="sql-string">'1'</span> <span class="sql-keyword">THEN</span> <span class="sql-number">1</span>  <span class="sql-comment">-- 超级管理员</span>
        <span class="sql-keyword">WHEN</span> sr.data_scope = <span class="sql-string">'4'</span> <span class="sql-keyword">AND</span> tm.role_type = <span class="sql-string">'admin'</span> <span class="sql-keyword">THEN</span> <span class="sql-number">1</span>  <span class="sql-comment">-- 团队管理员</span>
        <span class="sql-keyword">WHEN</span> tm.role_type = <span class="sql-string">'owner'</span> <span class="sql-keyword">THEN</span> <span class="sql-number">1</span>  <span class="sql-comment">-- 负责人</span>
        <span class="sql-keyword">ELSE</span> <span class="sql-number">0</span>
    <span class="sql-keyword">END as</span> can_manage_team
<span class="sql-keyword">FROM</span> crm_team_members tm
<span class="sql-keyword">LEFT JOIN</span> sys_user u <span class="sql-keyword">ON</span> tm.user_id = u.user_id
<span class="sql-keyword">LEFT JOIN</span> sys_user_role sur <span class="sql-keyword">ON</span> u.user_id = sur.user_id
<span class="sql-keyword">LEFT JOIN</span> sys_role sr <span class="sql-keyword">ON</span> sur.role_id = sr.role_id
<span class="sql-keyword">WHERE</span> tm.status = <span class="sql-string">'0'</span> <span class="sql-keyword">AND</span> u.status = <span class="sql-string">'0'</span>;</code></pre>
        
        <h3>Java实体设计</h3>
        
        <h4>1. CrmTeamMember实体类</h4>
        <pre><code class="java"><span class="java-keyword">package</span> com.ruoyi.crm.domain;

<span class="java-keyword">import</span> com.fasterxml.jackson.annotation.JsonFormat;
<span class="java-keyword">import</span> com.ruoyi.common.annotation.Excel;
<span class="java-keyword">import</span> com.ruoyi.common.core.domain.BaseEntity;

<span class="java-keyword">import</span> java.util.Date;
<span class="java-keyword">import</span> java.util.List;

<span class="java-comment">/**
 * CRM团队成员对象 crm_team_members
 */</span>
<span class="java-keyword">public class</span> CrmTeamMember <span class="java-keyword">extends</span> BaseEntity {
    <span class="java-keyword">private static final long</span> serialVersionUID = 1L;

    <span class="java-comment">/** 主键ID */</span>
    <span class="java-keyword">private</span> Long id;

    <span class="java-comment">/** 实体类型 */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"实体类型"</span>)
    <span class="java-keyword">private</span> String entityType;

    <span class="java-comment">/** 实体ID */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"实体ID"</span>)
    <span class="java-keyword">private</span> Long entityId;

    <span class="java-comment">/** 用户ID */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"用户ID"</span>)
    <span class="java-keyword">private</span> Long userId;

    <span class="java-comment">/** 角色类型 */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"角色类型"</span>)
    <span class="java-keyword">private</span> String roleType;

    <span class="java-comment">/** 权限列表 */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"权限列表"</span>)
    <span class="java-keyword">private</span> List&lt;String&gt; permissions;

    <span class="java-comment">/** 分配人ID */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"分配人ID"</span>)
    <span class="java-keyword">private</span> Long assignedBy;

    <span class="java-comment">/** 分配时间 */</span>
    <span class="java-annotation">@JsonFormat</span>(pattern = <span class="java-string">"yyyy-MM-dd HH:mm:ss"</span>)
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"分配时间"</span>, width = 30, dateFormat = <span class="java-string">"yyyy-MM-dd HH:mm:ss"</span>)
    <span class="java-keyword">private</span> Date assignedAt;

    <span class="java-comment">/** 状态（0正常 1停用） */</span>
    <span class="java-annotation">@Excel</span>(name = <span class="java-string">"状态"</span>, readConverterExp = <span class="java-string">"0=正常,1=停用"</span>)
    <span class="java-keyword">private</span> String status;

    <span class="java-comment">// 非数据库字段</span>
    <span class="java-keyword">private</span> String userName;
    <span class="java-keyword">private</span> String nickName;
    <span class="java-keyword">private</span> String systemRole;
    <span class="java-keyword">private</span> String dataScope;
    <span class="java-keyword">private</span> Boolean canManageTeam;

    <span class="java-comment">// Getters and Setters...</span>
}</code></pre>
        
        <h4>2. CrmTeamMemberMapper接口</h4>
        <pre><code class="java"><span class="java-keyword">package</span> com.ruoyi.crm.mapper;

<span class="java-keyword">import</span> com.ruoyi.crm.domain.CrmTeamMember;
<span class="java-keyword">import</span> org.apache.ibatis.annotations.Param;

<span class="java-keyword">import</span> java.util.List;

<span class="java-comment">/**
 * 团队成员数据层
 */</span>
<span class="java-keyword">public interface</span> CrmTeamMemberMapper {
    
    <span class="java-comment">/**
     * 查询团队成员列表
     */</span>
    <span class="java-keyword">public</span> List&lt;CrmTeamMember&gt; selectTeamMemberList(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 根据实体类型和ID获取团队成员
     */</span>
    <span class="java-keyword">public</span> List&lt;CrmTeamMember&gt; selectTeamMembersByEntity(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"entityId"</span>) Long entityId);
    
    <span class="java-comment">/**
     * 根据ID查询团队成员
     */</span>
    <span class="java-keyword">public</span> CrmTeamMember selectTeamMemberById(Long id);
    
    <span class="java-comment">/**
     * 查询用户在实体中的角色
     */</span>
    <span class="java-keyword">public</span> CrmTeamMember selectUserRoleInEntity(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"entityId"</span>) Long entityId, @Param(<span class="java-string">"userId"</span>) Long userId);
    
    <span class="java-comment">/**
     * 新增团队成员
     */</span>
    <span class="java-keyword">public int</span> insertTeamMember(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 修改团队成员
     */</span>
    <span class="java-keyword">public int</span> updateTeamMember(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 删除团队成员
     */</span>
    <span class="java-keyword">public int</span> deleteTeamMemberById(Long id);
    
    <span class="java-comment">/**
     * 批量删除团队成员
     */</span>
    <span class="java-keyword">public int</span> deleteTeamMemberByIds(Long[] ids);
    
    <span class="java-comment">/**
     * 删除实体的所有团队成员
     */</span>
    <span class="java-keyword">public int</span> deleteTeamMembersByEntity(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"entityId"</span>) Long entityId);
    
    <span class="java-comment">/**
     * 检查用户是否有指定权限
     */</span>
    <span class="java-keyword">public</span> Boolean checkUserPermission(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"entityId"</span>) Long entityId, @Param(<span class="java-string">"userId"</span>) Long userId, @Param(<span class="java-string">"permission"</span>) String permission);
    
    <span class="java-comment">/**
     * 检查用户是否可以管理团队
     */</span>
    <span class="java-keyword">public</span> Boolean canManageTeam(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"entityId"</span>) Long entityId, @Param(<span class="java-string">"userId"</span>) Long userId);
    
    <span class="java-comment">/**
     * 获取用户可访问的实体ID列表
     */</span>
    <span class="java-keyword">public</span> List&lt;Long&gt; getAccessibleEntityIds(@Param(<span class="java-string">"entityType"</span>) String entityType, @Param(<span class="java-string">"userId"</span>) Long userId);
}</code></pre>
        
        <h4>3. ICrmTeamMemberService接口</h4>
        <pre><code class="java"><span class="java-keyword">package</span> com.ruoyi.crm.service;

<span class="java-keyword">import</span> com.ruoyi.crm.domain.CrmTeamMember;

<span class="java-keyword">import</span> java.util.List;

<span class="java-comment">/**
 * 团队成员服务接口
 */</span>
<span class="java-keyword">public interface</span> ICrmTeamMemberService {
    
    <span class="java-comment">/**
     * 查询团队成员列表
     */</span>
    <span class="java-keyword">public</span> List&lt;CrmTeamMember&gt; selectTeamMemberList(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 根据实体类型和ID获取团队成员
     */</span>
    <span class="java-keyword">public</span> List&lt;CrmTeamMember&gt; getTeamMembers(String entityType, Long entityId);
    
    <span class="java-comment">/**
     * 添加团队成员
     */</span>
    <span class="java-keyword">public int</span> addTeamMember(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 批量添加团队成员
     */</span>
    <span class="java-keyword">public int</span> batchAddMembers(String entityType, Long entityId, List&lt;Long&gt; userIds, String roleType);
    
    <span class="java-comment">/**
     * 更新成员角色
     */</span>
    <span class="java-keyword">public int</span> updateMemberRole(Long id, String roleType, List&lt;String&gt; permissions);
    
    <span class="java-comment">/**
     * 移除团队成员
     */</span>
    <span class="java-keyword">public int</span> removeTeamMember(Long id);
    
    <span class="java-comment">/**
     * 检查用户权限
     */</span>
    <span class="java-keyword">public boolean</span> hasPermission(String entityType, Long entityId, Long userId, String permission);
    
    <span class="java-comment">/**
     * 检查是否可以管理团队
     */</span>
    <span class="java-keyword">public boolean</span> canManageTeam(String entityType, Long entityId, Long userId);
    
    <span class="java-comment">/**
     * 获取用户可访问的实体ID列表
     */</span>
    <span class="java-keyword">public</span> List&lt;Long&gt; getAccessibleEntityIds(String entityType, Long userId);
}</code></pre>
        
        <h4>4. CrmTeamController类</h4>
        <pre><code class="java"><span class="java-keyword">package</span> com.ruoyi.crm.controller;

<span class="java-keyword">import</span> com.ruoyi.common.annotation.Log;
<span class="java-keyword">import</span> com.ruoyi.common.core.controller.BaseController;
<span class="java-keyword">import</span> com.ruoyi.common.core.domain.AjaxResult;
<span class="java-keyword">import</span> com.ruoyi.common.core.page.TableDataInfo;
<span class="java-keyword">import</span> com.ruoyi.common.enums.BusinessType;
<span class="java-keyword">import</span> com.ruoyi.common.utils.SecurityUtils;
<span class="java-keyword">import</span> com.ruoyi.crm.domain.CrmTeamMember;
<span class="java-keyword">import</span> com.ruoyi.crm.service.ICrmTeamMemberService;
<span class="java-keyword">import</span> org.springframework.beans.factory.annotation.Autowired;
<span class="java-keyword">import</span> org.springframework.security.access.prepost.PreAuthorize;
<span class="java-keyword">import</span> org.springframework.web.bind.annotation.*;

<span class="java-keyword">import</span> java.util.List;

<span class="java-comment">/**
 * 团队成员管理Controller
 */</span>
<span class="java-annotation">@RestController</span>
<span class="java-annotation">@RequestMapping</span>(<span class="java-string">"/crm/team"</span>)
<span class="java-keyword">public class</span> CrmTeamController <span class="java-keyword">extends</span> BaseController {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> ICrmTeamMemberService teamMemberService;
    
    <span class="java-comment">/**
     * 获取团队成员列表
     */</span>
    <span class="java-annotation">@PreAuthorize</span>(<span class="java-string">"@ss.hasPermi('crm:team:list')"</span>)
    <span class="java-annotation">@GetMapping</span>(<span class="java-string">"/list/{entityType}/{entityId}"</span>)
    <span class="java-keyword">public</span> TableDataInfo list(@PathVariable(<span class="java-string">"entityType"</span>) String entityType, 
                             @PathVariable(<span class="java-string">"entityId"</span>) Long entityId) {
        startPage();
        List&lt;CrmTeamMember&gt; list = teamMemberService.getTeamMembers(entityType, entityId);
        <span class="java-keyword">return</span> getDataTable(list);
    }
    
    <span class="java-comment">/**
     * 添加团队成员
     */</span>
    <span class="java-annotation">@PreAuthorize</span>(<span class="java-string">"@ss.hasPermi('crm:team:add')"</span>)
    <span class="java-annotation">@Log</span>(title = <span class="java-string">"团队成员管理"</span>, businessType = BusinessType.INSERT)
    <span class="java-annotation">@PostMapping</span>(<span class="java-string">"/add"</span>)
    <span class="java-keyword">public</span> AjaxResult add(<span class="java-annotation">@RequestBody</span> CrmTeamMember member) {
        <span class="java-comment">// 检查当前用户是否有权限管理团队</span>
        Long currentUserId = SecurityUtils.getUserId();
        <span class="java-keyword">if</span> (!teamMemberService.canManageTeam(member.getEntityType(), member.getEntityId(), currentUserId)) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"您没有权限管理该团队"</span>);
        }
        
        <span class="java-comment">// 设置分配人为当前用户</span>
        member.setAssignedBy(currentUserId);
        <span class="java-keyword">return</span> toAjax(teamMemberService.addTeamMember(member));
    }
    
    <span class="java-comment">/**
     * 批量添加团队成员
     */</span>
    <span class="java-annotation">@PreAuthorize</span>(<span class="java-string">"@ss.hasPermi('crm:team:add')"</span>)
    <span class="java-annotation">@Log</span>(title = <span class="java-string">"团队成员管理"</span>, businessType = BusinessType.INSERT)
    <span class="java-annotation">@PostMapping</span>(<span class="java-string">"/batchAdd/{entityType}/{entityId}"</span>)
    <span class="java-keyword">public</span> AjaxResult batchAdd(@PathVariable(<span class="java-string">"entityType"</span>) String entityType,
                              @PathVariable(<span class="java-string">"entityId"</span>) Long entityId,
                              <span class="java-annotation">@RequestBody</span> List&lt;Long&gt; userIds,
                              <span class="java-annotation">@RequestParam</span> String roleType) {
        <span class="java-comment">// 检查当前用户是否有权限管理团队</span>
        Long currentUserId = SecurityUtils.getUserId();
        <span class="java-keyword">if</span> (!teamMemberService.canManageTeam(entityType, entityId, currentUserId)) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"您没有权限管理该团队"</span>);
        }
        
        <span class="java-keyword">return</span> toAjax(teamMemberService.batchAddMembers(entityType, entityId, userIds, roleType));
    }
    
    <span class="java-comment">/**
     * 更新团队成员角色
     */</span>
    <span class="java-annotation">@PreAuthorize</span>(<span class="java-string">"@ss.hasPermi('crm:team:edit')"</span>)
    <span class="java-annotation">@Log</span>(title = <span class="java-string">"团队成员管理"</span>, businessType = BusinessType.UPDATE)
    <span class="java-annotation">@PutMapping</span>(<span class="java-string">"/update/{id}"</span>)
    <span class="java-keyword">public</span> AjaxResult update(@PathVariable(<span class="java-string">"id"</span>) Long id,
                               <span class="java-annotation">@RequestParam</span> String roleType,
                               <span class="java-annotation">@RequestBody</span> List&lt;String&gt; permissions) {
        <span class="java-comment">// 获取团队成员信息</span>
        CrmTeamMember member = teamMemberService.selectTeamMemberList(
                <span class="java-keyword">new</span> CrmTeamMember()).stream()
                .filter(m -> m.getId().equals(id))
                .findFirst()
                .orElse(<span class="java-keyword">null</span>);
        
        <span class="java-keyword">if</span> (member == <span class="java-keyword">null</span>) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"团队成员不存在"</span>);
        }
        
        <span class="java-comment">// 检查当前用户是否有权限管理团队</span>
        Long currentUserId = SecurityUtils.getUserId();
        <span class="java-keyword">if</span> (!teamMemberService.canManageTeam(member.getEntityType(), member.getEntityId(), currentUserId)) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"您没有权限管理该团队"</span>);
        }
        
        <span class="java-keyword">return</span> toAjax(teamMemberService.updateMemberRole(id, roleType, permissions));
    }
    
    <span class="java-comment">/**
     * 移除团队成员
     */</span>
    <span class="java-annotation">@PreAuthorize</span>(<span class="java-string">"@ss.hasPermi('crm:team:remove')"</span>)
    <span class="java-annotation">@Log</span>(title = <span class="java-string">"团队成员管理"</span>, businessType = BusinessType.DELETE)
    <span class="java-annotation">@DeleteMapping</span>(<span class="java-string">"/remove/{id}"</span>)
    <span class="java-keyword">public</span> AjaxResult remove(@PathVariable(<span class="java-string">"id"</span>) Long id) {
        <span class="java-comment">// 获取团队成员信息</span>
        CrmTeamMember member = teamMemberService.selectTeamMemberList(
                <span class="java-keyword">new</span> CrmTeamMember()).stream()
                .filter(m -> m.getId().equals(id))
                .findFirst()
                .orElse(<span class="java-keyword">null</span>);
        
        <span class="java-keyword">if</span> (member == <span class="java-keyword">null</span>) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"团队成员不存在"</span>);
        }
        
        <span class="java-comment">// 检查当前用户是否有权限管理团队</span>
        Long currentUserId = SecurityUtils.getUserId();
        <span class="java-keyword">if</span> (!teamMemberService.canManageTeam(member.getEntityType(), member.getEntityId(), currentUserId)) {
            <span class="java-keyword">return</span> AjaxResult.error(<span class="java-string">"您没有权限管理该团队"</span>);
        }
        
        <span class="java-keyword">return</span> toAjax(teamMemberService.removeTeamMember(id));
    }
    
    <span class="java-comment">/**
     * 检查用户权限
     */</span>
    <span class="java-annotation">@GetMapping</span>(<span class="java-string">"/checkPermission/{entityType}/{entityId}/{permission}"</span>)
    <span class="java-keyword">public</span> AjaxResult checkPermission(@PathVariable(<span class="java-string">"entityType"</span>) String entityType,
                                     @PathVariable(<span class="java-string">"entityId"</span>) Long entityId,
                                     @PathVariable(<span class="java-string">"permission"</span>) String permission) {
        Long currentUserId = SecurityUtils.getUserId();
        <span class="java-keyword">boolean</span> hasPermission = teamMemberService.hasPermission(entityType, entityId, currentUserId, permission);
        <span class="java-keyword">return</span> AjaxResult.success(hasPermission);
    }
}</code></pre>
        
        <h2>数据迁移策略</h2>
        
        <div class="phase-box">
            <h3>第一阶段：创建新表和迁移联系人团队数据</h3>
            
            <pre><code class="sql"><span class="sql-comment">-- 1. 创建新的统一团队成员表</span>
<span class="sql-keyword">CREATE TABLE</span> crm_team_members (
    <span class="sql-comment">-- 表结构定义（见上文）</span>
);

<span class="sql-comment">-- 2. 迁移现有联系人团队成员数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, permissions, 
    assigned_by, assigned_at, status, create_by, create_time, 
    update_by, update_time, remark
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'contact'</span> <span class="sql-keyword">AS</span> entity_type,
    contact_id <span class="sql-keyword">AS</span> entity_id,
    user_id,
    role_type,
    permissions,
    assigned_by,
    assigned_at,
    status,
    create_by,
    create_time,
    update_by,
    update_time,
    remark
<span class="sql-keyword">FROM</span> crm_contact_team_members;</code></pre>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：迁移其他实体的负责人数据</h3>
            
            <pre><code class="sql"><span class="sql-comment">-- 1. 迁移公司负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_by, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'customer'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    responsible_person_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    create_by,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_customers
<span class="sql-keyword">WHERE</span> responsible_person_id IS <span class="sql-keyword">NOT NULL</span>;

<span class="sql-comment">-- 2. 迁移商机负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'opportunity'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    manager_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_opportunities
<span class="sql-keyword">WHERE</span> manager_id IS <span class="sql-keyword">NOT NULL</span>;

<span class="sql-comment">-- 3. 迁移线索负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_by, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'lead'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    responsible_person_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    create_by,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_leads
<span class="sql-keyword">WHERE</span> responsible_person_id IS <span class="sql-keyword">NOT NULL</span>;</code></pre>
        </div>
        
        <h2>实施计划</h2>
        
        <div class="phase-box">
            <h3>第一阶段：基础设施建设 <span class="priority-high">优先级：高</span></h3>
            <ol>
                <li>创建统一团队成员表和权限视图</li>
                <li>开发CrmTeamMember实体类和Mapper接口</li>
                <li>实现ICrmTeamMemberService接口及其实现类</li>
                <li>开发CrmTeamController</li>
            </ol>
            <p><strong>预估时间：</strong> 3天</p>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：数据迁移 <span class="priority-high">优先级：高</span></h3>
            <ol>
                <li>迁移联系人团队成员数据</li>
                <li>迁移其他实体的负责人数据</li>
                <li>验证数据迁移的正确性</li>
            </ol>
            <p><strong>预估时间：</strong> 2天</p>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：业务逻辑适配 <span class="priority-medium">优先级：中</span></h3>
            <ol>
                <li>修改联系人模块，使用新的团队成员服务</li>
                <li>修改客户模块，使用新的团队成员服务</li>
                <li>修改商机模块，使用新的团队成员服务</li>
                <li>修改线索模块，使用新的团队成员服务</li>
            </ol>
            <p><strong>预估时间：</strong> 4天</p>
        </div>
        
        <div class="phase-box">
            <h3>第四阶段：前端适配 <span class="priority-medium">优先级：中</span></h3>
            <ol>
                <li>开发通用的团队成员管理组件</li>
                <li>在各实体详情页中集成团队成员管理组件</li>
                <li>实现团队成员列表、添加、编辑、删除等功能</li>
                <li>优化用户界面和交互体验</li>
            </ol>
            <p><strong>预估时间：</strong> 3天</p>
        </div>
        
        <div class="phase-box">
            <h3>第五阶段：测试与部署 <span class