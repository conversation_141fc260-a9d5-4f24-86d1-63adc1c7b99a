# CRM412 项目架构分层规则

## 重要原则
**绝对不要随意删除 common 目录下的文件！**

## 目录结构设计说明

### 1. ruoyi-common 工程
- **用途**：全局通用组件和工具
- **范围**：整个项目的所有模块都可以使用
- **示例**：通用工具类、全局配置、基础框架组件

### 2. ruoyi-crm/src/main/java/com/ruoyi/common/
- **用途**：CRM模块内的通用组件（模块级通用）
- **范围**：仅限CRM模块内使用的通用代码
- **示例**：CRM模块的通用mapper、通用service、通用工具类
- **重要**：这是用户自定义的分层设计，符合模块化开发原则
- **✅ 明确规则**：
  - **Mapper接口**：直接放在 `com.ruoyi.common.mapper` 包下
  - **实体类**：直接放在 `com.ruoyi.common.domain` 包下
  - **通用工具类**：直接放在 `com.ruoyi.common.utils` 包下

### 3. ruoyi-crm/src/main/java/com/ruoyi/crm/
- **用途**：CRM模块的具体业务逻辑
- **范围**：主要放置控制器和服务层实现
- **✅ 明确规则**：
  - **Controller层**：放在 `com.ruoyi.crm.controller` 包下
  - **Service层**：放在 `com.ruoyi.crm.service` 包下
  - **业务相关组件**：其他业务相关的特定实现

## 架构优势
1. **模块化**：每个模块可以有自己的通用组件层
2. **解耦**：模块间依赖清晰，便于维护
3. **复用**：模块内通用代码可在模块内复用
4. **扩展**：新增模块时可以遵循相同的分层原则

## 开发规范
1. 不要随意删除 common 目录下的文件
2. 理解 common 和 crm 目录的职责区别
3. 新增功能时遵循现有的分层结构
4. 模块级通用组件放在对应模块的 common 包下

## 🎯 代码放置明细规则

### ruoyi-crm/src/main/java/com/ruoyi/common/ 目录
```
com.ruoyi.common/
├── mapper/           # ✅ Mapper接口（如：CrmContactTeamMemberMapper）
├── domain/           # ✅ 实体类（如：CrmContactTeamMember）
├── service/          # ✅ 通用服务接口
└── utils/            # ✅ CRM模块通用工具类
```

### ruoyi-crm/src/main/java/com/ruoyi/crm/ 目录
```
com.ruoyi.crm/
├── controller/       # ✅ 控制器层（如：CrmContactController）
├── service/          # ✅ 服务层实现（如：CrmContactServiceImpl）
└── common/           # ✅ 业务相关通用组件
```

### 代码生成规则
- **生成的Mapper接口** → `ruoyi-crm/src/main/java/com/ruoyi/common/mapper/`
- **生成的实体类** → `ruoyi-crm/src/main/java/com/ruoyi/common/domain/`
- **手写的Controller** → `ruoyi-crm/src/main/java/com/ruoyi/crm/controller/`
- **手写的Service实现** → `ruoyi-crm/src/main/java/com/ruoyi/crm/service/`

## 历史教训
- 曾经错误地删除或建议删除 common 目录下的文件
- 需要深刻理解用户的架构设计意图
- 不能简单地认为 common 和 crm 包重复就要删除

---
*此文档用于记录项目架构规则，避免重复犯错*
