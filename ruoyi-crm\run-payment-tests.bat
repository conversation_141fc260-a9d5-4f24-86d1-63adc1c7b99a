@echo off
echo ================================
echo 回款模块集成测试运行脚本
echo ================================

cd /d "%~dp0"

echo 正在运行回款模块集成测试...
echo.

echo 1. 运行回款计划Controller测试
mvn test -Dtest=CrmPaymentPlanControllerIntegrationTest

if %ERRORLEVEL% NEQ 0 (
    echo 回款计划测试失败！
    pause
    exit /b 1
)

echo.
echo 2. 运行回款记录Controller测试
mvn test -Dtest=CrmPaymentRecordControllerIntegrationTest

if %ERRORLEVEL% NEQ 0 (
    echo 回款记录测试失败！
    pause
    exit /b 1
)

echo.
echo 3. 运行分期管理Controller测试
mvn test -Dtest=CrmPaymentInstallmentControllerIntegrationTest

if %ERRORLEVEL% NEQ 0 (
    echo 分期管理测试失败！
    pause
    exit /b 1
)

echo.
echo 4. 运行整个回款模块测试套件
mvn test -Dtest=PaymentModuleIntegrationTestSuite

if %ERRORLEVEL% NEQ 0 (
    echo 回款模块测试套件失败！
    pause
    exit /b 1
)

echo.
echo ================================
echo 所有回款模块测试完成！
echo ================================
echo.
echo 测试报告位置：target/surefire-reports/
echo.

pause