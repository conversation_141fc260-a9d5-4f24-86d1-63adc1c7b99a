<?xml version="1.0" encoding="UTF-8"?>
<workflows xmlns="http://www.ruoyi.com/workflow" version="1.0">
    
    <!-- 对账单审核流程配置 -->
    <workflow id="reconciliation-approval" name="对账单审核流程" version="1.0">
        <description>业务员创建对账单后的审核流程</description>
        
        <participants>
            <participant id="applicant" name="申请人" type="user"/>
            <participant id="financeManager" name="财务经理" type="role"/>
        </participants>
        
        <steps>
            <step id="start" name="开始" type="start">
                <description>业务员提交对账单审核</description>
                <next>finance-approval</next>
            </step>
            
            <step id="finance-approval" name="财务审核" type="approval">
                <description>财务经理审核对账单</description>
                <assignee>financeManager</assignee>
                <form>
                    <field name="approvalResult" label="审核结果" type="select" required="true">
                        <option value="approved">通过</option>
                        <option value="rejected">驳回</option>
                    </field>
                    <field name="approvalNotes" label="审核意见" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="approvalResult == 'approved'" next="approved"/>
                    <condition expression="approvalResult == 'rejected'" next="modify"/>
                </conditions>
            </step>
            
            <step id="modify" name="修改对账单" type="task">
                <description>业务员修改对账单</description>
                <assignee>applicant</assignee>
                <form>
                    <field name="modifyAction" label="操作" type="select" required="true">
                        <option value="resubmit">重新提交</option>
                        <option value="cancel">取消申请</option>
                    </field>
                    <field name="modifyNotes" label="修改说明" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="modifyAction == 'resubmit'" next="finance-approval"/>
                    <condition expression="modifyAction == 'cancel'" next="cancelled"/>
                </conditions>
            </step>
            
            <step id="approved" name="审核通过" type="end">
                <description>对账单审核通过</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">approved</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
            
            <step id="cancelled" name="申请取消" type="end">
                <description>用户取消申请</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">cancelled</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
        </steps>
        
        <settings>
            <setting name="timeout" value="7"/>
            <setting name="timeoutUnit" value="days"/>
            <setting name="autoAssign" value="true"/>
            <setting name="notification" value="email,sms"/>
        </settings>
    </workflow>
    
    <!-- 开票申请审批流程配置 -->
    <workflow id="invoice-application-approval" name="开票申请审批流程" version="1.0">
        <description>基于对账单的开票申请审批流程</description>
        
        <participants>
            <participant id="applicant" name="申请人" type="user"/>
            <participant id="financeManager" name="财务经理" type="role"/>
            <participant id="financeClerk" name="财务人员" type="role"/>
        </participants>
        
        <steps>
            <step id="start" name="开始" type="start">
                <description>业务员提交开票申请</description>
                <next>validation</next>
            </step>
            
            <step id="validation" name="信息验证" type="service">
                <description>验证开票申请信息</description>
                <service>invoiceApplicationValidationService</service>
                <conditions>
                    <condition expression="validationResult == 'success'" next="manager-approval"/>
                    <condition expression="validationResult == 'failed'" next="fix-error"/>
                </conditions>
            </step>
            
            <step id="fix-error" name="修正错误" type="task">
                <description>修正验证错误</description>
                <assignee>applicant</assignee>
                <form>
                    <field name="fixAction" label="操作" type="select" required="true">
                        <option value="fix">修正重试</option>
                        <option value="cancel">取消申请</option>
                    </field>
                </form>
                <conditions>
                    <condition expression="fixAction == 'fix'" next="validation"/>
                    <condition expression="fixAction == 'cancel'" next="cancelled"/>
                </conditions>
            </step>
            
            <step id="manager-approval" name="财务经理审批" type="approval">
                <description>财务经理审批开票申请</description>
                <assignee>financeManager</assignee>
                <form>
                    <field name="approvalResult" label="审批结果" type="select" required="true">
                        <option value="approved">同意</option>
                        <option value="rejected">驳回</option>
                        <option value="needModify">需要修改</option>
                    </field>
                    <field name="approvalNotes" label="审批意见" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="approvalResult == 'approved'" next="issue-invoice"/>
                    <condition expression="approvalResult == 'rejected'" next="rejected"/>
                    <condition expression="approvalResult == 'needModify'" next="modify-application"/>
                </conditions>
            </step>
            
            <step id="modify-application" name="修改申请" type="task">
                <description>申请人修改开票申请</description>
                <assignee>applicant</assignee>
                <form>
                    <field name="modifyAction" label="操作" type="select" required="true">
                        <option value="resubmit">重新提交</option>
                        <option value="cancel">取消申请</option>
                    </field>
                    <field name="modifyNotes" label="修改说明" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="modifyAction == 'resubmit'" next="manager-approval"/>
                    <condition expression="modifyAction == 'cancel'" next="cancelled"/>
                </conditions>
            </step>
            
            <step id="issue-invoice" name="开具发票" type="task">
                <description>财务人员开具发票</description>
                <assignee>financeClerk</assignee>
                <form>
                    <field name="invoiceNo" label="发票号码" type="text" required="true"/>
                    <field name="invoiceDate" label="开票日期" type="date" required="true"/>
                    <field name="taxAmount" label="税额" type="number"/>
                    <field name="remarks" label="备注" type="textarea"/>
                </form>
                <next>record-invoice</next>
            </step>
            
            <step id="record-invoice" name="记录发票" type="service">
                <description>记录发票信息</description>
                <service>invoiceRecordService</service>
                <next>success</next>
            </step>
            
            <step id="success" name="开票成功" type="end">
                <description>开票申请成功完成</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">completed</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
            
            <step id="rejected" name="申请驳回" type="end">
                <description>申请被驳回</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">rejected</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
            
            <step id="cancelled" name="申请取消" type="end">
                <description>申请被取消</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">cancelled</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
        </steps>
        
        <settings>
            <setting name="timeout" value="5"/>
            <setting name="timeoutUnit" value="days"/>
            <setting name="autoAssign" value="true"/>
            <setting name="notification" value="email,sms"/>
        </settings>
    </workflow>
    
    <!-- 回款审批流程配置 -->
    <workflow id="payment-approval" name="回款审批流程" version="1.0">
        <description>回款记录的审批流程</description>
        
        <participants>
            <participant id="applicant" name="申请人" type="user"/>
            <participant id="financeManager" name="财务经理" type="role"/>
            <participant id="financeClerk" name="财务人员" type="role"/>
        </participants>
        
        <steps>
            <step id="start" name="开始" type="start">
                <description>业务员提交回款记录</description>
                <next>validation</next>
            </step>
            
            <step id="validation" name="金额验证" type="service">
                <description>验证回款金额</description>
                <service>paymentValidationService</service>
                <next>amount-check</next>
            </step>
            
            <step id="amount-check" name="金额判断" type="gateway">
                <description>根据金额决定审批流程</description>
                <conditions>
                    <condition expression="paymentAmount >= 50000" next="manager-approval"/>
                    <condition expression="paymentAmount < 50000" next="clerk-audit"/>
                </conditions>
            </step>
            
            <step id="manager-approval" name="财务经理审批" type="approval">
                <description>大额回款需要经理审批</description>
                <assignee>financeManager</assignee>
                <form>
                    <field name="approvalResult" label="审批结果" type="select" required="true">
                        <option value="approved">同意</option>
                        <option value="rejected">驳回</option>
                    </field>
                    <field name="approvalNotes" label="审批意见" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="approvalResult == 'approved'" next="clerk-audit"/>
                    <condition expression="approvalResult == 'rejected'" next="rejected"/>
                </conditions>
            </step>
            
            <step id="clerk-audit" name="财务审核" type="approval">
                <description>财务人员审核回款信息</description>
                <assignee>financeClerk</assignee>
                <form>
                    <field name="auditResult" label="审核结果" type="select" required="true">
                        <option value="passed">通过</option>
                        <option value="rejected">驳回</option>
                    </field>
                    <field name="auditNotes" label="审核意见" type="textarea"/>
                    <field name="bankSlipVerified" label="银行回单验证" type="checkbox"/>
                </form>
                <conditions>
                    <condition expression="auditResult == 'passed'" next="confirm"/>
                    <condition expression="auditResult == 'rejected'" next="correct"/>
                </conditions>
            </step>
            
            <step id="correct" name="修正信息" type="task">
                <description>业务员修正回款信息</description>
                <assignee>applicant</assignee>
                <form>
                    <field name="correctAction" label="操作" type="select" required="true">
                        <option value="resubmit">重新提交</option>
                        <option value="cancel">取消回款</option>
                    </field>
                    <field name="correctNotes" label="修正说明" type="textarea"/>
                </form>
                <conditions>
                    <condition expression="correctAction == 'resubmit'" next="validation"/>
                    <condition expression="correctAction == 'cancel'" next="cancelled"/>
                </conditions>
            </step>
            
            <step id="confirm" name="回款确认" type="service">
                <description>确认回款，更新系统状态</description>
                <service>paymentConfirmationService</service>
                <next>success</next>
            </step>
            
            <step id="success" name="回款成功" type="end">
                <description>回款处理成功</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">completed</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
            
            <step id="rejected" name="回款驳回" type="end">
                <description>回款被驳回</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">rejected</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
            
            <step id="cancelled" name="回款取消" type="end">
                <description>回款被取消</description>
                <action>updateStatus</action>
                <parameters>
                    <parameter name="status">cancelled</parameter>
                    <parameter name="notify">true</parameter>
                </parameters>
            </step>
        </steps>
        
        <settings>
            <setting name="timeout" value="3"/>
            <setting name="timeoutUnit" value="days"/>
            <setting name="autoAssign" value="true"/>
            <setting name="notification" value="email,sms"/>
            <setting name="largeAmountThreshold" value="50000"/>
        </settings>
    </workflow>
    
</workflows>