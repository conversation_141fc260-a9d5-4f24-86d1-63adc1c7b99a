package com.ruoyi.crm.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.mapper.CrmPaymentApprovalMapper;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;
import com.ruoyi.crm.service.ICrmPaymentApprovalService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 回款审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class CrmPaymentApprovalServiceImpl implements ICrmPaymentApprovalService 
{
    @Autowired
    private CrmPaymentApprovalMapper crmPaymentApprovalMapper;

    /**
     * 查询回款审批
     * 
     * @param id 回款审批主键
     * @return 回款审批
     */
    @Override
    public CrmPaymentApproval selectCrmPaymentApprovalById(Long id)
    {
        return crmPaymentApprovalMapper.selectCrmPaymentApprovalById(id);
    }

    /**
     * 查询回款审批列表
     * 
     * @param crmPaymentApproval 回款审批
     * @return 回款审批
     */
    @Override
    public List<CrmPaymentApproval> selectCrmPaymentApprovalList(CrmPaymentApproval crmPaymentApproval)
    {
        return crmPaymentApprovalMapper.selectCrmPaymentApprovalList(crmPaymentApproval);
    }

    /**
     * 根据计划ID查询审批列表
     * 
     * @param planId 计划ID
     * @return 回款审批集合
     */
    @Override
    public List<CrmPaymentApproval> selectApprovalsByPlanId(Long planId)
    {
        return crmPaymentApprovalMapper.selectApprovalsByPlanId(planId);
    }

    /**
     * 新增回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    @Override
    public int insertCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval)
    {
        crmPaymentApproval.setCreateTime(DateUtils.getNowDate());
        crmPaymentApproval.setCreateBy(SecurityUtils.getUsername());
        
        if (crmPaymentApproval.getApprovalStatus() == null) {
            crmPaymentApproval.setApprovalStatus("待审批");
        }
        
        return crmPaymentApprovalMapper.insertCrmPaymentApproval(crmPaymentApproval);
    }

    /**
     * 批量新增回款审批
     * 
     * @param approvals 回款审批列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertApprovals(List<CrmPaymentApproval> approvals)
    {
        if (approvals == null || approvals.isEmpty()) {
            return 0;
        }
        
        for (CrmPaymentApproval approval : approvals) {
            approval.setCreateTime(DateUtils.getNowDate());
            approval.setCreateBy(SecurityUtils.getUsername());
            
            if (approval.getApprovalStatus() == null) {
                approval.setApprovalStatus("待审批");
            }
        }
        
        return crmPaymentApprovalMapper.batchInsertApprovals(approvals);
    }

    /**
     * 修改回款审批
     * 
     * @param crmPaymentApproval 回款审批
     * @return 结果
     */
    @Override
    public int updateCrmPaymentApproval(CrmPaymentApproval crmPaymentApproval)
    {
        crmPaymentApproval.setUpdateTime(DateUtils.getNowDate());
        crmPaymentApproval.setUpdateBy(SecurityUtils.getUsername());
        return crmPaymentApprovalMapper.updateCrmPaymentApproval(crmPaymentApproval);
    }

    /**
     * 批量删除回款审批
     * 
     * @param ids 需要删除的回款审批主键集合
     * @return 结果
     */
    @Override
    public int deleteCrmPaymentApprovalByIds(Long[] ids)
    {
        return crmPaymentApprovalMapper.deleteCrmPaymentApprovalByIds(ids);
    }

    /**
     * 删除回款审批信息
     * 
     * @param id 回款审批主键
     * @return 结果
     */
    @Override
    public int deleteCrmPaymentApprovalById(Long id)
    {
        return crmPaymentApprovalMapper.deleteCrmPaymentApprovalById(id);
    }

    /**
     * 根据计划ID删除审批
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    public int deleteApprovalsByPlanId(Long planId)
    {
        return crmPaymentApprovalMapper.deleteApprovalsByPlanId(planId);
    }

    /**
     * 查询待审批的记录
     * 
     * @param approverId 审批人ID
     * @return 回款审批集合
     */
    @Override
    public List<CrmPaymentApproval> selectPendingApprovals(Long approverId)
    {
        return crmPaymentApprovalMapper.selectPendingApprovals(approverId);
    }

    /**
     * 创建审批流程
     * 
     * @param planId 计划ID
     * @param approverIds 审批人ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int createApprovalFlow(Long planId, List<Long> approverIds)
    {
        if (approverIds == null || approverIds.isEmpty()) {
            return 0;
        }
        
        List<CrmPaymentApproval> approvals = new ArrayList<>();
        
        for (int i = 0; i < approverIds.size(); i++) {
            CrmPaymentApproval approval = new CrmPaymentApproval();
            approval.setPlanId(planId);
            approval.setApprovalLevel(i + 1);
            approval.setApproverId(approverIds.get(i));
            // 这里简化处理，实际应该从用户表获取用户名
            approval.setApproverName("审批人" + approverIds.get(i));
            approval.setApprovalStatus(i == 0 ? "待审批" : "待审批"); // 第一级立即可审批，其他级等待
            approval.setCreateBy(SecurityUtils.getUsername());
            approval.setCreateTime(DateUtils.getNowDate());
            
            approvals.add(approval);
        }
        
        return batchInsertApprovals(approvals);
    }

    /**
     * 获取当前审批级别
     * 
     * @param planId 计划ID
     * @return 当前审批级别
     */
    @Override
    public Integer getCurrentApprovalLevel(Long planId)
    {
        Integer level = crmPaymentApprovalMapper.getCurrentApprovalLevel(planId);
        return level != null ? level : 0;
    }

    /**
     * 获取下一个待审批人
     * 
     * @param planId 计划ID
     * @return 下一个审批记录
     */
    @Override
    public CrmPaymentApproval getNextApprover(Long planId)
    {
        Integer currentLevel = getCurrentApprovalLevel(planId);
        int nextLevel = currentLevel + 1;
        
        CrmPaymentApproval query = new CrmPaymentApproval();
        query.setPlanId(planId);
        query.setApprovalLevel(nextLevel);
        query.setApprovalStatus("待审批");
        
        List<CrmPaymentApproval> approvals = selectCrmPaymentApprovalList(query);
        
        return approvals.isEmpty() ? null : approvals.get(0);
    }
}