<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmContractUserRelationMapper">
    
    <resultMap type="CrmContractUserRelation" id="CrmContractUserRelationResult">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="userId" column="user_id"/>
        <result property="relationType" column="relation_type"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCrmContractUserRelationVo">
        select id, contract_id, user_id, relation_type, created_at, updated_at, del_flag, create_by, create_time, update_by, update_time
        from crm_business_contract_user_relations
    </sql>

    <select id="selectCrmContractUserRelationList" parameterType="CrmContractUserRelation" resultMap="CrmContractUserRelationResult">
        <include refid="selectCrmContractUserRelationVo"/>
        <where>  
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
        </where>
    </select>
    
    <select id="selectCrmContractUserRelationById" parameterType="Long" resultMap="CrmContractUserRelationResult">
        <include refid="selectCrmContractUserRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCrmContractUserRelation" parameterType="CrmContractUserRelation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_contract_user_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractId != null">contract_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractId != null">#{contractId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmContractUserRelation" parameterType="CrmContractUserRelation">
        update crm_business_contract_user_relations
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmContractUserRelationById" parameterType="Long">
        delete from crm_business_contract_user_relations where id = #{id}
    </delete>

    <delete id="deleteCrmContractUserRelationByIds" parameterType="String">
        delete from crm_business_contract_user_relations where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 