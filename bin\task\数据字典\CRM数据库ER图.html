<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM数据库ER图分析</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 1200px; 
            margin: 20px auto; 
            padding: 0 20px; 
            background-color: #f5f7fa;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4, h5, h6 { 
            color: #111; 
            font-weight: 600; 
            margin-top: 2.4em; 
            margin-bottom: 1em; 
        }
        h1 { 
            font-size: 2.2em; 
            border-bottom: 2px solid #4CAF50; 
            padding-bottom: 0.3em;
            color: #2c5530;
        }
        h2 { 
            font-size: 1.75em; 
            border-bottom: 1px solid #81C784; 
            padding-bottom: 0.3em;
            color: #388E3C;
        }
        h3 { 
            font-size: 1.4em; 
            color: #4CAF50;
        }
        h4 { 
            font-size: 1.2em; 
            color: #689F38;
        }
        
        .mermaid { 
            text-align: center; 
            margin: 20px 0;
            background: #fafafa;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .diagram-container {
            position: relative;
            overflow: auto;
            max-height: 80vh;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .diagram-content {
            transform-origin: 0 0;
            transition: transform 0.3s ease;
            min-width: 100%;
            min-height: 100%;
        }
        
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .zoom-btn {
            width: 40px;
            height: 40px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            background: #45a049;
            transform: scale(1.1);
        }
        
        .zoom-level {
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            text-align: center;
            min-width: 50px;
        }
        
        .fullscreen-btn {
            background: #2196F3;
        }
        
        .fullscreen-btn:hover {
            background: #1976D2;
        }
        
        .diagram-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: white;
            overflow: auto;
        }
        
        .diagram-fullscreen .zoom-controls {
            top: 20px;
            right: 20px;
        }
        
        .table-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .table-group {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            padding: 15px;
        }
        
        .table-group h4 {
            margin-top: 0;
            color: #2c5530;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
        }
        
        .table-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .table-item {
            background: #f5f7fa;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
            font-size: 0.9em;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4CAF50, #81C784);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        
        ul li {
            margin-bottom: 8px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 15px;
        }
        
        .feature-item h4 {
            margin-top: 0;
            color: #2e7d32;
        }

        .toc {
            background: #f0f4f8;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .toc h3 {
            margin-top: 0;
            color: #2d3748;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }

        .toc li {
            margin: 8px 0;
        }

        .toc a {
            color: #4CAF50;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            display: block;
            transition: background-color 0.3s;
        }

        .toc a:hover {
            background-color: #e8f5e8;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRM数据库结构分析与ER图</h1>
        
        <div class="toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#overview">1. 数据库概述</a></li>
                <li><a href="#stats">2. 统计数据</a></li>
                <li><a href="#er-diagram">3. 完整ER图</a></li>
                <li><a href="#core-business">4. 核心业务模块ER图</a></li>
                <li><a href="#table-analysis">5. 数据表分析</a></li>
                <li><a href="#features">6. 系统特性</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>1. 数据库概述</h2>
            <div class="highlight">
                <p><strong>CRM412数据库</strong>是一个完整的客户关系管理系统数据库，涵盖了从线索管理到客户维护、商机跟踪、合同管理、回款管理等完整的销售流程。</p>
                <p>数据库包含了工作流引擎(Activiti)、定时任务(Quartz)、系统管理等多个功能模块，提供了一个企业级的CRM解决方案。</p>
            </div>
            
            <div class="highlight" style="background: #e3f2fd; border-color: #2196F3;">
                <h4>📖 ER图使用说明</h4>
                <ul>
                    <li><strong>缩放控制</strong>：每个ER图右上角都有缩放控制按钮</li>
                    <li><strong>放大/缩小</strong>：点击 + / - 按钮，或使用鼠标滚轮</li>
                    <li><strong>重置缩放</strong>：点击 ⌂ 按钮恢复100%显示</li>
                    <li><strong>全屏查看</strong>：点击 ⛶ 按钮进入全屏模式，按ESC键退出</li>
                    <li><strong>拖拽查看</strong>：在缩放状态下可以拖拽图表查看不同区域（鼠标会显示为抓手图标）</li>
                    <li><strong>键盘快捷键</strong>：Ctrl/Cmd + 放大，Ctrl/Cmd - 缩小，Ctrl/Cmd 0 重置</li>
                </ul>
            </div>
        </section>

        <section id="stats">
            <h2>2. 统计数据</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">95+</div>
                    <div class="stat-label">数据表总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">CRM业务表</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">20+</div>
                    <div class="stat-label">工作流表</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">系统管理表</div>
                </div>
            </div>
        </section>

        <section id="er-diagram">
            <h2>3. 完整数据库ER图</h2>
            <p>以下是CRM数据库的完整实体关系图，展示了所有主要实体及其关系：</p>
            
            <div class="diagram-container" id="diagram1">
                <div class="zoom-controls">
                    <button class="zoom-btn" onclick="zoomIn('diagram1')" title="放大">+</button>
                    <div class="zoom-level" id="zoom-level-1">100%</div>
                    <button class="zoom-btn" onclick="zoomOut('diagram1')" title="缩小">-</button>
                    <button class="zoom-btn" onclick="resetZoom('diagram1')" title="重置">⌂</button>
                    <button class="zoom-btn fullscreen-btn" onclick="toggleFullscreen('diagram1')" title="全屏">⛶</button>
                </div>
                <div class="diagram-content mermaid" id="diagram-content-1">
---
title: CRM数据库完整ER图
---
erDiagram
    %% 核心业务实体
    CUSTOMER ||--o{ CONTACT_RELATION : "has"
    CONTACT ||--o{ CONTACT_RELATION : "belongs_to"
    CUSTOMER ||--o{ LEAD : "converts_from"
    CUSTOMER ||--o{ OPPORTUNITY : "has"
    CUSTOMER ||--o{ CONTRACT : "signs"
    CUSTOMER ||--o{ PAYMENT : "pays"
    CONTRACT ||--o{ PAYMENT : "receives"
    OPPORTUNITY ||--o{ CONTRACT : "converts_to"
    
    %% 跟进记录关系
    CUSTOMER ||--o{ CUSTOMER_FOLLOWUP : "has_followup"
    CONTACT ||--o{ CONTACT_FOLLOWUP : "has_followup"
    LEAD ||--o{ LEAD_FOLLOWUP : "has_followup"
    OPPORTUNITY ||--o{ OPPORTUNITY_FOLLOWUP : "has_followup"
    CONTRACT ||--o{ CONTRACT_FOLLOWUP : "has_followup"
    PAYMENT ||--o{ PAYMENT_FOLLOWUP : "has_followup"
    
    %% 关注关系
    USER ||--o{ LEAD_FOLLOWER : "follows"
    LEAD ||--o{ LEAD_FOLLOWER : "followed_by"
    USER ||--o{ CONTACT_FOLLOWER : "follows"
    CONTACT ||--o{ CONTACT_FOLLOWER : "followed_by"
    
    %% 系统管理
    USER ||--o{ DEPT : "belongs_to"
    USER ||--o{ ROLE : "has_role"
    ROLE ||--o{ MENU : "has_permission"
    USER ||--o{ USER_HIERARCHY : "manages"
    
    %% 支撑功能
    STATIC_FILE ||--o{ CUSTOMER : "attached_to"
    STATIC_FILE ||--o{ LEAD : "attached_to"
    STATIC_FILE ||--o{ CONTRACT : "attached_to"
    PRODUCT ||--o{ OPPORTUNITY : "includes"
    
    %% 实体定义
    CUSTOMER {
        int id PK
        string customer_name
        string responsible_person_id FK
        string customer_source
        string mobile
        string phone
        string email
        string website
        string customer_industry
        string customer_level
        string customer_address
        string primary_contact
        string deal_status
        datetime next_contact_time
        text remarks
        char del_flag
        datetime created_at
        datetime updated_at
    }
    
    CONTACT {
        int id PK
        string responsible_person_id FK
        string name
        string mobile
        string phone
        string email
        string position
        string is_key_decision_maker
        string direct_superior
        string address
        string detailed_address
        datetime next_contact_time
        string gender
        text remarks
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    CONTACT_RELATION {
        int id PK
        int customer_id FK
        int contact_id FK
        string relation_type
        tinyint is_primary
        date start_date
        date end_date
        string status
        text remarks
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    LEAD {
        int id PK
        string responsible_person_id FK
        string lead_name
        string lead_source
        string mobile
        string phone
        string email
        string address
        string detailed_address
        string customer_industry
        string customer_level
        datetime next_contact_time
        text remarks
        string customer_name
        string status
        char del_flag
        datetime created_at
        datetime updated_at
    }
    
    OPPORTUNITY {
        int id PK
        int manager_id FK
        string opportunity_name
        string customer_name
        decimal opportunity_amount
        string opportunity_stage
        decimal win_rate
        date expected_close_date
        string opportunity_source
        string opportunity_type
        text remarks
        datetime created_at
        datetime updated_at
    }
    
    CONTRACT {
        int id PK
        int manager_id FK
        string contract_number
        string contract_name
        string customer_name
        string quotation_number
        string opportunity_name
        decimal contract_amount
        date order_date
        date start_date
        date end_date
        string customer_signatory
        string company_signatory
        text remarks
        string contract_type
        blob contract_image
        blob attachments
        decimal profit
        decimal total_product_cost
        string status
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    PAYMENT {
        int id PK
        int manager_id FK
        string payment_number
        int customer_id FK
        int contract_id FK
        text payment_details
        date payment_date
        decimal payment_amount
        string payment_method
        text remarks
        datetime created_at
        datetime updated_at
    }
    
    USER {
        bigint user_id PK
        bigint dept_id FK
        string user_name
        string nick_name
        string user_type
        string email
        string phonenumber
        char sex
        string avatar
        string password
        char status
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    DEPT {
        bigint dept_id PK
        bigint parent_id FK
        string ancestors
        string dept_name
        int order_num
        string leader
        string phone
        string email
        char status
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    ROLE {
        bigint role_id PK
        string role_name
        string role_key
        int role_sort
        char data_scope
        char status
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    MENU {
        bigint menu_id PK
        string menu_name
        bigint parent_id FK
        int order_num
        string path
        string component
        char menu_type
        char visible
        char status
        string perms
        string icon
        datetime create_time
        datetime update_time
    }
    
    STATIC_FILE {
        bigint id PK
        string module_type
        bigint module_id FK
        string storage_type
        string file_url
        string file_name
        bigint file_size
        string file_type
        string file_extension
        string md5_hash
        json metadata
        string description
        string tags
        tinyint is_public
        int download_count
        int view_count
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    PRODUCT {
        bigint id PK
        string name
        decimal price
        string image_url
        string product_link
        text material_properties
        text material_process
        text tech_specs
        text advantages
        text disadvantages
        text application_areas
        string type
        datetime created_at
        datetime updated_at
    }
    
    CUSTOMER_FOLLOWUP {
        bigint id PK
        bigint customer_id FK
        text follow_up_content
        string follow_up_type
        string business_stage
        decimal potential_value
        string cooperation_intention
        text competitor_analysis
        text decision_maker_info
        text next_action_plan
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    CONTACT_FOLLOWUP {
        bigint id PK
        bigint contact_id FK
        text follow_up_content
        string next_contact_method
        string follow_up_method
        datetime next_contact_time
        string communication_result
        text meeting_summary
        string contact_quality
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    LEAD_FOLLOWUP {
        bigint id PK
        bigint lead_id FK
        text follow_up_content
        string lead_status
        string qualification_level
        decimal estimated_value
        string conversion_probability
        datetime expected_conversion_date
        string contact_preference
        text pain_points
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    OPPORTUNITY_FOLLOWUP {
        bigint id PK
        bigint opportunity_id FK
        text follow_up_content
        string opportunity_stage
        decimal win_probability
        string competitor_info
        text negotiation_points
        datetime expected_close_date
        tinyint budget_confirmed
        string decision_timeline
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    CONTRACT_FOLLOWUP {
        bigint id PK
        bigint contract_id FK
        text follow_up_content
        string execution_status
        string milestone_progress
        text risk_assessment
        string payment_status
        datetime next_review_date
        string delivery_progress
        string customer_satisfaction
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    PAYMENT_FOLLOWUP {
        bigint id PK
        bigint payment_id FK
        text follow_up_content
        string payment_status
        decimal outstanding_amount
        string collection_method
        text delay_reason
        datetime expected_payment_date
        string collection_difficulty
        tinyint legal_action_required
        json related_files
        bigint creator_id FK
        datetime created_at
        datetime updated_at
    }
    
    LEAD_FOLLOWER {
        int id PK
        int lead_id FK
        int follower_id FK
        string status
        char del_flag
        datetime create_time
        datetime update_time
    }
    
    CONTACT_FOLLOWER {
        bigint id PK
        bigint contact_id FK
        bigint follower_id FK
        datetime follow_time
        tinyint is_active
        datetime create_time
    }
    
    USER_HIERARCHY {
        bigint id PK
        bigint user_id FK
        bigint superior_id FK
        int hierarchy_level
        char del_flag
        datetime create_time
        datetime update_time
    }
                </div>
            </div>
        </section>

        <section id="core-business">
            <h2>4. 核心业务模块ER图</h2>
            <p>核心业务流程：线索 → 客户 → 商机 → 合同 → 回款</p>
            
            <div class="diagram-container" id="diagram2">
                <div class="zoom-controls">
                    <button class="zoom-btn" onclick="zoomIn('diagram2')" title="放大">+</button>
                    <div class="zoom-level" id="zoom-level-2">100%</div>
                    <button class="zoom-btn" onclick="zoomOut('diagram2')" title="缩小">-</button>
                    <button class="zoom-btn" onclick="resetZoom('diagram2')" title="重置">⌂</button>
                    <button class="zoom-btn fullscreen-btn" onclick="toggleFullscreen('diagram2')" title="全屏">⛶</button>
                </div>
                <div class="diagram-content mermaid" id="diagram-content-2">
---
title: CRM核心业务流程ER图
---
erDiagram
    LEAD ||--o| CUSTOMER : "converts_to"
    CUSTOMER ||--o{ OPPORTUNITY : "generates"
    OPPORTUNITY ||--o| CONTRACT : "converts_to"
    CONTRACT ||--o{ PAYMENT : "receives"
    CUSTOMER ||--o{ CONTRACT : "signs"
    CUSTOMER ||--o{ CONTACT_RELATION : "has"
    CONTACT ||--o{ CONTACT_RELATION : "belongs_to"
    
    LEAD {
        int id PK
        string lead_name "线索名称"
        string lead_source "线索来源"
        string mobile "手机号"
        string email "邮箱"
        string customer_industry "行业"
        string status "状态"
        datetime created_at "创建时间"
    }
    
    CUSTOMER {
        int id PK
        string customer_name "客户名称"
        string customer_source "客户来源"
        string mobile "手机号"
        string email "邮箱"
        string customer_industry "所属行业"
        string customer_level "客户级别"
        string deal_status "成交状态"
        datetime created_at "创建时间"
    }
    
    CONTACT {
        int id PK
        string name "联系人姓名"
        string mobile "手机号"
        string email "邮箱"
        string position "职务"
        string is_key_decision_maker "是否决策人"
    }
    
    CONTACT_RELATION {
        int id PK
        int customer_id FK "客户ID"
        int contact_id FK "联系人ID"
        string relation_type "关系类型"
        tinyint is_primary "是否主要联系人"
    }
    
    OPPORTUNITY {
        int id PK
        string opportunity_name "商机名称"
        string customer_name "客户名称"
        decimal opportunity_amount "商机金额"
        string opportunity_stage "商机阶段"
        decimal win_rate "赢单率"
        date expected_close_date "预计关闭日期"
    }
    
    CONTRACT {
        int id PK
        string contract_number "合同编号"
        string contract_name "合同名称"
        string customer_name "客户名称"
        decimal contract_amount "合同金额"
        date start_date "开始日期"
        date end_date "结束日期"
        string status "状态"
    }
    
    PAYMENT {
        int id PK
        string payment_number "回款编号"
        int customer_id FK "客户ID"
        int contract_id FK "合同ID"
        date payment_date "回款日期"
        decimal payment_amount "回款金额"
        string payment_method "回款方式"
    }
                </div>
            </div>
        </section>

        <section id="table-analysis">
            <h2>5. 数据表分析</h2>
            
            <div class="table-summary">
                <h3>表格分类统计</h3>
                <p>数据库共包含约95张表，按功能模块分类如下：</p>
            </div>

            <div class="table-group">
                <h4>CRM核心业务表 (25张)</h4>
                <div class="table-list">
                    <div class="table-item">crm_business_customers - 客户表</div>
                    <div class="table-item">crm_business_contacts - 联系人表</div>
                    <div class="table-item">crm_business_leads - 线索表</div>
                    <div class="table-item">crm_business_opportunities - 商机表</div>
                    <div class="table-item">crm_business_contracts - 合同表</div>
                    <div class="table-item">crm_business_payments - 回款表</div>
                    <div class="table-item">crm_business_payment_plans - 回款计划表</div>
                    <div class="table-item">crm_business_payment_details - 回款明细表</div>
                    <div class="table-item">crm_customer_contact_relations - 客户联系人关联表</div>
                    <div class="table-item">crm_customer_followup_records - 客户跟进记录表</div>
                    <div class="table-item">crm_contact_followup_records - 联系人跟进记录表</div>
                    <div class="table-item">crm_lead_followup_records - 线索跟进记录表</div>
                    <div class="table-item">crm_opportunity_followup_records - 商机跟进记录表</div>
                    <div class="table-item">crm_contract_followup_records - 合同跟进记录表</div>
                    <div class="table-item">crm_payment_followup_records - 回款跟进记录表</div>
                    <div class="table-item">crm_contact_followers - 联系人关注表</div>
                    <div class="table-item">crm_business_lead_followers - 线索关注者表</div>
                    <div class="table-item">crm_business_lead_assignment_history - 线索分配历史表</div>
                    <div class="table-item">crm_business_lead_user_associations - 线索用户关联表</div>
                    <div class="table-item">crm_customer_operation_log - 客户操作日志表</div>
                    <div class="table-item">crm_lead_operation_log - 线索操作日志表</div>
                    <div class="table-item">crm_products - 产品表</div>
                    <div class="table-item">crm_static_files - 静态文件记录表</div>
                    <div class="table-item">crm_user_hierarchy - 用户层级关系表</div>
                    <div class="table-item">crm_business_contract_user_relations - 合同用户关系表</div>
                </div>
            </div>

            <div class="table-group">
                <h4>工作流引擎表 (20+张)</h4>
                <div class="table-list">
                    <div class="table-item">act_evt_log - 事件日志表</div>
                    <div class="table-item">act_ge_bytearray - 二进制数据表</div>
                    <div class="table-item">act_ge_property - 属性表</div>
                    <div class="table-item">act_re_deployment - 部署表</div>
                    <div class="table-item">act_re_procdef - 流程定义表</div>
                    <div class="table-item">act_ru_execution - 流程执行表</div>
                    <div class="table-item">act_ru_task - 任务表</div>
                    <div class="table-item">act_ru_variable - 变量表</div>
                    <div class="table-item">act_hi_procinst - 历史流程实例表</div>
                    <div class="table-item">act_hi_taskinst - 历史任务实例表</div>
                    <div class="table-item">crm_business_ap_approval_history - 审批历史表</div>
                    <div class="table-item">crm_business_ap_approval_process - 审批处理表</div>
                    <div class="table-item">crm_business_ap_my_application - 我的申请表</div>
                    <div class="table-item">crm_business_ap_node_detail - 审批节点详情表</div>
                    <div class="table-item">crm_business_ap_process_instance - 流程实例表</div>
                </div>
            </div>

            <div class="table-group">
                <h4>系统管理表 (15+张)</h4>
                <div class="table-list">
                    <div class="table-item">sys_user - 用户信息表</div>
                    <div class="table-item">sys_dept - 部门表</div>
                    <div class="table-item">sys_role - 角色信息表</div>
                    <div class="table-item">sys_menu - 菜单权限表</div>
                    <div class="table-item">sys_user_role - 用户角色关联表</div>
                    <div class="table-item">sys_role_menu - 角色菜单关联表</div>
                    <div class="table-item">sys_user_post - 用户岗位关联表</div>
                    <div class="table-item">sys_post - 岗位信息表</div>
                    <div class="table-item">sys_config - 参数配置表</div>
                    <div class="table-item">sys_dict_type - 字典类型表</div>
                    <div class="table-item">sys_dict_data - 字典数据表</div>
                    <div class="table-item">sys_notice - 通知公告表</div>
                    <div class="table-item">sys_oper_log - 操作日志记录</div>
                    <div class="table-item">sys_logininfor - 系统访问记录</div>
                    <div class="table-item">crm_menu - CRM菜单权限表</div>
                </div>
            </div>

            <div class="table-group">
                <h4>定时任务表 (10+张)</h4>
                <div class="table-list">
                    <div class="table-item">sys_job - 定时任务调度表</div>
                    <div class="table-item">sys_job_log - 定时任务调度日志表</div>
                    <div class="table-item">qrtz_job_details - 任务详细信息表</div>
                    <div class="table-item">qrtz_triggers - 触发器详细信息表</div>
                    <div class="table-item">qrtz_cron_triggers - Cron类型触发器表</div>
                    <div class="table-item">qrtz_simple_triggers - 简单触发器信息表</div>
                    <div class="table-item">qrtz_fired_triggers - 已触发触发器表</div>
                    <div class="table-item">qrtz_scheduler_state - 调度器状态表</div>
                    <div class="table-item">qrtz_locks - 存储悲观锁信息表</div>
                    <div class="table-item">qrtz_calendars - 日历信息表</div>
                </div>
            </div>

            <div class="table-group">
                <h4>第三方集成表 (5张)</h4>
                <div class="table-list">
                    <div class="table-item">crm_thirdparty_wechat - 企业微信第三方登录关联表</div>
                    <div class="table-item">crm_wecom_config - 企业微信配置表</div>
                    <div class="table-item">gen_table - 代码生成业务表</div>
                    <div class="table-item">gen_table_column - 代码生成业务表字段</div>
                    <div class="table-item">crm_business - 业务表</div>
                </div>
            </div>

            <div class="table-group">
                <h4>视图 (3个)</h4>
                <div class="table-list">
                    <div class="table-item">v_customer_contacts - 客户联系人视图</div>
                    <div class="table-item">v_user_followed_contacts_stats - 用户关注联系人统计视图</div>
                    <div class="table-item">v_user_hierarchy_relations - 用户层级关系视图</div>
                </div>
            </div>
        </section>

        <section id="features">
            <h2>6. 系统特性</h2>
            
            <div class="feature-list">
                <div class="feature-item">
                    <h4>📊 完整的销售流程管理</h4>
                    <ul>
                        <li>线索管理：线索获取、分配、跟进、转化</li>
                        <li>客户管理：客户信息、联系人关系、跟进记录</li>
                        <li>商机管理：商机创建、阶段管理、赢单率跟踪</li>
                        <li>合同管理：合同签订、执行跟踪、状态管理</li>
                        <li>回款管理：回款计划、回款记录、催收管理</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>🔄 工作流支持</h4>
                    <ul>
                        <li>基于Activiti工作流引擎</li>
                        <li>审批流程管理</li>
                        <li>流程实例跟踪</li>
                        <li>历史记录追溯</li>
                        <li>自定义审批节点</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>👥 用户权限管理</h4>
                    <ul>
                        <li>多级部门组织架构</li>
                        <li>角色权限控制</li>
                        <li>菜单权限管理</li>
                        <li>数据权限隔离</li>
                        <li>用户层级关系</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>📝 跟进记录系统</h4>
                    <ul>
                        <li>各业务模块独立跟进记录</li>
                        <li>多媒体文件附件支持</li>
                        <li>跟进质量评级</li>
                        <li>下次联系时间提醒</li>
                        <li>跟进内容搜索</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>🔍 操作日志追踪</h4>
                    <ul>
                        <li>客户操作日志</li>
                        <li>线索操作日志</li>
                        <li>系统操作日志</li>
                        <li>登录访问记录</li>
                        <li>审批历史记录</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>📱 第三方集成</h4>
                    <ul>
                        <li>企业微信集成</li>
                        <li>单点登录支持</li>
                        <li>文件存储管理</li>
                        <li>产品目录管理</li>
                        <li>代码生成工具</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>⏰ 定时任务调度</h4>
                    <ul>
                        <li>基于Quartz调度器</li>
                        <li>任务执行监控</li>
                        <li>任务日志记录</li>
                        <li>失败重试机制</li>
                        <li>任务状态管理</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>📊 数据统计分析</h4>
                    <ul>
                        <li>客户关注统计视图</li>
                        <li>用户层级关系视图</li>
                        <li>销售漏斗分析</li>
                        <li>业绩统计报表</li>
                        <li>转化率分析</li>
                    </ul>
                </div>
            </div>

            <div class="highlight">
                <h4>💡 数据库设计亮点</h4>
                <ul>
                    <li><strong>模块化设计</strong>：各业务模块相对独立，便于维护和扩展</li>
                    <li><strong>软删除机制</strong>：使用del_flag字段实现软删除，保护历史数据</li>
                    <li><strong>审计字段完整</strong>：create_time、update_time、create_by、update_by等审计字段齐全</li>
                    <li><strong>关系设计合理</strong>：通过中间表处理多对多关系，避免数据冗余</li>
                    <li><strong>索引设计优化</strong>：为常用查询字段建立了合适的索引</li>
                    <li><strong>JSON字段支持</strong>：使用JSON字段存储非结构化数据，提高灵活性</li>
                    <li><strong>视图简化查询</strong>：创建视图简化复杂的关联查询</li>
                </ul>
            </div>
        </section>

        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; color: #666;">
            <p><strong>CRM数据库分析报告</strong></p>
            <p>生成时间: 2025年6月29日 | 版本: CRM412</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#4CAF50',
                primaryTextColor: '#fff',
                primaryBorderColor: '#2c5530',
                lineColor: '#666',
                sectionBkgColor: '#f5f7fa',
                altSectionBkgColor: '#e8f5e8',
                gridColor: '#ccc',
                textColor: '#333'
            }
        });
        
        // 缩放功能实现
        const zoomLevels = {
            'diagram1': 1,
            'diagram2': 1
        };
        
        function updateZoomLevel(diagramId, scale) {
            const content = document.getElementById(`diagram-content-${diagramId.slice(-1)}`);
            const levelDisplay = document.getElementById(`zoom-level-${diagramId.slice(-1)}`);
            
            content.style.transform = `scale(${scale})`;
            levelDisplay.textContent = Math.round(scale * 100) + '%';
            zoomLevels[diagramId] = scale;
        }
        
        function zoomIn(diagramId) {
            const currentScale = zoomLevels[diagramId];
            const newScale = Math.min(currentScale * 1.2, 3); // 最大3倍
            updateZoomLevel(diagramId, newScale);
        }
        
        function zoomOut(diagramId) {
            const currentScale = zoomLevels[diagramId];
            const newScale = Math.max(currentScale / 1.2, 0.3); // 最小0.3倍
            updateZoomLevel(diagramId, newScale);
        }
        
        function resetZoom(diagramId) {
            updateZoomLevel(diagramId, 1);
        }
        
        function toggleFullscreen(diagramId) {
            const container = document.getElementById(diagramId);
            
            if (container.classList.contains('diagram-fullscreen')) {
                container.classList.remove('diagram-fullscreen');
                document.body.style.overflow = 'auto';
            } else {
                container.classList.add('diagram-fullscreen');
                document.body.style.overflow = 'hidden';
            }
        }
        
        // 鼠标滚轮缩放
        function addWheelZoom(diagramId) {
            const container = document.getElementById(diagramId);
            
            container.addEventListener('wheel', function(e) {
                e.preventDefault();
                
                const currentScale = zoomLevels[diagramId];
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const newScale = Math.max(0.3, Math.min(3, currentScale * delta));
                
                updateZoomLevel(diagramId, newScale);
            });
        }
        
        // 拖拽功能
        function addDragFunctionality(diagramId) {
            const container = document.getElementById(diagramId);
            const content = document.getElementById(`diagram-content-${diagramId.slice(-1)}`);
            
            let isDragging = false;
            let startX, startY;
            let scrollLeft, scrollTop;
            
            container.addEventListener('mousedown', function(e) {
                // 只在内容区域启用拖拽，避免与控制按钮冲突
                if (e.target.closest('.zoom-controls')) return;
                
                isDragging = true;
                container.style.cursor = 'grabbing';
                
                startX = e.pageX - container.offsetLeft;
                startY = e.pageY - container.offsetTop;
                scrollLeft = container.scrollLeft;
                scrollTop = container.scrollTop;
                
                e.preventDefault();
            });
            
            container.addEventListener('mouseleave', function() {
                isDragging = false;
                container.style.cursor = 'default';
            });
            
            container.addEventListener('mouseup', function() {
                isDragging = false;
                container.style.cursor = 'default';
            });
            
            container.addEventListener('mousemove', function(e) {
                if (!isDragging) return;
                
                e.preventDefault();
                
                const x = e.pageX - container.offsetLeft;
                const y = e.pageY - container.offsetTop;
                const walkX = (x - startX) * 2; // 调整拖拽灵敏度
                const walkY = (y - startY) * 2;
                
                container.scrollLeft = scrollLeft - walkX;
                container.scrollTop = scrollTop - walkY;
            });
            
            // 设置默认鼠标样式
            container.style.cursor = 'grab';
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // 退出所有全屏
                document.querySelectorAll('.diagram-fullscreen').forEach(el => {
                    el.classList.remove('diagram-fullscreen');
                });
                document.body.style.overflow = 'auto';
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addWheelZoom('diagram1');
            addWheelZoom('diagram2');
            addDragFunctionality('diagram1');
            addDragFunctionality('diagram2');
        });
    </script>
</body>
</html>
