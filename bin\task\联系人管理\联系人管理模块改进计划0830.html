<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人管理模块改进计划</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        /* 代码高亮样式 */
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .sql-keyword { color: #63b3ed; font-weight: bold; }
        .sql-string { color: #68d391; }
        .sql-comment { color: #a0aec0; font-style: italic; }
        .sql-number { color: #f6ad55; }
        .java-keyword { color: #63b3ed; font-weight: bold; }
        .java-annotation { color: #f6ad55; }
        .java-string { color: #68d391; }
        .java-comment { color: #a0aec0; font-style: italic; }
        .java-number { color: #f6ad55; }
        .java-type { color: #81c784; }
        .java-identifier { color: #e2e8f0; }
        .yaml-key { color: #63b3ed; font-weight: bold; }
        .yaml-value { color: #68d391; }
        .yaml-comment { color: #a0aec0; font-style: italic; }
        .bash-keyword { color: #63b3ed; font-weight: bold; }
        .bash-string { color: #68d391; }
        .bash-comment { color: #a0aec0; font-style: italic; }
        .bash-variable { color: #f6ad55; }
        /* Mermaid图表容器 */
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        .status-completed {
            color: #27ae60;
            font-weight: bold;
        }
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        .priority-high {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-medium {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-low {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .risk-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .phase-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .architecture-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        /* 任务清单样式 */
        .phase-box ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .phase-box ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        .phase-box ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        .phase-box ul li label:hover {
            color: #007bff;
        }
        
        /* 验收标准样式 */
        h3 + ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        h3 + ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        h3 + ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        h3 + ul li label:hover {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人管理模块改进计划</h1>
        
        <h2>项目背景</h2>
        <p>基于对现有联系人管理模块的深入分析，发现了数据设计、功能实现和用户体验方面的关键问题。本改进计划旨在通过系统性的优化，提升联系人管理的完整性和用户体验，并实现CRM系统中各实体的统一团队成员管理。</p>
        
        <h2>现状分析</h2>
        
        <h3>已完成功能</h3>
        <ul>
            <li><span class="status-completed">✅</span> 基础联系人CRUD操作</li>
            <li><span class="status-completed">✅</span> 联系人列表展示和分页</li>
            <li><span class="status-completed">✅</span> 基本的搜索和筛选</li>
            <li><span class="status-completed">✅</span> 联系人详情抽屉框架</li>
        </ul>
        
        <h3>发现的关键问题</h3>
        <ul>
            <li><span class="status-pending">❌</span> <strong>功能不完整</strong>："下属负责的联系人"筛选无法实现</li>
            <li><span class="status-pending">❌</span> <strong>关注功能缺失</strong>：缺少联系人关注表和相关功能</li>
            <li><span class="status-pending">❌</span> <strong>前后端不一致</strong>：前端传递filterType参数，后端未处理</li>
            <li><span class="status-pending">❌</span> <strong>查询逻辑不完整</strong>：CrmContactsMapper.xml缺少基于用户关系的筛选</li>
            <li><span class="status-pending">❌</span> <strong>团队管理不统一</strong>：联系人有团队成员管理，而其他实体只有单一负责人</li>
            <li><span class="status-pending">❌</span> <strong>代码重复</strong>：各实体的团队/负责人管理逻辑分散，导致代码重复</li>
        </ul>
        
        <h2>改进目标</h2>
        
        <div class="success-box">
            <h3>核心目标</h3>
            <ol>
                <li><strong>完善数据架构</strong>：建立完整的用户关系和联系人关注体系</li>
                <li><strong>实现完整筛选</strong>：支持"我负责的"、"下属负责的"、"我关注的"等筛选功能</li>
                <li><strong>提升用户体验</strong>：优化界面交互和数据展示</li>
                <li><strong>增强系统扩展性</strong>：为未来功能扩展奠定基础</li>
                <li><strong>统一团队管理</strong>：实现CRM各实体（联系人、客户、商机、线索等）的统一团队成员管理</li>
            </ol>
        </div>
        
        <h2>技术架构设计</h2>
        
        <div class="architecture-box">
            <h3>数据库架构改进</h3>
            
            <div class="mermaid">
erDiagram
    crm_business_contacts {
        bigint id PK
        bigint responsible_person_id FK
        varchar name
        varchar mobile
        varchar phone
        varchar email
        varchar position
        tinyint is_key_decision_maker
        varchar direct_superior
        varchar address
        varchar detailed_address
        datetime next_contact_time
        date selected_date
        char gender
        text remarks
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
    }
    
    crm_user_hierarchy {
        bigint id PK
        bigint user_id FK
        bigint superior_id FK
        int hierarchy_level
        datetime create_time
        datetime update_time
        varchar create_by
        varchar update_by
        char del_flag
    }
    
    crm_contact_followers {
        bigint id PK
        bigint contact_id FK
        bigint follower_id FK
        datetime follow_time
        tinyint is_active
        varchar create_by
        datetime create_time
    }
    
    crm_team_members {
        bigint id PK
        varchar entity_type
        bigint entity_id
        bigint user_id FK
        varchar role_type
        json permissions
        bigint assigned_by FK
        datetime assigned_at
        char status
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
        varchar remark
    }
    
    sys_user {
        bigint user_id PK
        bigint dept_id FK
        varchar user_name
        varchar nick_name
        varchar email
        varchar phonenumber
        char sex
        varchar avatar
        varchar password
        char status
        char del_flag
        varchar login_ip
        datetime login_date
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
        varchar remark
    }
    
    crm_business_contacts ||--o{ crm_contact_followers : "被关注"
    sys_user ||--o{ crm_contact_followers : "关注者"
    sys_user ||--o{ crm_business_contacts : "负责人"
    sys_user ||--o{ crm_user_hierarchy : "用户"
    sys_user ||--o{ crm_user_hierarchy : "上级"
    sys_user ||--o{ crm_team_members : "团队成员"
    sys_user ||--o{ crm_team_members : "分配人"
            </div>
            
            <h4>1. 统一团队成员表设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 创建统一的团队成员表</span>
<span class="sql-keyword">CREATE TABLE</span> crm_team_members (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
    entity_type <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'实体类型：contact-联系人, customer-客户, opportunity-商机, lead-线索'</span>,
    entity_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'实体ID'</span>,
    user_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'用户ID，关联sys_user表'</span>,
    role_type <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL DEFAULT</span> <span class="sql-string">'member'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'角色类型：owner-负责人，admin-管理员，member-成员'</span>,
    permissions <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'权限列表：["view","edit","delete","assign"]'</span>,
    assigned_by <span class="sql-keyword">BIGINT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'分配人ID'</span>,
    assigned_at <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'分配时间'</span>,
    status <span class="sql-keyword">CHAR</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'0'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'状态：0-正常，1-停用'</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
    update_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新者'</span>,
    update_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
    remark <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">500</span>) <span class="sql-keyword">DEFAULT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'备注'</span>,
    
    <span class="sql-keyword">INDEX</span> idx_entity_type_id (entity_type, entity_id),
    <span class="sql-keyword">INDEX</span> idx_user_id (user_id),
    <span class="sql-keyword">INDEX</span> idx_role_type (role_type),
    <span class="sql-keyword">INDEX</span> idx_status (status),
    <span class="sql-keyword">UNIQUE KEY</span> uk_entity_user (entity_type, entity_id, user_id),
    
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (assigned_by) <span class="sql-keyword">REFERENCES</span> sys_user(user_id) <span class="sql-keyword">ON DELETE SET NULL</span>
) <span class="sql-keyword">ENGINE</span>=InnoDB <span class="sql-keyword">DEFAULT CHARSET</span>=utf8mb4 <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM统一团队成员关系表'</span>;</code></pre>
            
            <h4>2. 团队权限视图设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 创建统一的团队权限视图</span>
<span class="sql-keyword">CREATE VIEW</span> v_team_permissions <span class="sql-keyword">AS</span>
<span class="sql-keyword">SELECT</span> 
    tm.entity_type,
    tm.entity_id,
    tm.user_id,
    u.user_name,
    u.nick_name,
    tm.role_type,
    tm.permissions,
    tm.status,
    sr.role_name <span class="sql-keyword">as</span> system_role,
    sr.data_scope,
    <span class="sql-keyword">CASE</span> 
        <span class="sql-keyword">WHEN</span> sr.data_scope = <span class="sql-string">'1'</span> <span class="sql-keyword">THEN</span> 1  <span class="sql-comment">-- 超级管理员</span>
        <span class="sql-keyword">WHEN</span> sr.data_scope = <span class="sql-string">'4'</span> <span class="sql-keyword">AND</span> tm.role_type = <span class="sql-string">'admin'</span> <span class="sql-keyword">THEN</span> 1  <span class="sql-comment">-- 团队管理员</span>
        <span class="sql-keyword">WHEN</span> tm.role_type = <span class="sql-string">'owner'</span> <span class="sql-keyword">THEN</span> 1  <span class="sql-comment">-- 负责人</span>
        <span class="sql-keyword">ELSE</span> 0
    <span class="sql-keyword">END as</span> can_manage_team
<span class="sql-keyword">FROM</span> crm_team_members tm
<span class="sql-keyword">LEFT JOIN</span> sys_user u <span class="sql-keyword">ON</span> tm.user_id = u.user_id
<span class="sql-keyword">LEFT JOIN</span> sys_user_role sur <span class="sql-keyword">ON</span> u.user_id = sur.user_id
<span class="sql-keyword">LEFT JOIN</span> sys_role sr <span class="sql-keyword">ON</span> sur.role_id = sr.role_id
<span class="sql-keyword">WHERE</span> tm.status = <span class="sql-string">'0'</span> <span class="sql-keyword">AND</span> u.status = <span class="sql-string">'0'</span>;</code></pre>
            
            <h4>3. CRM用户层级关系表设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 新增CRM用户层级关系表（不侵入框架sys表）</span>
<span class="sql-keyword">CREATE TABLE</span> crm_user_hierarchy (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span>,
    user_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'用户ID'</span>,
    superior_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'上级用户ID'</span>,
    hierarchy_level <span class="sql-keyword">INT DEFAULT</span> <span class="sql-number">1</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'层级深度'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span>,
    update_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    update_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新者'</span>,
    del_flag <span class="sql-keyword">CHAR</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'0'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'删除标志（0代表存在 2代表删除）'</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">FOREIGN KEY</span> (superior_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">UNIQUE KEY</span> uk_user_superior (user_id, superior_id)
) <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM用户层级关系表'</span>;</code></pre>
            
            <h4>4. 联系人关注表设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 新增联系人关注表</span>
<span class="sql-keyword">CREATE TABLE</span> crm_contact_followers (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span>,
    contact_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系人ID'</span>,
    follower_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注者用户ID'</span>,
    follow_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注时间'</span>,
    is_active <span class="sql-keyword">TINYINT</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-number">1</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'是否有效关注'</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (contact_id) <span class="sql-keyword">REFERENCES</span> crm_business_contacts(id),
    <span class="sql-keyword">FOREIGN KEY</span> (follower_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">UNIQUE KEY</span> uk_contact_follower (contact_id, follower_id)
) <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'联系人关注表'</span>;</code></pre>
            
            <h4>5. 数据初始化脚本</h4>
            <pre><code class="sql"><span class="sql-comment">-- 基于部门关系初始化用户层级关系（示例）</span>
<span class="sql-keyword">INSERT INTO</span> crm_user_hierarchy (user_id, superior_id, hierarchy_level, create_by)
<span class="sql-keyword">SELECT</span> 
    u1.user_id,
    u2.user_id <span class="sql-keyword">AS</span> superior_id,
    <span class="sql-number">1</span> <span class="sql-keyword">AS</span> hierarchy_level,
    <span class="sql-string">'system'</span> <span class="sql-keyword">AS</span> create_by
<span class="sql-keyword">FROM</span> sys_user u1
<span class="sql-keyword">JOIN</span> sys_dept d1 <span class="sql-keyword">ON</span> u1.dept_id = d1.dept_id
<span class="sql-keyword">JOIN</span> sys_dept d2 <span class="sql-keyword">ON</span> d1.parent_id = d2.dept_id
<span class="sql-keyword">JOIN</span> sys_user u2 <span class="sql-keyword">ON</span> u2.dept_id = d2.dept_id
<span class="sql-keyword">WHERE</span> u1.del_flag = <span class="sql-string">'0'</span> <span class="sql-keyword">AND</span> u2.del_flag = <span class="sql-string">'0'</span>
<span class="sql-keyword">AND</span> u1.user_id != u2.user_id;</code></pre>
        </div>
        
        <h2>实施计划</h2>
        
        <div class="phase-box">
            <h3>第一阶段：数据库结构改进 <span class="priority-high">优先级：高</span></h3>
            
            <h4>1.1 创建用户关系表</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>执行用户层级关系表创建SQL</li>
                <li>初始化现有用户的层级关系数据</li>
                <li>创建用户关系管理的基础API</li>
            </ol>
            
            <h4>1.2 创建联系人关注表</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>执行联系人关注表创建SQL</li>
                <li>设计关注/取消关注的业务逻辑</li>
                <li>创建关注管理的API接口</li>
            </ol>
            
            <h4>1.3 创建统一团队成员表</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>执行统一团队成员表创建SQL</li>
                <li>创建团队权限视图</li>
                <li>开发CrmTeamMember实体类和Mapper接口</li>
                <li>实现ICrmTeamMemberService接口及其实现类</li>
                <li>开发CrmTeamController</li>
            </ol>
                    
            <p><strong>预估时间：</strong> 4天</p>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：后端API增强 <span class="priority-high">优先级：高</span></h3>
            
            <div class="mermaid">
flowchart TD
    A[前端筛选请求] --> B{筛选类型判断}
    B -->|mine| C[查询我负责的联系人]
    B -->|subordinate| D[查询下属负责的联系人]
    B -->|following| E[查询我关注的联系人]
    B -->|all| F[查询全部联系人]
    
    D --> G[CrmUserHierarchyService]
    G --> H[获取下属用户ID列表]
    
    E --> I[CrmContactFollowService]
    I --> J[获取关注的联系人ID列表]
    
    C --> K[CrmContactsService]
    H --> K
    J --> K
    F --> K
    
    K --> L[CrmContactsMapper]
    L --> M[数据库查询]
    M --> N[返回联系人列表]
            </div>
            
            <h4>2.1 增强CrmContactsController</h4>
            <pre><code class="java"><span class="java-comment">// 修改联系人列表查询方法</span>
<span class="java-annotation">@GetMapping</span>(<span class="java-string">"/list"</span>)
<span class="java-keyword">public</span> TableDataInfo getContactsList(CrmContacts crmContacts, 
                                   <span class="java-annotation">@RequestParam</span>(required = <span class="java-keyword">false</span>) String filterType) {
    startPage();
    <span class="java-comment">// 获取当前登录用户</span>
    Long currentUserId = SecurityUtils.getUserId();
    
    <span class="java-comment">// 根据筛选类型设置查询条件</span>
    <span class="java-keyword">if</span> (<span class="java-string">"mine"</span>.equals(filterType)) {
        crmContacts.setResponsiblePersonId(currentUserId);
    } <span class="java-keyword">else if</span> (<span class="java-string">"subordinate"</span>.equals(filterType)) {
        <span class="java-comment">// 查询下属负责的联系人</span>
        List&lt;Long&gt; subordinateIds = crmUserHierarchyService.getSubordinateIds(currentUserId);
        crmContacts.setSubordinateIds(subordinateIds);
    } <span class="java-keyword">else if</span> (<span class="java-string">"following"</span>.equals(filterType)) {
        <span class="java-comment">// 查询关注的联系人</span>
        crmContacts.setFollowerId(currentUserId);
    }
    
    List&lt;CrmContacts&gt; list = crmContactsService.selectCrmContactsList(crmContacts);
    <span class="java-keyword">return</span> getDataTable(list);
}</code></pre>
            
            <h4>2.2 创建CRM用户层级服务</h4>
            <pre><code class="java"><span class="java-comment">// CrmUserHierarchyService接口</span>
<span class="java-keyword">public interface</span> ICrmUserHierarchyService {
    <span class="java-comment">/**
     * 获取用户的直接下属ID列表
     */</span>
    List&lt;Long&gt; getSubordinateIds(Long userId);
    
    <span class="java-comment">/**
     * 获取用户的所有下属ID列表（多级）
     */</span>
    List&lt;Long&gt; getAllSubordinateIds(Long userId, <span class="java-keyword">int</span> maxLevel);
    
    <span class="java-comment">/**
     * 添加用户层级关系
     */</span>
    <span class="java-keyword">int</span> insertUserHierarchy(CrmUserHierarchy hierarchy);
    
    <span class="java-comment">/**
     * 删除用户层级关系
     */</span>
    <span class="java-keyword">int</span> deleteUserHierarchy(Long userId, Long superiorId);
}</code></pre>
            
            <h4>2.3 创建联系人关注服务</h4>
            <pre><code class="java"><span class="java-comment">// CrmContactFollowService接口</span>
<span class="java-keyword">public interface</span> ICrmContactFollowService {
    <span class="java-comment">/**
     * 关注联系人
     */</span>
    <span class="java-keyword">int</span> followContact(Long contactId, Long followerId);
    
    <span class="java-comment">/**
     * 取消关注联系人
     */</span>
    <span class="java-keyword">int</span> unfollowContact(Long contactId, Long followerId);
    
    <span class="java-comment">/**
     * 检查是否关注
     */</span>
    <span class="java-keyword">boolean</span> isFollowing(Long contactId, Long followerId);
    
    <span class="java-comment">/**
     * 获取用户关注的联系人ID列表
     */</span>
    List&lt;Long&gt; getFollowingContactIds(Long followerId);
}</code></pre>
            
            <h4>2.4 创建统一团队成员服务</h4>
            <pre><code class="java"><span class="java-comment">// CrmTeamMemberService接口</span>
<span class="java-keyword">public interface</span> ICrmTeamMemberService {
    <span class="java-comment">/**
     * 查询团队成员列表
     */</span>
    List&lt;CrmTeamMember&gt; selectTeamMemberList(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 根据实体类型和ID获取团队成员
     */</span>
    List&lt;CrmTeamMember&gt; getTeamMembers(String entityType, Long entityId);
    
    <span class="java-comment">/**
     * 添加团队成员
     */</span>
    <span class="java-keyword">int</span> addTeamMember(CrmTeamMember member);
    
    <span class="java-comment">/**
     * 批量添加团队成员
     */</span>
    <span class="java-keyword">int</span> batchAddMembers(String entityType, Long entityId, List&lt;Long&gt; userIds, String roleType);
    
    <span class="java-comment">/**
     * 更新成员角色
     */</span>
    <span class="java-keyword">int</span> updateMemberRole(Long id, String roleType, List&lt;String&gt; permissions);
    
    <span class="java-comment">/**
     * 移除团队成员
     */</span>
    <span class="java-keyword">int</span> removeTeamMember(Long id);
    
    <span class="java-comment">/**
     * 检查用户权限
     */</span>
    <span class="java-keyword">boolean</span> hasPermission(String entityType, Long entityId, Long userId, String permission);
    
    <span class="java-comment">/**
     * 检查是否可以管理团队
     */</span>
    <span class="java-keyword">boolean</span> canManageTeam(String entityType, Long entityId, Long userId);
}</code></pre>
            
            <p><strong>预估时间：</strong> 5天</p>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：前端UI优化 <span class="priority-medium">优先级：中</span></h3>
            
            <h4>3.1 联系人列表筛选组件</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>增强联系人列表页面的筛选功能</li>
                <li>添加"我负责的"、"下属负责的"、"我关注的"筛选选项</li>
                <li>实现筛选条件与后端API的交互</li>
            </ol>
            
            <h4>3.2 联系人关注功能</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>在联系人详情页添加关注/取消关注按钮</li>
                <li>实现关注状态的切换和显示</li>
                <li>优化关注列表的展示</li>
            </ol>
            
            <h4>3.3 统一团队成员管理组件</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>开发通用的团队成员管理组件</li>
                <li>在各实体详情页中集成团队成员管理组件</li>
                <li>实现团队成员列表、添加、编辑、删除等功能</li>
                <li>优化用户界面和交互体验</li>
            </ol>
            
            <p><strong>预估时间：</strong> 4天</p>
        </div>
        
        <div class="phase-box">
            <h3>第四阶段：测试与部署 <span class="priority-medium">优先级：中</span></h3>
            
            <h4>4.1 功能测试</h4>
            <p><strong>测试内容：</strong></p>
            <ol>
                <li>用户层级关系管理功能</li>
                <li>联系人关注功能</li>
                <li>联系人列表筛选功能</li>
                <li>统一团队成员管理功能</li>
                <li>权限控制和数据安全</li>
            </ol>
            
            <h4>4.2 性能优化</h4>
            <p><strong>优化内容：</strong></p>
            <ol>
                <li>SQL查询优化</li>
                <li>缓存策略实现</li>
                <li>前端组件渲染优化</li>
            </ol>
            
            <h4>4.3 部署上线</h4>
            <p><strong>部署步骤：</strong></p>
            <ol>
                <li>数据库脚本执行</li>
                <li>应用程序部署</li>
                <li>系统配置更新</li>
                <li>用户培训和文档更新</li>
            </ol>
            
            <p><strong>预估时间：</strong> 3天</p>
        </div>
        
        <h2>数据迁移策略</h2>
        
        <div class="phase-box">
            <h3>第一阶段：迁移联系人团队数据</h3>
            
            <pre><code class="sql"><span class="sql-comment">-- 1. 迁移现有联系人团队成员数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, permissions, 
    assigned_by, assigned_at, status, create_by, create_time, 
    update_by, update_time, remark
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'contact'</span> <span class="sql-keyword">AS</span> entity_type,
    contact_id <span class="sql-keyword">AS</span> entity_id,
    user_id,
    role_type,
    permissions,
    assigned_by,
    assigned_at,
    status,
    create_by,
    create_time,
    update_by,
    update_time,
    remark
<span class="sql-keyword">FROM</span> crm_contact_team_members;</code></pre>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：迁移其他实体的负责人数据</h3>
            
            <pre><code class="sql"><span class="sql-comment">-- 1. 迁移公司负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_by, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'customer'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    responsible_person_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    create_by,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_customers
<span class="sql-keyword">WHERE</span> responsible_person_id IS <span class="sql-keyword">NOT NULL</span>;

<span class="sql-comment">-- 2. 迁移商机负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'opportunity'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    manager_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_opportunities
<span class="sql-keyword">WHERE</span> manager_id IS <span class="sql-keyword">NOT NULL</span>;

<span class="sql-comment">-- 3. 迁移线索负责人数据</span>
<span class="sql-keyword">INSERT INTO</span> crm_team_members (
    entity_type, entity_id, user_id, role_type, 
    status, create_by, create_time
)
<span class="sql-keyword">SELECT</span> 
    <span class="sql-string">'lead'</span> <span class="sql-keyword">AS</span> entity_type,
    id <span class="sql-keyword">AS</span> entity_id,
    responsible_person_id <span class="sql-keyword">AS</span> user_id,
    <span class="sql-string">'owner'</span> <span class="sql-keyword">AS</span> role_type,
    <span class="sql-string">'0'</span> <span class="sql-keyword">AS</span> status,
    create_by,
    created_at <span class="sql-keyword">AS</span> create_time
<span class="sql-keyword">FROM</span> crm_business_leads
<span class="sql-keyword">WHERE</span> responsible_person_id IS <span class="sql-keyword">NOT NULL</span>;</code></pre>
        </div>
        
        <h2>联系人管理模块实施确认事项</h2>
        
        <div class="highlight">
            <h3>实施前需确认的关键事项</h3>
            <ol>
                <li><strong>数据库表名和字段命名</strong>：确认统一团队成员表的命名是否符合系统规范</li>
                <li><strong>权限设计</strong>：确认团队成员权限设计是否满足业务需求</li>
                <li><strong>数据迁移策略</strong>：确认现有联系人团队数据和其他实体负责人数据的迁移方案</li>
                <li><strong>前端组件复用</strong>：确认统一团队成员管理组件的设计和复用方案</li>
                <li><strong>API接口设计</strong>：确认统一团队成员管理API的设计和参数规范</li>
                <li><strong>性能考量</strong>：评估统一团队成员管理对系统性能的影响</li>
                <li><strong>用户体验</strong>：确认新功能对现有用户操作流程的影响</li>
                <li><strong>测试计划</strong>：制定详细的功能测试和回归测试计划</li>
                <li><strong>回滚方案</strong>：准备数据迁移失败时的回滚方案</li>
                <li><strong>上线时间</strong>：确定功能上线时间和用户培训计划</li>
            </ol>
        </div>
        
        <div class="success-box">
            <h3>预期收益</h3>
            <ul>
                <li><strong>统一的团队管理体验</strong>：所有CRM实体（联系人、客户、商机、线索）采用相同的团队成员管理模式</li>
                <li><strong>精细化的权限控制</strong>：支持不同角色类型和权限组合</li>
                <li><strong>代码复用</strong>：减少重复代码，提高系统可维护性</li>
                <li><strong>扩展性提升</strong>：新增实体类型可以直接复用团队成员管理功能</li>
                <li><strong>用户体验一致性</strong>：提供一致的用户界面和操作流程</li>
            </ul>
        </div>
    </div>
</body>
</html>