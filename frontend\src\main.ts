import { createPinia } from "pinia";
import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";

// Element Plus 相关导入
import * as Icons from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';
import 'uno.css';
// 组件注册中心
import { createComponentRegistry } from '~/components/registry';

// 样式导入 - 确保正确的加载顺序
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/src/base.scss";
import "element-plus/theme-chalk/src/message-box.scss"; // 添加MessageBox样式
import "element-plus/theme-chalk/src/message.scss";
import "./styles/index.scss";
// 确保我们的自定义样式覆盖在最后导入
// import "./styles/element-override.scss";

import './permission'; // 引入路由守卫

// 引入自定义指令
import directive from '@/directive'

// 引入全局工具函数
import { parseTime } from '@/utils/ruoyi'

const app = createApp(App);
const pinia = createPinia();

// 全局挂载 parseTime 函数
app.config.globalProperties.parseTime = parseTime;

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(Icons)) {
    app.component(key, component);
}

// app.use(ElementPlus);
// 使用插件 - 添加配置选项
app.use(ElementPlus, {
  // 设置全局配置
  size: 'default',
  zIndex: 3000,
});
app.use(createComponentRegistry());
app.use(directive); // 注册自定义指令
app.use(pinia);
app.use(router);

app.mount("#app");
