# 业务操作日志系统

## 概述

新的业务操作日志系统是对原有操作日志功能的全面升级，提供了更加灵活、强大的日志记录能力。

## 主要特性

- **灵活的注解配置**：支持方法级别的精确控制
- **自动识别功能**：自动识别业务类型和操作类型
- **字段级变更跟踪**：记录具体的字段变更信息
- **异步处理**：支持异步日志记录，不影响主业务性能
- **统一存储**：所有业务操作日志存储在统一的表中
- **丰富的查询能力**：支持多种查询方式和分页查询

## 核心组件

### 1. 注解系统

#### @BusinessLog 注解

```java
@BusinessLog(
    businessType = BusinessType.LEAD,           // 业务类型
    operationType = OperationType.CREATE,       // 操作类型
    description = "创建新线索",                  // 操作描述
    entityClass = CrmLeads.class,              // 实体类
    trackChanges = true,                       // 是否跟踪字段变更
    enabled = true,                            // 是否启用
    template = "创建了线索 {leadName}"          // 自定义模板
)
```

#### 业务类型枚举

```java
public enum BusinessType {
    AUTO,           // 自动识别
    LEAD,           // 线索
    CONTACT,        // 联系人
    CUSTOMER,       // 客户
    OPPORTUNITY,    // 商机
    CONTRACT,       // 合同
    VISIT_PLAN,     // 拜访计划
    TEAM,           // 团队
    PAYMENT,        // 支付
    RECONCILIATION  // 对账单
}
```

#### 操作类型枚举

```java
public enum OperationType {
    AUTO,           // 自动识别
    CREATE,         // 创建
    UPDATE,         // 更新
    DELETE,         // 删除
    CONVERT,        // 转换
    ASSIGN,         // 分配
    FOLLOW,         // 跟进
    UNFOLLOW,       // 取消跟进
    APPROVE,        // 审批
    REJECT,         // 拒绝
    TRANSFER,       // 转移
    IMPORT,         // 导入
    EXPORT,         // 导出
    CUSTOM          // 自定义
}
```

### 2. 核心服务

#### BusinessLogService

统一的业务日志服务接口：

```java
public interface BusinessLogService {
    // 记录单条日志
    void recordLog(BusinessLogContext context);
    
    // 批量记录日志
    void recordBatchLog(List<BusinessLogContext> contexts);
    
    // 异步记录日志
    void recordLogAsync(BusinessLogContext context);
    
    // 查询日志
    List<BusinessOperationLog> queryLogs(String businessType, Long businessId);
    
    // 分页查询日志
    List<BusinessOperationLog> queryLogsPage(String businessType, Long businessId, int pageNum, int pageSize);
    
    // 根据操作人查询日志
    List<BusinessOperationLog> queryLogsByOperator(Long operatorId, int pageNum, int pageSize);
    
    // 删除过期日志
    int deleteExpiredLogs(int days);
}
```

### 3. 数据模型

#### BusinessLogContext

操作日志上下文，包含完整的操作信息：

```java
public class BusinessLogContext {
    private BusinessType businessType;      // 业务类型
    private OperationType operationType;    // 操作类型
    private Long businessId;                // 业务ID
    private String businessName;            // 业务名称
    private String description;             // 操作描述
    private Object beforeData;              // 操作前数据
    private Object afterData;               // 操作后数据
    private List<FieldChange> fieldChanges; // 字段变更列表
    private Long operatorId;                // 操作人ID
    private String operatorName;            // 操作人姓名
    private Date operationTime;             // 操作时间
    private String ipAddress;               // IP地址
    private String userAgent;               // 用户代理
    private String extraData;               // 扩展数据
    private Boolean success;                // 是否成功
    private String errorMessage;            // 错误信息
    private Long executionTime;             // 执行时间
    // ... 其他字段
}
```

#### FieldChange

字段变更记录：

```java
public class FieldChange {
    private String fieldName;     // 字段名
    private String fieldLabel;    // 字段标签
    private Object oldValue;      // 旧值
    private Object newValue;      // 新值
    private String changeType;    // 变更类型
    private String fieldType;     // 字段类型
}
```

### 4. 数据库表结构

```sql
CREATE TABLE `business_operation_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `business_type` varchar(50) NOT NULL,
    `business_id` bigint(20) NOT NULL,
    `business_name` varchar(200) DEFAULT NULL,
    `operation_type` varchar(50) NOT NULL,
    `operation_desc` varchar(500) DEFAULT NULL,
    `field_changes` json DEFAULT NULL,
    `before_data` json DEFAULT NULL,
    `after_data` json DEFAULT NULL,
    `operator_id` bigint(20) NOT NULL,
    `operator_name` varchar(100) NOT NULL,
    `operation_time` datetime NOT NULL,
    `ip_address` varchar(50) DEFAULT NULL,
    `user_agent` varchar(500) DEFAULT NULL,
    `extra_data` text DEFAULT NULL,
    `success` tinyint(1) DEFAULT 1,
    `error_message` varchar(1000) DEFAULT NULL,
    `method_name` varchar(200) DEFAULT NULL,
    `request_params` text DEFAULT NULL,
    `response_result` text DEFAULT NULL,
    `execution_time` bigint(20) DEFAULT NULL,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_business` (`business_type`, `business_id`),
    KEY `idx_operation` (`operation_type`, `operation_time`),
    KEY `idx_operator` (`operator_id`, `operation_time`)
);
```

## 使用方法

### 1. 基本使用

在控制器方法上添加 `@BusinessLog` 注解：

```java
@RestController
@RequestMapping("/api/leads")
public class LeadController {
    
    @PostMapping
    @BusinessLog(
        businessType = BusinessType.LEAD,
        operationType = OperationType.CREATE,
        description = "创建新线索"
    )
    public AjaxResult createLead(@RequestBody Lead lead) {
        return leadService.create(lead);
    }
    
    @PutMapping("/{id}")
    @BusinessLog(
        businessType = BusinessType.LEAD,
        operationType = OperationType.UPDATE,
        description = "更新线索信息",
        trackChanges = true
    )
    public AjaxResult updateLead(@PathVariable Long id, @RequestBody Lead lead) {
        return leadService.update(id, lead);
    }
}
```

### 2. 自动识别

系统可以自动识别业务类型和操作类型：

```java
@PostMapping
@BusinessLog  // 自动识别为 LEAD.CREATE
public AjaxResult createLead(@RequestBody Lead lead) {
    return leadService.create(lead);
}
```

### 3. 自定义模板

使用自定义模板生成操作描述：

```java
@PostMapping("/{id}/convert")
@BusinessLog(
    businessType = BusinessType.LEAD,
    operationType = OperationType.CONVERT,
    template = "将线索 {leadName} 转换为客户"
)
public AjaxResult convertLead(@PathVariable Long id, @RequestBody ConvertRequest request) {
    return leadService.convertToCustomer(id, request);
}
```

### 4. 禁用日志记录

对于某些不需要记录日志的操作：

```java
@PostMapping("/export")
@BusinessLog(enabled = false)
public AjaxResult export(@RequestBody ExportRequest request) {
    return exportService.export(request);
}
```

### 5. 查询操作日志

通过服务接口查询操作日志：

```java
@Autowired
private BusinessLogService businessLogService;

// 查询线索的所有操作日志
List<BusinessOperationLog> logs = businessLogService.queryLogs("LEAD", 1L);

// 分页查询
List<BusinessOperationLog> pageData = businessLogService.queryLogsPage("LEAD", 1L, 1, 10);

// 查询操作人的日志
List<BusinessOperationLog> operatorLogs = businessLogService.queryLogsByOperator(1L, 1, 10);
```

## 性能特性

### 1. 异步处理

日志记录采用异步处理，不影响主业务性能：

```java
@Async
public void recordLogAsync(BusinessLogContext context) {
    recordLog(context);
}
```

### 2. 批量处理

支持批量记录操作日志：

```java
public void recordBatchLog(List<BusinessLogContext> contexts) {
    List<BusinessOperationLog> logs = contexts.stream()
            .map(this::convertToLog)
            .collect(Collectors.toList());
    businessOperationLogMapper.insertBatchLogs(logs);
}
```

### 3. 索引优化

数据库表建立了合适的索引：

- `idx_business` (business_type, business_id)
- `idx_operation` (operation_type, operation_time)
- `idx_operator` (operator_id, operation_time)

## 测试

### 1. 运行测试

```bash
# 运行所有业务日志测试
./testbat/run-business-log-tests.bat

# 运行特定测试
mvn test -Dtest=BusinessLogAspectTest
mvn test -Dtest=BusinessLogServiceIntegrationTest
mvn test -Dtest=BusinessLogServicePerformanceTest
```

### 2. 测试覆盖

- **AOP测试**：测试注解驱动的日志记录
- **集成测试**：测试完整的日志记录流程
- **性能测试**：测试大量日志记录的性能

## 迁移指南

### 1. 从旧系统迁移

```java
// 旧系统
@OperationLog(
    businessType = "线索",
    entityClass = CrmLeads.class,
    serviceClass = CrmLeadOperationLogServiceImpl.class
)

// 新系统
@BusinessLog(
    businessType = BusinessType.LEAD,
    operationType = OperationType.CREATE,
    description = "创建线索"
)
```

### 2. 数据迁移

可以编写迁移脚本将旧的日志数据迁移到新表：

```sql
-- 迁移线索操作日志
INSERT INTO business_operation_log 
SELECT 
    id,
    'LEAD' as business_type,
    lead_id as business_id,
    -- 其他字段映射
FROM crm_lead_operation_log;
```

## 最佳实践

1. **合理使用注解**：只在需要记录日志的关键操作上使用注解
2. **描述清晰**：操作描述应该清晰明了，便于后续查询和分析
3. **字段跟踪**：对于重要的更新操作，启用字段变更跟踪
4. **异步处理**：使用异步日志记录，避免影响主业务性能
5. **定期清理**：定期清理过期的日志数据，保持系统性能

## 常见问题

### 1. 日志记录失败怎么办？

日志记录失败不会影响主业务，系统会记录错误信息：

```java
try {
    businessLogService.recordLogAsync(context);
} catch (Exception e) {
    logger.error("记录业务操作日志失败", e);
}
```

### 2. 如何自定义业务类型？

可以在枚举中添加新的业务类型：

```java
public enum BusinessType {
    // ... 现有类型
    CUSTOM_TYPE("CUSTOM_TYPE", "自定义类型");
}
```

### 3. 如何扩展字段变更跟踪？

可以通过实现自定义的字段变更分析器来扩展功能。

## 总结

新的业务操作日志系统提供了强大、灵活的日志记录能力，支持自动识别、字段跟踪、异步处理等特性，能够满足各种复杂的业务需求。通过合理使用，可以大大提升系统的可观测性和审计能力。