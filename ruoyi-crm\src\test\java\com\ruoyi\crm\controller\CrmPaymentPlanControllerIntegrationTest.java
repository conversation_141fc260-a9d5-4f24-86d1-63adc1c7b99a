package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.BaseTestCase;

@AutoConfigureWebMvc
@DisplayName("回款计划Controller集成测试")
class CrmPaymentPlanControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    private MockMvc mockMvc;

    private CrmPaymentPlan testPaymentPlan;

    @BeforeEach
    void setUp() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 创建测试数据
        testPaymentPlan = new CrmPaymentPlan();
        testPaymentPlan.setPlanNumber("PLN20240724001");
        testPaymentPlan.setCustomerId(1L);
        testPaymentPlan.setCustomerName("测试客户A");
        testPaymentPlan.setResponsibleUserId(1L);
        testPaymentPlan.setResponsibleUserName("张三");
        testPaymentPlan.setTotalAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setReceivedAmount(new BigDecimal("0.00"));
        testPaymentPlan.setRemainingAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setPlanStatus("草稿");
        testPaymentPlan.setApprovalStatus("待提交");
        testPaymentPlan.setPaymentMethod("银行转账");
        testPaymentPlan.setCurrency("CNY");
        testPaymentPlan.setPlanType("普通");
        testPaymentPlan.setRiskLevel("低");
        testPaymentPlan.setRemark("测试回款计划");
        testPaymentPlan.setCreateTime(new Date());
        testPaymentPlan.setCreateBy("test");
        testPaymentPlan.setDelFlag("0");
    }

    @Test
    @DisplayName("测试回款计划列表查询")
    void testListPaymentPlan() throws Exception {
        // 先插入测试数据
        paymentPlanService.insertPaymentPlan(testPaymentPlan);

        mockMvc.perform(get("/crm/paymentPlan/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @DisplayName("测试新增回款计划")
    void testAddPaymentPlan() throws Exception {
        String jsonContent = objectMapper.writeValueAsString(testPaymentPlan);

        MvcResult result = mockMvc.perform(post("/crm/paymentPlan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andReturn();

        // 验证数据库中是否成功插入
        String response = result.getResponse().getContentAsString();
        System.out.println("新增回款计划响应: " + response);
    }

    @Test
    @DisplayName("测试根据ID查询回款计划详情")
    void testGetPaymentPlan() throws Exception {
        // 先插入测试数据
        int rows = paymentPlanService.insertPaymentPlan(testPaymentPlan);
        assert rows > 0;
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/{id}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(planId))
                .andExpect(jsonPath("$.data.planNumber").value("PLN20240724001"))
                .andExpect(jsonPath("$.data.customerName").value("测试客户A"));
    }

    @Test
    @DisplayName("测试修改回款计划")
    void testUpdatePaymentPlan() throws Exception {
        // 先插入测试数据
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        // 修改数据
        testPaymentPlan.setCustomerName("修改后的客户名称");
        testPaymentPlan.setTotalAmount(new BigDecimal("150000.00"));
        testPaymentPlan.setRemainingAmount(new BigDecimal("150000.00"));

        String jsonContent = objectMapper.writeValueAsString(testPaymentPlan);

        mockMvc.perform(put("/crm/paymentPlan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证修改是否成功
        CrmPaymentPlan updated = paymentPlanService.selectPaymentPlanById(planId);
        assert updated.getCustomerName().equals("修改后的客户名称");
        assert updated.getTotalAmount().compareTo(new BigDecimal("150000.00")) == 0;
    }

    @Test
    @DisplayName("测试删除回款计划")
    void testDeletePaymentPlan() throws Exception {
        // 先插入测试数据
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(delete("/crm/paymentPlan/{ids}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证数据是否被软删除
        CrmPaymentPlan deleted = paymentPlanService.selectPaymentPlanById(planId);
        assert deleted == null || "1".equals(deleted.getDelFlag());
    }

    @Test
    @DisplayName("测试生成回款计划编号")
    void testGeneratePlanNumber() throws Exception {
        mockMvc.perform(get("/crm/paymentPlan/generateNumber"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isString())
                .andExpect(jsonPath("$.data").value(org.hamcrest.Matchers.startsWith("PLN")));
    }

    @Test
    @DisplayName("测试提交审批")
    void testSubmitForApproval() throws Exception {
        // 先插入测试数据
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(post("/crm/paymentPlan/submitApproval/{id}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证状态是否更新
        CrmPaymentPlan updated = paymentPlanService.selectPaymentPlanById(planId);
        assert "待审批".equals(updated.getPlanStatus()) || "审批中".equals(updated.getPlanStatus());
    }

    @Test
    @DisplayName("测试根据合同ID查询回款计划")
    void testGetPaymentPlanByContract() throws Exception {
        // 设置合同编号
        testPaymentPlan.setContractNumber("CON20240724001");
        paymentPlanService.insertPaymentPlan(testPaymentPlan);

        mockMvc.perform(get("/crm/paymentPlan/contract/{contractId}", "CON20240724001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试回款计划统计")
    void testGetCustomerStatistics() throws Exception {
        // 插入测试数据
        testPaymentPlan.setCustomerId(1L);
        paymentPlanService.insertPaymentPlan(testPaymentPlan);

        mockMvc.perform(get("/crm/paymentPlan/statistics/customer/{customerId}", 1L))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    @Test
    @DisplayName("测试回款计划导出")
    void testExportPaymentPlan() throws Exception {
        mockMvc.perform(post("/crm/paymentPlan/export")
                .param("planNumber", "")
                .param("customerName", ""))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试回款计划详情查询（包含分期和审批）")
    void testGetPaymentPlanDetails() throws Exception {
        // 先插入测试数据
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        Long planId = testPaymentPlan.getId();

        mockMvc.perform(get("/crm/paymentPlan/details/{id}", planId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.plan").isMap())
                .andExpect(jsonPath("$.data.installments").isArray())
                .andExpect(jsonPath("$.data.approvals").isArray());
    }

    @Test
    @DisplayName("测试回款计划参数验证")
    void testPaymentPlanValidation() throws Exception {
        // 测试必填字段验证
        CrmPaymentPlan invalidPlan = new CrmPaymentPlan();
        String jsonContent = objectMapper.writeValueAsString(invalidPlan);

        mockMvc.perform(post("/crm/paymentPlan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().is4xxClientError());
    }

    @Test
    @DisplayName("测试分页查询")
    void testPaginationQuery() throws Exception {
        // 插入多条测试数据
        for (int i = 1; i <= 15; i++) {
            CrmPaymentPlan plan = new CrmPaymentPlan();
            plan.setPlanNumber("PLN2024072400" + i);
            plan.setCustomerId((long) i);
            plan.setCustomerName("测试客户" + i);
            plan.setResponsibleUserId(1L);
            plan.setResponsibleUserName("张三");
            plan.setTotalAmount(new BigDecimal("10000.00"));
            plan.setReceivedAmount(new BigDecimal("0.00"));
            plan.setRemainingAmount(new BigDecimal("10000.00"));
            plan.setPlanStatus("草稿");
            plan.setApprovalStatus("待提交");
            plan.setPaymentMethod("银行转账");
            plan.setCurrency("CNY");
            plan.setPlanType("普通");
            plan.setRiskLevel("低");
            plan.setCreateTime(new Date());
            plan.setCreateBy("test");
            plan.setDelFlag("0");
            paymentPlanService.insertPaymentPlan(plan);
        }

        // 测试第一页
        mockMvc.perform(get("/crm/paymentPlan/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows.length()").value(10))
                .andExpect(jsonPath("$.total").value(15));

        // 测试第二页
        mockMvc.perform(get("/crm/paymentPlan/list")
                .param("pageNum", "2")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows.length()").value(5))
                .andExpect(jsonPath("$.total").value(15));
    }

    @Test
    @DisplayName("测试搜索功能")
    void testSearchFunctionality() throws Exception {
        // 插入测试数据
        testPaymentPlan.setCustomerName("特殊客户名称");
        paymentPlanService.insertPaymentPlan(testPaymentPlan);

        // 按客户名称搜索
        mockMvc.perform(get("/crm/paymentPlan/list")
                .param("customerName", "特殊客户"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows[0].customerName").value("特殊客户名称"));

        // 按计划编号搜索
        mockMvc.perform(get("/crm/paymentPlan/list")
                .param("planNumber", "PLN20240724001"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows[0].planNumber").value("PLN20240724001"));
    }
}