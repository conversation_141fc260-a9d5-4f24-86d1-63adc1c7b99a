<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmMenuMapper">
    
    <resultMap type="CrmMenu" id="CrmMenuResult">
        <id     property="menuId"         column="menu_id"        />
        <result property="menuName"       column="menu_name"      />
        <result property="parentId"       column="parent_id"      />
        <result property="orderNum"       column="order_num"      />
        <result property="path"           column="path"           />
        <result property="component"      column="component"      />
        <result property="query"          column="query"          />
        <result property="isFrame"        column="is_frame"       />
        <result property="isCache"        column="is_cache"       />
        <result property="menuType"       column="menu_type"      />
        <result property="visible"        column="visible"        />
        <result property="status"         column="status"         />
        <result property="perms"          column="perms"          />
        <result property="icon"           column="icon"           />
        <result property="createBy"       column="create_by"      />
        <result property="createTime"     column="create_time"    />
        <result property="updateBy"       column="update_by"      />
        <result property="updateTime"     column="update_time"    />
        <result property="remark"         column="remark"         />
        <result property="enabled"        column="enabled"        />
    </resultMap>
    
    <sql id="selectMenuVo">
        select menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, ifnull(perms,'') as perms, icon, create_by, create_time, update_by, update_time, remark, enabled 
        from crm_menu
    </sql>
    
    <select id="selectMenuList" parameterType="CrmMenu" resultMap="CrmMenuResult">
        <include refid="selectMenuVo"/>
        <where>
            <if test="menuName != null and menuName != ''">
                AND menu_name like concat('%', #{menuName}, '%')
            </if>
            <if test="visible != null and visible != ''">
                AND visible = #{visible}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="enabled != null and enabled != ''">
                AND enabled = #{enabled}
            </if>
        </where>
        order by parent_id, order_num
    </select>
    
    <select id="selectMenuById" parameterType="Long" resultMap="CrmMenuResult">
        <include refid="selectMenuVo"/>
        where menu_id = #{menuId}
    </select>
        
    <insert id="insertMenu" parameterType="CrmMenu">
        insert into crm_menu(
            <if test="menuId != null">menu_id,</if>
            <if test="menuName != null">menu_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="path != null">path,</if>
            <if test="component != null">component,</if>
            <if test="isFrame != null">is_frame,</if>
            <if test="isCache != null">is_cache,</if>
            <if test="menuType != null">menu_type,</if>
            <if test="visible != null">visible,</if>
            <if test="status != null">status,</if>
            <if test="perms != null">perms,</if>
            <if test="icon != null">icon,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        )values(
            <if test="menuId != null">#{menuId},</if>
            <if test="menuName != null">#{menuName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="path != null">#{path},</if>
            <if test="component != null">#{component},</if>
            <if test="isFrame != null">#{isFrame},</if>
            <if test="isCache != null">#{isCache},</if>
            <if test="menuType != null">#{menuType},</if>
            <if test="visible != null">#{visible},</if>
            <if test="status != null">#{status},</if>
            <if test="perms != null">#{perms},</if>
            <if test="icon != null">#{icon},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        )
    </insert>

    <update id="updateMenu" parameterType="CrmMenu">
        update crm_menu
        <set>
            <if test="menuName != null">menu_name = #{menuName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="isFrame != null">is_frame = #{isFrame},</if>
            <if test="isCache != null">is_cache = #{isCache},</if>
            <if test="menuType != null">menu_type = #{menuType},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="status != null">status = #{status},</if>
            <if test="perms != null">perms = #{perms},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where menu_id = #{menuId}
    </update>

    <delete id="deleteMenuById" parameterType="Long">
        delete from crm_menu where menu_id = #{menuId}
    </delete>

    <delete id="deleteMenuByIds" parameterType="Long">
        delete from crm_menu where menu_id in 
        <foreach collection="array" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <select id="selectMenuTreeAll" resultMap="CrmMenuResult">
        select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, m.perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from crm_menu m where m.menu_type in ('M', 'C') and m.status = 0
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="CrmMenuResult">
        select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, m.perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from crm_menu m
        left join crm_role_menu rm on m.menu_id = rm.menu_id
        left join crm_user_role ur on rm.role_id = ur.role_id
        left join crm_role ro on ur.role_id = ro.role_id
        where ur.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 0  AND ro.status = 0
        order by m.parent_id, m.order_num
    </select>
</mapper> 