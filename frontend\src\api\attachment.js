import request from '@/utils/request'

// 查询CRM通用附件列表
export function listCrmAttachment(query) {
  return request({
    url: '/front/crm/attachments/list',
    method: 'get',
    params: query
  })
}

// 查询CRM通用附件详细
export function getCrmAttachment(id) {
  return request({
    url: '/front/crm/attachments/' + id,
    method: 'get'
  })
}

// 新增CRM通用附件
export function addCrmAttachment(data) {
  return request({
    url: '/front/crm/attachments',
    method: 'post',
    data: data
  })
}

// 修改CRM通用附件
export function updateCrmAttachment(data) {
  return request({
    url: '/front/crm/attachments',
    method: 'put',
    data: data
  })
}

// 删除CRM通用附件
export function delCrmAttachment(id) {
  return request({
    url: '/front/crm/attachments/' + id,
    method: 'delete'
  })
}

// 根据实体类型和ID获取附件列表
export function getAttachmentsByEntity(entityType, entityId) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}`,
    method: 'get'
  })
}

// 获取附件统计信息
export function getAttachmentStats(entityType, entityId) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}/stats`,
    method: 'get'
  })
}

// 上传附件
export function uploadAttachment(entityType, entityId, formData) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传附件
export function batchUploadAttachment(entityType, entityId, formData) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}/batch`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载附件
export function downloadAttachment(id) {
  return request({
    url: `/front/crm/attachments/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 预览附件
export function previewAttachment(id) {
  return request({
    url: `/front/crm/attachments/preview/${id}`,
    method: 'get'
  })
}

// 根据分类获取附件列表
export function getAttachmentsByCategory(entityType, entityId, category) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}/category/${category}`,
    method: 'get'
  })
}

// 获取最近上传的附件
export function getRecentAttachments(entityType, entityId, limit = 10) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}/recent`,
    method: 'get',
    params: { limit }
  })
}

// 批量删除附件
export function batchDeleteAttachments(ids) {
  return request({
    url: '/front/crm/attachments/batch',
    method: 'delete',
    params: { ids: ids.join(',') }
  })
}

// 获取附件数量
export function getAttachmentCount(entityType, entityId) {
  return request({
    url: `/front/crm/attachments/${entityType}/${entityId}/count`,
    method: 'get'
  })
}
