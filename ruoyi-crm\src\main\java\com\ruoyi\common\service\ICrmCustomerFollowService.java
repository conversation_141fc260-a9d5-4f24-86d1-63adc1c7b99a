package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.domain.entity.CrmCustomerFollowers;

/**
 * 客户关注Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface ICrmCustomerFollowService {
    
    /**
     * 查询客户关注列表
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 客户关注集合
     */
    List<CrmCustomerFollowers> selectCrmCustomerFollowersList(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 新增客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    int insertCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 修改客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    int updateCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 删除客户关注信息
     * 
     * @param id 客户关注主键
     * @return 结果
     */
    int deleteCrmCustomerFollowersById(Long id);

    /**
     * 关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int followCustomer(Long customerId, Long followerId);

    /**
     * 取消关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int unfollowCustomer(Long customerId, Long followerId);

    /**
     * 判断是否关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 是否关注
     */
    boolean isFollowing(Long customerId, Long followerId);

    /**
     * 批量关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int batchFollowCustomers(List<Long> customerIds, Long followerId);

    /**
     * 批量取消关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int batchUnfollowCustomers(List<Long> customerIds, Long followerId);

    /**
     * 统计用户关注的客户数量
     * 
     * @param followerId 关注者用户ID
     * @return 关注数量
     */
    int countFollowedCustomers(Long followerId);

    /**
     * 统计客户被关注的数量
     * 
     * @param customerId 客户ID
     * @return 被关注数量
     */
    int countCustomerFollowers(Long customerId);

    /**
     * 查询热门客户排行
     * 
     * @param limit 限制数量
     * @return 热门客户列表
     */
    List<CrmCustomerFollowers> selectPopularCustomers(Integer limit);
}