-- 客户关注表
CREATE TABLE `crm_customer_followers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `follower_id` bigint(20) NOT NULL COMMENT '关注者用户ID',
  `follow_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效关注（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_customer_follower` (`customer_id`,`follower_id`) USING BTREE,
  KEY `idx_customer_id` (`customer_id`) USING BTREE,
  KEY `idx_follower_id` (`follower_id`) USING BTREE,
  KEY `idx_follow_time` (`follow_time`) USING BTREE,
  KEY `idx_is_active` (`is_active`) USING BTREE,
  KEY `idx_follower_active` (`follower_id`,`is_active`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='客户关注表';

-- 添加外键约束（可选）
-- ALTER TABLE `crm_customer_followers` 
-- ADD CONSTRAINT `fk_customer_followers_customer` 
-- FOREIGN KEY (`customer_id`) REFERENCES `crm_business_customers` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `crm_customer_followers` 
-- ADD CONSTRAINT `fk_customer_followers_user` 
-- FOREIGN KEY (`follower_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE;