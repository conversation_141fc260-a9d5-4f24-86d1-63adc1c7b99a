<template>
  <div class="lead-activity-tab">
    <div class="activity-header-section">
      <el-button type="primary" class="add-activity-btn" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        添加跟进记录
      </el-button>
      
      <div class="activity-stats">
        <div class="stat-item">
          <span class="stat-label">今日活动</span>
          <span class="stat-value">{{ getTodayActivities.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">总活动数</span>
          <span class="stat-value">{{ activities.length }}</span>
        </div>
      </div>
    </div>

    <el-timeline>
      <transition-group name="fade-list">
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="activity.id"
          :timestamp="activity.time"
          :type="activity.type"
          class="custom-timeline-item"
          :hollow="activity.type === 'info'"
          :size="activity.type === 'primary' ? 'large' : 'normal'"
        >
          <el-card class="activity-card" :class="activity.category">
            <div class="activity-header">
              <h4>
                <el-icon :class="['activity-icon', activity.type]">
                  <component :is="getActivityIcon(activity.category)" />
                </el-icon>
                {{ activity.title }}
                <el-tag v-if="activity.contactType" size="small" effect="plain" class="ml-2">
                  {{ getContactTypeLabel(activity.contactType) }}
                </el-tag>
              </h4>
              <el-tag :type="activity.type" effect="light" class="activity-tag" round>
                {{ getActivityLabel(activity.category) }}
              </el-tag>
            </div>
            <p class="activity-content">{{ activity.content }}</p>
            <div class="activity-meta-info" v-if="activity.duration || activity.nextFollowTime">
              <el-tag size="small" effect="plain" type="info" v-if="activity.duration">
                <el-icon><Timer /></el-icon>
                沟通时长: {{ activity.duration }}分钟
              </el-tag>
              <el-tag size="small" effect="plain" type="warning" v-if="activity.nextFollowTime">
                <el-icon><Calendar /></el-icon>
                下次跟进: {{ formatDate(activity.nextFollowTime) }}
              </el-tag>
            </div>
            <div class="activity-footer">
              <div class="activity-meta" v-if="activity.operator">
                <el-avatar :size="24" class="operator-avatar">{{ activity.operator.charAt(0) }}</el-avatar>
                <span class="operator-name">{{ activity.operator }}</span>
              </div>
              <div class="activity-actions">
                <el-button link type="primary" size="small" @click="editActivity(activity)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button link type="danger" size="small" @click="deleteActivity(activity)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </transition-group>
    </el-timeline>

    <!-- 添加/编辑活动记录对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑活动记录' : '添加活动记录'"
      width="600px"
      class="activity-dialog"
    >
      <el-form ref="activityForm" :model="activityForm" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动分类" prop="category">
              <el-select v-model="activityForm.category" placeholder="请选择活动分类" class="w-full">
                <el-option
                  v-for="item in activityCategories"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-tag :type="item.type" class="mr-2">{{ item.label }}</el-tag>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactType">
              <el-select v-model="activityForm.contactType" placeholder="请选择联系方式" class="w-full">
                <el-option
                  v-for="item in contactTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-form-item label="活动标题" prop="title">
          <el-input v-model="activityForm.title" placeholder="请输入活动标题" />
        </el-form-item> -->

        <el-form-item label="活动内容" prop="content">
          <el-input
            v-model="activityForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入活动内容"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="沟通时长" prop="duration">
              <el-input-number 
                v-model="activityForm.duration" 
                :min="1"
                :max="480"
                placeholder="请输入时长(分钟)"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次跟进" prop="nextFollowTime">
              <el-date-picker
                v-model="activityForm.nextFollowTime"
                type="datetime"
                placeholder="选择下次跟进时间"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveActivity">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Calendar, Delete, Document, Edit, Phone, Plus, Timer } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  name: 'LeadActivityTab',
  components: {
    Edit,
    Phone,
    Document,
    Plus,
    Delete,
    Timer,
    Calendar
  },
  data() {
    return {
      activities: [
        {
          id: 1,
          time: '2024-02-20 10:00:00',
          title: '创建线索',
          content: '创建了新的线索记录',
          type: 'primary',
          operator: '张三',
          category: 'create',
          nextFollowTime: null,
          contactType: null,
          duration: null
        },
        {
          id: 2,
          time: '2024-02-20 14:30:00',
          title: '电话跟进',
          content: '与客户进行了电话沟通，客户表示有意向进一步了解产品',
          type: 'success',
          operator: '李四',
          category: 'follow',
          nextFollowTime: '2024-02-25 14:30:00',
          contactType: 'phone',
          duration: 30
        },
        {
          id: 3,
          time: '2024-02-21 09:15:00',
          title: '发送方案',
          content: '向客户发送了产品解决方案文档',
          type: 'info',
          operator: '张三',
          category: 'document',
          nextFollowTime: null,
          contactType: 'email',
          duration: null
        }
      ],
      activityCategories: [
        { label: '创建', value: 'create', type: 'primary' },
        { label: '跟进', value: 'follow', type: 'success' },
        { label: '文档', value: 'document', type: 'info' },
        { label: '会议', value: 'meeting', type: 'warning' },
        { label: '拜访', value: 'visit', type: 'danger' }
      ],
      contactTypes: [
        { label: '电话', value: 'phone' },
        { label: '邮件', value: 'email' },
        { label: '微信', value: 'wechat' },
        { label: '现场', value: 'onsite' },
        { label: '其他', value: 'other' }
      ],
      dialogVisible: false,
      isEdit: false,
      editIndex: -1,
      activityForm: {
        type: '',
        title: '',
        content: '',
        category: '',
        nextFollowTime: null,
        contactType: null,
        duration: null
      },
      rules: {
        type: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
        title: [{ required: true, message: '请输入活动标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入活动内容', trigger: 'blur' }],
        category: [{ required: true, message: '请选择活动分类', trigger: 'change' }],
        contactType: [{ required: true, message: '请选择联系方式', trigger: 'change' }]
      }
    }
  },
  computed: {
    getTodayActivities() {
      const today = new Date().toISOString().split('T')[0];
      return this.activities.filter(activity => 
        activity.time.startsWith(today)
      );
    }
  },
  methods: {
    getActivityIcon(category) {
      const iconMap = {
        create: 'Edit',
        follow: 'Phone',
        document: 'Document',
        meeting: 'Timer',
        visit: 'Location'
      }
      return iconMap[category] || 'Document'
    },
    getActivityLabel(category) {
      return this.activityCategories.find(item => item.value === category)?.label || category
    },
    getContactTypeLabel(type) {
      return this.contactTypes.find(item => item.value === type)?.label || type
    },
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    showAddDialog() {
      this.isEdit = false;
      this.editIndex = -1;
      this.activityForm = {
        type: '',
        title: '',
        content: '',
        category: '',
        nextFollowTime: null,
        contactType: null,
        duration: null
      };
      this.dialogVisible = true;
    },
    editActivity(activity) {
      this.isEdit = true;
      this.editIndex = this.activities.indexOf(activity);
      this.activityForm = { ...activity };
      this.dialogVisible = true;
    },
    deleteActivity(activity) {
      ElMessageBox.confirm(
        '确定要删除这条活动记录吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        const index = this.activities.indexOf(activity);
        this.activities.splice(index, 1);
        ElMessage.success('删除成功');
      }).catch(() => {});
    },
    saveActivity() {
      this.$refs.activityForm.validate((valid) => {
        if (valid) {
          const newActivity = {
            ...this.activityForm,
            time: new Date().toLocaleString(),
            operator: '当前用户' // 这里应该使用实际的登录用户信息
          };

          if (this.isEdit) {
            this.activities.splice(this.editIndex, 1, newActivity);
            ElMessage.success('编辑成功');
          } else {
            this.activities.unshift(newActivity);
            ElMessage.success('添加成功');
          }

          this.dialogVisible = false;
        }
      });
    }
  }
}
</script>

<style scoped>
.lead-activity-tab {
  padding: 12px;
}

.activity-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.add-activity-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  font-weight: 500;
}

.activity-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.custom-timeline-item {
  padding-bottom: 16px;
  transform-origin: top;
  animation: slideDown 0.3s ease-out;
}

.activity-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  padding: 12px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.activity-icon {
  font-size: 14px;
  padding: 6px;
  border-radius: 6px;
}

.activity-content {
  margin: 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  padding: 4px 0;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 6px;
}

.activity-actions {
  display: flex;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-card:hover .activity-actions {
  opacity: 1;
}

.activity-tag {
  font-size: 11px;
  padding: 0 8px;
  height: 20px;
  line-height: 18px;
}

.operator-avatar {
  background: var(--el-color-primary);
  color: white;
  font-size: 12px;
}

.operator-name {
  font-size: 12px;
  color: #666;
}

:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

:deep(.el-timeline-item__node.is-hollow) {
  background-color: #fff;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e8e8e8;
}

:deep(.el-timeline-item__timestamp) {
  color: #999;
  font-size: 13px;
}

/* 列表过渡动画 */
.fade-list-enter-active,
.fade-list-leave-active {
  transition: all 0.5s ease;
}

.fade-list-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.fade-list-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* 列表项动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.w-full {
  width: 100%;
}

.activity-meta-info {
  display: flex;
  gap: 8px;
  margin: 8px 0;
}

.activity-meta-info .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.activity-card {
  position: relative;
  overflow: visible;
}

.activity-card.create {
  border-left: 4px solid var(--el-color-primary);
}

.activity-card.follow {
  border-left: 4px solid var(--el-color-success);
}

.activity-card.document {
  border-left: 4px solid var(--el-color-info);
}

.activity-card.meeting {
  border-left: 4px solid var(--el-color-warning);
}

.activity-card.visit {
  border-left: 4px solid var(--el-color-danger);
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.activity-dialog :deep(.el-input-number) {
  width: 100%;
}
</style> 