// 暗色主题
html[data-theme="dark"] {
  // 主色系
  --ep-color-primary: #409EFF;
  --ep-color-success: #67C23A;
  --ep-color-warning: #E6A23C;
  --ep-color-danger: #F56C6C;
  --ep-color-info: #909399;

  // 中性色系
  --ep-color-white: #ffffff;
  --ep-color-black: #000000;
  --ep-color-text-primary: #E5EAF3;
  --ep-color-text-regular: #CFD3DC;
  --ep-color-text-secondary: #A3A6AD;
  --ep-color-text-placeholder: #8D9095;

  // 背景颜色
  --ep-background-color-base: #141414;
  
  // 边框颜色
  --ep-border-color-base: #4C4D4F;
  --ep-border-color-light: #363637;
  --ep-border-color-lighter: #1D1D1D;
  --ep-border-color-extra-light: #191919;

  // 主色的变体
  --ep-color-primary-light-9: #1D1E1F;
  --ep-color-primary-light-5: #2B2B2C;
  --ep-color-primary-light-3: #385263;
  --ep-color-primary-dark: #337ECC;

  // 阴影
  --ep-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  --ep-box-shadow-base: 0 2px 4px rgba(0, 0, 0, .3), 0 0 6px rgba(0, 0, 0, .2);
  --ep-box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.4);
} 