import request from '@/utils/request'

// 查询回款计划列表
export function listPaymentPlan(query) {
  return request({
    url: '/crm/paymentPlan/list',
    method: 'get',
    params: query
  })
}

// 查询回款计划详细
export function getPaymentPlan(id) {
  return request({
    url: '/crm/paymentPlan/' + id,
    method: 'get'
  })
}

// 查询回款计划详细信息（包含分期和审批）
export function getPaymentPlanDetails(id) {
  return request({
    url: '/crm/paymentPlan/details/' + id,
    method: 'get'
  })
}

// 新增回款计划
export function addPaymentPlan(data) {
  return request({
    url: '/crm/paymentPlan',
    method: 'post',
    data: data
  })
}

// 修改回款计划
export function updatePaymentPlan(data) {
  return request({
    url: '/crm/paymentPlan',
    method: 'put',
    data: data
  })
}

// 删除回款计划
export function delPaymentPlan(ids) {
  return request({
    url: '/crm/paymentPlan/' + ids,
    method: 'delete'
  })
}

// 根据合同ID查询回款计划
export function getPaymentPlanByContract(contractId) {
  return request({
    url: '/crm/paymentPlan/contract/' + contractId,
    method: 'get'
  })
}

// 生成回款计划编号
export function generatePlanNumber() {
  return request({
    url: '/crm/paymentPlan/generateNumber',
    method: 'get'
  })
}

// 提交审批
export function submitForApproval(id) {
  return request({
    url: '/crm/paymentPlan/submitApproval/' + id,
    method: 'post'
  })
}

// 审批处理
export function processApproval(data) {
  return request({
    url: '/crm/paymentPlan/processApproval',
    method: 'post',
    params: data
  })
}

// 获取客户回款计划统计
export function getCustomerStatistics(customerId) {
  return request({
    url: '/crm/paymentPlan/statistics/customer/' + customerId,
    method: 'get'
  })
}

// 更新回款金额统计
export function updatePaymentAmounts(id) {
  return request({
    url: '/crm/paymentPlan/updateAmounts/' + id,
    method: 'post'
  })
}

// ========================= 分期管理相关接口 =========================

// 查询分期列表
export function getInstallments(planId) {
  return request({
    url: '/crm/paymentPlan/installments/' + planId,
    method: 'get'
  })
}

// 新增分期
export function addInstallment(data) {
  return request({
    url: '/crm/paymentPlan/installments',
    method: 'post',
    data: data
  })
}

// 修改分期
export function updateInstallment(data) {
  return request({
    url: '/crm/paymentPlan/installments',
    method: 'put',
    data: data
  })
}

// 删除分期
export function delInstallments(ids) {
  return request({
    url: '/crm/paymentPlan/installments/' + ids,
    method: 'delete'
  })
}

// 记录实际回款
export function recordPayment(data) {
  return request({
    url: '/crm/paymentPlan/installments/payment',
    method: 'post',
    params: data
  })
}

// 更新逾期天数
export function updateOverdueDays() {
  return request({
    url: '/crm/paymentPlan/installments/updateOverdue',
    method: 'post'
  })
}

// ========================= 审批管理相关接口 =========================

// 查询审批列表
export function getApprovals(planId) {
  return request({
    url: '/crm/paymentPlan/approvals/' + planId,
    method: 'get'
  })
}

// 查询待我审批的记录
export function getPendingApprovals() {
  return request({
    url: '/crm/paymentPlan/approvals/pending',
    method: 'get'
  })
}

// 获取当前审批级别
export function getCurrentApprovalLevel(planId) {
  return request({
    url: '/crm/paymentPlan/approvals/currentLevel/' + planId,
    method: 'get'
  })
}

// 获取下一个审批人
export function getNextApprover(planId) {
  return request({
    url: '/crm/paymentPlan/approvals/nextApprover/' + planId,
    method: 'get'
  })
}

// 导出回款计划列表
export function exportPaymentPlan(query) {
  return request({
    url: '/crm/paymentPlan/export',
    method: 'post',
    params: query
  })
}