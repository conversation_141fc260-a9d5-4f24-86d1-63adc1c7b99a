package com.ruoyi.crm.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.IPaymentPlanWorkflowService;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.common.exception.ServiceException;

/**
 * 回款计划工作流Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class PaymentPlanWorkflowServiceImpl implements IPaymentPlanWorkflowService {
    
    @Autowired
    private ProcessEngine processEngine;
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private HistoryService historyService;
    
    @Autowired
    private RepositoryService repositoryService;
    
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;
    
    private static final String PROCESS_DEFINITION_KEY = "paymentPlanApproval";
    
    /**
     * 启动回款计划审批流程
     */
    @Override
    @Transactional
    public String startApprovalProcess(CrmPaymentPlan paymentPlan, Map<String, Object> variables) {
        try {
            // 准备流程变量
            if (variables == null) {
                variables = new HashMap<>();
            }
            
            variables.put("planId", paymentPlan.getId());
            variables.put("planNumber", paymentPlan.getPlanNumber());
            variables.put("customerName", paymentPlan.getCustomerName());
            variables.put("totalAmount", paymentPlan.getTotalAmount());
            variables.put("submitterId", SecurityUtils.getUserId());
            variables.put("submitterName", SecurityUtils.getUsername());
            variables.put("submitTime", new Date());
            
            // 根据金额设置审批级别
            if (paymentPlan.getTotalAmount().compareTo(new java.math.BigDecimal("100000")) >= 0) {
                variables.put("approvalLevel", "high");
                variables.put("requiresFinanceApproval", true);
                variables.put("requiresGeneralManagerApproval", true);
            } else if (paymentPlan.getTotalAmount().compareTo(new java.math.BigDecimal("50000")) >= 0) {
                variables.put("approvalLevel", "medium");
                variables.put("requiresFinanceApproval", true);
                variables.put("requiresGeneralManagerApproval", false);
            } else {
                variables.put("approvalLevel", "low");
                variables.put("requiresFinanceApproval", false);
                variables.put("requiresGeneralManagerApproval", false);
            }
            
            // 启动流程实例
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                PROCESS_DEFINITION_KEY, 
                paymentPlan.getPlanNumber(),
                variables
            );
            
            // 更新回款计划状态
            paymentPlan.setPlanStatus("审批中");
            paymentPlan.setApprovalStatus("审批中");
            paymentPlan.setProcessInstanceId(processInstance.getId());
            paymentPlan.setProcessDefinitionKey(PROCESS_DEFINITION_KEY);
            paymentPlan.setProcessStatus("RUNNING");
            
            paymentPlanService.updatePaymentPlan(paymentPlan);
            
            return processInstance.getId();
            
        } catch (Exception e) {
            throw new ServiceException("启动审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成当前任务
     */
    @Override
    @Transactional
    public boolean completeTask(String taskId, Map<String, Object> variables, String comment) {
        try {
            // 添加审批意见
            if (comment != null && !comment.trim().isEmpty()) {
                taskService.addComment(taskId, null, comment);
            }
            
            // 完成任务
            taskService.complete(taskId, variables);
            
            return true;
        } catch (Exception e) {
            throw new ServiceException("完成任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 审批通过
     */
    @Override
    @Transactional
    public boolean approveTask(String taskId, Long approverId, String comment) {
        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new ServiceException("任务不存在");
            }
            
            // 准备审批变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("approved", true);
            variables.put("approverId", approverId);
            variables.put("approvalTime", new Date());
            variables.put("approvalComment", comment);
            
            // 添加审批意见
            taskService.addComment(taskId, task.getProcessInstanceId(), 
                "审批通过: " + (comment != null ? comment : ""));
            
            // 完成任务
            taskService.complete(taskId, variables);
            
            // 检查流程是否结束，更新计划状态
            updatePlanStatusAfterTask(task.getProcessInstanceId());
            
            return true;
        } catch (Exception e) {
            throw new ServiceException("审批通过失败: " + e.getMessage());
        }
    }
    
    /**
     * 审批拒绝
     */
    @Override
    @Transactional
    public boolean rejectTask(String taskId, Long approverId, String comment) {
        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new ServiceException("任务不存在");
            }
            
            // 准备拒绝变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("approved", false);
            variables.put("approverId", approverId);
            variables.put("approvalTime", new Date());
            variables.put("approvalComment", comment);
            
            // 添加审批意见
            taskService.addComment(taskId, task.getProcessInstanceId(), 
                "审批拒绝: " + (comment != null ? comment : ""));
            
            // 完成任务
            taskService.complete(taskId, variables);
            
            // 更新计划状态为已拒绝
            updatePlanStatusAfterTask(task.getProcessInstanceId());
            
            return true;
        } catch (Exception e) {
            throw new ServiceException("审批拒绝失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的待办任务
     */
    @Override
    public List<Map<String, Object>> getPendingTasks(Long userId) {
        try {
            TaskQuery taskQuery = taskService.createTaskQuery()
                .taskAssignee(userId.toString())
                .processDefinitionKey(PROCESS_DEFINITION_KEY)
                .active();
            
            List<Task> tasks = taskQuery.list();
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (Task task : tasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskId", task.getId());
                taskInfo.put("taskName", task.getName());
                taskInfo.put("createTime", task.getCreateTime());
                taskInfo.put("processInstanceId", task.getProcessInstanceId());
                
                // 获取流程变量
                Map<String, Object> variables = runtimeService.getVariables(task.getProcessInstanceId());
                taskInfo.put("planId", variables.get("planId"));
                taskInfo.put("planNumber", variables.get("planNumber"));
                taskInfo.put("customerName", variables.get("customerName"));
                taskInfo.put("totalAmount", variables.get("totalAmount"));
                taskInfo.put("submitterName", variables.get("submitterName"));
                
                result.add(taskInfo);
            }
            
            return result;
        } catch (Exception e) {
            throw new ServiceException("获取待办任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取流程实例的任务历史
     */
    @Override
    public List<Map<String, Object>> getTaskHistory(String processInstanceId) {
        try {
            List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceEndTime()
                .desc()
                .list();
            
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (HistoricTaskInstance task : historicTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskId", task.getId());
                taskInfo.put("taskName", task.getName());
                taskInfo.put("assignee", task.getAssignee());
                taskInfo.put("startTime", task.getStartTime());
                taskInfo.put("endTime", task.getEndTime());
                taskInfo.put("durationInMillis", task.getDurationInMillis());
                
                // 获取任务评论
                List<org.activiti.engine.task.Comment> comments = taskService.getTaskComments(task.getId());
                if (!comments.isEmpty()) {
                    taskInfo.put("comment", comments.get(comments.size() - 1).getFullMessage());
                }
                
                result.add(taskInfo);
            }
            
            return result;
        } catch (Exception e) {
            throw new ServiceException("获取任务历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取流程实例的当前任务
     */
    @Override
    public Map<String, Object> getCurrentTask(String processInstanceId) {
        try {
            Task task = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .active()
                .singleResult();
            
            if (task == null) {
                return null;
            }
            
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskId", task.getId());
            taskInfo.put("taskName", task.getName());
            taskInfo.put("assignee", task.getAssignee());
            taskInfo.put("createTime", task.getCreateTime());
            taskInfo.put("processInstanceId", task.getProcessInstanceId());
            
            return taskInfo;
        } catch (Exception e) {
            throw new ServiceException("获取当前任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 撤销流程
     */
    @Override
    @Transactional
    public boolean cancelProcess(String processInstanceId, String reason) {
        try {
            // 删除流程实例
            runtimeService.deleteProcessInstance(processInstanceId, reason);
            
            // 更新计划状态
            Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(processInstanceId)
                .list()
                .stream()
                .collect(HashMap::new, 
                    (map, var) -> map.put(var.getVariableName(), var.getValue()),
                    HashMap::putAll);
            
            Long planId = (Long) variables.get("planId");
            if (planId != null) {
                CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
                if (plan != null) {
                    plan.setPlanStatus("草稿");
                    plan.setApprovalStatus("已撤销");
                    plan.setProcessStatus("CANCELLED");
                    paymentPlanService.updatePaymentPlan(plan);
                }
            }
            
            return true;
        } catch (Exception e) {
            throw new ServiceException("撤销流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询流程定义
     */
    @Override
    public List<Map<String, Object>> getProcessDefinitions() {
        try {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(PROCESS_DEFINITION_KEY)
                .latestVersion()
                .list();
            
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (ProcessDefinition definition : processDefinitions) {
                Map<String, Object> defInfo = new HashMap<>();
                defInfo.put("id", definition.getId());
                defInfo.put("key", definition.getKey());
                defInfo.put("name", definition.getName());
                defInfo.put("version", definition.getVersion());
                defInfo.put("deploymentId", definition.getDeploymentId());
                
                result.add(defInfo);
            }
            
            return result;
        } catch (Exception e) {
            throw new ServiceException("查询流程定义失败: " + e.getMessage());
        }
    }
    
    /**
     * 部署流程定义
     */
    @Override
    public String deployProcess(String resourceName, String bpmnXml) {
        try {
            Deployment deployment = repositoryService.createDeployment()
                .name("回款计划审批流程")
                .addString(resourceName, bpmnXml)
                .deploy();
            
            return deployment.getId();
        } catch (Exception e) {
            throw new ServiceException("部署流程定义失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据计划ID获取流程实例
     */
    @Override
    public Map<String, Object> getProcessInstanceByPlanId(Long planId) {
        try {
            CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
            if (plan == null || plan.getProcessInstanceId() == null) {
                return null;
            }
            
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(plan.getProcessInstanceId())
                .singleResult();
            
            if (processInstance == null) {
                return null;
            }
            
            Map<String, Object> processInfo = new HashMap<>();
            processInfo.put("processInstanceId", processInstance.getId());
            processInfo.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
            processInfo.put("startTime", processInstance.getStartTime());
            processInfo.put("endTime", processInstance.getEndTime());
            processInfo.put("durationInMillis", processInstance.getDurationInMillis());
            processInfo.put("startUserId", processInstance.getStartUserId());
            
            return processInfo;
        } catch (Exception e) {
            throw new ServiceException("获取流程实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查计划是否在审批中
     */
    @Override
    public boolean isInApproval(Long planId) {
        try {
            CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
            if (plan == null || plan.getProcessInstanceId() == null) {
                return false;
            }
            
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(plan.getProcessInstanceId())
                .active()
                .singleResult();
            
            return processInstance != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取流程实例变量
     */
    @Override
    public Map<String, Object> getProcessVariables(String processInstanceId) {
        try {
            return runtimeService.getVariables(processInstanceId);
        } catch (Exception e) {
            throw new ServiceException("获取流程变量失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置流程实例变量
     */
    @Override
    public boolean setProcessVariables(String processInstanceId, Map<String, Object> variables) {
        try {
            runtimeService.setVariables(processInstanceId, variables);
            return true;
        } catch (Exception e) {
            throw new ServiceException("设置流程变量失败: " + e.getMessage());
        }
    }
    
    /**
     * 任务完成后更新计划状态
     */
    private void updatePlanStatusAfterTask(String processInstanceId) {
        try {
            // 检查流程是否结束
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
            
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            Long planId = (Long) variables.get("planId");
            
            if (planId != null) {
                CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
                if (plan != null) {
                    if (processInstance == null) {
                        // 流程已结束
                        HistoricProcessInstance historicProcess = historyService.createHistoricProcessInstanceQuery()
                            .processInstanceId(processInstanceId)
                            .singleResult();
                        
                        if (historicProcess != null && historicProcess.getEndTime() != null) {
                            // 检查最终结果
                            Boolean approved = (Boolean) variables.get("approved");
                            if (approved != null && approved) {
                                plan.setPlanStatus("执行中");
                                plan.setApprovalStatus("已通过");
                                plan.setProcessStatus("COMPLETED");
                            } else {
                                plan.setPlanStatus("草稿");
                                plan.setApprovalStatus("已拒绝");
                                plan.setProcessStatus("REJECTED");
                            }
                        }
                    } else {
                        // 流程仍在进行中
                        plan.setPlanStatus("审批中");
                        plan.setApprovalStatus("审批中");
                        plan.setProcessStatus("RUNNING");
                        
                        // 更新当前任务信息
                        Map<String, Object> currentTask = getCurrentTask(processInstanceId);
                        if (currentTask != null) {
                            plan.setCurrentTaskId((String) currentTask.get("taskId"));
                            plan.setCurrentTaskName((String) currentTask.get("taskName"));
                            plan.setCurrentAssignee((String) currentTask.get("assignee"));
                        }
                    }
                    
                    paymentPlanService.updatePaymentPlan(plan);
                }
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主流程
            System.err.println("更新计划状态失败: " + e.getMessage());
        }
    }
}