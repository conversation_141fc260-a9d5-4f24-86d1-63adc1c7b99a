<template>
  <div class="leads-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <common-filter
        v-model:searchValue="searchInput"
        v-model:filterValue="filterType"
        :config="leadsFilterConfig"
        @search="handleSearch"
        @filter="handleFilterChange"
      />
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 数据表格 -->
      <el-table 
        ref="leadsTable" 
        :data="leads" 
        border 
        sortable 
        tooltip-effect="dark"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
        style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
        @selection-change="handleSelectionChange"
        :max-height="tableMaxHeight"
      >
        <template v-for="col in tableColumns" :key="col.prop">
          <el-table-column v-bind="col">
            <template #default="scope" v-if="col.link">
              <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">
                {{ scope.row.leadName }}
              </el-button>
            </template>
          </el-table-column>
        </template>
        <!-- 表格操作栏 tableButtons  本地配置 包含了操作的按钮 类型 图标 事件 -->
        <table-operations :buttons="tableButtons" />
      </el-table>
    </div>

    <!-- 分页容器 -->
    <div class="pagination-wrapper">
      <pagination 
        v-show="totalLeads > 0"
        :total="totalLeads"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="handlePagination" 
      />
    </div>

    <!-- 使用新的抽屉组件 -->
    <common-drawer 
      v-model="drawerVisible" 
      :config="localDrawerConfig"
      :data="currentLead"
      :actions="drawerActions"
      @action="handleDrawerAction"
    />

    <!-- 新建线索对话框 -->
    <el-dialog
      title="新建线索"
      v-model="leadDialogVisible"
      width="800px"
      :before-close="handleCloseDialog"
    >
      <common-form
        ref="newLeadFormRef"
        :config="newLeadFormConfig"
        v-model="newLead"
        @submit="handleSubmitLead"
        @cancel="handleCloseDialog"
      />
    </el-dialog>

    <!-- 分配对话框 -->
    <el-dialog
      title="分配线索"
      v-model="assignDialogVisible"
      width="500px"
    >
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="选择用户" required>
          <el-select v-model="assignForm.userId" placeholder="请选择用户" style="width: 100%">
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分配原因">
          <el-input
            v-model="assignForm.reason"
            type="textarea"
            placeholder="请输入分配原因"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAssign">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 转化对话框 -->
    <el-dialog
      title="转化线索"
      v-model="convertDialogVisible"
      width="400px"
    >
      <div class="convert-content">
        <p>确定要将此线索转化为客户吗？</p>
        <p style="color: #909399; font-size: 14px;">转化后将在客户管理中创建新的客户记录</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="convertDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmConvert">确定转化</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, markRaw, onMounted, reactive, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { Action, FilterType, LeadEntity, TableButton } from './types';
import { DEFAULT_LEAD } from './types';
import {
  addLeads,
  assignLead,
  deleteLeads,
  getLeads,
  listLeads,
  updateLeads,
  type LeadForm
} from './api';
import { drawerConfig as baseDrawerConfig } from './config';
import { newLeadFormConfig as defaultFormConfig } from './config/formConfig';
import { leadsFilterConfig, tableColumns } from './config';
import LeadActivityTab from './tabs/LeadActivityTab.vue';
import LeadAttachmentsTab from './tabs/LeadAttachmentsTab.vue';
import LeadDetailsTab from './tabs/LeadDetailsTab.vue';
import LeadOperationsTab from './tabs/LeadOperationsTab.vue';

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  searchKeyword: '',
  filterType: 'all' as FilterType
});

// 状态定义
const filterType = ref<FilterType>('all');
const searchInput = ref('');
const totalLeads = ref(0);
const leads = ref<LeadEntity[]>([]);
const leadDialogVisible = ref(false);
const newLead = ref<LeadEntity>({ ...DEFAULT_LEAD });
const drawerVisible = ref(false);
const currentLead = ref<LeadEntity>({ ...DEFAULT_LEAD });
const newLeadFormConfig = ref(defaultFormConfig);
const loading = ref(false);

// 分配对话框相关状态
const assignDialogVisible = ref(false);
const assignForm = reactive({
  userId: null as number | null,
  reason: ''
});
const userOptions = ref<Array<{ label: string, value: number }>>([]);

// 转化对话框相关状态
const convertDialogVisible = ref(false);
const currentLeadId = ref(0);

// 表格高度计算
const tableMaxHeight = computed(() => {
  return `calc(100vh - 300px)`;
});

// 表格操作按钮配置
const tableButtons: TableButton[] = [
  {
    label: '分配',
    type: 'primary',
    link: true,
    icon: 'Share',
    handler: (row: LeadEntity) => handleAssign(row)
  },
  {
    label: '转化',
    type: 'success',
    link: true,
    icon: 'Promotion',
    handler: (row: LeadEntity) => handleConvert(row)
  },
  {
    label: '删除',
    type: 'danger',
    link: true,
    icon: 'Delete',
    handler: (row: LeadEntity) => handleDelete(row)
  }
];

// 抽屉操作按钮配置
const drawerActions: Action[] = [
  {
    label: '分配',
    icon: 'Share',
    handler: (data: LeadEntity) => {
      handleAssign(data);
    }
  },
  {
    label: '打印',
    icon: 'Printer',
    handler: (data: LeadEntity) => {
      console.log('打印', data);
    }
  },
  {
    label: '转化',
    type: 'success',
    icon: 'Promotion',
    handler: (data: LeadEntity) => {
      handleConvert(data);
    }
  },
  {
    label: '删除',
    type: 'danger',
    icon: 'Delete',
    handler: (data: LeadEntity) => {
      handleDelete(data);
    }
  }
];

// 抽屉配置
const localDrawerConfig = reactive({
  ...baseDrawerConfig,
  menuItems: [
    {
      key: 'activity',
      label: '跟进记录',
      icon: 'Timer',
      component: markRaw(LeadActivityTab)
    },
    {
      key: 'details',
      label: '详细资料',
      icon: 'Document',
      component: markRaw(LeadDetailsTab)
    },
    {
      key: 'operations',
      label: '操作记录',
      icon: 'List',
      component: markRaw(LeadOperationsTab)
    },
    {
      key: 'attachments',
      label: '附件',
      icon: 'Folder',
      component: markRaw(LeadAttachmentsTab),
      badge: true
    }
  ]
});

// 事件处理函数
const handleSearch = (value: string) => {
  queryParams.searchKeyword = value;
  queryParams.pageNum = 1;
  fetchLeads();
};

const handleFilterChange = (value: FilterType) => {
  queryParams.filterType = value;
  queryParams.pageNum = 1;
  fetchLeads();
};

const handleSelectionChange = (selection: LeadEntity[]) => {
  // 处理选择变化
};

const handlePagination = (pagination: { page: number; limit: number }) => {
  queryParams.pageNum = pagination.page;
  queryParams.pageSize = pagination.limit;
  fetchLeads();
};

const openDrawer = (lead: LeadEntity) => {
  currentLead.value = { ...lead };
  drawerVisible.value = true;
};

const handleDrawerAction = (action: string, data: LeadEntity) => {
  console.log('抽屉操作:', action, data);
};

const handleAssign = (lead: LeadEntity) => {
  currentLeadId.value = lead.id;
  assignForm.userId = null;
  assignForm.reason = '';
  assignDialogVisible.value = true;
};

const handleConfirmAssign = async () => {
  if (!assignForm.userId) {
    ElMessage.warning('请选择用户');
    return;
  }
  
  try {
    await assignLead(currentLeadId.value, assignForm.userId, assignForm.reason);
    ElMessage.success('分配成功');
    assignDialogVisible.value = false;
    fetchLeads();
  } catch (error) {
    ElMessage.error('分配失败');
  }
};

const handleConvert = (lead: LeadEntity) => {
  currentLeadId.value = lead.id;
  convertDialogVisible.value = true;
};

const handleConfirmConvert = () => {
  ElMessage.success('转化功能开发中...');
  convertDialogVisible.value = false;
};

const handleDelete = async (lead: LeadEntity) => {
  try {
    await ElMessageBox.confirm('确定要删除这条线索吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteLeads([lead.id]);
    ElMessage.success('删除成功');
    fetchLeads();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleCloseDialog = () => {
  leadDialogVisible.value = false;
  newLead.value = { ...DEFAULT_LEAD };
};

const handleSubmitLead = async (formData: LeadForm) => {
  try {
    await addLeads(formData);
    ElMessage.success('创建成功');
    leadDialogVisible.value = false;
    fetchLeads();
  } catch (error) {
    ElMessage.error('创建失败');
  }
};

// 获取线索列表
const fetchLeads = async () => {
  loading.value = true;
  try {
    const response = await listLeads(queryParams);
    leads.value = response.rows || [];
    totalLeads.value = response.total || 0;
  } catch (error) {
    ElMessage.error('获取线索列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取用户选项
const fetchUserOptions = async () => {
  // 这里应该调用获取用户列表的API
  userOptions.value = [
    { label: '张三', value: 1 },
    { label: '李四', value: 2 },
    { label: '王五', value: 3 }
  ];
};

// 初始化
onMounted(() => {
  fetchLeads();
  fetchUserOptions();
});
</script>

<style scoped>
.leads-container {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.link-button {
  padding: 0;
  height: auto;
  font-weight: normal;
}

.link-button:hover {
  text-decoration: underline;
}

.convert-content {
  padding: 20px 0;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
