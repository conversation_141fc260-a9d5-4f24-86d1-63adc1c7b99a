<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用团队与业务模块关联方案 (V2)</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8; color: #333; max-width: 1200px; margin: 20px auto; padding: 20px; background-color: #f9f9f9; }
        .container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 6px 20px rgba(0,0,0,0.07); }
        h1, h2, h3 { color: #2c3e50; }
        h1 { text-align: center; border-bottom: 4px solid #27ae60; padding-bottom: 15px; margin-bottom: 40px; }
        h2 { border-left: 5px solid #27ae60; padding-left: 15px; margin-top: 45px; margin-bottom: 25px; }
        h3 { color: #229954; margin-top: 30px; margin-bottom: 15px; }
        pre { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; overflow-x: auto; font-family: 'Consolas', monospace; font-size: 14px; }
        .mermaid { text-align: center; margin: 25px 0; background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; }
        .info-box { background: #e9f7ef; border-left: 5px solid #27ae60; padding: 20px; border-radius: 8px; margin: 25px 0; }
        table { width: 100%; border-collapse: collapse; margin: 25px 0; }
        th, td { border: 1px solid #ddd; padding: 14px; text-align: left; }
        th { background-color: #27ae60; color: white; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .highlight { color: #c0392b; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>通用团队与业务模块关联方案 (V2)</h1>

        <h2>1. 核心设计思想 (V2)</h2>
        <div class="info-box">
            <p>根据你的反馈，我们采纳了<span class="highlight">“通用关联表”</span>的架构。此方案的核心是创建一个统一的关联表 <code>crm_team_relations</code>，用于记录任意业务模块的实体（如联系人、商机、客户）与团队之间的归属关系。
            </p>
            <p><strong>优点：</strong></p>
            <ul>
                <li><strong>低耦合、无侵入</strong>: 无需修改任何现有业务表（如 <code>crm_contacts</code>, <code>crm_leads</code>），保持业务模块的独立性。</li>
                <li><strong>高扩展性</strong>: 未来若想让团队管理“合同”、“订单”等新模块，只需在该关联表中增加新的记录即可，无需改动表结构。</li>
                <li><strong>集中管理</strong>: 所有团队与业务的关联关系都集中在一张表中，便于维护和查询。</li>
            </ul>
        </div>

        <h2>2. 架构设计 (V2)</h2>
        <h3>2.1. 数据模型设计</h3>
        <p>引入一张全新的通用关联表 <code>crm_team_relations</code>。</p>
        <div class="mermaid">
erDiagram
    crm_teams {
        bigint id PK
        varchar team_name
    }

    crm_team_relations {
        bigint id PK
        bigint team_id FK
        bigint biz_id
        varchar biz_type
    }

    crm_contacts {
        bigint id PK
        varchar name
    }

    crm_leads {
        bigint id PK
        varchar name
    }

    crm_teams ||--|{ crm_team_relations : "关联"
    crm_team_relations }o--|| crm_contacts : "关联联系人"
    crm_team_relations }o--|| crm_leads : "关联线索"
        </div>
        <p class="text-center"><em><code>biz_type</code> 用来区分关联的是哪个业务模块，例如 'CONTACT', 'LEAD', 'CUSTOMER'。</em></p>

        <h3>2.2. SQL建表语句</h3>
        <h4>通用团队业务关联表 (crm_team_relations)</h4>
        <pre><code>CREATE TABLE `crm_team_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `biz_id` bigint(20) NOT NULL COMMENT '业务主键ID (例如: crm_contacts.id)',
  `biz_type` varchar(50) NOT NULL COMMENT '业务类型 (例如: CONTACT, LEAD, CUSTOMER)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_biz` (`team_id`, `biz_id`, `biz_type`),
  KEY `idx_biz` (`biz_id`, `biz_type`)
) ENGINE=InnoDB COMMENT='通用团队业务关联表';</code></pre>

        <h2>3. 后端改造计划 (V2)</h2>
        <ul>
            <li><strong>实体类</strong>: 创建 <code>CrmTeamRelation.java</code> 实体。</li>
            <li><strong>Mapper</strong>: 创建 <code>CrmTeamRelationMapper.java</code> 及其XML文件。</li>
            <li><strong>Service</strong>: 创建 <code>ICrmTeamRelationService.java</code> 和实现类，提供通用的关联管理方法，如：
                <ul>
                    <li><code>void assignTeamToBiz(Long teamId, Long bizId, String bizType)</code>: 将业务对象分配给团队。</li>
                    <li><code>void unassignTeamFromBiz(Long bizId, String bizType)</code>: 取消分配。</li>
                    <li><code>Long getTeamIdByBiz(Long bizId, String bizType)</code>: 根据业务对象查询所属团队ID。</li>
                </ul>
            </li>
            <li><strong>Controller</strong>:
                <ul>
                    <li>创建一个新的 <code>CrmTeamRelationController.java</code>。</li>
                    <li>提供API用于分配和取消分配，例如:
                        <ul>
                            <li><code>POST /crm/relation/assign</code> (Body: {teamId, bizId, bizType})</li>
                            <li><code>POST /crm/relation/unassign</code> (Body: {bizId, bizType})</li>
                        </ul>
                    </li>
                    <li>在联系人、商机等模块的Controller中，注入 <code>ICrmTeamRelationService</code>，在查询详情和列表时，调用服务获取团队信息并组装到返回的DTO中。</li>
                </ul>
            </li>
        </ul>

        <h2>4. 前端改造计划 (V2)</h2>
        <p>前端的改造思路与V1类似，但调用的API发生了变化。</p>
        <ol>
            <li><strong>分配团队</strong>: 在联系人、商机等页面的操作菜单中，增加“分配团队”按钮。点击后弹出对话框，选择一个团队，然后调用 <code>POST /crm/relation/assign</code> 接口。</li>
            <li><strong>详情抽屉改造</strong>:
                <ul>
                    <li>在联系人详情抽屉中，新增“团队成员”Tab。</li>
                    <li>进入抽屉时，首先调用一个接口（例如 <code>GET /crm/relation/team?bizId=...&bizType=CONTACT</code>）获取当前联系人关联的团队信息（teamId 和 teamName）。</li>
                    <li>获取到 <code>teamId</code> 后，再调用 <code>GET /crm/team/{teamId}/members</code> 接口获取成员列表并展示。</li>
                </ul>
            </li>
            <li><strong>团队管理页面增强</strong>:
                <ul>
                    <li>在“团队管理”的详情页，可以增加新的Tab，如“关联的联系人”、“关联的商机”。</li>
                    <li>点击Tab后，调用接口（例如 <code>GET /crm/relation/bizs?teamId=...&bizType=CONTACT</code>）查询该团队管理的所有联系人列表。</li>
                </ul>
            </li>
        </ol>

        <h2>5. 实施任务清单 (V2)</h2>
        <ul class="task-list">
            <li><span class="label">数据库</span> <span class="status-todo">[待办]</span> 创建 `crm_team_relations` 通用关联表。</li>
            <li><span class="label">后端实体</span> <span class="status-todo">[待办]</span> 创建 `CrmTeamRelation` 实体类。</li>
            <li><span class="label">后端Mapper</span> <span class="status-todo">[待办]</span> 创建 `CrmTeamRelationMapper` 接口及XML。</li>
            <li><span class="label">后端Service</span> <span class="status-todo">[待办]</span> 创建 `CrmTeamRelationService` 并实现通用关联逻辑。</li>
            <li><span class="label">后端Controller</span> <span class="status-todo">[待办]</span> 创建 `CrmTeamRelationController` 并提供分配/查询API。</li>
            <li><span class="label">业务模块改造</span> <span class="status-todo">[待办]</span> 在联系人等模块的Service和Controller中集成团队关联查询。</li>
            <li><span class="label">前端分配功能</span> <span class="status-todo">[待办]</span> 在各业务模块页面添“分配团队”功能。</li>
            <li><span class="label">前端抽屉改造</span> <span class="status-todo">[待办]</span> 在详情抽屉中新增“团队成员”Tab并实现数据加载。</li>
            <li><span class="label">团队管理页增强</span> <span class="status-todo">[待办]</span> 在团队详情页反向查询关联的业务对象。</li>
            <li><span class="label">联调测试</span> <span class="status-todo">[待办]</span> 进行前后端联调，确保新架构下的功能完整可用。</li>
        </ul>
    </div>
</body>
</html>
