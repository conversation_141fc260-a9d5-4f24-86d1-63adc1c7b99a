<template>
  <el-dialog title="选择订单" :model-value="visible" @update:modelValue="$emit('update:visible', $event)" width="60%">
    <!-- 搜索栏 -->
    <el-form :model="orderQueryParams" ref="orderQueryForm" :inline="true" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="orderQueryParams.orderNo" placeholder="请输入订单号" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleOrderQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <!-- 订单列表 -->
    <el-table :data="orderList" @selection-change="handleOrderSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" prop="orderNo" />
      <el-table-column label="订单金额" prop="amount" />
      <el-table-column label="下单日期" prop="orderDate" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="orderTotal > 0"
      :total="orderTotal"
      v-model:page="orderQueryParams.pageNum"
      v-model:limit="orderQueryParams.pageSize"
      @pagination="getOrderList"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import Pagination from '@/components/Pagination/index.vue';
import { Order } from '../types';
import { getUnreconciledOrders } from '../api';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  customerId: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['update:visible', 'confirm']);

const orderList = ref<Order[]>([]);
const selectedOrders = ref<Order[]>([]);
const orderTotal = ref(0);
const orderQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderNo: '',
  customerId: props.customerId,
});

// 监视customerId变化，变化时重新获取订单
watch(() => props.customerId, (newVal) => {
  if (newVal) {
    orderQueryParams.customerId = newVal;
    getOrderList();
  }
});

function getOrderList() {
  if (!props.customerId) return;
  
  getUnreconciledOrders(props.customerId, {
    pageNum: orderQueryParams.pageNum,
    pageSize: orderQueryParams.pageSize,
    orderNo: orderQueryParams.orderNo || undefined
  }).then((response: any) => {
    orderList.value = response.rows || [];
    orderTotal.value = response.total || 0;
  }).catch(() => {
    orderList.value = [];
    orderTotal.value = 0;
  });
}

function handleOrderQuery() {
  orderQueryParams.pageNum = 1;
  getOrderList();
}

function handleOrderSelectionChange(selection: Order[]) {
  selectedOrders.value = selection;
}

function handleConfirm() {
  emit('confirm', selectedOrders.value);
  emit('update:visible', false);
}
</script>
