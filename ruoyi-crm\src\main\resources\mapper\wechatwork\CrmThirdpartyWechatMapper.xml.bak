<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wechatwork.mapper.CrmThirdpartyWechatMapper">
    
    <resultMap type="CrmThirdpartyWechat" id="CrmThirdpartyWechatResult">
        <id     property="id"           column="id"             />
        <result property="userId"       column="user_id"        />
        <result property="wecomUserId"  column="wecom_user_id"  />
        <result property="createBy"     column="create_by"      />
        <result property="createTime"   column="create_time"    />
        <result property="updateBy"     column="update_by"      />
        <result property="updateTime"   column="update_time"    />
    </resultMap>

    <sql id="selectCrmThirdpartyWechatVo">
        select id, user_id, wecom_user_id, create_by, create_time, update_by, update_time
        from crm_thirdparty_wechat
    </sql>

    <select id="selectCrmThirdpartyWechatList" parameterType="CrmThirdpartyWechat" resultMap="CrmThirdpartyWechatResult">
        <include refid="selectCrmThirdpartyWechatVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="wecomUserId != null  and wecomUserId != ''">and wecom_user_id = #{wecomUserId}</if>
        </where>
    </select>
    
    <select id="selectCrmThirdpartyWechatById" parameterType="Long" resultMap="CrmThirdpartyWechatResult">
        <include refid="selectCrmThirdpartyWechatVo"/>
        where id = #{id}
    </select>
    
    <select id="selectCrmThirdpartyWechatByWecomUserId" parameterType="String" resultMap="CrmThirdpartyWechatResult">
        <include refid="selectCrmThirdpartyWechatVo"/>
        where wecom_user_id = #{wecomUserId}
    </select>
    
    <select id="selectCrmThirdpartyWechatByUserId" parameterType="Long" resultMap="CrmThirdpartyWechatResult">
        <include refid="selectCrmThirdpartyWechatVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertCrmThirdpartyWechat" parameterType="CrmThirdpartyWechat" useGeneratedKeys="true" keyProperty="id">
        insert into crm_thirdparty_wechat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="wecomUserId != null">wecom_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="wecomUserId != null">#{wecomUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmThirdpartyWechat" parameterType="CrmThirdpartyWechat">
        update crm_thirdparty_wechat
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="wecomUserId != null">wecom_user_id = #{wecomUserId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmThirdpartyWechatById" parameterType="Long">
        delete from crm_thirdparty_wechat where id = #{id}
    </delete>

    <delete id="deleteCrmThirdpartyWechatByIds" parameterType="String">
        delete from crm_thirdparty_wechat where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 