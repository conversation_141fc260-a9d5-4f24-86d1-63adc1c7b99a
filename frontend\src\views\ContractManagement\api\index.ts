import request from '@/utils/request';
import type { ContractData, QueryParams } from '../types';

// 查询合同列表
export function listContracts(params: QueryParams) {
    return request({
        url: '/front/crm/contracts/list',
        method: 'get',
        params
    });
}

// 获取合同详情
export function getContract(id: number) {
    return request({
        url: `/front/crm/contracts/${id}`,
        method: 'get'
    });
}

// 新增合同
export function addContract(data: ContractData) {
    return request({
        url: '/front/crm/contracts',
        method: 'post',
        data
    });
}

// 更新合同
export function updateContract(data: ContractData) {
    return request({
        url: '/front/crm/contracts',
        method: 'put',
        data
    });
}

// 删除合同（支持批量）
export function deleteContracts(ids: number[]) {
    return request({
        url: `/front/crm/contracts/${ids.join(',')}`,
        method: 'delete'
    });
}

// 导出合同列表
export function exportContracts(params: QueryParams) {
    return request({
        url: '/front/crm/contracts/export',
        method: 'post',
        data: params,
        responseType: 'blob'
    });
} 