<template>
    <div class="contract-products">
        <el-table :data="products" border style="width: 100%">
            <el-table-column prop="product_name" label="产品名称" />
            <el-table-column prop="specification" label="规格型号" />
            <el-table-column prop="quantity" label="数量" />
            <el-table-column prop="unit_price" label="单价" />
            <el-table-column prop="total_price" label="总价" />
            <el-table-column prop="delivery_date" label="交付日期" />
            <el-table-column prop="remarks" label="备注" />
        </el-table>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

interface Product {
    product_name: string;
    specification: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    delivery_date: string;
    remarks: string;
}

export default defineComponent({
    name: 'ContractProducts',
    props: {
        contract: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            products: [] as Product[]
        };
    },
    created() {
        // TODO: 从API获取产品列表
        this.products = [
            {
                product_name: '产品A',
                specification: '规格A',
                quantity: 10,
                unit_price: 1000,
                total_price: 10000,
                delivery_date: '2024-12-01',
                remarks: '无'
            }
        ];
    }
});
</script>

<style scoped>
.contract-products {
    padding: 20px;
}
</style> 