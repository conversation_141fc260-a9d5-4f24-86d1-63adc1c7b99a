<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:bpsim="http://www.bpsim.org/schemas/1.0" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:drools="http://www.jboss.org/drools" id="_6wTZkDF8ED6sEd75gNRmsA" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd http://www.jboss.org/drools drools.xsd http://www.bpsim.org/schemas/1.0 bpsim.xsd http://www.omg.org/spec/DD/20100524/DC DC.xsd http://www.omg.org/spec/DD/20100524/DI DI.xsd " exporter="jBPM Process Modeler" exporterVersion="2.0" targetNamespace="http://www.omg.org/bpmn20">
  <bpmn2:itemDefinition id="_ceoApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_ceoApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_deptDirectorApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_financeApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_SkippableInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_PriorityInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_CommentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_DescriptionInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_CreatedByInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_TaskNameInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_GroupIdInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_ContentInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_NotStartedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_NotCompletedReassignInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_NotStartedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:itemDefinition id="_managerApproval_NotCompletedNotifyInputXItem" structureRef="Object"/>
  <bpmn2:collaboration id="_8E9B9A37-2FF1-43BC-A1F2-12CC57010DBD" name="Default Collaboration">
    <bpmn2:participant id="_6833125E-93B0-4FF5-B420-DC68FFFB6DA5" name="Pool Participant" processRef="expense"/>
  </bpmn2:collaboration>
  <bpmn2:process id="expense" drools:packageName="com.example" drools:version="1.0" drools:adHoc="false" name="expense" isExecutable="true" processType="Public">
    <bpmn2:documentation><![CDATA[这是一个参考文件，展示报销审批流程的设计]]></bpmn2:documentation>
    <bpmn2:sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"/>
    <bpmn2:sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="amountGateway"/>
    <bpmn2:sequenceFlow id="flowLowAmount" name="小额报销(<1000)" sourceRef="amountGateway" targetRef="financeApproval">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[小额报销(<1000)]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[${amount < 1000}]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="flowMidAmount" name="中额报销(1000-5000)" sourceRef="amountGateway" targetRef="deptDirectorApproval">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[中额报销(1000-5000)]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[${amount >= 1000 && amount <= 5000}]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="flowHighAmount" name="大额报销(>5000)" sourceRef="amountGateway" targetRef="ceoApproval">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[大额报销(>5000)]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[${amount > 5000}]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="flow8" sourceRef="ceoApproval" targetRef="mergeGateway"/>
    <bpmn2:sequenceFlow id="flow7" sourceRef="deptDirectorApproval" targetRef="mergeGateway"/>
    <bpmn2:sequenceFlow id="flow6" sourceRef="financeApproval" targetRef="mergeGateway"/>
    <bpmn2:sequenceFlow id="flow9" sourceRef="mergeGateway" targetRef="paymentProcess"/>
    <bpmn2:sequenceFlow id="flow10" sourceRef="paymentProcess" targetRef="endEvent"/>
    <bpmn2:startEvent id="startEvent" name="提交报销申请">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[提交报销申请]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:outgoing>flow1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:userTask id="managerApproval" name="直属主管审批">
      <bpmn2:documentation><![CDATA[直属主管审批报销申请]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[直属主管审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow1</bpmn2:incoming>
      <bpmn2:outgoing>flow2</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="managerApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_managerApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="managerApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_managerApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>managerApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>managerApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>managerApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[managerApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>managerApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[managerApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:exclusiveGateway id="amountGateway" name="金额判断" gatewayDirection="Diverging">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[金额判断]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow2</bpmn2:incoming>
      <bpmn2:outgoing>flowHighAmount</bpmn2:outgoing>
      <bpmn2:outgoing>flowMidAmount</bpmn2:outgoing>
      <bpmn2:outgoing>flowLowAmount</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:userTask id="financeApproval" name="财务审批">
      <bpmn2:documentation><![CDATA[小额报销，财务审批后完成]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[财务审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flowLowAmount</bpmn2:incoming>
      <bpmn2:outgoing>flow6</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="financeApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_financeApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="financeApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_financeApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>financeApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>financeApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>financeApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[financeApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>financeApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[financeApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:userTask id="deptDirectorApproval" name="部门总监审批">
      <bpmn2:documentation><![CDATA[中额报销，需要部门总监审批]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[部门总监审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flowMidAmount</bpmn2:incoming>
      <bpmn2:outgoing>flow7</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="deptDirectorApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_deptDirectorApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="deptDirectorApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_deptDirectorApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>deptDirectorApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>deptDirectorApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>deptDirectorApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[deptDirectorApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>deptDirectorApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[deptDirectorApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:userTask id="ceoApproval" name="总经理审批">
      <bpmn2:documentation><![CDATA[大额报销，需要总经理审批]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[总经理审批]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flowHighAmount</bpmn2:incoming>
      <bpmn2:outgoing>flow8</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="ceoApproval_TaskNameInputX" drools:dtype="Object" itemSubjectRef="_ceoApproval_TaskNameInputXItem" name="TaskName"/>
        <bpmn2:dataInput id="ceoApproval_SkippableInputX" drools:dtype="Object" itemSubjectRef="_ceoApproval_SkippableInputXItem" name="Skippable"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>ceoApproval_TaskNameInputX</bpmn2:dataInputRefs>
          <bpmn2:dataInputRefs>ceoApproval_SkippableInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>ceoApproval_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[ceoApproval_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>ceoApproval_SkippableInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[false]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[ceoApproval_SkippableInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:userTask>
    <bpmn2:exclusiveGateway id="mergeGateway" name="流程汇聚" gatewayDirection="Converging">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[流程汇聚]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow6</bpmn2:incoming>
      <bpmn2:incoming>flow7</bpmn2:incoming>
      <bpmn2:incoming>flow8</bpmn2:incoming>
      <bpmn2:outgoing>flow9</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:task id="paymentProcess" drools:taskName="" name="财务付款">
      <bpmn2:documentation><![CDATA[自动处理付款流程]]></bpmn2:documentation>
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[财务付款]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow9</bpmn2:incoming>
      <bpmn2:outgoing>flow10</bpmn2:outgoing>
      <bpmn2:ioSpecification>
        <bpmn2:dataInput id="paymentProcess_TaskNameInputX" drools:dtype="Object" name="TaskName"/>
        <bpmn2:inputSet>
          <bpmn2:dataInputRefs>paymentProcess_TaskNameInputX</bpmn2:dataInputRefs>
        </bpmn2:inputSet>
      </bpmn2:ioSpecification>
      <bpmn2:dataInputAssociation>
        <bpmn2:targetRef>paymentProcess_TaskNameInputX</bpmn2:targetRef>
        <bpmn2:assignment>
          <bpmn2:from xsi:type="bpmn2:tFormalExpression"><![CDATA[Task]]></bpmn2:from>
          <bpmn2:to xsi:type="bpmn2:tFormalExpression"><![CDATA[paymentProcess_TaskNameInputX]]></bpmn2:to>
        </bpmn2:assignment>
      </bpmn2:dataInputAssociation>
    </bpmn2:task>
    <bpmn2:endEvent id="endEvent" name="报销完成">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[报销完成]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>flow10</bpmn2:incoming>
    </bpmn2:endEvent>
  </bpmn2:process>
  <bpmndi:BPMNDiagram>
    <bpmndi:BPMNPlane bpmnElement="expense">
      <bpmndi:BPMNShape id="shape_endEvent" bpmnElement="endEvent">
        <dc:Bounds height="56" width="56" x="770" y="200"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_paymentProcess" bpmnElement="paymentProcess">
        <dc:Bounds height="80" width="100" x="620" y="178"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_mergeGateway" bpmnElement="mergeGateway">
        <dc:Bounds height="56" width="56" x="520" y="193"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_ceoApproval" bpmnElement="ceoApproval">
        <dc:Bounds height="80" width="100" x="370" y="300"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_deptDirectorApproval" bpmnElement="deptDirectorApproval">
        <dc:Bounds height="80" width="100" x="370" y="200"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_financeApproval" bpmnElement="financeApproval">
        <dc:Bounds height="80" width="100" x="370" y="100"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_amountGateway" bpmnElement="amountGateway">
        <dc:Bounds height="56" width="56" x="280" y="193"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_managerApproval" bpmnElement="managerApproval">
        <dc:Bounds height="80" width="100" x="130" y="178"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_startEvent" bpmnElement="startEvent">
        <dc:Bounds height="56" width="56" x="50" y="200"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge_shape_paymentProcess_to_shape_endEvent" bpmnElement="flow10">
        <di:waypoint x="720" y="218"/>
        <di:waypoint x="770" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_mergeGateway_to_shape_paymentProcess" bpmnElement="flow9">
        <di:waypoint x="570" y="218"/>
        <di:waypoint x="620" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_financeApproval_to_shape_mergeGateway" bpmnElement="flow6">
        <di:waypoint x="470" y="140"/>
        <di:waypoint x="520" y="193"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_deptDirectorApproval_to_shape_mergeGateway" bpmnElement="flow7">
        <di:waypoint x="470" y="240"/>
        <di:waypoint x="520" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_ceoApproval_to_shape_mergeGateway" bpmnElement="flow8">
        <di:waypoint x="470" y="340"/>
        <di:waypoint x="520" y="243"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_amountGateway_to_shape_ceoApproval" bpmnElement="flowHighAmount">
        <di:waypoint x="305" y="243"/>
        <di:waypoint x="305" y="340"/>
        <di:waypoint x="370" y="340"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_amountGateway_to_shape_deptDirectorApproval" bpmnElement="flowMidAmount">
        <di:waypoint x="330" y="218"/>
        <di:waypoint x="370" y="240"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_amountGateway_to_shape_financeApproval" bpmnElement="flowLowAmount">
        <di:waypoint x="305" y="193"/>
        <di:waypoint x="305" y="140"/>
        <di:waypoint x="370" y="140"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_managerApproval_to_shape_amountGateway" bpmnElement="flow2">
        <di:waypoint x="230" y="218"/>
        <di:waypoint x="280" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_startEvent_to_shape_managerApproval" bpmnElement="flow1">
        <di:waypoint x="86" y="218"/>
        <di:waypoint x="130" y="218"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmn2:relationship type="BPSimData">
    <bpmn2:extensionElements>
      <bpsim:BPSimData>
        <bpsim:Scenario id="default" name="Simulationscenario">
          <bpsim:ScenarioParameters/>
          <bpsim:ElementParameters elementRef="paymentProcess">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="ceoApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="deptDirectorApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="financeApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="managerApproval">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="startEvent">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
          </bpsim:ElementParameters>
        </bpsim:Scenario>
      </bpsim:BPSimData>
    </bpmn2:extensionElements>
    <bpmn2:source>_6wTZkDF8ED6sEd75gNRmsA</bpmn2:source>
    <bpmn2:target>_6wTZkDF8ED6sEd75gNRmsA</bpmn2:target>
  </bpmn2:relationship>
</bpmn2:definitions>