package com.ruoyi.crm.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.domain.entity.CrmPaymentPlan;

/**
 * 回款计划工作流Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IPaymentPlanWorkflowService {
    
    /**
     * 启动回款计划审批流程
     * 
     * @param paymentPlan 回款计划
     * @param variables 流程变量
     * @return 流程实例ID
     */
    String startApprovalProcess(CrmPaymentPlan paymentPlan, Map<String, Object> variables);
    
    /**
     * 完成当前任务
     * 
     * @param taskId 任务ID
     * @param variables 任务变量
     * @param comment 审批意见
     * @return 操作结果
     */
    boolean completeTask(String taskId, Map<String, Object> variables, String comment);
    
    /**
     * 审批通过
     * 
     * @param taskId 任务ID
     * @param approverId 审批人ID
     * @param comment 审批意见
     * @return 操作结果
     */
    boolean approveTask(String taskId, Long approverId, String comment);
    
    /**
     * 审批拒绝
     * 
     * @param taskId 任务ID
     * @param approverId 审批人ID
     * @param comment 拒绝理由
     * @return 操作结果
     */
    boolean rejectTask(String taskId, Long approverId, String comment);
    
    /**
     * 获取用户的待办任务
     * 
     * @param userId 用户ID
     * @return 待办任务列表
     */
    List<Map<String, Object>> getPendingTasks(Long userId);
    
    /**
     * 获取流程实例的任务历史
     * 
     * @param processInstanceId 流程实例ID
     * @return 任务历史列表
     */
    List<Map<String, Object>> getTaskHistory(String processInstanceId);
    
    /**
     * 获取流程实例的当前任务
     * 
     * @param processInstanceId 流程实例ID
     * @return 当前任务信息
     */
    Map<String, Object> getCurrentTask(String processInstanceId);
    
    /**
     * 撤销流程
     * 
     * @param processInstanceId 流程实例ID
     * @param reason 撤销原因
     * @return 操作结果
     */
    boolean cancelProcess(String processInstanceId, String reason);
    
    /**
     * 查询流程定义
     * 
     * @return 流程定义列表
     */
    List<Map<String, Object>> getProcessDefinitions();
    
    /**
     * 部署流程定义
     * 
     * @param resourceName 资源名称
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    String deployProcess(String resourceName, String bpmnXml);
    
    /**
     * 根据计划ID获取流程实例
     * 
     * @param planId 计划ID
     * @return 流程实例信息
     */
    Map<String, Object> getProcessInstanceByPlanId(Long planId);
    
    /**
     * 检查计划是否在审批中
     * 
     * @param planId 计划ID
     * @return 是否在审批中
     */
    boolean isInApproval(Long planId);
    
    /**
     * 获取流程实例变量
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程变量
     */
    Map<String, Object> getProcessVariables(String processInstanceId);
    
    /**
     * 设置流程实例变量
     * 
     * @param processInstanceId 流程实例ID
     * @param variables 变量映射
     * @return 操作结果
     */
    boolean setProcessVariables(String processInstanceId, Map<String, Object> variables);
}