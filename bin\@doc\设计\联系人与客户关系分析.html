<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人与客户关系分析</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .table-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .table-info h4 {
            color: #495057;
            margin-top: 0;
            margin-bottom: 10px;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d73a49;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 20px;
        }
        .problem-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .suggestion-section {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .plan-section {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人与客户关系分析</h1>
        
        <div class="toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#database-structure">数据库表结构分析</a></li>
                <li><a href="#entity-analysis">实体类分析</a></li>
                <li><a href="#relationship-diagram">关系图</a></li>
                <li><a href="#relationship-summary">关系总结</a></li>
                <li><a href="#solved-problems">已解决的问题</a></li>
                <li><a href="#new-architecture-advantages">新架构优势</a></li>
                <li><a href="#implementation-status">实施完成情况</a></li>
            </ul>
        </div>

        <h2 id="database-structure">数据库表结构分析</h2>

        <h3>主要实体表</h3>

        <div class="table-info">
            <h4>1. crm_business_customers (客户表)</h4>
            <ul>
                <li>主键：<code>id</code></li>
                <li>核心字段：<code>responsible_person_id</code>, <code>customer_name</code>, <code>mobile</code>, <code>phone</code>, <code>email</code>, <code>website</code>, <code>customer_industry</code> 等</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>2. crm_business_contacts (联系人表)</h4>
            <ul>
                <li>主键：<code>id</code></li>
                <li>核心字段：<code>responsible_person_id</code>, <code>name</code>, <code>mobile</code>, <code>phone</code>, <code>email</code>, <code>position</code> 等</li>
                <li><span class="highlight">已移除</span>：<code>customer_name</code> 字段（通过关联表建立关系）</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>3. crm_business_leads (线索表)</h4>
            <ul>
                <li>主键：<code>id</code></li>
                <li>核心字段：<code>responsible_person_id</code>, <code>lead_name</code>, <code>customer_name</code>, <code>mobile</code>, <code>phone</code>, <code>email</code> 等</li>
            </ul>
        </div>

        <h3>关联关系表</h3>

        <div class="table-info">
            <h4>1. crm_customer_contact_relations (客户联系人关联表) <span class="highlight">新增</span></h4>
            <p>多对多关联表，核心字段：</p>
            <ul>
                <li><code>id</code> - 主键</li>
                <li><code>customer_id</code> - 客户ID（外键）</li>
                <li><code>contact_id</code> - 联系人ID（外键）</li>
                <li><code>relation_type</code> - 关系类型（主要联系人、次要联系人、决策者等）</li>
                <li><code>is_primary</code> - 是否为主联系人</li>
                <li><code>start_date</code> - 关系开始时间</li>
                <li><code>end_date</code> - 关系结束时间</li>
                <li><code>status</code> - 关系状态（有效、无效）</li>
                <li><code>remarks</code> - 备注</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>2. crm_business_follow_up_records (跟进记录表)</h4>
            <p>关联字段：</p>
            <ul>
                <li><code>related_customer_id</code> - 关联客户ID</li>
                <li><code>related_contact_id</code> - 相关联系人ID</li>
                <li><code>module_type</code> - 所属模块（customer:客户, lead:线索, opportunity:商机, contract:合同）</li>
                <li><span class="highlight">建议</span>：可通过关联表查询客户的联系人关系</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>2. crm_business_opportunities (商机表)</h4>
            <ul>
                <li>关联字段：<code>customer_name</code> - 客户名称</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>3. crm_business_contracts (合同表)</h4>
            <ul>
                <li>通过合同关联客户和联系人</li>
            </ul>
        </div>

        <div class="table-info">
            <h4>4. crm_business_payments (回款表)</h4>
            <ul>
                <li>关联字段：<code>customer_id</code> - 客户ID</li>
            </ul>
        </div>

        <h2 id="entity-analysis">实体类分析</h2>

        <h3>CrmCustomer.java (客户实体类)</h3>
        <ul>
            <li>包含客户基本信息：ID、负责人、客户名称、联系方式、行业等</li>
            <li>提供了 <code>setIndustry</code> 方法用于设置行业信息</li>
            <li><span class="highlight">新增</span>：<code>contactRelations</code> - 客户联系人关联关系集合</li>
            <li><span class="highlight">新增</span>：<code>contacts</code> - 关联的联系人集合</li>
        </ul>

        <h3>CrmContacts.java (联系人实体类)</h3>
        <ul>
            <li>包含联系人基本信息：ID、负责人、姓名、联系方式、职位等</li>
            <li><span class="highlight">已移除</span>：<code>customerName</code> 字段</li>
            <li><span class="highlight">新增</span>：<code>customerRelations</code> - 联系人客户关联关系集合</li>
            <li><span class="highlight">新增</span>：<code>customers</code> - 关联的客户集合</li>
        </ul>

        <h3>CrmCustomerContactRelation.java (客户联系人关联实体类) <span class="highlight">新增</span></h3>
        <ul>
            <li>定义客户与联系人之间的多对多关联关系</li>
            <li>包含关系类型、主联系人标识、时间范围、状态等扩展信息</li>
            <li>提供关系类型和状态的常量定义</li>
            <li>支持复杂的客户联系人关系管理</li>
        </ul>

        <h2 id="relationship-diagram">关系图</h2>

        <div class="mermaid">
            erDiagram
                CUSTOMERS {
                    int id PK
                    varchar responsible_person_id
                    varchar customer_name
                    varchar mobile
                    varchar phone
                    varchar email
                    varchar website
                    varchar customer_industry
                    timestamp created_at
                    timestamp updated_at
                }
                
                CONTACTS {
                    int id PK
                    varchar responsible_person_id
                    varchar name
                    varchar mobile
                    varchar phone
                    varchar email
                    varchar position
                    timestamp created_at
                    timestamp updated_at
                }
                
                CUSTOMER_CONTACT_RELATIONS {
                    int id PK
                    int customer_id FK
                    int contact_id FK
                    varchar relation_type
                    tinyint is_primary
                    date start_date
                    date end_date
                    varchar status
                    text remarks
                    tinyint del_flag
                    timestamp created_at
                    timestamp updated_at
                }
                
                LEADS {
                    int id PK
                    varchar responsible_person_id
                    varchar lead_name
                    varchar customer_name
                    varchar mobile
                    varchar phone
                    varchar email
                    varchar lead_source
                    timestamp created_at
                    timestamp updated_at
                }
                
                FOLLOW_UP_RECORDS {
                    int id PK
                    varchar module_type
                    text follow_up_content
                    int related_customer_id FK
                    int related_contact_id FK
                    int related_opportunity_id FK
                    timestamp created_at
                    timestamp updated_at
                }
                
                OPPORTUNITIES {
                    int id PK
                    int manager_id
                    varchar opportunity_name
                    varchar customer_name
                    decimal opportunity_amount
                    varchar opportunity_stage
                    timestamp created_at
                    timestamp updated_at
                }
                
                CONTRACTS {
                    int id PK
                    varchar contract_name
                    int customer_id FK
                    decimal contract_amount
                    timestamp created_at
                    timestamp updated_at
                }
                
                PAYMENTS {
                    int id PK
                    int manager_id
                    varchar payment_number
                    int customer_id FK
                    int contract_id FK
                    decimal payment_amount
                    timestamp created_at
                    timestamp updated_at
                }
                
                CUSTOMERS ||--o{ CUSTOMER_CONTACT_RELATIONS : "customer_id"
                CONTACTS ||--o{ CUSTOMER_CONTACT_RELATIONS : "contact_id"
                CUSTOMERS ||--o{ FOLLOW_UP_RECORDS : "related_customer_id"
                CONTACTS ||--o{ FOLLOW_UP_RECORDS : "related_contact_id"
                CUSTOMERS ||--o{ OPPORTUNITIES : "customer_name"
                CUSTOMERS ||--o{ CONTRACTS : "customer_id"
                CUSTOMERS ||--o{ PAYMENTS : "customer_id"
                CONTRACTS ||--o{ PAYMENTS : "contract_id"
                LEADS ||--o{ FOLLOW_UP_RECORDS : "module_type"
                OPPORTUNITIES ||--o{ FOLLOW_UP_RECORDS : "related_opportunity_id"
        </div>

        <h2 id="relationship-summary">关系总结</h2>

        <h3>1. 客户与联系人关系 <span class="highlight">已重构</span></h3>
        <ul>
            <li><span class="highlight">多对多关系</span>：一个客户可以有多个联系人，一个联系人也可以关联多个客户</li>
            <li>通过 <code>crm_customer_contact_relations</code> 关联表建立外键关联</li>
            <li>支持关系类型、主联系人标识、时间范围等扩展信息</li>
            <li>数据完整性通过外键约束保障</li>
        </ul>

        <h3>2. 关联表功能特性</h3>
        <ul>
            <li><strong>关系类型管理</strong>：支持主要联系人、次要联系人、决策者、技术联系人等</li>
            <li><strong>主联系人标识</strong>：可设置客户的主要联系人</li>
            <li><strong>时间范围控制</strong>：支持关系的开始和结束时间</li>
            <li><strong>状态管理</strong>：有效、无效状态控制</li>
            <li><strong>备注信息</strong>：支持关系的详细说明</li>
        </ul>

        <h3>3. 跟进记录作为中心枢纽</h3>
        <ul>
            <li>跟进记录表通过外键关联客户和联系人</li>
            <li>支持多模块跟进（客户、线索、商机、合同）</li>
            <li>可通过关联表查询客户的所有联系人关系</li>
        </ul>

        <h3>4. 业务流程关联</h3>
        <ul>
            <li><strong>业务流程</strong>：线索 → 客户 → 联系人 → 商机 → 合同 → 回款</li>
            <li>每个环节都可以通过跟进记录进行追踪</li>
            <li>客户与联系人的多对多关系支持更复杂的业务场景</li>
        </ul>

        <div class="plan-section">
            <h2 id="solved-problems">已解决的问题</h2>

            <h3>1. 数据一致性问题 <span class="highlight">已解决</span></h3>
            <ul>
                <li>通过 <code>crm_customer_contact_relations</code> 关联表建立外键约束</li>
                <li>移除联系人表中的 <code>customer_name</code> 字段，消除字符串关联风险</li>
                <li>客户名称变更不再影响联系人关联关系</li>
                <li>数据库层面保障引用完整性</li>
            </ul>

            <h3>2. 数据冗余问题 <span class="highlight">已解决</span></h3>
            <ul>
                <li>联系人表不再存储客户名称，消除数据冗余</li>
                <li>通过关联表统一管理客户与联系人关系</li>
                <li>避免数据不一致问题</li>
            </ul>

            <h3>3. 关系复杂性问题 <span class="highlight">已解决</span></h3>
            <ul>
                <li>支持多对多关系，满足复杂业务场景需求</li>
                <li>一个联系人可以关联多个客户（如代理商、合作伙伴场景）</li>
                <li>提供关系类型、主联系人等扩展信息管理</li>
            </ul>
        </div>

        <div class="suggestion-section">
            <h2 id="new-architecture-advantages">新架构优势</h2>

            <h3>1. 数据完整性保障</h3>
            <ul>
                <li>通过外键约束确保客户与联系人关联的数据完整性</li>
                <li>防止无效的客户或联系人关联</li>
                <li>支持级联删除和更新操作</li>
            </ul>

            <h3>2. 业务场景支持</h3>
            <ul>
                <li>支持一个联系人关联多个客户的复杂业务场景</li>
                <li>提供关系类型管理，区分不同类型的联系人角色</li>
                <li>支持主联系人标识，便于业务处理</li>
                <li>时间范围控制，支持历史关系管理</li>
            </ul>

            <h3>3. 查询性能优化</h3>
            <ul>
                <li>通过索引优化客户与联系人的关联查询</li>
                <li>支持高效的双向查询（客户查联系人、联系人查客户）</li>
                <li>减少数据冗余，提高存储效率</li>
            </ul>

            <h3>4. 扩展性增强</h3>
            <ul>
                <li>关联表设计支持未来业务需求扩展</li>
                <li>可灵活添加新的关系属性</li>
                <li>支持复杂的客户联系人关系分析</li>
            </ul>
        </div>

        <div class="plan-section">
            <h2 id="implementation-status">实施完成情况</h2>

            <h3>1. 数据库层面 <span class="highlight">已完成</span></h3>
            <ul>
                <li>✅ 创建 <code>crm_customer_contact_relations</code> 关联表</li>
                <li>✅ 设计完整的多对多关系结构</li>
                <li>✅ 提供数据迁移SQL脚本</li>
                <li>✅ 建立外键约束和索引</li>
            </ul>

            <h3>2. 实体层面 <span class="highlight">已完成</span></h3>
            <ul>
                <li>✅ 创建 <code>CrmCustomerContactRelation</code> 实体类</li>
                <li>✅ 修改 <code>CrmCustomer</code> 实体，添加关联属性</li>
                <li>✅ 修改 <code>CrmContacts</code> 实体，移除 <code>customerName</code> 字段</li>
                <li>✅ 配置JPA关联关系映射</li>
            </ul>

            <h3>3. 数据访问层面 <span class="highlight">已完成</span></h3>
            <ul>
                <li>✅ 创建 <code>CrmCustomerContactRelationMapper</code> 接口</li>
                <li>✅ 实现 <code>CrmCustomerContactRelationMapper.xml</code> 映射文件</li>
                <li>✅ 更新 <code>CrmContactsMapper.xml</code>，移除 <code>customer_name</code> 相关映射</li>
                <li>✅ 提供完整的CRUD操作支持</li>
            </ul>

            <h3>4. 业务逻辑层面 <span class="highlight">已完成</span></h3>
            <ul>
                <li>✅ 创建 <code>ICrmCustomerContactRelationService</code> 接口</li>
                <li>✅ 实现 <code>CrmCustomerContactRelationServiceImpl</code> 服务类</li>
                <li>✅ 提供关联关系管理、批量操作等业务方法</li>
                <li>✅ 支持主联系人设置和关系类型管理</li>
            </ul>

            <h3>5. 后续建议</h3>
            <ul>
                <li>🔄 更新前端界面，支持新的多对多关系管理</li>
                <li>🔄 执行数据迁移脚本，将现有数据转换为新结构</li>
                <li>🔄 进行全面测试，验证新架构的稳定性</li>
                <li>🔄 更新相关业务流程和用户文档</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e'
            }
        });
    </script>
</body>
</html>