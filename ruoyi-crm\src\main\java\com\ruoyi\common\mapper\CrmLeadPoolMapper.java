package com.ruoyi.common.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.domain.entity.CrmLeadPool;

/**
 * 线索池Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface CrmLeadPoolMapper {
    
    /**
     * 查询线索池
     * 
     * @param id 线索池主键
     * @return 线索池
     */
    CrmLeadPool selectCrmLeadPoolById(Long id);

    /**
     * 查询线索池列表
     * 
     * @param crmLeadPool 线索池
     * @return 线索池集合
     */
    List<CrmLeadPool> selectCrmLeadPoolList(CrmLeadPool crmLeadPool);

    /**
     * 查询可用的线索池列表
     * 
     * @param crmLeadPool 线索池查询条件
     * @return 线索池集合
     */
    List<CrmLeadPool> selectAvailableLeadPoolList(CrmLeadPool crmLeadPool);

    /**
     * 根据质量等级查询线索池列表
     * 
     * @param qualityLevel 质量等级
     * @return 线索池集合
     */
    List<CrmLeadPool> selectLeadPoolByQualityLevel(@Param("qualityLevel") String qualityLevel);

    /**
     * 根据地区查询线索池列表
     * 
     * @param region 地区
     * @return 线索池集合
     */
    List<CrmLeadPool> selectLeadPoolByRegion(@Param("region") String region);

    /**
     * 根据行业查询线索池列表
     * 
     * @param industry 行业
     * @return 线索池集合
     */
    List<CrmLeadPool> selectLeadPoolByIndustry(@Param("industry") String industry);

    /**
     * 新增线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    int insertCrmLeadPool(CrmLeadPool crmLeadPool);

    /**
     * 修改线索池
     * 
     * @param crmLeadPool 线索池
     * @return 结果
     */
    int updateCrmLeadPool(CrmLeadPool crmLeadPool);

    /**
     * 更新线索池状态
     * 
     * @param id 线索池ID
     * @param poolStatus 池状态
     * @param updateBy 更新人
     * @return 结果
     */
    int updateLeadPoolStatus(@Param("id") Long id, 
                           @Param("poolStatus") String poolStatus, 
                           @Param("updateBy") String updateBy);

    /**
     * 批量更新线索池状态
     * 
     * @param ids 线索池ID数组
     * @param poolStatus 池状态
     * @param updateBy 更新人
     * @return 结果
     */
    int updateLeadPoolStatusBatch(@Param("ids") Long[] ids, 
                                @Param("poolStatus") String poolStatus, 
                                @Param("updateBy") String updateBy);

    /**
     * 删除线索池
     * 
     * @param id 线索池主键
     * @return 结果
     */
    int deleteCrmLeadPoolById(Long id);

    /**
     * 批量删除线索池
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCrmLeadPoolByIds(Long[] ids);

    /**
     * 根据线索ID查询线索池
     * 
     * @param leadId 线索ID
     * @return 线索池
     */
    CrmLeadPool selectCrmLeadPoolByLeadId(@Param("leadId") Long leadId);

    /**
     * 统计线索池数量
     * 
     * @param crmLeadPool 查询条件
     * @return 数量
     */
    int countLeadPool(CrmLeadPool crmLeadPool);

    /**
     * 统计各状态线索池数量
     * 
     * @return 统计结果
     */
    List<CrmLeadPool> countLeadPoolByStatus();

    /**
     * 统计各质量等级线索池数量
     * 
     * @return 统计结果
     */
    List<CrmLeadPool> countLeadPoolByQualityLevel();

    /**
     * 统计各地区线索池数量
     * 
     * @return 统计结果
     */
    List<CrmLeadPool> countLeadPoolByRegion();

    /**
     * 统计各行业线索池数量
     * 
     * @return 统计结果
     */
    List<CrmLeadPool> countLeadPoolByIndustry();
}
