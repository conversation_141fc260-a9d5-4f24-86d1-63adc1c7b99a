import request from '@/utils/request';

interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

// 定义接口类型
export interface LeadQuery {
    pageNum: number;
    pageSize: number;
    searchKeyword?: string;
    filterType?: string;
    [key: string]: any;
}

export interface LeadForm {
    id?: number;
    leadName: string;
    customerName: string;
    leadSource: string;
    phone?: string;
    email?: string;
    customerIndustry?: string;
    remarks?: string;
    nextContactTime?: string;
    responsiblePersonId: number;
    [key: string]: any;
}

export interface AssignForm {
    leadId: number;
    newOwnerId: number;
    [key: string]: any;
}

export interface StatusForm {
    leadId: number;
    status: string;
    convertType?: 'customer' | 'opportunity';
    [key: string]: any;
}

// 查询线索列表
export function listLeads(query: LeadQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leads/list',
        method: 'get',
        params: query
    });
}

// 查询线索详细
export function getLeads(id: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/leads/${id}`,
        method: 'get'
    });
}

// 新增线索
export function addLeads(data: LeadForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leads',
        method: 'post',
        data: data
    });
}

// 修改线索
export function updateLeads(data: LeadForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leads',
        method: 'put',
        data: data
    });
}

// 删除线索
export function deleteLeads(ids: number | number[]): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/leads/${typeof ids === 'number' ? ids : ids.join(',')}`,
        method: 'delete'
    });
}

// 导出线索
export function exportLeads(query: LeadQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/leads/export',
        method: 'post',
        params: query
    });
}

// 查询线索和用户关联列表
export function listLeadUserAssociations(query: LeadQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/associations/list',
        method: 'get',
        params: query
    });
}

// 更新线索状态
export function updateLeadStatus(data: StatusForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/associations',
        method: 'put',
        data: data
    });
}

// 分配线索
export function assignLead(data: AssignForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/associations',
        method: 'post',
        data: data
    });
}

// 获取线索分配历史
export function getLeadAssignmentHistory(query: LeadQuery): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/history/list',
        method: 'get',
        params: query
    });
} 