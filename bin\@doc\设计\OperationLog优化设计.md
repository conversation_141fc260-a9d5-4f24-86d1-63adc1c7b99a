 # OperationLog 注解优化设计

## 一、功能介绍

`@OperationLog` 注解用于自动记录业务操作日志，支持类级别和方法级别配置，大幅简化了日志记录的配置复杂度。优化后的设计采用配置继承机制，减少重复配置，提高代码可维护性。

---

## 二、设计优化要点

### 2.1 配置继承机制
- **类级别配置**：定义通用的 `businessType`、`entityClass`、`serviceClass`
- **方法级别配置**：继承类级别配置，只需配置特殊操作类型
- **配置覆盖**：方法级别可以覆盖类级别的任何配置

### 2.2 智能操作类型识别
- **自动识别**：根据方法名前缀自动识别操作类型
  - `add*`、`insert*` → `CREATE`
  - `edit*`、`update*` → `UPDATE`
  - `remove*`、`delete*` → `DELETE`
- **手动指定**：特殊操作可通过注解指定，如 `@OperationLog("CONVERT")`

### 2.3 简化语法
- **值语法**：`@OperationLog("CONVERT")` 等价于 `@OperationLog(operation = "CONVERT")`
- **禁用支持**：`@OperationLog(enabled = false)` 可禁用特定方法的日志记录

---

## 三、使用示例

### 3.1 基础配置（推荐）

```java
/**
 * 线索管理控制器
 * 类级别配置：所有方法继承这些配置
 */
@OperationLog(
    businessType = "线索",
    entityClass = CrmLeads.class,
    serviceClass = CrmLeadOperationLogServiceImpl.class
)
@RestController
@RequestMapping("/front/crm/leads")
public class CrmLeadController extends BaseController {

    // CRUD操作：无需任何注解，自动识别操作类型
    @PostMapping
    public AjaxResult addWithRecord(@RequestBody CrmLeads crmLeads) {
        // 自动识别为 CREATE 操作
    }

    @PutMapping
    public AjaxResult editWithRecord(@RequestBody CrmLeads crmLeads) {
        // 自动识别为 UPDATE 操作
    }

    @DeleteMapping("/{ids}")
    public AjaxResult removeWithRecord(@PathVariable Long[] ids) {
        // 自动识别为 DELETE 操作
    }

    // 特殊操作：需要指定操作类型
    @PostMapping("/convert")
    @OperationLog("CONVERT")
    public AjaxResult convertLead(@RequestBody LeadConvertDTO convertDTO) {
        // 指定为 CONVERT 操作
    }

    @PostMapping("/assign")
    @OperationLog("ASSIGN")
    public AjaxResult assignLead(@RequestBody AssignForm form) {
        // 指定为 ASSIGN 操作
    }

    // 不需要记录日志的操作
    @PostMapping("/export")
    @OperationLog(enabled = false)
    public AjaxResult export(CrmLeads crmLeads) {
        // 禁用日志记录
    }
}
```

### 3.2 对比：优化前 vs 优化后

#### 优化前（臃肿）
```java
@OperationLog(
    businessType = "线索",
    entityClass = CrmLeads.class,
    serviceClass = CrmLeadOperationLogServiceImpl.class
)
@RestController
public class CrmLeadController {

    @PostMapping("/convert")
    @OperationLog(
        businessType = "线索",
        entityClass = CrmLeads.class,
        serviceClass = CrmLeadOperationLogServiceImpl.class,
        customOperation = "CONVERT"  // 重复配置
    )
    public AjaxResult convertLead() { }

    @PostMapping("/assign")
    @OperationLog(
        businessType = "线索",
        entityClass = CrmLeads.class,
        serviceClass = CrmLeadOperationLogServiceImpl.class,
        customOperation = "ASSIGN"  // 重复配置
    )
    public AjaxResult assignLead() { }
}
```

#### 优化后（简洁）
```java
@OperationLog(
    businessType = "线索",
    entityClass = CrmLeads.class,
    serviceClass = CrmLeadOperationLogServiceImpl.class
)
@RestController
public class CrmLeadController {

    @PostMapping("/convert")
    @OperationLog("CONVERT")  // 简洁配置
    public AjaxResult convertLead() { }

    @PostMapping("/assign")
    @OperationLog("ASSIGN")   // 简洁配置
    public AjaxResult assignLead() { }
}
```

---

## 四、注解属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `businessType` | String | `""` | 业务类型，如"线索"、"客户" |
| `entityClass` | Class<?> | `Object.class` | 实体类类型 |
| `serviceClass` | Class<?> | `Object.class` | 日志服务类类型 |
| `value` | String | `""` | 操作类型简写，如"CONVERT" |
| `operation` | String | `""` | 操作类型全写，与value等价 |
| `enabled` | boolean | `true` | 是否启用日志记录 |

---

## 五、配置继承规则

### 5.1 继承优先级
1. **方法级别配置** > **类级别配置** > **默认值**
2. 方法级别未配置的属性会继承类级别配置
3. 操作类型优先级：`value`/`operation` > 方法名自动识别

### 5.2 典型继承场景

```java
@OperationLog(
    businessType = "客户",
    entityClass = CrmCustomer.class,
    serviceClass = CrmCustomerOperationLogServiceImpl.class
)
public class CrmCustomerController {

    // 继承类级别所有配置，操作类型自动识别为CREATE
    @PostMapping
    public AjaxResult addWithRecord(@RequestBody CrmCustomer customer) { }

    // 继承类级别配置，覆盖操作类型为CONVERT
    @PostMapping("/convert")
    @OperationLog("CONVERT")
    public AjaxResult convertCustomer() { }

    // 覆盖业务类型，其他继承类级别配置
    @PostMapping("/special")
    @OperationLog(businessType = "特殊客户操作")
    public AjaxResult specialOperation() { }
}
```

---

## 六、自动操作类型识别规则

| 方法名前缀 | 识别的操作类型 | 示例 |
|-----------|---------------|------|
| `add*`, `insert*` | CREATE | `addWithRecord`, `insertCustomer` |
| `edit*`, `update*` | UPDATE | `editWithRecord`, `updateCustomer` |
| `remove*`, `delete*` | DELETE | `removeWithRecord`, `deleteCustomer` |
| 其他 | 需要手动指定 | `convert*`, `assign*` |

---

## 七、最佳实践

### 7.1 推荐做法
1. **类级别配置**：在控制器类上配置通用属性
2. **方法级别简化**：只配置特殊操作类型，如 `@OperationLog("CONVERT")`
3. **命名规范**：遵循方法名前缀规范，利用自动识别
4. **禁用机制**：对不需要记录的方法使用 `enabled = false`

### 7.2 避免的做法
1. ❌ 在每个方法上重复配置相同的 `businessType`、`entityClass`、`serviceClass`
2. ❌ 为常规CRUD操作添加不必要的注解
3. ❌ 使用不规范的方法命名，影响自动识别

---

## 八、配置简化效果

### 8.1 代码行数对比
- **优化前**：每个特殊方法需要 6-8 行注解配置
- **优化后**：每个特殊方法只需要 1 行注解配置
- **减少比例**：约 85% 的注解配置代码

### 8.2 维护性提升
- **集中配置**：类级别统一管理业务配置
- **易于修改**：修改业务类型只需改一处
- **降低错误**：减少重复配置导致的配置错误

---

## 九、迁移指南

### 9.1 现有代码迁移步骤
1. 在控制器类上添加 `@OperationLog` 类级别配置
2. 删除方法级别的重复配置（`businessType`、`entityClass`、`serviceClass`）
3. 保留特殊操作的 `customOperation`，并改为 `value` 或 `operation`
4. 确认方法命名符合自动识别规范

### 9.2 迁移示例

```java
// 迁移前
@RestController
public class CrmLeadController {
    @PostMapping
    @OperationLog(
        businessType = "线索",
        entityClass = CrmLeads.class,
        serviceClass = CrmLeadOperationLogServiceImpl.class
    )
    public AjaxResult add() { }
}

// 迁移后
@OperationLog(
    businessType = "线索",
    entityClass = CrmLeads.class,
    serviceClass = CrmLeadOperationLogServiceImpl.class
)
@RestController
public class CrmLeadController {
    @PostMapping
    public AjaxResult add() { }  // 无需注解，自动识别
}
```

---

此优化设计在保持功能完整性的前提下，大幅简化了配置，提高了代码的可读性和可维护性。通过合理的配置继承和自动操作类型识别，开发者可以更专注于业务逻辑的实现，而无需过多关注日志记录的配置。