import request from '@/utils/request'

// 团队关联相关接口

/**
 * 查询团队关联列表
 */
export function getTeamRelationList(query: any) {
  return request({
    url: '/crm/relation/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取团队关联详情
 */
export function getTeamRelation(id: number) {
  return request({
    url: `/crm/relation/${id}`,
    method: 'get'
  })
}

/**
 * 新增团队关联
 */
export function addTeamRelation(data: any) {
  return request({
    url: '/crm/relation',
    method: 'post',
    data: data
  })
}

/**
 * 修改团队关联
 */
export function updateTeamRelation(data: any) {
  return request({
    url: '/crm/relation',
    method: 'put',
    data: data
  })
}

/**
 * 删除团队关联
 */
export function delTeamRelation(ids: number[]) {
  return request({
    url: `/crm/relation/${ids}`,
    method: 'delete'
  })
}

/**
 * 分配业务对象到团队
 */
export function assignTeamToBiz(teamId: number, bizId: number, bizType: string) {
  return request({
    url: '/crm/relation/assign',
    method: 'post',
    params: {
      teamId,
      bizId,
      bizType
    }
  })
}

/**
 * 取消业务对象的团队分配
 */
export function unassignTeamFromBiz(bizId: number, bizType: string) {
  return request({
    url: '/crm/relation/unassign',
    method: 'post',
    params: {
      bizId,
      bizType
    }
  })
}

/**
 * 根据业务对象查询所属团队
 */
export function getTeamByBiz(bizId: number, bizType: string) {
  return request({
    url: '/crm/relation/team',
    method: 'get',
    params: {
      bizId,
      bizType
    }
  })
}

/**
 * 根据团队ID查询关联的业务对象列表
 */
export function getBizsByTeam(teamId: number, bizType?: string) {
  return request({
    url: '/crm/relation/bizs',
    method: 'get',
    params: {
      teamId,
      bizType
    }
  })
}

/**
 * 批量分配业务对象到团队
 */
export function batchAssignTeamToBiz(teamId: number, bizIds: number[], bizType: string) {
  return request({
    url: '/crm/relation/batch-assign',
    method: 'post',
    data: {
      teamId,
      bizIds,
      bizType
    }
  })
}

/**
 * 批量取消业务对象的团队分配
 */
export function batchUnassignTeamFromBiz(bizIds: number[], bizType: string) {
  return request({
    url: '/crm/relation/batch-unassign',
    method: 'post',
    data: {
      bizIds,
      bizType
    }
  })
}

/**
 * 检查业务对象是否已分配给团队
 */
export function checkBizAssigned(bizId: number, bizType: string) {
  return request({
    url: '/crm/relation/check',
    method: 'get',
    params: {
      bizId,
      bizType
    }
  })
}

/**
 * 统计团队关联的业务对象数量
 */
export function countBizByTeam(teamId: number, bizType?: string) {
  return request({
    url: '/crm/relation/count',
    method: 'get',
    params: {
      teamId,
      bizType
    }
  })
}

/**
 * 根据业务类型统计各团队的业务对象数量
 */
export function getTeamStatistics(bizType?: string) {
  return request({
    url: '/crm/relation/statistics',
    method: 'get',
    params: {
      bizType
    }
  })
}

// 团队成员相关接口

/**
 * 查询团队成员列表
 */
export function getTeamMemberList(query: any) {
  return request({
    url: '/crm/team-member/list',
    method: 'get',
    params: query
  })
}

/**
 * 根据团队ID查询成员
 */
export function getTeamMembersByTeamId(teamId: number) {
  return request({
    url: `/crm/team-member/team/${teamId}`,
    method: 'get'
  })
}

/**
 * 根据用户ID查询所属团队
 */
export function getTeamsByUserId(userId: number) {
  return request({
    url: `/crm/team-member/user/${userId}`,
    method: 'get'
  })
}

/**
 * 添加团队成员
 */
export function addTeamMember(data: any) {
  return request({
    url: '/crm/team-member',
    method: 'post',
    data: data
  })
}

/**
 * 批量添加团队成员
 */
export function batchAddTeamMembers(teamId: number, userIds: number[], roleType: string) {
  return request({
    url: '/crm/team-member/batch-add',
    method: 'post',
    data: {
      teamId,
      userIds,
      roleType
    }
  })
}

/**
 * 更新团队成员
 */
export function updateTeamMember(data: any) {
  return request({
    url: '/crm/team-member',
    method: 'put',
    data: data
  })
}

/**
 * 移除团队成员
 */
export function removeTeamMember(teamId: number, userId: number) {
  return request({
    url: '/crm/team-member/remove',
    method: 'delete',
    params: {
      teamId,
      userId
    }
  })
}

/**
 * 批量移除团队成员
 */
export function batchRemoveTeamMembers(userIds: number[]) {
  return request({
    url: '/crm/team-member/batch-remove',
    method: 'delete',
    data: {
      userIds
    }
  })
}

/**
 * 获取可用用户列表
 */
export function getAvailableUsers() {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: {
      status: '0' // 只获取正常状态的用户
    }
  })
}

/**
 * 根据业务对象获取团队成员 (正确的实现)
 * 逻辑：业务对象 → 绑定的团队 → 团队成员
 */
export async function getTeamMembersByBiz(bizId: number, bizType: string) {
  try {
    // 第一步：获取业务对象绑定的团队
    const teamResponse = await getTeamByBiz(bizId, bizType)
    const teamRelation = teamResponse.data

    if (!teamRelation || !teamRelation.teamId) {
      // 如果没有绑定团队，返回空列表
      return {
        data: [],
        code: 200,
        msg: '该业务对象未分配团队'
      }
    }

    // 第二步：获取团队成员
    const membersResponse = await getTeamMembersByTeamId(teamRelation.teamId)
    return membersResponse
  } catch (error) {
    console.error('获取业务对象团队成员失败:', error)
    return {
      data: [],
      code: 500,
      msg: '获取团队成员失败'
    }
  }
}

/**
 * 查询可关联的业务对象列表
 */
export function getUnassignedBizList(query: any) {
  return request({
    url: '/crm/relation/bizs/unassigned',
    method: 'get',
    params: query
  })
}

/**
 * 为业务对象创建团队
 */
export function createTeamForBiz(bizId: number, bizType: string, teamName?: string) {
  return request({
    url: '/crm/team/create-for-biz',
    method: 'post',
    data: {
      bizId,
      bizType,
      teamName
    }
  })
}

// 业务类型常量
export const BizType = {
  CONTACT: 'CONTACT',
  LEAD: 'LEAD', 
  CUSTOMER: 'CUSTOMER',
  OPPORTUNITY: 'OPPORTUNITY',
  CONTRACT: 'CONTRACT',
  VISIT_PLAN: 'VISIT_PLAN'
} as const

// 角色类型常量
export const RoleType = {
  OWNER: 'leader',
  ADMIN: 'admin',
  MEMBER: 'member'
} as const

// 状态常量
export const Status = {
  NORMAL: '0',
  DISABLED: '1'
} as const
