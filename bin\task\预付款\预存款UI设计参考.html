<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM预存款管理UI设计参考</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .ui-section {
            margin-bottom: 50px;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .ui-section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
        }
        
        .ui-section h2::before {
            content: attr(data-icon);
            margin-right: 15px;
            font-size: 1.3em;
        }
        
        /* 预存款列表页面样式 */
        .prepaid-list {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .list-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .list-title {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .toolbar {
            background: #f8f9ff;
            padding: 20px 25px;
            border-bottom: 1px solid #e0e6ff;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .search-box {
            flex: 1;
            min-width: 200px;
        }
        
        .search-input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e0e6ff;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #e9ecef;
            color: #495057;
        }
        
        .btn-secondary:hover {
            background: #dee2e6;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            color: white;
        }
        
        .status-filters {
            display: flex;
            gap: 10px;
        }
        
        .status-chip {
            padding: 6px 15px;
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .status-chip.active {
            background: #667eea;
            color: white;
        }
        
        /* 表格样式 */
        .prepaid-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .prepaid-table th,
        .prepaid-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .prepaid-table th {
            background: #f8f9ff;
            color: #667eea;
            font-weight: bold;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .prepaid-table tr:hover {
            background: #f8f9ff;
        }
        
        .prepaid-no {
            font-family: 'Consolas', monospace;
            color: #667eea;
            font-weight: bold;
        }
        
        .customer-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-draft {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-pending {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-locked {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-view {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .action-edit {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .action-delete {
            background: #ffebee;
            color: #d32f2f;
        }
        
        /* 创建预存款表单样式 */
        .create-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            margin: -30px -30px 30px -30px;
            border-radius: 15px 15px 0 0;
        }
        
        .form-header h3 {
            font-size: 1.5em;
            margin: 0;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        /* 明细表格样式 */
        .details-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9ff;
            border-radius: 10px;
        }
        
        .details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .details-table th,
        .details-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .details-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            font-size: 13px;
        }
        
        .details-input {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            width: 100%;
        }
        
        /* 预存款详情页面样式 */
        .detail-page {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .detail-main {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .detail-sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .detail-title {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .detail-content {
            padding: 30px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-item {
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .info-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        /* 余额卡片样式 */
        .balance-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .balance-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .balance-amount {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .balance-details {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* 消费记录样式 */
        .consumption-records {
            margin-top: 30px;
        }
        
        .record-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .record-item:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .record-no {
            font-family: 'Consolas', monospace;
            color: #667eea;
            font-weight: bold;
        }
        
        .record-amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #dc3545;
        }
        
        .record-date {
            color: #6c757d;
            font-size: 14px;
        }
        
        .record-description {
            color: #495057;
            margin-top: 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .form-row {
                flex-direction: column;
            }
            
            .detail-page {
                grid-template-columns: 1fr;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                margin-bottom: 15px;
            }
            
            .prepaid-table {
                font-size: 12px;
            }
            
            .prepaid-table th,
            .prepaid-table td {
                padding: 10px 8px;
            }
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .prepaid-list,
        .create-form,
        .detail-main,
        .detail-sidebar {
            animation: fadeIn 0.6s ease-out;
        }
        
        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 进度条样式 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 4px 10px;
            background: #e9ecef;
            color: #495057;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        
        .tag.primary {
            background: #667eea;
            color: white;
        }
        
        .tag.success {
            background: #28a745;
            color: white;
        }
        
        .tag.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .tag.danger {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1>🎨 CRM预存款管理UI设计参考</h1>
            <p>现代化、响应式的预存款管理界面设计方案</p>
        </div>

        <!-- 1. 预存款列表页面 -->
        <div class="ui-section">
            <h2 data-icon="📋">1. 预存款列表页面</h2>
            
            <div class="prepaid-list">
                <!-- 页面头部 -->
                <div class="list-header">
                    <div class="list-title">💰 客户预存款管理</div>
                    <div>
                        <span class="tag primary">总预存款: ¥2,456,789.00</span>
                        <span class="tag success">可用余额: ¥1,234,567.89</span>
                    </div>
                </div>
                
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="search-box">
                        <input type="text" class="search-input" placeholder="🔍 搜索预存款编号、客户名称..." />
                    </div>
                    
                    <div class="status-filters">
                        <button class="status-chip active">全部</button>
                        <button class="status-chip">草稿</button>
                        <button class="status-chip">审批中</button>
                        <button class="status-chip">已审批</button>
                        <button class="status-chip">已锁定</button>
                    </div>
                    
                    <button class="btn btn-primary">
                        ➕ 新建预存款
                    </button>
                    
                    <button class="btn btn-secondary">
                        📊 导出数据
                    </button>
                </div>
                
                <!-- 预存款表格 -->
                <table class="prepaid-table">
                    <thead>
                        <tr>
                            <th>预存款编号</th>
                            <th>客户名称</th>
                            <th>预存总额</th>
                            <th>已消费</th>
                            <th>余额</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>负责人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="prepaid-no">PRE20250702000001</span></td>
                            <td><span class="customer-name">杭州科德旺有限公司</span></td>
                            <td><span class="amount">¥100,000.00</span></td>
                            <td><span class="amount">¥35,680.00</span></td>
                            <td><span class="amount">¥64,320.00</span></td>
                            <td><span class="status-badge status-approved">已审批</span></td>
                            <td>2025-07-02 09:15</td>
                            <td>张经理</td>
                            <td>
                                <div class="actions">
                                    <button class="action-btn action-view">👁️ 详情</button>
                                    <button class="action-btn action-edit">✏️ 编辑</button>
                                    <button class="action-btn action-delete">❌ 删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="prepaid-no">PRE20250701000002</span></td>
                            <td><span class="customer-name">上海泰科科技有限公司</span></td>
                            <td><span class="amount">¥80,000.00</span></td>
                            <td><span class="amount">¥12,500.00</span></td>
                            <td><span class="amount">¥67,500.00</span></td>
                            <td><span class="status-badge status-locked">🔒 已锁定</span></td>
                            <td>2025-07-01 14:30</td>
                            <td>李经理</td>
                            <td>
                                <div class="actions">
                                    <button class="action-btn action-view">👁️ 详情</button>
                                    <button class="action-btn action-edit">✏️ 编辑</button>
                                    <button class="action-btn action-delete">❌ 删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="prepaid-no">PRE20250701000001</span></td>
                            <td><span class="customer-name">北京智能制造股份有限公司</span></td>
                            <td><span class="amount">¥150,000.00</span></td>
                            <td><span class="amount">¥0.00</span></td>
                            <td><span class="amount">¥150,000.00</span></td>
                            <td><span class="status-badge status-pending">审批中</span></td>
                            <td>2025-07-01 10:45</td>
                            <td>王经理</td>
                            <td>
                                <div class="actions">
                                    <button class="action-btn action-view">👁️ 详情</button>
                                    <button class="action-btn action-edit">✏️ 编辑</button>
                                    <button class="action-btn action-delete">❌ 删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 2. 创建预存款表单 -->
        <div class="ui-section">
            <h2 data-icon="➕">2. 创建预存款表单</h2>
            
            <div class="create-form">
                <div class="form-header">
                    <h3>📝 新建客户预存款</h3>
                </div>
                
                <!-- 基础信息 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>预存款编号</label>
                        <input type="text" class="form-input" value="PRE20250702000003" readonly />
                    </div>
                    <div class="form-group">
                        <label>选择客户 *</label>
                        <select class="form-select">
                            <option>请选择客户...</option>
                            <option>杭州科德旺有限公司</option>
                            <option>上海泰科科技有限公司</option>
                            <option>北京智能制造股份有限公司</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>合同模板类型</label>
                        <select class="form-select">
                            <option>发票样式模板</option>
                            <option>标准合同模板</option>
                            <option>简化合同模板</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>负责人</label>
                        <select class="form-select">
                            <option>张经理</option>
                            <option>李经理</option>
                            <option>王经理</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>团队</label>
                        <select class="form-select">
                            <option>销售一组</option>
                            <option>销售二组</option>
                            <option>大客户组</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <input type="text" class="form-input" placeholder="请输入备注信息..." />
                    </div>
                </div>
                
                <!-- 产品明细 -->
                <div class="details-section">
                    <div class="details-header">
                        <h4>📦 产品明细</h4>
                        <button class="btn btn-success">➕ 添加明细</button>
                    </div>
                    
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>产品类别</th>
                                <th>规格型号</th>
                                <th>数量</th>
                                <th>单位</th>
                                <th>单价</th>
                                <th>金额</th>
                                <th>税率</th>
                                <th>税额</th>
                                <th>含税总额</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" class="details-input" value="3D打印材料" /></td>
                                <td><input type="text" class="details-input" value="PLA-1.75mm" /></td>
                                <td><input type="number" class="details-input" value="100" /></td>
                                <td><input type="text" class="details-input" value="件" /></td>
                                <td><input type="number" class="details-input" value="15.00" /></td>
                                <td><input type="number" class="details-input" value="1500.00" readonly /></td>
                                <td>
                                    <select class="details-input">
                                        <option>13%</option>
                                        <option>6%</option>
                                        <option>0%</option>
                                    </select>
                                </td>
                                <td><input type="number" class="details-input" value="195.00" readonly /></td>
                                <td><input type="number" class="details-input" value="1695.00" readonly /></td>
                                <td>
                                    <button class="action-btn action-delete">❌</button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="text" class="details-input" value="3D打印设备" /></td>
                                <td><input type="text" class="details-input" value="Ender-3 V2" /></td>
                                <td><input type="number" class="details-input" value="2" /></td>
                                <td><input type="text" class="details-input" value="台" /></td>
                                <td><input type="number" class="details-input" value="1200.00" /></td>
                                <td><input type="number" class="details-input" value="2400.00" readonly /></td>
                                <td>
                                    <select class="details-input">
                                        <option>13%</option>
                                        <option>6%</option>
                                        <option>0%</option>
                                    </select>
                                </td>
                                <td><input type="number" class="details-input" value="312.00" readonly /></td>
                                <td><input type="number" class="details-input" value="2712.00" readonly /></td>
                                <td>
                                    <button class="action-btn action-delete">❌</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div style="text-align: right; margin-top: 20px; padding: 20px; background: #f8f9ff; border-radius: 8px;">
                        <strong>合计金额: ¥3,900.00 | 税额: ¥507.00 | 含税总额: ¥4,407.00</strong>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div style="text-align: center; margin-top: 30px; padding-top: 30px; border-top: 1px solid #e9ecef;">
                    <button class="btn btn-secondary" style="margin-right: 15px;">💾 保存草稿</button>
                    <button class="btn btn-warning" style="margin-right: 15px;">📤 提交审批</button>
                    <button class="btn btn-primary">✅ 创建预存款</button>
                </div>
            </div>
        </div>

        <!-- 3. 预存款详情页面 -->
        <div class="ui-section">
            <h2 data-icon="👁️">3. 预存款详情页面</h2>
            
            <div class="detail-page">
                <!-- 主要内容区 -->
                <div class="detail-main">
                    <div class="detail-header">
                        <div>
                            <div class="detail-title">PRE20250702000001</div>
                            <div style="opacity: 0.9; margin-top: 5px;">杭州科德旺有限公司</div>
                        </div>
                        <div>
                            <span class="status-badge status-approved">已审批</span>
                            <button class="btn btn-warning" style="margin-left: 10px;">🔒 锁定</button>
                        </div>
                    </div>
                    
                    <div class="detail-content">
                        <!-- 基础信息 -->
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">预存总额</div>
                                <div class="info-value">¥100,000.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">已消费金额</div>
                                <div class="info-value">¥35,680.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">余额</div>
                                <div class="info-value">¥64,320.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">负责人</div>
                                <div class="info-value">张经理</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">团队</div>
                                <div class="info-value">销售一组</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">2025-07-02 09:15</div>
                            </div>
                        </div>
                        
                        <!-- 产品明细 -->
                        <h4 style="margin-bottom: 15px; color: #667eea;">📦 产品明细</h4>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>产品类别</th>
                                    <th>规格型号</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>金额</th>
                                    <th>税率</th>
                                    <th>含税总额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>3D打印材料</td>
                                    <td>PLA-1.75mm</td>
                                    <td>3,000件</td>
                                    <td>¥17.88</td>
                                    <td>¥53,640.00</td>
                                    <td>13%</td>
                                    <td>¥60,613.20</td>
                                </tr>
                                <tr>
                                    <td>3D打印设备</td>
                                    <td>Ender-3 V2</td>
                                    <td>10台</td>
                                    <td>¥1,380.00</td>
                                    <td>¥13,800.00</td>
                                    <td>13%</td>
                                    <td>¥15,594.00</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <!-- 消费记录 -->
                        <div class="consumption-records">
                            <h4 style="margin-bottom: 20px; color: #667eea;">🧾 消费记录</h4>
                            
                            <div class="record-item">
                                <div class="record-header">
                                    <div>
                                        <span class="record-no">CON20250702001</span>
                                        <span class="record-date">2025-07-02 14:30</span>
                                    </div>
                                    <span class="record-amount">-¥15,340.00</span>
                                </div>
                                <div class="record-description">
                                    订单消费：3D打印材料采购订单，PLA材料500件
                                </div>
                            </div>
                            
                            <div class="record-item">
                                <div class="record-header">
                                    <div>
                                        <span class="record-no">CON20250701003</span>
                                        <span class="record-date">2025-07-01 16:45</span>
                                    </div>
                                    <span class="record-amount">-¥20,340.00</span>
                                </div>
                                <div class="record-description">
                                    合同消费：3D打印设备维护服务合同
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 侧边栏 -->
                <div class="detail-sidebar">
                    <!-- 余额卡片 -->
                    <div class="balance-card">
                        <div class="balance-label">当前可用余额</div>
                        <div class="balance-amount">¥64,320</div>
                        <div class="balance-details">
                            <span>预存总额: ¥100,000</span>
                            <span>已消费: ¥35,680</span>
                        </div>
                    </div>
                    
                    <!-- 使用进度 -->
                    <h4 style="margin-bottom: 15px; color: #667eea;">📊 使用进度</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 35.68%;"></div>
                    </div>
                    <p style="text-align: center; color: #6c757d; margin-bottom: 25px;">已使用 35.68%</p>
                    
                    <!-- 操作按钮 -->
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <button class="btn btn-primary">💰 添加充值</button>
                        <button class="btn btn-warning">🧾 记录消费</button>
                        <button class="btn btn-secondary">📝 编辑信息</button>
                        <button class="btn btn-danger">🔒 锁定账户</button>
                    </div>
                    
                    <!-- 标签 -->
                    <div style="margin-top: 25px;">
                        <h5 style="margin-bottom: 10px; color: #667eea;">🏷️ 标签</h5>
                        <div>
                            <span class="tag primary">VIP客户</span>
                            <span class="tag success">优质合作</span>
                            <span class="tag warning">重点关注</span>
                        </div>
                    </div>
                    
                    <!-- 最近活动 -->
                    <div style="margin-top: 25px;">
                        <h5 style="margin-bottom: 15px; color: #667eea;">📅 最近活动</h5>
                        <div style="font-size: 14px; color: #6c757d; line-height: 1.8;">
                            <div>• 2小时前 - 锁定团队</div>
                            <div>• 5小时前 - 消费记录更新</div>
                            <div>• 昨天 - 余额查询</div>
                            <div>• 2天前 - 创建预存款</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计特点说明 -->
        <div class="ui-section">
            <h2 data-icon="✨">设计特点与亮点</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                <div class="info-item">
                    <div class="info-label">🎨 现代化设计</div>
                    <div class="info-value">采用渐变色彩、圆角边框、阴影效果，界面美观现代</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">📱 响应式布局</div>
                    <div class="info-value">适配桌面端、平板、手机等多种设备尺寸</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">🚀 交互体验</div>
                    <div class="info-value">悬停效果、加载动画、状态反馈等提升用户体验</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">🎯 功能完整</div>
                    <div class="info-value">涵盖列表、创建、详情、消费记录等全功能模块</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">📊 数据可视化</div>
                    <div class="info-value">进度条、状态标签、余额卡片等直观展示数据</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">🔍 搜索筛选</div>
                    <div class="info-value">支持多条件搜索和状态筛选，快速定位数据</div>
                </div>
            </div>
        </div>

        <!-- 技术实现建议 -->
        <div class="ui-section">
            <h2 data-icon="🛠️">技术实现建议</h2>
            
            <div style="background: #f8f9ff; padding: 25px; border-radius: 10px; border-left: 5px solid #667eea;">
                <h4 style="color: #667eea; margin-bottom: 15px;">前端技术栈</h4>
                <ul style="line-height: 2;">
                    <li><strong>Vue 3 + Element Plus</strong> - 主体框架和UI组件库</li>
                    <li><strong>CSS3 + Flexbox/Grid</strong> - 布局和样式实现</li>
                    <li><strong>Axios</strong> - API请求处理</li>
                    <li><strong>Vue Router</strong> - 路由管理</li>
                    <li><strong>Pinia</strong> - 状态管理</li>
                </ul>
                
                <h4 style="color: #667eea; margin: 25px 0 15px 0;">样式特性</h4>
                <ul style="line-height: 2;">
                    <li><strong>CSS变量</strong> - 统一颜色主题管理</li>
                    <li><strong>Flexbox/Grid</strong> - 响应式布局</li>
                    <li><strong>CSS动画</strong> - 交互反馈和过渡效果</li>
                    <li><strong>媒体查询</strong> - 移动端适配</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
