<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadOperationLogMapper">

    <resultMap type="com.ruoyi.common.domain.entity.CrmLeadOperationLog" id="CrmLeadOperationLogResult">
        <id     property="id"                column="id"                />
        <result property="leadId"            column="lead_id"           />
        <result property="businessType"      column="business_type"     />
        <result property="operationType"     column="operation_type"    />
        <result property="operationContent"  column="operation_content" />
        <result property="operationDetails"  column="operation_details" />
        <result property="operatorId"        column="operator_id"       />
        <result property="operatorName"      column="operator_name"     />
        <result property="operationTime"     column="operation_time"    />
        <result property="extraData"         column="extra_data"        />
        <result property="createTime"        column="create_time"       />
        <result property="updateTime"        column="update_time"       />
    </resultMap>

    <sql id="selectCrmLeadOperationLogVo">
        select id, lead_id, business_type, operation_type, operation_content, 
               operation_details, operator_id, operator_name, operation_time, 
               extra_data, create_time, update_time
        from crm_lead_operation_log
    </sql>

    <select id="selectCrmLeadOperationLogList" parameterType="com.ruoyi.common.domain.entity.CrmLeadOperationLog" resultMap="CrmLeadOperationLogResult">
        <include refid="selectCrmLeadOperationLogVo"/>
        <where>
            <if test="leadId != null">
                AND lead_id = #{leadId}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="operationType != null and operationType != ''">
                AND operation_type = #{operationType}
            </if>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="operationTime != null">
                AND operation_time = #{operationTime}
            </if>
        </where>
    </select>

    <select id="selectCrmLeadOperationLogById" parameterType="Long" resultMap="CrmLeadOperationLogResult">
        <include refid="selectCrmLeadOperationLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmLeadOperationLog" parameterType="com.ruoyi.common.domain.entity.CrmLeadOperationLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_lead_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">lead_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="operationContent != null">operation_content,</if>
            <if test="operationDetails != null">operation_details,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="extraData != null">extra_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="operationContent != null">#{operationContent},</if>
            <if test="operationDetails != null">#{operationDetails},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="extraData != null">#{extraData},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCrmLeadOperationLog" parameterType="com.ruoyi.common.domain.entity.CrmLeadOperationLog">
        update crm_lead_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">lead_id = #{leadId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="operationType != null">operation_type = #{operationType},</if>
            <if test="operationContent != null">operation_content = #{operationContent},</if>
            <if test="operationDetails != null">operation_details = #{operationDetails},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="extraData != null">extra_data = #{extraData},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmLeadOperationLogById" parameterType="Long">
        delete from crm_lead_operation_log where id = #{id}
    </delete>

    <delete id="deleteCrmLeadOperationLogByIds" parameterType="Long">
        delete from crm_lead_operation_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectCrmLeadOperationLogByTimeRange" resultMap="CrmLeadOperationLogResult">
        <include refid="selectCrmLeadOperationLogVo"/>
        where operation_time between #{startTime} and #{endTime}
    </select>
</mapper>
