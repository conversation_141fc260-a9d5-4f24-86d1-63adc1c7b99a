import request from '@/utils/request'

// 查询联系人列表
export function listContacts(query) {
  return request({
    url: '/crm/contacts/list',
    method: 'get',
    params: query
  })
}

// 查询联系人详细
export function getContacts(id) {
  return request({
    url: '/crm/contacts/' + id,
    method: 'get'
  })
}

// 新增联系人
export function addContacts(data) {
  return request({
    url: '/crm/contacts',
    method: 'post',
    data: data
  })
}

// 修改联系人
export function updateContacts(data) {
  return request({
    url: '/crm/contacts',
    method: 'put',
    data: data
  })
}

// 删除联系人
export function delContacts(id) {
  return request({
    url: '/crm/contacts/' + id,
    method: 'delete'
  })
}

// 根据客户ID查询联系人
export function getContactsByCustomer(customerId) {
  return request({
    url: '/front/crm/contacts/customer/' + customerId,
    method: 'get'
  })
}

// 搜索联系人
export function searchContacts(keyword) {
  return request({
    url: '/front/crm/contacts/search',
    method: 'get',
    params: { 
      keyword: keyword || '',
      pageNum: 1,
      pageSize: 100
    }
  })
}
