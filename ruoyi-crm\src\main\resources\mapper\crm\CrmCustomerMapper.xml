<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmCustomer" id="CrmCustomerResult">
        <id     property="id"                column="id"                />
        <result property="responsiblePersonId" column="responsible_person_id" />
        <result property="customerName"      column="customer_name"     />
        <result property="customerSource"    column="customer_source"   />
        <result property="mobile"            column="mobile"            />
        <result property="phone"             column="phone"             />
        <result property="email"             column="email"             />
        <result property="website"           column="website"           />
        <result property="customerIndustry"  column="customer_industry" />
        <result property="customerLevel"     column="customer_level"    />
        <result property="customerAddress"   column="customer_address"  />
        <result property="primaryContact"    column="primary_contact"   />
        <result property="dealStatus"        column="deal_status"       />
        <result property="nextContactTime"   column="next_contact_time" />
        <result property="selectedDate"      column="selected_date"     />
        <result property="remarks"           column="remarks"           />
        <result property="createTime"        column="created_at"        />
        <result property="updateTime"        column="updated_at"        />
        <result property="status"            column="status"            />
        <result property="delFlag"           column="del_flag"          />
        <result property="createBy"          column="create_by"         />
        <result property="updateBy"          column="update_by"         />
    </resultMap>

    <sql id="selectCrmCustomerVo">
        select id, responsible_person_id, customer_name, customer_source, 
               mobile, phone, email, website, customer_industry, 
               customer_level, customer_address, primary_contact, 
               deal_status, next_contact_time, selected_date, remarks,
               created_at, updated_at, status, del_flag, create_by, update_by
        from crm_business_customers
    </sql>

    <select id="selectCrmCustomerList" parameterType="com.ruoyi.common.domain.entity.CrmCustomer" resultMap="CrmCustomerResult">
        SELECT 
            c.*,
            CASE WHEN cf.is_active = 1 THEN 1 ELSE 0 END as is_following
        FROM (
            <include refid="selectCrmCustomerVo"/>
        ) c
        LEFT JOIN crm_customer_followers cf ON c.id = cf.customer_id AND cf.follower_id = #{followerId} AND cf.is_active = 1
        <where>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="customerSource != null and customerSource != ''">
                AND c.customer_source = #{customerSource}
            </if>
            <if test="mobile != null and mobile != ''">
                AND c.mobile like concat('%', #{mobile}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND c.phone like concat('%', #{phone}, '%')
            </if>
            <if test="email != null and email != ''">
                AND c.email like concat('%', #{email}, '%')
            </if>
            <if test="customerIndustry != null and customerIndustry != ''">
                AND c.customer_industry = #{customerIndustry}
            </if>
            <if test="customerLevel != null and customerLevel != ''">
                AND c.customer_level = #{customerLevel}
            </if>
            <if test="dealStatus != null and dealStatus != ''">
                AND c.deal_status = #{dealStatus}
            </if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">
                AND c.responsible_person_id = #{responsiblePersonId}
            </if>
            <!-- 下属负责的客户筛选 -->
            <if test="subordinateUserIds != null and subordinateUserIds.size() > 0">
                AND c.responsible_person_id IN
                <foreach collection="subordinateUserIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <!-- 我关注的客户筛选 -->
            <if test="followerId != null and isFollowing == true">
                AND cf.customer_id IS NOT NULL
            </if>
            AND c.del_flag = '0'
        </where>
        ORDER BY c.updated_at DESC
    </select>

    <select id="selectCrmCustomerById" parameterType="Long" resultMap="CrmCustomerResult">
        <include refid="selectCrmCustomerVo"/>
        where id = #{id} AND del_flag = '0'
    </select>

    <insert id="insertCrmCustomer" parameterType="com.ruoyi.common.domain.entity.CrmCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_customers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="responsiblePersonId != null">responsible_person_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="customerSource != null">customer_source,</if>
            <if test="mobile != null">mobile,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="website != null">website,</if>
            <if test="customerIndustry != null">customer_industry,</if>
            <if test="customerLevel != null">customer_level,</if>
            <if test="customerAddress != null">customer_address,</if>
            <if test="primaryContact != null">primary_contact,</if>
            <if test="dealStatus != null">deal_status,</if>
            <if test="nextContactTime != null">next_contact_time,</if>
            <if test="selectedDate != null">selected_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createTime != null">created_at,</if>
            <if test="updateTime != null">updated_at,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="responsiblePersonId != null">#{responsiblePersonId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="customerSource != null">#{customerSource},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="website != null">#{website},</if>
            <if test="customerIndustry != null">#{customerIndustry},</if>
            <if test="customerLevel != null">#{customerLevel},</if>
            <if test="customerAddress != null">#{customerAddress},</if>
            <if test="primaryContact != null">#{primaryContact},</if>
            <if test="dealStatus != null">#{dealStatus},</if>
            <if test="nextContactTime != null">#{nextContactTime},</if>
            <if test="selectedDate != null">#{selectedDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateCrmCustomer" parameterType="com.ruoyi.common.domain.entity.CrmCustomer">
        update crm_business_customers
        <trim prefix="SET" suffixOverrides=",">
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerSource != null">customer_source = #{customerSource},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="website != null">website = #{website},</if>
            <if test="customerIndustry != null">customer_industry = #{customerIndustry},</if>
            <if test="customerLevel != null">customer_level = #{customerLevel},</if>
            <if test="customerAddress != null">customer_address = #{customerAddress},</if>
            <if test="primaryContact != null">primary_contact = #{primaryContact},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="nextContactTime != null">next_contact_time = #{nextContactTime},</if>
            <if test="selectedDate != null">selected_date = #{selectedDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="updateTime != null">updated_at = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id} AND del_flag = '0'
    </update>

    <update id="deleteCrmCustomerById" parameterType="Long">
        update crm_business_customers set del_flag = '2' where id = #{id}
    </update>

    <update id="deleteCrmCustomerByIds" parameterType="Long">
        update crm_business_customers set del_flag = '2' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>