<template>
  <div class="error-page">
    <div class="error-content">
      <h1>401</h1>
      <h2>抱歉，您没有访问权限</h2>
      <p>请确认您是否有权限访问此页面，或联系系统管理员</p>
      <el-button type="primary" @click="goBack">返回上一页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 72px;
  color: #409eff;
  margin: 0 0 20px;
}

h2 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 15px;
  font-weight: 500;
}

p {
  color: #606266;
  margin: 0 0 25px;
}
</style> 