<template>
    <div class="payment-operations-tab">
        <div class="operations-header">
            <h3>操作记录</h3>
            <div class="filter-controls">
                <el-select v-model="filterType" placeholder="操作类型" size="small" style="width: 120px;">
                    <el-option label="全部" value="" />
                    <el-option label="创建" value="create" />
                    <el-option label="修改" value="update" />
                    <el-option label="删除" value="delete" />
                    <el-option label="审核" value="audit" />
                    <el-option label="导出" value="export" />
                </el-select>
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 240px; margin-left: 10px;"
                />
            </div>
        </div>

        <el-table :data="filteredOperations" style="width: 100%" border>
            <el-table-column prop="operation" label="操作类型" width="120">
                <template #default="scope">
                    <el-tag :type="getOperationType(scope.row.operation)">{{ scope.row.operation }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="operator" label="操作人" width="120" />
            <el-table-column prop="operationTime" label="操作时间" width="180" />
            <el-table-column prop="description" label="操作描述" min-width="200" />
            <el-table-column prop="details" label="详细信息" min-width="250">
                <template #default="scope">
                    <div class="operation-details">
                        <div v-for="(detail, index) in scope.row.details" :key="index" class="detail-item">
                            <span class="detail-label">{{ detail.label }}:</span>
                            <span class="detail-value">{{ detail.value }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="ipAddress" label="IP地址" width="140" />
            <el-table-column label="操作" width="100">
                <template #default="scope">
                    <el-button link type="primary" size="small" @click="viewDetails(scope.row)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 详情对话框 -->
        <el-dialog v-model="detailVisible" title="操作详情" width="600px">
            <div v-if="selectedOperation" class="operation-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="操作类型">
                        <el-tag :type="getOperationType(selectedOperation.operation)">{{ selectedOperation.operation }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作人">{{ selectedOperation.operator }}</el-descriptions-item>
                    <el-descriptions-item label="操作时间">{{ selectedOperation.operationTime }}</el-descriptions-item>
                    <el-descriptions-item label="IP地址">{{ selectedOperation.ipAddress }}</el-descriptions-item>
                    <el-descriptions-item label="操作描述" :span="2">{{ selectedOperation.description }}</el-descriptions-item>
                </el-descriptions>
                
                <div class="detail-changes" v-if="selectedOperation.changes">
                    <h4>变更详情</h4>
                    <el-table :data="selectedOperation.changes" border size="small">
                        <el-table-column prop="field" label="字段" width="120" />
                        <el-table-column prop="oldValue" label="原值" />
                        <el-table-column prop="newValue" label="新值" />
                    </el-table>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Props {
    entityData: any;
}

defineProps<Props>();

const filterType = ref('');
const dateRange = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const detailVisible = ref(false);
const selectedOperation = ref<any>(null);

// 模拟操作记录数据
const operations = ref([
    {
        id: 1,
        operation: '创建',
        operator: '张三',
        operationTime: '2024-02-21 10:30:15',
        description: '创建回款记录',
        details: [
            { label: '回款编号', value: 'PAY20240221001' },
            { label: '回款金额', value: '¥50,000.00' },
            { label: '客户名称', value: '客户A' }
        ],
        ipAddress: '*************',
        changes: null
    },
    {
        id: 2,
        operation: '修改',
        operator: '李四',
        operationTime: '2024-02-21 14:20:30',
        description: '修改回款状态',
        details: [
            { label: '修改字段', value: '回款状态' },
            { label: '原值', value: '待收款' },
            { label: '新值', value: '已收款' }
        ],
        ipAddress: '*************',
        changes: [
            { field: '回款状态', oldValue: '待收款', newValue: '已收款' },
            { field: '修改时间', oldValue: '-', newValue: '2024-02-21 14:20:30' }
        ]
    },
    {
        id: 3,
        operation: '审核',
        operator: '王五',
        operationTime: '2024-02-21 16:45:20',
        description: '审核通过回款记录',
        details: [
            { label: '审核结果', value: '通过' },
            { label: '审核意见', value: '回款信息完整，审核通过' }
        ],
        ipAddress: '*************',
        changes: [
            { field: '审核状态', oldValue: '待审核', newValue: '已审核' },
            { field: '审核人', oldValue: '-', newValue: '王五' }
        ]
    },
    {
        id: 4,
        operation: '导出',
        operator: '赵六',
        operationTime: '2024-02-22 09:15:45',
        description: '导出回款记录',
        details: [
            { label: '导出格式', value: 'Excel' },
            { label: '导出范围', value: '当前记录' }
        ],
        ipAddress: '*************',
        changes: null
    }
]);

// 过滤后的操作记录
const filteredOperations = computed(() => {
    let filtered = operations.value;
    
    if (filterType.value) {
        filtered = filtered.filter(op => op.operation === filterType.value);
    }
    
    // 这里可以添加日期范围过滤逻辑
    
    total.value = filtered.length;
    return filtered;
});

// 获取操作类型标签样式
const getOperationType = (operation: string) => {
    switch (operation) {
        case '创建':
            return 'success';
        case '修改':
            return 'warning';
        case '删除':
            return 'danger';
        case '审核':
            return 'primary';
        case '导出':
            return 'info';
        default:
            return '';
    }
};

// 查看详情
const viewDetails = (operation: any) => {
    selectedOperation.value = operation;
    detailVisible.value = true;
};

// 分页处理
const handleSizeChange = (val: number) => {
    pageSize.value = val;
    // 重新加载数据
};

const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    // 重新加载数据
};
</script>

<style scoped>
.payment-operations-tab {
    padding: 20px;
}

.operations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.operations-header h3 {
    margin: 0;
    color: #303133;
}

.filter-controls {
    display: flex;
    align-items: center;
}

.operation-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item {
    display: flex;
    align-items: center;
    font-size: 12px;
}

.detail-label {
    color: #909399;
    margin-right: 4px;
    min-width: 60px;
}

.detail-value {
    color: #303133;
    font-weight: 500;
}

.pagination-wrapper {
    margin-top: 20px;
    text-align: right;
}

.operation-detail {
    padding: 10px 0;
}

.detail-changes {
    margin-top: 20px;
}

.detail-changes h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
}
</style>