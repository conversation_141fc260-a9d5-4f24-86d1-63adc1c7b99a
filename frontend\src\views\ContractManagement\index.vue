<!-- 合同管理主页面 -->
<template>
    <el-container class="contract-management">
        <!-- 侧边导航栏 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>合同管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openContractDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建合同
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 筛选与搜索区域 -->
                <el-row :gutter="20" class="filters">
                    <el-col :span="8" style="display: contents;">
                        <el-input v-model="searchInput" style="width: 240px" placeholder="合同编号/客户名称">
                            <template #prefix>
                                <el-icon class="el-input__icon">
                                    <search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-text class="mx-1" style="margin-left: 10px;">显示：</el-text>
                        <el-radio-group v-model="filterType" size="default">
                            <el-radio-button value="all">全部合同</el-radio-button>
                            <el-radio-button value="mine">我负责的</el-radio-button>
                            <el-radio-button value="subordinate">下属负责的</el-radio-button>
                            <el-radio-button value="following">我关注的合同</el-radio-button>
                        </el-radio-group>
                    </el-col>
                </el-row>

                <!-- 合同数据表格 -->
                <el-table ref="contractTable" :data="contractList" border sortable tooltip-effect="dark"
                    :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                    style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                    @selection-change="handleSelectionChange">
                    <template v-for="col in tableColumns" :key="col.prop">
                        <el-table-column v-bind="col">
                            <template #default="scope" v-if="col.prop === 'contract_number'">
                                <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">{{
                                    scope.row.contract_number }}</el-button>
                            </template>
                        </el-table-column>
                    </template>
                    <table-operations :config="tableOperations" @operation="handleTableOperation" />
                </el-table>
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="handlePagination" />
            </el-main>
        </el-container>

        <!-- 合同详情抽屉组件 -->
        <!-- 注意：entity-type 仅支持lead/customer/contact等，合同建议后续扩展类型。此处临时用customer消除类型报错 -->
        <common-drawer v-model="drawerVisible" entity-type="customer" :entity-data="(currentContractData as any)" :drawer-config="drawerConfig" model-name="合同"
            @update:entity-data="handleContractUpdate" />

        <!-- 新建合同对话框 -->
        <el-dialog v-model="contractDialogVisible" width="900px" draggable>
            <el-row>
                <h1 style="margin-left: 30px;">新建合同</h1>
            </el-row>
            <el-row style="margin: 10; position: contents; display: flex; justify-content: left; align-items: center;">
                <div style="border-radius: 30px; background: blue; width: 4px; height: 1.2em; left: 20px; margin-bottom: 0px; margin-left: 30px"></div>
                <h4 style="margin-left: 10px;">请填写合同信息</h4>
            </el-row>
            <el-form :model="newContract"
                :label-position="newContractFormConfig.layout.labelPosition"
                :size="newContractFormConfig.layout.size" style="padding: 0 30px 30px;">
                <el-row :gutter="20">
                    <el-col v-for="field in newContractFormConfig.fields" :key="field.field" :span="field.colSpan">
                        <el-form-item :label="field.label">
                            <component :is="field.component" v-model="newContract[field.field]"
                                v-bind="('props' in field && field.props) ? field.props : {}" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelContract">取消</el-button>
                <el-button type="primary" @click="handleContractSubmit">保存</el-button>
            </span>
        </el-dialog>
    </el-container>
</template>

<script lang="ts">
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import SideNav from '@/components/SideNav/index.vue';
import TableOperations from '@/components/TableOperations/index.vue';
import { Plus, Search } from '@element-plus/icons-vue';
import { defineComponent, onMounted, reactive, ref, watch } from 'vue';
import { addContract, deleteContracts, listContracts, updateContract } from './api';
import { drawerConfig, navConfig, newContractFormConfig, tableColumns, tableOperations } from './config';
import type { ContractData, FilterType, QueryParams } from './types';

export default defineComponent({
    name: 'ContractManagement',
    components: {
        CommonDrawer,
        TableOperations,
        SideNav,
        Search
    },
    setup() {
        // ++ 变量区域 ++
        const activeTab = ref('contracts');
        const filterType = ref<FilterType>('all');
        const searchInput = ref('');
        const total = ref(0);
        const queryParams = reactive({
            pageNum: 1,
            pageSize: 10,
            filterType: 'all' as FilterType,
            searchInput: ''
        } as QueryParams);
        const contractList = ref<ContractData[]>([]);
        const contractDialogVisible = ref(false);
        const newContract = ref<ContractData>({} as ContractData);
        const drawerVisible = ref(false);
        const currentContractData = ref<ContractData>({} as ContractData);
        // 配置项
        // tableColumns, newContractFormConfig, drawerConfig, tableOperations, navConfig

        // ++ 生命周期与监听 ++
        onMounted(() => {
            getList();
        });
        watch(filterType, (newType: FilterType) => {
            queryParams.filterType = newType;
            getList();
        });
        watch(searchInput, () => {
            queryParams.searchInput = searchInput.value;
            getList();
        });

        // ++ 方法定义 ++
        /**
         * 获取合同列表
         */
        const getList = async () => {
            try {
                const response = await listContracts(queryParams);
                // 处理不同的响应数据结构
                if (response.data) {
                    if (response.data.list) {
                        contractList.value = response.data.list;
                        total.value = response.data.total || 0;
                    } else if (Array.isArray(response.data)) {
                        contractList.value = response.data;
                        total.value = response.data.length;
                    } else {
                        contractList.value = [];
                        total.value = 0;
                    }
                } else {
                    contractList.value = [];
                    total.value = 0;
                }
            } catch (error) {
                console.error('获取合同列表失败:', error);
                contractList.value = [];
                total.value = 0;
            }
        };

        /**
         * 处理表格选择变化
         */
        const handleSelectionChange = (val: ContractData[]): void => {
            // 可扩展多选逻辑
            console.log('选中的合同:', val);
        };

        /**
         * 打开新建合同对话框
         */
        const openContractDialog = (): void => {
            contractDialogVisible.value = true;
        };

        /**
         * 取消新建合同
         */
        const cancelContract = (): void => {
            contractDialogVisible.value = false;
            newContract.value = {} as ContractData;
        };

        /**
         * 保存新建的合同
         */
        const handleContractSubmit = async (): Promise<void> => {
            try {
                await addContract(newContract.value);
                contractDialogVisible.value = false;
                newContract.value = {} as ContractData;
                getList();
            } catch (error) {
                console.error('创建合同失败:', error);
            }
        };

        /**
         * 打开合同详情抽屉
         */
        const openDrawer = (row: ContractData): void => {
            currentContractData.value = row;
            drawerVisible.value = true;
        };

        /**
         * 处理合同数据更新
         */
        const handleContractUpdate = async (newData: ContractData): Promise<void> => {
            try {
                await updateContract(newData);
                getList();
            } catch (error) {
                console.error('更新合同失败:', error);
            }
        };

        /**
         * 处理表格操作事件
         */
        const handleTableOperation = async ({ handler, row }: any): Promise<void> => {
            if (handler === 'handleDelete') {
                try {
                    await deleteContracts([row.id]);
                    getList();
                } catch (error) {
                    console.error('删除合同失败:', error);
                }
                return;
            }
            if (typeof handler === 'function') {
                handler(row);
                return;
            }
            if ((handleTableOperation as any)[handler]) {
                (handleTableOperation as any)[handler](row);
                return;
            }
            console.warn(`Handler ${handler} not found`);
        };

        /**
         * 处理分页变化
         */
        const handlePagination = (val: { page: number; limit: number }) => {
            queryParams.pageNum = val.page;
            queryParams.pageSize = val.limit;
            getList();
        };

        return {
            activeTab,
            filterType,
            searchInput,
            total,
            queryParams,
            contractList,
            contractDialogVisible,
            newContract,
            drawerVisible,
            currentContractData,
            tableColumns,
            newContractFormConfig,
            drawerConfig,
            tableOperations,
            navConfig,
            getList,
            handleSelectionChange,
            openContractDialog,
            cancelContract,
            handleContractSubmit,
            openDrawer,
            handleContractUpdate,
            handleTableOperation,
            handlePagination
        };
    }
});
</script>

<style scoped>
.contract-management {
    height: 100vh;
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}
.filters {
    margin: 20px 0;
}
.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.link-button {
    padding: 0;
}
</style>
