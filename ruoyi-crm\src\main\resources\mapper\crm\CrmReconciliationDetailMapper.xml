<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmReconciliationDetailMapper">

    <resultMap type="com.ruoyi.common.domain.entity.CrmReconciliationDetail" id="CrmReconciliationDetailResult">
        <id     property="detailId"        column="detail_id"        />
        <result property="reconciliationId"  column="reconciliation_id"  />
        <result property="orderId"           column="order_id"           />
        <result property="orderNo"           column="order_no"           />
        <result property="amount"            column="amount"             />
        <result property="detailType"        column="detail_type"        />
        <result property="remark"            column="remark"             />
    </resultMap>

    <sql id="selectCrmReconciliationDetailVo">
        select detail_id, reconciliation_id, order_id, order_no, amount, detail_type, remark from crm_reconciliation_detail
    </sql>

    <select id="selectCrmReconciliationDetailListByReconciliationId" parameterType="Long" resultMap="CrmReconciliationDetailResult">
        <include refid="selectCrmReconciliationDetailVo"/>
        where reconciliation_id = #{reconciliationId}
    </select>

    <insert id="batchInsertCrmReconciliationDetail">
        insert into crm_reconciliation_detail (reconciliation_id, order_id, order_no, amount, detail_type, remark) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.reconciliationId}, #{item.orderId}, #{item.orderNo}, #{item.amount}, #{item.detailType}, #{item.remark})
        </foreach>
    </insert>

    <delete id="deleteCrmReconciliationDetailByReconciliationId" parameterType="Long">
        delete from crm_reconciliation_detail where reconciliation_id = #{reconciliationId}
    </delete>

</mapper>

