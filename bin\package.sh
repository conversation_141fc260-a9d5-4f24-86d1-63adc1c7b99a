#!/bin/bash
echo ""
echo "[信息] 打包Web工程，生成war/jar包文件。"
echo ""

# 获取脚本所在的目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

# 切换到项目根目录
cd "$SCRIPT_DIR/.."

# 执行Maven打包命令
echo "正在执行 Maven 打包..."
mvn clean package -Dmaven.test.skip=true

# 检查打包是否成功
if [ $? -ne 0 ]; then
    echo "[错误] Maven 打包失败。"
    exit 1
fi

echo "打包成功。"
echo ""
echo "[信息] 启动应用..."

# 切换到 admin 模块的 target 目录
cd "$SCRIPT_DIR/../ruoyi-admin/target"

# 设置Java虚拟机参数
export JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"

# 运行jar包
java $JAVA_OPTS -jar ruoyi-admin.jar
