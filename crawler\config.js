export default {
  db: {
    host: 'localhost',
    port: 3306,
    user: 'mycrm41',
    password: 'mycrm41',
    database: 'crm41',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    connectTimeout: 10000,
    acquireTimeout: 10000
  },
  crawler: {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Referer': 'https://www.example.com/'
    },
    retryTimes: 3,
    timeout: 10000,
    delay: {
      min: 1000,
      max: 3000
    },
    proxy: {
      enabled: false,
      list: []
    }
  }
};
