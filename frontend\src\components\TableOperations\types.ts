import type { ButtonType } from 'element-plus';

// 按钮类型定义
export interface TableButton {
    label: string;
    type?: ButtonType;
    link?: boolean;
    handler: string | ((row: any) => void);
    icon?: string;
    show?: boolean | ((row: any) => boolean);
    disabled?: boolean | ((row: any) => boolean);
    auth?: string; // 权限标识
}

// 操作栏配置类型定义
export interface TableOperationsConfig {
    width?: number;
    fixed?: boolean | 'right' | 'left';
    label?: string;
    buttons: TableButton[];
}

// 操作事件参数类型定义
export interface OperationEvent {
    handler: string | ((row: any) => void);
    row: any;
}

// 表格行数据类型定义（示例，可根据实际需求扩展）
export interface TableRowData {
    id: number | string;
    [key: string]: any;
} 