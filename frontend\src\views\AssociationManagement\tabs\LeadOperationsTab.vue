<template>
  <div class="lead-operations-tab">
    <el-table 
      :data="operations" 
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
      class="custom-table"
    >
      <el-table-column prop="createTime" label="操作时间" min-width="180">
        <template #default="{ row }">
          <span class="time-text">{{ row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operationType" label="操作类型" width="120">
        <template #default="{ row }">
          <div class="type-tag-wrapper">
            <el-tag 
              :type="getTagType(row.operationType)" 
              effect="light"
              class="custom-tag"
            >
              <el-icon class="tag-icon">
                <component :is="getTypeIcon(row.operationType)" />
              </el-icon>
              {{ row.operationType }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="操作人" width="120">
        <template #default="{ row }">
          <div class="operator-info">
            <template v-if="row.operatorName">
              <el-avatar :size="24" class="operator-avatar">{{ row.operatorName.charAt(0) }}</el-avatar>
              <span>{{ row.operatorName }}</span>
            </template>
            <template v-else>
              <span>--</span>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="operationContent" label="操作描述">
        <template #default="{ row }">
          <div class="operation-description">
            <span class="description-text">{{ row.operationContent }}</span>
            <div class="operation-details" v-if="row.details">
              <el-collapse class="custom-collapse">
                <el-collapse-item>
                  <template #title>
                    <span class="details-title">
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                      查看详情
                    </span>
                  </template>
                  <div class="details-content">
                    <pre class="details-value">{{ JSON.stringify(row.operationDetails, null, 2) }}</pre>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { LeadOperationLog, listLeadOperationLog, type LeadOperationLogQuery } from '@/api/crm/leads/operationlog';
import { InfoFilled } from '@element-plus/icons-vue';
import { onMounted, ref, watch } from 'vue';
import { LeadEntity } from '~/types/entity';

defineOptions({
  name: 'LeadOperationsTab'
});


const props = defineProps<{
  entityData: LeadEntity;
}>();

const operations = ref<LeadOperationLog[]>([]);
const queryParams: LeadOperationLogQuery = {
  leadId: undefined
};

const headerCellStyle = {
  background: 'rgba(247, 248, 250, 0.9)',
  color: '#333',
  fontWeight: '600',
  borderBottom: '2px solid #e8e8e8',
  padding: '16px',
  backdropFilter: 'blur(10px)'
};

const cellStyle = {
  padding: '16px'
};

// 获取列表数据
const getList = async (): Promise<void> => {
  try {
    console.log('获取线索操作日志，参数：', queryParams);
    const response = await listLeadOperationLog(queryParams);
    if (response.code === 200 && response.rows) {
      operations.value = response.rows;
    }
    console.log('获取线索操作日志成功:', JSON.stringify(operations.value, null, 2));
  } catch (error) {
    console.error('获取线索操作日志失败:', error);
  }
};

type TagType = 'success' | 'warning' | 'info' | 'primary' | 'danger';

const getTagType = (type: string): TagType | undefined => {
  const typeMap: Record<string, TagType> = {
    '创建': 'success',
    '修改': 'warning',
    '分配': 'info',
    '删除': 'danger'
  };
  return typeMap[type];
};

const getTypeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    '创建': 'Edit',
    '修改': 'Edit',
    '分配': 'Share',
    '删除': 'Delete'
  };
  return iconMap[type];
};

onMounted(() => {
  queryParams.leadId = props.entityData.id;
  getList();
});

// 新增：监听 entityData.id 变化
watch(
    () => props.entityData.id,
    (newId, oldId) => {
        if (newId && newId !== oldId) {
            queryParams.leadId = newId;
            getList();
        }
    }
);

</script>

<style scoped>
.lead-operations-tab {
  padding: 20px;
}

.custom-table {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);

  :deep(.el-table__header) {
    border-radius: 12px 12px 0 0;
    overflow: hidden;
  }
}

.type-tag-wrapper {
  display: flex;
  align-items: center;
}

.custom-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.tag-icon {
  font-size: 14px;
}

.operator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.operator-avatar {
  background: var(--el-color-primary);
  color: white;
  font-size: 14px;
}

.time-text {
  color: #666;
}

.operation-description {
  font-size: 14px;
}

.description-text {
  color: #333;
}

.custom-collapse {
  margin-top: 12px;
  border: none;
  background: transparent;

  :deep(.el-collapse-item__header) {
    border: none;
    background: transparent;
    height: 32px;
  }

  :deep(.el-collapse-item__content) {
    padding: 16px;
    background: #f7f8fa;
    border-radius: 8px;
  }
}

.details-title {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-color-primary);
  font-size: 13px;

  .el-icon {
    font-size: 14px;
  }
}

.details-content {
  font-size: 13px;
}

.details-item {
  display: flex;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.details-label {
  color: #666;
  margin-right: 8px;
  min-width: 60px;
}

.details-value {
  color: #333;
}

:deep(.el-table) {
  --el-table-border-color: rgba(0, 0, 0, 0.05);
  --el-table-header-bg-color: #f7f8fa;
  --el-table-row-hover-bg-color: #f5f7fa;
}

:deep(.el-table::before),
:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__body) tr:last-child td {
  border-bottom: none;
}
</style>
