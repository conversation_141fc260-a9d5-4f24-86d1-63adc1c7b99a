import type { FormConfig } from '@/types';

// 线索来源选项
const sourceOptions = [
  { label: '电话营销', value: 'phone' },
  { label: '邮件营销', value: 'email' },
  { label: '网络推广', value: 'web' },
  { label: '客户介绍', value: 'referral' },
  { label: '展会获取', value: 'exhibition' },
  { label: '其他', value: 'other' }
];

// 行业选项
const industryOptions = [
  { label: '互联网', value: 'internet' },
  { label: '金融', value: 'finance' },
  { label: '教育', value: 'education' },
  { label: '医疗', value: 'medical' },
  { label: '制造业', value: 'manufacturing' },
  { label: '其他', value: 'other' }
];

// 新建线索表单配置
export const newLeadFormConfig: FormConfig = {
  layout: {
    labelPosition: 'right',
    labelWidth: '100px',
    size: 'default',
    gutter: 20
  },
  fields: [
    {
      field: 'name',
      label: '线索名称',
      type: 'input',
      colSpan: 12,
      required: true,
      clearable: true,
      maxLength: 50,
      showWordLimit: true,
      placeholder: '请输入线索名称',
      prefixIcon: 'User',
      rules: [
        { required: true, message: '请输入线索名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ]
    },
    {
      field: 'responsiblePersonId',
      label: '负责人',
      type: 'select',
      colSpan: 12,
      required: true,
      clearable: true,
      filterable: true,
      placeholder: '请选择负责人',
      options: [], // 这个选项列表需要从后端获取
      rules: [
        { required: true, message: '请选择负责人', trigger: 'change' }
      ]
    },
    {
      field: 'source',
      label: '线索来源',
      type: 'select',
      colSpan: 12,
      required: true,
      clearable: true,
      filterable: true,
      placeholder: '请选择线索来源',
      options: sourceOptions,
      rules: [
        { required: true, message: '请选择线索来源', trigger: 'change' }
      ]
    },
    {
      field: 'phone',
      label: '联系电话',
      type: 'input',
      colSpan: 12,
      required: true,
      clearable: true,
      placeholder: '请输入联系电话',
      prefixIcon: 'Phone',
      rules: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    },
    {
      field: 'email',
      label: '电子邮箱',
      type: 'input',
      colSpan: 12,
      clearable: true,
      placeholder: '请输入电子邮箱',
      prefixIcon: 'Message',
      rules: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    {
      field: 'industry',
      label: '所属行业',
      type: 'select',
      colSpan: 12,
      clearable: true,
      filterable: true,
      placeholder: '请选择所属行业',
      options: industryOptions
    },
    {
      field: 'nextContactTime',
      label: '下次联系时间',
      type: 'date',
      colSpan: 12,
      clearable: true,
      placeholder: '请选择下次联系时间',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm',
      props: {
        type: 'datetime',
        disabledDate: (time: Date) => {
          return time.getTime() < Date.now() - 8.64e7;
        }
      }
    },
    {
      field: 'remarks',
      label: '备注',
      type: 'textarea',
      colSpan: 24,
      clearable: true,
      placeholder: '请输入备注信息',
      maxLength: 500,
      showWordLimit: true,
      rows: 4
    }
  ]
}; 