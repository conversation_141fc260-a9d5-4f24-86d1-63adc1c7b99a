import { crawlProductList, crawlProductDetail } from './crawler.js';

async function debugCrawler() {
  try {
    const listUrl = 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/15.html';
    // 添加延迟函数
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
    
    const products = await crawlProductList(listUrl);
    
    if (products.length > 0) {
      console.log('测试第一个产品详情页');
      console.log('产品URL:', products[0].link);
      
      // 添加延迟，等待页面加载
      await delay(2000);
      
      const detail = await crawlProductDetail(products[0].link, listUrl);
      console.log('\n最终抓取结果：');
      console.log(JSON.stringify(detail, null, 2));
    }
  } catch (error) {
    console.error('调试时发生错误:', error);
  }
}

debugCrawler();