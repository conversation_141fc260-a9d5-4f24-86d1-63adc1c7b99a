import type { CommonFilterConfig } from '@/types';

// 筛选类型选项
const filterOptions = [
    { label: '全部联系人', value: 'all' },
    { label: '我负责的', value: 'mine' },
    { label: '下属负责的', value: 'subordinate' },
    { label: '我关注的联系人', value: 'following' }
];

// 联系人筛选配置
export const contactFilterConfig: CommonFilterConfig = {
    search: {
        placeholder: '联系人姓名/手机/电话',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '显示：',
        options: filterOptions,
        buttonStyle: true,
        size: 'default'
    }
}; 