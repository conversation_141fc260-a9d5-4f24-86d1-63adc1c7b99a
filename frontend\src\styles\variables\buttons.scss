// 按钮样式变量
:root {
  // 按钮基础样式
  --ep-button-font-weight: var(--ep-font-weight-primary);
  --ep-button-border-radius: 4px;
  --ep-button-padding-vertical: 8px;
  --ep-button-padding-horizontal: 16px;

  // 默认状态
  --ep-button-text-color: var(--ep-color-primary);
  --ep-button-bg-color: var(--ep-color-primary-light-9);
  --ep-button-border-color: var(--ep-color-primary-light-5);

  // 悬停状态
  --ep-button-hover-text-color: var(--ep-color-white);
  --ep-button-hover-bg-color: var(--ep-color-primary);
  --ep-button-hover-border-color: var(--ep-color-primary);

  // 激活状态
  --ep-button-active-text-color: var(--ep-color-white);
  --ep-button-active-bg-color: var(--ep-color-primary-dark);
  --ep-button-active-border-color: var(--ep-color-primary-dark);

  // 聚焦状态
  --ep-button-focus-text-color: var(--ep-color-white);
  --ep-button-focus-bg-color: var(--ep-color-primary);
  --ep-button-focus-border-color: var(--ep-color-primary);

  // 禁用状态
  --ep-button-disabled-text-color: #9e9e9e;
  --ep-button-disabled-bg-color: #e0e0e0;
  --ep-button-disabled-border-color: #bdbdbd;
} 