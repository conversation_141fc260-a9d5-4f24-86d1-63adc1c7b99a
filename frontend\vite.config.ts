// vite.config.js
import vue from '@vitejs/plugin-vue';
import path from 'path';
import {
  presetAttributify,
  presetIcons,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss';
import Unocss from 'unocss/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv } from 'vite';
import svgLoader from 'vite-svg-loader';

const pathSrc = path.resolve(__dirname, 'src');

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  return {
    define: {
      'process.env': {}
    },
    server: {
      port: 5173, // 设置自定义端口号，默认是5173
      host: true, // 允许所有网络请求，包括localhost和127.0.0.1
      proxy: {

        [`${env.VITE_APP_API_PREFIX}`]: {
          target: 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_API_PREFIX}`), ''),
        },
      }
    },
    resolve: {
      alias: {
        '~/': `${pathSrc}/`,
        '@/': `${pathSrc}/`,
        '@': path.resolve(__dirname, 'src')
      },
      dedupe: ['vue'], // 避免加载重复模块
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "~/styles/element/index.scss" as *;`,
          quietDeps: true,
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
    publicDir: 'public',
    plugins: [
      vue(),
      svgLoader({
        defaultImport: 'url', // 修改为url方式导入
        svgo: false, // 禁用svgo以排除可能的格式化问题
        svgoConfig: undefined // 移除可能冲突的配置
      }),
      Components({
        extensions: ['vue', 'md'],
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
        dts: 'src/components.d.ts',
      }),
      Unocss({
        presets: [
          presetUno(),
          presetAttributify(),
          presetIcons({
            scale: 1.2,
            warn: true,
          }),
        ],
        transformers: [
          transformerDirectives(),
          transformerVariantGroup(),
        ]
      }),
    ],
    optimizeDeps: {
      include: ['vue'], // 预构建依赖
    },
    build: {
      assetsInlineLimit: 0, // 禁用小资源内联，确保所有资源文件单独生成
      cssCodeSplit: true,
      sourcemap: false,
    }
  };
});