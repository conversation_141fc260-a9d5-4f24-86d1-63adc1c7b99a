import type { ButtonType } from 'element-plus';

export interface EntityData {
    [key: string]: any;
    id?: number;
    name?: string;
    owner?: number;
    attachmentsCount?: number;
    createTime?: string;
    updateTime?: string;
    status?: string;
    type?: string;
    description?: string;
    tags?: string[];
    priority?: number;
    progress?: number;
    dueDate?: string;
    assignee?: number;
    followers?: number[];
    customFields?: Record<string, any>;
}

export interface HeaderField {
    field: string;
    label: string;
    type?: 'text' | 'select' | 'date' | 'number' | 'tag';
    options?: Array<{ label: string; value: any }>;
    editable?: boolean;
    required?: boolean;
    validation?: (value: any) => boolean | string;
    formatter?: (value: any) => string;
}

export interface MenuItem {
    key: string;
    label: string;
    icon?: string;
    component?: string;
    badge?: number | boolean;
    hidden?: boolean;
    disabled?: boolean;
    order?: number;
    children?: MenuItem[];
}

export interface Action {
    label: string;
    type?: ButtonType;
    icon?: string;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    hidden?: boolean;
    confirm?: {
        title: string;
        message: string;
        type?: 'warning' | 'info' | 'success' | 'error';
    };
    handler?: (data: any) => void | Promise<void>;
}

export interface DrawerConfig {
    menuItems?: MenuItem[];
    headerFields?: HeaderField[];
    actions?: Action[];
    defaultTab?: string;
    width?: string | number;
    direction?: 'rtl' | 'ltr' | 'ttb' | 'btt';
    showClose?: boolean;
    modal?: boolean;
    appendToBody?: boolean;
    lockScroll?: boolean;
    customClass?: string;
    destroyOnClose?: boolean;
}

export interface DrawerState {
    editingField: string;
    editingValue: any;
    isEdited: boolean;
    activeTab: string;
    loading: boolean;
    saving: boolean;
    error: string | null;
}

export interface UserOption {
    label: string;
    value: number;
    avatar?: string;
    email?: string;
    department?: string;
    role?: string;
}

export interface Tag {
    id: number;
    name: string;
    color: string;
    description?: string;
}

export interface Attachment {
    id: number;
    name: string;
    url: string;
    size: number;
    type: string;
    createTime: string;
    creator: number;
}

export interface Comment {
    id: number;
    content: string;
    createTime: string;
    creator: number;
    creatorName: string;
    creatorAvatar?: string;
    replies?: Comment[];
}

export interface Activity {
    id: number;
    type: string;
    content: string;
    createTime: string;
    creator: number;
    creatorName: string;
    creatorAvatar?: string;
    details?: Record<string, any>;
}

export interface ValidationRule {
    required?: boolean;
    message?: string;
    trigger?: 'blur' | 'change';
    validator?: (rule: any, value: any, callback: (error?: Error) => void) => void;
    pattern?: RegExp;
    min?: number;
    max?: number;
    type?: 'string' | 'number' | 'boolean' | 'array' | 'date' | 'url' | 'email';
}

export interface FieldConfig {
    field: string;
    label: string;
    type: string;
    placeholder?: string;
    options?: Array<{ label: string; value: any }>;
    rules?: ValidationRule[];
    disabled?: boolean;
    hidden?: boolean;
    defaultValue?: any;
    props?: Record<string, any>;
    slots?: Record<string, any>;
} 