import type { FormConfig } from '@/types';

// 商机阶段选项
const stageOptions = [
    { label: '初步接触', value: 'initial' },
    { label: '需求确认', value: 'requirement' },
    { label: '方案制定', value: 'solution' },
    { label: '商务谈判', value: 'negotiation' },
    { label: '合同签订', value: 'contract' }
];

// 新建商机表单配置
export const newOpportunityFormConfig: FormConfig = {
    layout: {
        labelPosition: 'right',
        labelWidth: '100px',
        size: 'default',
        gutter: 20
    },
    fields: [
        {
            field: 'name',
            label: '商机名称',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            maxLength: 50,
            showWordLimit: true,
            placeholder: '请输入商机名称',
            prefixIcon: 'Opportunity',
            rules: [
                { required: true, message: '请输入商机名称', trigger: 'blur' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
        },
        {
            field: 'customerId',
            label: '所属客户',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            filterable: true,
            placeholder: '请选择所属客户',
            options: [] // 需要动态获取客户列表
        },
        {
            field: 'stage',
            label: '商机阶段',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请选择商机阶段',
            options: stageOptions
        },
        {
            field: 'amount',
            label: '预计金额',
            type: 'number',
            colSpan: 12,
            required: true,
            controls: true,
            precision: 2,
            min: 0,
            placeholder: '请输入预计金额'
        },
        {
            field: 'probability',
            label: '成交概率',
            type: 'number',
            colSpan: 12,
            required: true,
            controls: true,
            min: 0,
            max: 100,
            placeholder: '请输入成交概率(%)'
        },
        {
            field: 'expectedClosingDate',
            label: '预计成交日期',
            type: 'date',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请选择预计成交日期',
            props: {
                type: 'date',
                valueFormat: 'YYYY-MM-DD'
            }
        },
        {
            field: 'remarks',
            label: '备注',
            type: 'textarea',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入备注信息',
            maxLength: 500,
            showWordLimit: true,
            rows: 4
        }
    ]
}; 