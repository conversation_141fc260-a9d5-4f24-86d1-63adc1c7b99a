<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmBusinessFollowUpRecordMapper">
    
    <resultMap type="CrmBusinessFollowUpRecord" id="CrmBusinessFollowUpRecordResult">
        <result property="id"    column="id"    />
        <result property="leadId"    column="related_customer_id"    />
        <result property="category"    column="follow_up_type"    />
        <result property="contactType"    column="follow_up_method"    />
        <result property="content"    column="follow_up_content"    />
        <result property="duration"    column="duration"    />
        <result property="nextFollowTime"    column="next_contact_method"    />
        <result property="operator"    column="effective_follow_up_person"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="updated_at"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCrmBusinessFollowUpRecordVo">
        select id, related_customer_id, follow_up_type, follow_up_method, follow_up_content, 
               next_contact_method, effective_follow_up_person, created_at, updated_at, 
               module_type
        from crm_business_follow_up_records
    </sql>

    <select id="selectByLeadId" parameterType="Long" resultMap="CrmBusinessFollowUpRecordResult">
        <include refid="selectCrmBusinessFollowUpRecordVo"/>
        where related_customer_id = #{leadId} and module_type = 'lead'
        order by created_at desc
    </select>

    <insert id="insert" parameterType="CrmBusinessFollowUpRecord" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_follow_up_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">related_customer_id,</if>
            <if test="category != null">follow_up_type,</if>
            <if test="contactType != null">follow_up_method,</if>
            <if test="content != null">follow_up_content,</if>
            <if test="nextFollowTime != null">next_contact_method,</if>
            <if test="operator != null">effective_follow_up_person,</if>
            module_type,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="category != null">#{category},</if>
            <if test="contactType != null">#{contactType},</if>
            <if test="content != null">#{content},</if>
            <if test="nextFollowTime != null">#{nextFollowTime},</if>
            <if test="operator != null">#{operator},</if>
            'lead',
         </trim>
    </insert>

    <update id="updateById" parameterType="CrmBusinessFollowUpRecord">
        update crm_business_follow_up_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">related_customer_id = #{leadId},</if>
            <if test="category != null">follow_up_type = #{category},</if>
            <if test="contactType != null">follow_up_method = #{contactType},</if>
            <if test="content != null">follow_up_content = #{content},</if>
            <if test="nextFollowTime != null">next_contact_method = #{nextFollowTime},</if>
            <if test="operator != null">effective_follow_up_person = #{operator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from crm_business_follow_up_records where id = #{id}
    </delete>

</mapper> 