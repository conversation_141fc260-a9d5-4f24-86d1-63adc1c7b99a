# UUTController

基础路径：`/wxcom/user`

## 接口列表

### 创建用户
- **方法**：POST
- **路径**：`/create`
- **参数**：
  - `userData` (请求体)：用户数据
- **功能**：创建新用户

### 获取用户信息
- **方法**：GET
- **路径**：`/info/{userId}`
- **参数**：
  - `userId` (路径参数)：用户ID
- **功能**：获取指定用户的信息

### 获取用户列表
- **方法**：GET
- **路径**：`/list`
- **参数**：
  - `departmentId` (默认值：1)：部门ID
  - `fetchChild` (默认值：true)：是否获取子部门用户
- **功能**：获取用户列表

### 更新用户信息
- **方法**：PUT
- **路径**：`/update`
- **参数**：
  - `userData` (请求体)：用户数据
- **功能**：更新用户信息

### 删除用户
- **方法**：DELETE
- **路径**：`/delete/{userId}`
- **参数**：
  - `userId` (路径参数)：用户ID
- **功能**：删除指定用户
