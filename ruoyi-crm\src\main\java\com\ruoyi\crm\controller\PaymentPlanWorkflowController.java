package com.ruoyi.crm.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.service.IPaymentPlanWorkflowService;

/**
 * 回款计划工作流Controller
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/crm/paymentPlan/workflow")
public class PaymentPlanWorkflowController extends BaseController {
    
    @Autowired
    private IPaymentPlanWorkflowService workflowService;
    
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;
    
    /**
     * 启动审批流程
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "启动审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{planId}")
    public AjaxResult startApproval(@PathVariable("planId") Long planId, 
                                   @RequestBody(required = false) Map<String, Object> variables) {
        CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
        if (plan == null) {
            return error("回款计划不存在");
        }
        
        if (!"草稿".equals(plan.getPlanStatus()) && !"已拒绝".equals(plan.getApprovalStatus())) {
            return error("只有草稿状态或已拒绝的计划才能提交审批");
        }
        
        if (variables == null) {
            variables = new HashMap<>();
        }
        
        try {
            String processInstanceId = workflowService.startApprovalProcess(plan, variables);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "审批流程启动成功");
            result.put("processInstanceId", processInstanceId);
            return success(result);
        } catch (Exception e) {
            return error("启动审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 审批通过
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "审批通过", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestParam("taskId") String taskId,
                             @RequestParam(value = "comment", required = false) String comment) {
        try {
            Long approverId = SecurityUtils.getUserId();
            boolean result = workflowService.approveTask(taskId, approverId, comment);
            
            if (result) {
                return success("审批通过成功");
            } else {
                return error("审批通过失败");
            }
        } catch (Exception e) {
            return error("审批通过失败: " + e.getMessage());
        }
    }
    
    /**
     * 审批拒绝
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "审批拒绝", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult reject(@RequestParam("taskId") String taskId,
                            @RequestParam(value = "comment", required = false) String comment) {
        try {
            Long approverId = SecurityUtils.getUserId();
            boolean result = workflowService.rejectTask(taskId, approverId, comment);
            
            if (result) {
                return success("审批拒绝成功");
            } else {
                return error("审批拒绝失败");
            }
        } catch (Exception e) {
            return error("审批拒绝失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取我的待办任务
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @GetMapping("/pending")
    public TableDataInfo getPendingTasks() {
        Long userId = SecurityUtils.getUserId();
        List<Map<String, Object>> list = workflowService.getPendingTasks(userId);
        return getDataTable(list);
    }
    
    /**
     * 获取流程任务历史
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/history/{processInstanceId}")
    public AjaxResult getTaskHistory(@PathVariable("processInstanceId") String processInstanceId) {
        try {
            List<Map<String, Object>> history = workflowService.getTaskHistory(processInstanceId);
            return success(history);
        } catch (Exception e) {
            return error("获取任务历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前任务
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/current/{processInstanceId}")
    public AjaxResult getCurrentTask(@PathVariable("processInstanceId") String processInstanceId) {
        try {
            Map<String, Object> currentTask = workflowService.getCurrentTask(processInstanceId);
            return success(currentTask);
        } catch (Exception e) {
            return error("获取当前任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 撤销流程
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "撤销审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel")
    public AjaxResult cancelProcess(@RequestParam("processInstanceId") String processInstanceId,
                                   @RequestParam(value = "reason", required = false) String reason) {
        try {
            boolean result = workflowService.cancelProcess(processInstanceId, reason);
            
            if (result) {
                return success("流程撤销成功");
            } else {
                return error("流程撤销失败");
            }
        } catch (Exception e) {
            return error("流程撤销失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据计划ID获取流程信息
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/process/{planId}")
    public AjaxResult getProcessByPlanId(@PathVariable("planId") Long planId) {
        try {
            Map<String, Object> processInfo = workflowService.getProcessInstanceByPlanId(planId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("processInfo", processInfo);
            
            if (processInfo != null) {
                String processInstanceId = (String) processInfo.get("processInstanceId");
                result.put("currentTask", workflowService.getCurrentTask(processInstanceId));
                result.put("taskHistory", workflowService.getTaskHistory(processInstanceId));
                result.put("isInApproval", workflowService.isInApproval(planId));
            } else {
                result.put("isInApproval", false);
            }
            
            return success(result);
        } catch (Exception e) {
            return error("获取流程信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查计划是否在审批中
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/status/{planId}")
    public AjaxResult getApprovalStatus(@PathVariable("planId") Long planId) {
        try {
            boolean isInApproval = workflowService.isInApproval(planId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("isInApproval", isInApproval);
            
            if (isInApproval) {
                Map<String, Object> processInfo = workflowService.getProcessInstanceByPlanId(planId);
                if (processInfo != null) {
                    String processInstanceId = (String) processInfo.get("processInstanceId");
                    result.put("currentTask", workflowService.getCurrentTask(processInstanceId));
                }
            }
            
            return success(result);
        } catch (Exception e) {
            return error("获取审批状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取流程定义列表
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/definitions")
    public AjaxResult getProcessDefinitions() {
        try {
            List<Map<String, Object>> definitions = workflowService.getProcessDefinitions();
            return success(definitions);
        } catch (Exception e) {
            return error("获取流程定义失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成任务 (通用接口)
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "完成审批任务", businessType = BusinessType.UPDATE)
    @PostMapping("/complete")
    public AjaxResult completeTask(@RequestParam("taskId") String taskId,
                                  @RequestBody(required = false) Map<String, Object> variables,
                                  @RequestParam(value = "comment", required = false) String comment) {
        try {
            boolean result = workflowService.completeTask(taskId, variables, comment);
            
            if (result) {
                return success("任务完成成功");
            } else {
                return error("任务完成失败");
            }
        } catch (Exception e) {
            return error("任务完成失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取流程变量
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:query')")
    @GetMapping("/variables/{processInstanceId}")
    public AjaxResult getProcessVariables(@PathVariable("processInstanceId") String processInstanceId) {
        try {
            Map<String, Object> variables = workflowService.getProcessVariables(processInstanceId);
            return success(variables);
        } catch (Exception e) {
            return error("获取流程变量失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置流程变量
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentPlan:approval')")
    @Log(title = "设置流程变量", businessType = BusinessType.UPDATE)
    @PostMapping("/variables/{processInstanceId}")
    public AjaxResult setProcessVariables(@PathVariable("processInstanceId") String processInstanceId,
                                         @RequestBody Map<String, Object> variables) {
        try {
            boolean result = workflowService.setProcessVariables(processInstanceId, variables);
            
            if (result) {
                return success("设置流程变量成功");
            } else {
                return error("设置流程变量失败");
            }
        } catch (Exception e) {
            return error("设置流程变量失败: " + e.getMessage());
        }
    }
}