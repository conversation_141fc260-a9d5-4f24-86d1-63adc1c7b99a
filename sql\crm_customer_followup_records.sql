-- 客户跟进记录表
DROP TABLE IF EXISTS crm_customer_followup_records;
CREATE TABLE crm_customer_followup_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    user_id BIGINT NOT NULL COMMENT '跟进人ID',
    followup_type VARCHAR(20) DEFAULT 'call' COMMENT '跟进方式（call-电话，visit-拜访，email-邮件，wechat-微信，other-其他）',
    followup_content TEXT NOT NULL COMMENT '跟进内容',
    followup_result VARCHAR(50) COMMENT '跟进结果（interested-有兴趣，no_interest-无兴趣，pending-待定，deal-成交）',
    next_followup_time DATETIME COMMENT '下次跟进时间',
    attachment_urls TEXT COMMENT '附件URL列表，JSON格式',
    is_important TINYINT(1) DEFAULT 0 COMMENT '是否重要跟进（0否 1是）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志（0存在 1删除）',
    INDEX idx_customer_id (customer_id) COMMENT '客户ID索引',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_followup_type (followup_type) COMMENT '跟进方式索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引',
    INDEX idx_next_followup (next_followup_time) COMMENT '下次跟进时间索引',
    INDEX idx_is_important (is_important) COMMENT '重要跟进索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户跟进记录表';