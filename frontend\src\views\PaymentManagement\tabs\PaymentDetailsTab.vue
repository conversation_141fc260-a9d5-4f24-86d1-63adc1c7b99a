<template>
    <div class="payment-details-tab">
        <el-form :model="entityData" label-width="120px" class="details-form">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="回款编号">
                        <el-input v-model="entityData.payment_number" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="合同编号">
                        <el-input v-model="entityData.contract_number" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="客户名称">
                        <el-input v-model="entityData.customer_name" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="回款金额">
                        <el-input v-model="entityData.payment_amount" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="回款日期">
                        <el-input v-model="entityData.payment_date" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="回款方式">
                        <el-input v-model="entityData.payment_method" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="回款状态">
                        <el-tag :type="getStatusType(entityData.payment_status)">{{ entityData.payment_status }}</el-tag>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="回款类型">
                        <el-input v-model="entityData.payment_type" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="银行账号">
                        <el-input v-model="entityData.bank_account" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="开户银行">
                        <el-input v-model="entityData.bank_name" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="开票状态">
                        <el-tag :type="getInvoiceStatusType(entityData.invoice_status)">{{ entityData.invoice_status }}</el-tag>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="发票编号">
                        <el-input v-model="entityData.invoice_number" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="发票金额">
                        <el-input v-model="entityData.invoice_amount" readonly />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="备注">
                        <el-input v-model="entityData.remarks" type="textarea" :rows="3" readonly />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script setup lang="ts">
// defineProps is a compiler macro, no import needed

interface Props {
    entityData: any;
}

defineProps<Props>();

// 获取状态标签类型
const getStatusType = (status: string) => {
    switch (status) {
        case '已收款':
            return 'success';
        case '待收款':
            return 'warning';
        case '部分收款':
            return 'info';
        case '已退款':
            return 'danger';
        default:
            return '';
    }
};

// 获取开票状态标签类型
const getInvoiceStatusType = (status: string) => {
    switch (status) {
        case '已开票':
            return 'success';
        case '未开票':
            return 'warning';
        case '部分开票':
            return 'info';
        default:
            return '';
    }
};
</script>

<style scoped>
.payment-details-tab {
    padding: 20px;
}

.details-form {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
}

.el-form-item {
    margin-bottom: 20px;
}
</style>