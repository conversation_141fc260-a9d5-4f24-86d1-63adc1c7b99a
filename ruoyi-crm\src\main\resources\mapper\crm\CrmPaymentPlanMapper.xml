<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPaymentPlanMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmPaymentPlan" id="CrmPaymentPlanResult">
        <result property="id"    column="id"    />
        <result property="planNumber"    column="plan_number"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="responsibleUserId"    column="responsible_user_id"    />
        <result property="responsibleUserName"    column="responsible_user_name"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="receivedAmount"    column="received_amount"    />
        <result property="remainingAmount"    column="remaining_amount"    />
        <result property="opportunityId"    column="opportunity_id"    />
        <result property="contactId"    column="contact_id"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="planStatus"    column="plan_status"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="currency"    column="currency"    />
        <result property="exchangeRate"    column="exchange_rate"    />
        <result property="parentPlanId"    column="parent_plan_id"    />
        <result property="planType"    column="plan_type"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="collectionUserId"    column="collection_user_id"    />
        <result property="remark"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="CrmPaymentPlanWithDetailsResult" type="com.ruoyi.common.domain.entity.CrmPaymentPlan" extends="CrmPaymentPlanResult">
        <collection property="installments" ofType="com.ruoyi.common.domain.entity.CrmPaymentInstallment">
            <result property="id"    column="inst_id"    />
            <result property="planId"    column="inst_plan_id"    />
            <result property="installmentNumber"    column="inst_installment_number"    />
            <result property="installmentName"    column="inst_installment_name"    />
            <result property="installmentAmount"    column="inst_installment_amount"    />
            <result property="installmentPercentage"    column="inst_installment_percentage"    />
            <result property="plannedDate"    column="inst_planned_date"    />
            <result property="actualDate"    column="inst_actual_date"    />
            <result property="actualAmount"    column="inst_actual_amount"    />
            <result property="overdueDays"    column="inst_overdue_days"    />
            <result property="penaltyAmount"    column="inst_penalty_amount"    />
            <result property="paymentVoucher"    column="inst_payment_voucher"    />
            <result property="installmentStatus"    column="inst_installment_status"    />
            <result property="remark"    column="inst_remarks"    />
        </collection>
        <collection property="approvals" ofType="com.ruoyi.common.domain.entity.CrmPaymentApproval">
            <result property="id"    column="appr_id"    />
            <result property="planId"    column="appr_plan_id"    />
            <result property="approvalLevel"    column="appr_approval_level"    />
            <result property="approverId"    column="appr_approver_id"    />
            <result property="approverName"    column="appr_approver_name"    />
            <result property="approvalStatus"    column="appr_approval_status"    />
            <result property="approvalTime"    column="appr_approval_time"    />
            <result property="approvalComments"    column="appr_approval_comments"    />
        </collection>
    </resultMap>

    <sql id="selectCrmPaymentPlanVo">
        select id, plan_number, customer_id, customer_name, responsible_user_id, responsible_user_name, 
               total_amount, received_amount, remaining_amount, opportunity_id, contact_id, contract_number, 
               payment_method, plan_status, approval_status, currency, exchange_rate, parent_plan_id, 
               plan_type, risk_level, collection_user_id, remarks, del_flag, create_by, create_time, 
               update_by, update_time 
        from crm_payment_plan
    </sql>

    <select id="selectPaymentPlanList" parameterType="com.ruoyi.common.domain.entity.CrmPaymentPlan" resultMap="CrmPaymentPlanResult">
        <include refid="selectCrmPaymentPlanVo"/>
        <where>
            del_flag = '0'
            <if test="planNumber != null  and planNumber != ''"> and plan_number = #{planNumber}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="responsibleUserId != null "> and responsible_user_id = #{responsibleUserId}</if>
            <if test="responsibleUserName != null  and responsibleUserName != ''"> and responsible_user_name like concat('%', #{responsibleUserName}, '%')</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="planStatus != null  and planStatus != ''"> and plan_status = #{planStatus}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPaymentPlanById" parameterType="Long" resultMap="CrmPaymentPlanResult">
        <include refid="selectCrmPaymentPlanVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectPaymentPlanWithDetailsById" parameterType="Long" resultMap="CrmPaymentPlanWithDetailsResult">
        select 
            p.id, p.plan_number, p.customer_id, p.customer_name, p.responsible_user_id, p.responsible_user_name, 
            p.total_amount, p.received_amount, p.remaining_amount, p.opportunity_id, p.contact_id, p.contract_number, 
            p.payment_method, p.plan_status, p.approval_status, p.currency, p.exchange_rate, p.parent_plan_id, 
            p.plan_type, p.risk_level, p.collection_user_id, p.remarks, p.del_flag, p.create_by, p.create_time, 
            p.update_by, p.update_time,
            i.id as inst_id, i.plan_id as inst_plan_id, i.installment_number as inst_installment_number, 
            i.installment_name as inst_installment_name, i.installment_amount as inst_installment_amount, 
            i.installment_percentage as inst_installment_percentage, i.planned_date as inst_planned_date, 
            i.actual_date as inst_actual_date, i.actual_amount as inst_actual_amount, i.overdue_days as inst_overdue_days, 
            i.penalty_amount as inst_penalty_amount, i.payment_voucher as inst_payment_voucher, 
            i.installment_status as inst_installment_status, i.remarks as inst_remarks,
            a.id as appr_id, a.plan_id as appr_plan_id, a.approval_level as appr_approval_level, 
            a.approver_id as appr_approver_id, a.approver_name as appr_approver_name, 
            a.approval_status as appr_approval_status, a.approval_time as appr_approval_time, 
            a.approval_comments as appr_approval_comments
        from crm_payment_plan p
        left join crm_payment_installment i on p.id = i.plan_id and i.del_flag = '0'
        left join crm_payment_approval a on p.id = a.plan_id and a.del_flag = '0'
        where p.id = #{id} and p.del_flag = '0'
        order by i.installment_number, a.approval_level
    </select>

    <select id="selectPaymentPlanByContractId" parameterType="Long" resultMap="CrmPaymentPlanResult">
        <include refid="selectCrmPaymentPlanVo"/>
        where contract_number = (select contract_no from crm_contract where id = #{contractId}) 
        and del_flag = '0'
    </select>
        
    <insert id="insertPaymentPlan" parameterType="com.ruoyi.common.domain.entity.CrmPaymentPlan" useGeneratedKeys="true" keyProperty="id">
        insert into crm_payment_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planNumber != null">plan_number,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="responsibleUserId != null">responsible_user_id,</if>
            <if test="responsibleUserName != null">responsible_user_name,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="receivedAmount != null">received_amount,</if>
            <if test="remainingAmount != null">remaining_amount,</if>
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="planStatus != null">plan_status,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="currency != null">currency,</if>
            <if test="exchangeRate != null">exchange_rate,</if>
            <if test="parentPlanId != null">parent_plan_id,</if>
            <if test="planType != null">plan_type,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="collectionUserId != null">collection_user_id,</if>
            <if test="remark != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planNumber != null">#{planNumber},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="responsibleUserId != null">#{responsibleUserId},</if>
            <if test="responsibleUserName != null">#{responsibleUserName},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="receivedAmount != null">#{receivedAmount},</if>
            <if test="remainingAmount != null">#{remainingAmount},</if>
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="planStatus != null">#{planStatus},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="currency != null">#{currency},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="parentPlanId != null">#{parentPlanId},</if>
            <if test="planType != null">#{planType},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="collectionUserId != null">#{collectionUserId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePaymentPlan" parameterType="com.ruoyi.common.domain.entity.CrmPaymentPlan">
        update crm_payment_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planNumber != null">plan_number = #{planNumber},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="responsibleUserId != null">responsible_user_id = #{responsibleUserId},</if>
            <if test="responsibleUserName != null">responsible_user_name = #{responsibleUserName},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="receivedAmount != null">received_amount = #{receivedAmount},</if>
            <if test="remainingAmount != null">remaining_amount = #{remainingAmount},</if>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="planStatus != null">plan_status = #{planStatus},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="parentPlanId != null">parent_plan_id = #{parentPlanId},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="collectionUserId != null">collection_user_id = #{collectionUserId},</if>
            <if test="remark != null">remarks = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updatePaymentPlanStatus">
        update crm_payment_plan
        set plan_status = #{planStatus},
            approval_status = #{approvalStatus},
            update_time = sysdate()
        where id = #{id}
    </update>

    <delete id="deletePaymentPlanById" parameterType="Long">
        update crm_payment_plan set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deletePaymentPlanByIds" parameterType="String">
        update crm_payment_plan set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="generatePlanNumber" parameterType="String" resultType="String">
        select concat(#{prefix}, date_format(now(), '%Y%m'), lpad(ifnull(max(cast(substring(plan_number, -3) as unsigned)), 0) + 1, 3, '0'))
        from crm_payment_plan
        where plan_number like concat(#{prefix}, date_format(now(), '%Y%m'), '%')
    </select>

    <select id="countByCustomerId" parameterType="Long" resultType="int">
        select count(1) from crm_payment_plan
        where customer_id = #{customerId} and del_flag = '0'
    </select>
</mapper>