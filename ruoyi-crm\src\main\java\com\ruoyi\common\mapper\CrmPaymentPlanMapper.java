package com.ruoyi.common.mapper;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import org.apache.ibatis.annotations.Param;

/**
 * 回款计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
public interface CrmPaymentPlanMapper {
    /**
     * 查询回款计划列表
     *
     * @param plan 回款计划信息
     * @return 回款计划集合
     */
    List<CrmPaymentPlan> selectPaymentPlanList(CrmPaymentPlan plan);

    /**
     * 查询回款计划详细信息
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    CrmPaymentPlan selectPaymentPlanById(Long id);

    /**
     * 新增回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    int insertPaymentPlan(CrmPaymentPlan plan);

    /**
     * 修改回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    int updatePaymentPlan(CrmPaymentPlan plan);

    /**
     * 删除回款计划
     *
     * @param id 回款计划ID
     * @return 结果
     */
    int deletePaymentPlanById(Long id);

    /**
     * 批量删除回款计划
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePaymentPlanByIds(Long[] ids);

    /**
     * 根据合同ID查询回款计划列表
     *
     * @param contractId 合同ID
     * @return 回款计划集合
     */
    List<CrmPaymentPlan> selectPaymentPlanByContractId(Long contractId);
    
    /**
     * 生成回款计划编号
     * 
     * @param prefix 前缀
     * @return 编号
     */
    String generatePlanNumber(@Param("prefix") String prefix);

    /**
     * 统计客户的回款计划数
     * 
     * @param customerId 客户ID
     * @return 结果
     */
    int countByCustomerId(@Param("customerId") Long customerId);
    
    /**
     * 查询包含分期和审批信息的回款计划
     * 
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    CrmPaymentPlan selectPaymentPlanWithDetailsById(Long id);
    
    /**
     * 更新回款计划状态
     * 
     * @param id 回款计划ID
     * @param planStatus 计划状态
     * @param approvalStatus 审批状态
     * @return 结果
     */
    int updatePaymentPlanStatus(@Param("id") Long id, 
                               @Param("planStatus") String planStatus, 
                               @Param("approvalStatus") String approvalStatus);
} 