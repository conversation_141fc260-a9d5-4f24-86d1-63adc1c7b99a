# Activiti 工作流引擎学习指南

## 概述
本指南提供了一系列经典的Activiti工作流场景，从简单到复杂，帮助您系统性地学习Activiti工作流引擎的各种特性。

---

## 学习场景列表

### 1. 基础场景：请假审批流程 (`leave.bpmn20.xml`)
**学习要点：**
- 基本的工作流结构：开始事件 → 用户任务 → 结束事件
- 简单的任务分配：`activiti:assignee="admin"`
- 序列流的使用

**适用场景：**
- 工作流入门
- 简单的线性审批流程

---

### 2. 多级审批：报销审批流程 (`expense.bpmn20.xml`)
**学习要点：**
- 串行任务执行
- 变量表达式分配：`activiti:assignee="${supervisor}"`
- 多级审批的实现
- 文档注释的使用

**适用场景：**
- 层级审批流程
- 需要多个角色依次审批的业务

**关键代码：**
```xml
<userTask id="supervisorApproval" name="直接主管审批" 
          activiti:assignee="${supervisor}">
    <documentation>直接主管审批报销申请</documentation>
</userTask>
```

---

### 3. 条件分支：采购审批流程 (`purchase.bpmn20.xml`)
**学习要点：**
- 排他网关（ExclusiveGateway）的使用
- 条件表达式：`${amount >= 10000}`
- 分支和汇聚流程
- 用户组分配：`activiti:candidateGroups="procurement"`

**适用场景：**
- 根据业务条件走不同审批路径
- 金额、优先级等条件判断

**关键代码：**
```xml
<exclusiveGateway id="amountGateway" name="金额判断"></exclusiveGateway>
<sequenceFlow id="flowHighAmount" name="大额采购(>10000)" 
              sourceRef="amountGateway" targetRef="ceoApproval">
    <conditionExpression xsi:type="tFormalExpression">${amount >= 10000}</conditionExpression>
</sequenceFlow>
```

---

### 4. 并行处理：并行审批流程 (`parallel-approval.bpmn20.xml`)
**学习要点：**
- 并行网关（ParallelGateway）的使用
- 并行任务的分叉和合并
- 多部门同时审批
- 等待所有分支完成

**适用场景：**
- 多部门并行审批
- 需要多个角色同时处理的任务

**关键代码：**
```xml
<parallelGateway id="parallelFork" name="并行分叉"></parallelGateway>
<parallelGateway id="parallelJoin" name="并行合并"></parallelGateway>
```

---

### 5. 任务分配：任务分配示例流程 (`task-assignment.bpmn20.xml`)
**学习要点：**
- 直接分配：`activiti:assignee="admin"`
- 变量分配：`activiti:assignee="${assigneeUser}"`
- 候选用户：`activiti:candidateUsers="user1,user2,user3"`
- 候选用户组：`activiti:candidateGroups="managers,supervisors"`
- 任务监听器的使用

**适用场景：**
- 灵活的任务分配策略
- 任务认领机制
- 动态任务分配

**分配方式对比：**
| 分配方式 | 特点 | 使用场景 |
|---------|------|----------|
| assignee | 直接分配给特定用户 | 明确知道处理人 |
| candidateUsers | 分配给候选用户列表 | 多个用户可选择处理 |
| candidateGroups | 分配给用户组 | 部门内任何人可处理 |
| 监听器分配 | 程序动态分配 | 复杂的分配逻辑 |

---

### 6. 高级场景：包容网关流程 (`inclusive-gateway.bpmn20.xml`)
**学习要点：**
- 包容网关（InclusiveGateway）的使用
- 条件性并行执行
- 动态分支选择
- 复杂的业务逻辑处理

**适用场景：**
- 根据条件决定执行哪些并行分支
- 灵活的审批路径组合

**关键代码：**
```xml
<inclusiveGateway id="inclusiveFork" name="包容分叉"></inclusiveGateway>
<sequenceFlow id="flowTechnical" name="需要技术评审" 
              sourceRef="inclusiveFork" targetRef="technicalReview">
    <conditionExpression xsi:type="tFormalExpression">${needTechnicalReview == true}</conditionExpression>
</sequenceFlow>
```

---

## 网关类型对比

| 网关类型 | 分叉行为 | 合并行为 | 使用场景 |
|---------|----------|----------|----------|
| 排他网关 | 只执行一个分支 | 等待一个分支完成 | 条件选择 |
| 并行网关 | 执行所有分支 | 等待所有分支完成 | 并行处理 |
| 包容网关 | 执行满足条件的分支 | 等待执行的分支完成 | 条件性并行 |

---

## 学习建议

### 1. 学习顺序
1. **基础场景** → 理解基本概念
2. **多级审批** → 掌握串行流程
3. **条件分支** → 学习条件判断
4. **并行处理** → 理解并行执行
5. **任务分配** → 掌握分配策略
6. **高级场景** → 学习复杂逻辑

### 2. 实践步骤
1. **部署流程**：将BPMN文件部署到Activiti引擎
2. **启动实例**：创建流程实例并设置变量
3. **任务处理**：查询、认领、完成任务
4. **流程监控**：查看流程状态和历史

### 3. 关键概念
- **流程定义**：BPMN文件中的流程模板
- **流程实例**：流程定义的具体执行
- **任务实例**：用户需要处理的具体任务
- **流程变量**：流程执行过程中的数据

### 4. 常用API
```java
// 部署流程
repositoryService.createDeployment()
    .addClasspathResource("processes/leave.bpmn20.xml")
    .deploy();

// 启动流程
ProcessInstance processInstance = runtimeService
    .startProcessInstanceByKey("leave", variables);

// 查询任务
List<Task> tasks = taskService.createTaskQuery()
    .taskAssignee("admin")
    .list();

// 完成任务
taskService.complete(taskId, variables);
```

---

## 扩展学习

### 1. 事件类型
- 开始事件：定时器、消息、信号
- 中间事件：边界事件、捕获事件
- 结束事件：消息、错误、终止

### 2. 高级特性
- 子流程和调用活动
- 多实例任务
- 补偿和事务
- 业务规则引擎集成

### 3. 监听器和委托
- 执行监听器
- 任务监听器
- Java委托类
- 表达式和脚本

---

## 总结

通过这些经典场景的学习，您将掌握：
1. ✅ Activiti的基本概念和使用方法
2. ✅ 各种网关的使用场景和区别
3. ✅ 任务分配的不同策略
4. ✅ 条件表达式和变量的使用
5. ✅ 复杂业务流程的建模方法

建议按照复杂度递增的顺序学习，每个场景都要亲自实践，这样能更好地理解Activiti工作流引擎的强大功能！ 