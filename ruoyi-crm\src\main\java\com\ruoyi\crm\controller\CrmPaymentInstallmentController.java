package com.ruoyi.crm.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;

import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.crm.service.ICrmPaymentInstallmentService;
import com.ruoyi.crm.service.ICrmPaymentPlanService;

/**
 * 回款分期管理Controller
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/crm/paymentInstallment")
public class CrmPaymentInstallmentController extends BaseController
{
    @Autowired
    private ICrmPaymentInstallmentService crmPaymentInstallmentService;
    
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    /**
     * 查询回款分期列表
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmPaymentInstallment crmPaymentInstallment)
    {
        startPage();
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectCrmPaymentInstallmentList(crmPaymentInstallment);
        return getDataTable(list);
    }
    
    /**
     * 根据回款计划ID查询分期列表
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:list')")
    @GetMapping("/plan/{planId}")
    public AjaxResult getInstallmentsByPlan(@PathVariable("planId") Long planId)
    {
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectInstallmentsByPlanId(planId);
        return success(list);
    }

    /**
     * 导出回款分期列表
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:export')")
    @Log(title = "回款分期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmPaymentInstallment crmPaymentInstallment)
    {
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectCrmPaymentInstallmentList(crmPaymentInstallment);
        ExcelUtil<CrmPaymentInstallment> util = new ExcelUtil<CrmPaymentInstallment>(CrmPaymentInstallment.class);
        util.exportExcel(response, list, "回款分期数据");
    }

    /**
     * 获取回款分期详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(crmPaymentInstallmentService.selectCrmPaymentInstallmentById(id));
    }

    /**
     * 新增回款分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:add')")
    @Log(title = "回款分期", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmPaymentInstallment crmPaymentInstallment)
    {
        return toAjax(crmPaymentInstallmentService.insertCrmPaymentInstallment(crmPaymentInstallment));
    }
    
    /**
     * 批量新增回款分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:add')")
    @Log(title = "回款分期", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<CrmPaymentInstallment> installments)
    {
        return toAjax(crmPaymentInstallmentService.batchInsertInstallments(installments));
    }

    /**
     * 修改回款分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:edit')")
    @Log(title = "回款分期", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmPaymentInstallment crmPaymentInstallment)
    {
        return toAjax(crmPaymentInstallmentService.updateCrmPaymentInstallment(crmPaymentInstallment));
    }

    /**
     * 删除回款分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:remove')")
    @Log(title = "回款分期", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(crmPaymentInstallmentService.deleteCrmPaymentInstallmentByIds(ids));
    }
    
    /**
     * 记录实际回款
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:edit')")
    @Log(title = "记录实际回款", businessType = BusinessType.UPDATE)
    @PostMapping("/recordPayment")
    public AjaxResult recordPayment(
            @RequestParam("installmentId") Long installmentId,
            @RequestParam("actualAmount") BigDecimal actualAmount,
            @RequestParam(value = "paymentVoucher", required = false) String paymentVoucher)
    {
        return toAjax(crmPaymentInstallmentService.recordActualPayment(installmentId, actualAmount, paymentVoucher));
    }
    
    /**
     * 更新分期状态
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:edit')")
    @Log(title = "更新分期状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(
            @RequestParam("installmentId") Long installmentId,
            @RequestParam("status") String status)
    {
        return toAjax(crmPaymentInstallmentService.updateInstallmentStatus(installmentId, status));
    }
    
    /**
     * 更新逾期天数
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:edit')")
    @Log(title = "更新逾期天数", businessType = BusinessType.UPDATE)
    @PostMapping("/updateOverdue")
    public AjaxResult updateOverdueDays()
    {
        return toAjax(crmPaymentInstallmentService.updateOverdueDays());
    }
    
    /**
     * 创建标准分期模板
     * 支持常见的分期模式：2期、3期、4期等
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:add')")
    @Log(title = "创建分期模板", businessType = BusinessType.INSERT)
    @PostMapping("/createTemplate")
    public AjaxResult createTemplate(@RequestBody Map<String, Object> params)
    {
        Long planId = Long.valueOf(params.get("planId").toString());
        String templateType = params.get("templateType").toString();
        
        // 获取回款计划信息
        CrmPaymentPlan plan = paymentPlanService.selectPaymentPlanById(planId);
        if (plan == null) {
            return error("回款计划不存在");
        }
        
        // 先删除已有分期
        crmPaymentInstallmentService.deleteInstallmentsByPlanId(planId);
        
        List<CrmPaymentInstallment> installments = new ArrayList<>();
        
        switch (templateType) {
            case "2_phase":
                installments = create2PhaseTemplate(planId, plan.getTotalAmount());
                break;
            case "3_phase":
                installments = create3PhaseTemplate(planId, plan.getTotalAmount());
                break;
            case "4_phase":
                installments = create4PhaseTemplate(planId, plan.getTotalAmount());
                break;
            case "custom":
                // 自定义分期，从参数中获取分期信息
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> phases = (List<Map<String, Object>>) params.get("phases");
                installments = createCustomTemplate(planId, plan.getTotalAmount(), phases);
                break;
            default:
                return error("不支持的分期模板类型");
        }
        
        int result = crmPaymentInstallmentService.batchInsertInstallments(installments);
        return toAjax(result);
    }
    
    /**
     * 创建2期分期模板：首期50%，尾期50%
     */
    private List<CrmPaymentInstallment> create2PhaseTemplate(Long planId, BigDecimal totalAmount) 
    {
        List<CrmPaymentInstallment> installments = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        
        // 首期50%，当前日期
        CrmPaymentInstallment first = new CrmPaymentInstallment();
        first.setPlanId(planId);
        first.setInstallmentNumber(1);
        first.setInstallmentName("首期款");
        first.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.50")));
        first.setInstallmentPercentage(new BigDecimal("50.00"));
        first.setPlannedDate(cal.getTime());
        first.setInstallmentStatus("待回款");
        installments.add(first);
        
        // 尾期50%，30天后
        cal.add(Calendar.DAY_OF_MONTH, 30);
        CrmPaymentInstallment second = new CrmPaymentInstallment();
        second.setPlanId(planId);
        second.setInstallmentNumber(2);
        second.setInstallmentName("尾期款");
        second.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.50")));
        second.setInstallmentPercentage(new BigDecimal("50.00"));
        second.setPlannedDate(cal.getTime());
        second.setInstallmentStatus("待回款");
        installments.add(second);
        
        return installments;
    }
    
    /**
     * 创建3期分期模板：首期40%，中期30%，尾期30%
     */
    private List<CrmPaymentInstallment> create3PhaseTemplate(Long planId, BigDecimal totalAmount) 
    {
        List<CrmPaymentInstallment> installments = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        
        // 首期40%
        CrmPaymentInstallment first = new CrmPaymentInstallment();
        first.setPlanId(planId);
        first.setInstallmentNumber(1);
        first.setInstallmentName("首期款");
        first.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.40")));
        first.setInstallmentPercentage(new BigDecimal("40.00"));
        first.setPlannedDate(cal.getTime());
        first.setInstallmentStatus("待回款");
        installments.add(first);
        
        // 中期30%，30天后
        cal.add(Calendar.DAY_OF_MONTH, 30);
        CrmPaymentInstallment second = new CrmPaymentInstallment();
        second.setPlanId(planId);
        second.setInstallmentNumber(2);
        second.setInstallmentName("中期款");
        second.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.30")));
        second.setInstallmentPercentage(new BigDecimal("30.00"));
        second.setPlannedDate(cal.getTime());
        second.setInstallmentStatus("待回款");
        installments.add(second);
        
        // 尾期30%，60天后
        cal.add(Calendar.DAY_OF_MONTH, 30);
        CrmPaymentInstallment third = new CrmPaymentInstallment();
        third.setPlanId(planId);
        third.setInstallmentNumber(3);
        third.setInstallmentName("尾期款");
        third.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.30")));
        third.setInstallmentPercentage(new BigDecimal("30.00"));
        third.setPlannedDate(cal.getTime());
        third.setInstallmentStatus("待回款");
        installments.add(third);
        
        return installments;
    }
    
    /**
     * 创建4期分期模板：每期25%
     */
    private List<CrmPaymentInstallment> create4PhaseTemplate(Long planId, BigDecimal totalAmount) 
    {
        List<CrmPaymentInstallment> installments = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        String[] phases = {"首期款", "二期款", "三期款", "尾期款"};
        
        for (int i = 0; i < 4; i++) {
            CrmPaymentInstallment installment = new CrmPaymentInstallment();
            installment.setPlanId(planId);
            installment.setInstallmentNumber(i + 1);
            installment.setInstallmentName(phases[i]);
            installment.setInstallmentAmount(totalAmount.multiply(new BigDecimal("0.25")));
            installment.setInstallmentPercentage(new BigDecimal("25.00"));
            installment.setPlannedDate(cal.getTime());
            installment.setInstallmentStatus("待回款");
            installments.add(installment);
            
            // 每期间隔30天
            cal.add(Calendar.DAY_OF_MONTH, 30);
        }
        
        return installments;
    }
    
    /**
     * 创建自定义分期模板
     */
    private List<CrmPaymentInstallment> createCustomTemplate(Long planId, BigDecimal totalAmount, List<Map<String, Object>> phases) 
    {
        List<CrmPaymentInstallment> installments = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        
        for (int i = 0; i < phases.size(); i++) {
            Map<String, Object> phase = phases.get(i);
            
            CrmPaymentInstallment installment = new CrmPaymentInstallment();
            installment.setPlanId(planId);
            installment.setInstallmentNumber(i + 1);
            installment.setInstallmentName(phase.get("name").toString());
            
            BigDecimal percentage = new BigDecimal(phase.get("percentage").toString());
            installment.setInstallmentPercentage(percentage);
            installment.setInstallmentAmount(totalAmount.multiply(percentage.divide(new BigDecimal("100"))));
            
            // 计算计划日期
            int daysOffset = Integer.parseInt(phase.get("daysOffset").toString());
            cal.setTime(new Date());
            cal.add(Calendar.DAY_OF_MONTH, daysOffset);
            installment.setPlannedDate(cal.getTime());
            
            installment.setInstallmentStatus("待回款");
            installments.add(installment);
        }
        
        return installments;
    }
    
    /**
     * 获取分期统计信息
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:list')")
    @GetMapping("/statistics/{planId}")
    public AjaxResult getStatistics(@PathVariable("planId") Long planId)
    {
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectInstallmentsByPlanId(planId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCount", list.size());
        
        int completedCount = 0;
        int overdueCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal receivedAmount = BigDecimal.ZERO;
        
        for (CrmPaymentInstallment installment : list) {
            totalAmount = totalAmount.add(installment.getInstallmentAmount());
            if (installment.getActualAmount() != null) {
                receivedAmount = receivedAmount.add(installment.getActualAmount());
            }
            
            if ("已回款".equals(installment.getInstallmentStatus())) {
                completedCount++;
            }
            
            if (installment.getOverdueDays() != null && installment.getOverdueDays() > 0) {
                overdueCount++;
            }
        }
        
        statistics.put("completedCount", completedCount);
        statistics.put("overdueCount", overdueCount);
        statistics.put("totalAmount", totalAmount);
        statistics.put("receivedAmount", receivedAmount);
        statistics.put("remainingAmount", totalAmount.subtract(receivedAmount));
        statistics.put("completionRate", list.size() > 0 ? (completedCount * 100 / list.size()) : 0);
        
        return success(statistics);
    }
    
    /**
     * 查询逾期分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:list')")
    @GetMapping("/overdue")
    public TableDataInfo getOverdueInstallments()
    {
        startPage();
        CrmPaymentInstallment query = new CrmPaymentInstallment();
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectCrmPaymentInstallmentList(query);
        
        // 过滤逾期的分期
        list.removeIf(installment -> installment.getOverdueDays() == null || installment.getOverdueDays() <= 0);
        
        return getDataTable(list);
    }
    
    /**
     * 查询即将到期分期
     */
    @PreAuthorize("@ss.hasPermi('crm:paymentInstall:list')")
    @GetMapping("/upcoming")
    public TableDataInfo getUpcomingInstallments(@RequestParam(value = "days", defaultValue = "7") int days)
    {
        startPage();
        CrmPaymentInstallment query = new CrmPaymentInstallment();
        List<CrmPaymentInstallment> list = crmPaymentInstallmentService.selectCrmPaymentInstallmentList(query);
        
        // 过滤即将到期的分期
        Date currentDate = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDate);
        cal.add(Calendar.DAY_OF_MONTH, days);
        Date futureDate = cal.getTime();
        
        list.removeIf(installment -> {
            Date plannedDate = installment.getPlannedDate();
            return plannedDate == null || 
                   plannedDate.before(currentDate) || 
                   plannedDate.after(futureDate) ||
                   "已回款".equals(installment.getInstallmentStatus());
        });
        
        return getDataTable(list);
    }
}