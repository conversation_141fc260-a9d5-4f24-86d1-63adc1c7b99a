# CRM通用附件模块技术设计方案

## 📐 架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端组件层                                │
├─────────────────────────────────────────────────────────────┤
│  AttachmentTab.vue (通用附件组件)                            │
│  ├── 上传区域 (拖拽/点击上传)                                │
│  ├── 附件列表 (卡片/列表/紧凑布局)                           │
│  ├── 操作按钮 (下载/预览/删除)                               │
│  └── 统计信息 (数量/大小/分类)                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API接口层                                │
├─────────────────────────────────────────────────────────────┤
│  /front/crm/attachments/{entityType}/{entityId}             │
│  ├── GET    - 获取附件列表                                  │
│  ├── POST   - 上传附件                                      │
│  ├── DELETE - 删除附件                                      │
│  └── PUT    - 更新附件信息                                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层                                │
├─────────────────────────────────────────────────────────────┤
│  ICrmAttachmentService & CrmAttachmentServiceImpl           │
│  ├── 文件上传处理                                           │
│  ├── 文件存储管理                                           │
│  ├── 权限验证                                               │
│  └── 业务逻辑处理                                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  CrmAttachmentMapper & CrmAttachmentMapper.xml              │
│  ├── 基础CRUD操作                                           │
│  ├── 按实体类型查询                                         │
│  ├── 统计查询                                               │
│  └── 批量操作                                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                               │
├─────────────────────────────────────────────────────────────┤
│  MySQL数据库: crm_attachments表                             │
│  文件系统: /uploads/{entityType}/{entityId}/                │
└─────────────────────────────────────────────────────────────┘
```

## 🗄️ 数据库设计详解

### 核心设计理念
1. **通用性设计**：使用 `entity_type` + `entity_id` 支持多种业务实体
2. **扩展性考虑**：预留分类、描述、排序等扩展字段
3. **性能优化**：设计合理的索引结构
4. **数据完整性**：软删除设计，保证数据追溯

### 表结构详细说明
```sql
CREATE TABLE `crm_attachments` (
  -- 基本信息
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型：contact/customer/opportunity/contract',
  `entity_id` bigint(20) NOT NULL COMMENT '关联实体的主键ID',
  
  -- 文件信息
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名(唯一)',
  `original_name` varchar(255) NOT NULL COMMENT '用户上传的原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件在服务器上的完整路径', 
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型(image/jpeg)',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名(.jpg)',
  
  -- 业务信息
  `category` varchar(50) DEFAULT NULL COMMENT '附件分类：contract/image/document/other',
  `description` varchar(500) DEFAULT NULL COMMENT '附件描述信息',
  `upload_by` varchar(64) DEFAULT NULL COMMENT '上传用户',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数统计',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开访问(0私有 1公开)',
  `sort_order` int(11) DEFAULT 0 COMMENT '显示排序(数字越小越靠前)',
  
  -- 系统信息
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者', 
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_name` (`file_name`),
  KEY `idx_entity` (`entity_type`, `entity_id`, `del_flag`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_category` (`category`),
  KEY `idx_upload_by` (`upload_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRM通用附件表';
```

### 索引设计说明
- **主键索引**：`id` 自增主键，保证唯一性
- **唯一索引**：`file_name` 防止文件名冲突
- **复合索引**：`(entity_type, entity_id, del_flag)` 优化业务查询
- **单列索引**：`upload_time`、`category`、`upload_by` 支持排序和筛选

## 🏗️ 后端技术设计

### 实体类设计
```java
@Entity
@Table(name = "crm_attachments")
public class CrmAttachment extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "entity_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private AttachmentEntityType entityType;
    
    @Column(name = "entity_id", nullable = false)
    private Long entityId;
    
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;
    
    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;
    
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;
    
    @Column(name = "file_size")
    private Long fileSize;
    
    @Column(name = "file_type", length = 100)
    private String fileType;
    
    @Column(name = "file_extension", length = 20)
    private String fileExtension;
    
    @Column(name = "category", length = 50)
    @Enumerated(EnumType.STRING)
    private AttachmentCategory category;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "upload_by", length = 64)
    private String uploadBy;
    
    @Column(name = "upload_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;
    
    @Column(name = "download_count")
    private Integer downloadCount = 0;
    
    @Column(name = "is_public")
    private Boolean isPublic = false;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    // 构造器、getter/setter、toString等方法
}
```

### 枚举设计
```java
public enum AttachmentEntityType {
    CONTACT("contact", "联系人"),
    CUSTOMER("customer", "客户"),
    OPPORTUNITY("opportunity", "商机"),
    CONTRACT("contract", "合同"),
    LEAD("lead", "线索"),
    PRODUCT("product", "产品");
    
    private final String code;
    private final String desc;
    
    AttachmentEntityType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static AttachmentEntityType fromCode(String code) {
        for (AttachmentEntityType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown entity type: " + code);
    }
}

public enum AttachmentCategory {
    CONTRACT("contract", "合同文件", "📄"),
    IMAGE("image", "图片", "🖼️"),
    DOCUMENT("document", "文档", "📝"),
    SPREADSHEET("spreadsheet", "表格", "📊"),
    PRESENTATION("presentation", "演示文稿", "📽️"),
    VIDEO("video", "视频", "🎥"),
    AUDIO("audio", "音频", "🎵"),
    OTHER("other", "其他", "📁");
    
    private final String code;
    private final String desc;
    private final String icon;
    
    // 构造器和方法
}
```

### 文件存储策略
```java
@Component
public class AttachmentStorageService {
    
    @Value("${crm.attachment.base-path:/uploads}")
    private String basePath;
    
    @Value("${crm.attachment.max-file-size:10MB}")
    private String maxFileSize;
    
    /**
     * 生成文件存储路径
     * 规则: /uploads/{entityType}/{entityId}/{yyyy/MM/dd}/
     */
    public String generateStoragePath(String entityType, Long entityId) {
        LocalDate now = LocalDate.now();
        return String.format("%s/%s/%s/%04d/%02d/%02d/", 
            basePath, entityType, entityId, 
            now.getYear(), now.getMonthValue(), now.getDayOfMonth());
    }
    
    /**
     * 生成唯一文件名
     * 规则: {timestamp}_{uuid}_{originalName}
     */
    public String generateUniqueFileName(String originalName) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String extension = getFileExtension(originalName);
        String nameWithoutExt = getFileNameWithoutExtension(originalName);
        
        return String.format("%s_%s_%s%s", timestamp, uuid, nameWithoutExt, extension);
    }
    
    /**
     * 保存上传文件
     */
    public AttachmentUploadResult saveFile(MultipartFile file, String entityType, Long entityId) {
        try {
            // 验证文件
            validateFile(file);
            
            // 生成存储路径
            String storagePath = generateStoragePath(entityType, entityId);
            String fileName = generateUniqueFileName(file.getOriginalFilename());
            String fullPath = storagePath + fileName;
            
            // 创建目录
            File dir = new File(storagePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 保存文件
            File destFile = new File(fullPath);
            file.transferTo(destFile);
            
            // 返回结果
            return AttachmentUploadResult.builder()
                .fileName(fileName)
                .filePath(fullPath)
                .fileSize(file.getSize())
                .fileType(file.getContentType())
                .originalName(file.getOriginalFilename())
                .build();
                
        } catch (Exception e) {
            throw new AttachmentException("文件保存失败", e);
        }
    }
    
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new AttachmentException("文件不能为空");
        }
        
        if (file.getSize() > parseSize(maxFileSize)) {
            throw new AttachmentException("文件大小超过限制: " + maxFileSize);
        }
        
        String fileType = file.getContentType();
        if (!isAllowedFileType(fileType)) {
            throw new AttachmentException("不支持的文件类型: " + fileType);
        }
    }
}
```

### 服务层设计
```java
@Service
@Transactional
public class CrmAttachmentServiceImpl implements ICrmAttachmentService {
    
    @Autowired
    private CrmAttachmentMapper attachmentMapper;
    
    @Autowired
    private AttachmentStorageService storageService;
    
    @Override
    public AjaxResult uploadAttachment(MultipartFile file, String entityType, Long entityId, 
                                     String category, String description) {
        try {
            // 验证实体是否存在
            validateEntity(entityType, entityId);
            
            // 保存文件
            AttachmentUploadResult uploadResult = storageService.saveFile(file, entityType, entityId);
            
            // 创建附件记录
            CrmAttachment attachment = new CrmAttachment();
            attachment.setEntityType(AttachmentEntityType.fromCode(entityType));
            attachment.setEntityId(entityId);
            attachment.setFileName(uploadResult.getFileName());
            attachment.setOriginalName(uploadResult.getOriginalName());
            attachment.setFilePath(uploadResult.getFilePath());
            attachment.setFileSize(uploadResult.getFileSize());
            attachment.setFileType(uploadResult.getFileType());
            attachment.setFileExtension(getFileExtension(uploadResult.getOriginalName()));
            attachment.setCategory(AttachmentCategory.fromFileType(uploadResult.getFileType()));
            attachment.setDescription(description);
            attachment.setUploadBy(SecurityUtils.getUsername());
            attachment.setUploadTime(new Date());
            
            // 保存到数据库
            int result = attachmentMapper.insertCrmAttachment(attachment);
            
            if (result > 0) {
                return AjaxResult.success("上传成功", attachment);
            } else {
                // 删除已上传的文件
                storageService.deleteFile(uploadResult.getFilePath());
                return AjaxResult.error("上传失败");
            }
            
        } catch (AttachmentException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("文件上传异常", e);
            return AjaxResult.error("系统异常，上传失败");
        }
    }
    
    @Override
    public List<CrmAttachment> getAttachmentsByEntity(String entityType, Long entityId) {
        CrmAttachment query = new CrmAttachment();
        query.setEntityType(AttachmentEntityType.fromCode(entityType));
        query.setEntityId(entityId);
        query.setDelFlag("0");
        
        return attachmentMapper.selectCrmAttachmentList(query);
    }
    
    @Override
    public AjaxResult downloadAttachment(Long id, HttpServletResponse response) {
        try {
            CrmAttachment attachment = attachmentMapper.selectCrmAttachmentById(id);
            if (attachment == null || "2".equals(attachment.getDelFlag())) {
                return AjaxResult.error("附件不存在");
            }
            
            // 权限验证
            validateDownloadPermission(attachment);
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(attachment.getOriginalName(), "UTF-8"));
            
            // 读取文件并输出
            File file = new File(attachment.getFilePath());
            if (!file.exists()) {
                return AjaxResult.error("文件不存在");
            }
            
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
            // 更新下载次数
            attachmentMapper.updateDownloadCount(id);
            
            return AjaxResult.success();
            
        } catch (Exception e) {
            log.error("文件下载异常", e);
            return AjaxResult.error("下载失败");
        }
    }
}
```

## 🎨 前端技术设计

### 组件架构设计
```vue
<template>
  <div class="attachment-tab">
    <!-- 上传区域 -->
    <AttachmentUploader
      :entity-type="entityType"
      :entity-id="entityId"
      :max-file-size="maxFileSize"
      :allowed-types="allowedTypes"
      :readonly="readonly"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
    
    <!-- 工具栏 -->
    <AttachmentToolbar
      v-model:layout="currentLayout"
      v-model:category="currentCategory"
      :show-category="showCategory"
      :categories="categories"
      :total-count="attachments.length"
      :total-size="totalSize"
      @refresh="loadAttachments"
      @batch-delete="handleBatchDelete"
    />
    
    <!-- 附件列表 -->
    <AttachmentList
      :attachments="filteredAttachments"
      :layout="currentLayout"
      :loading="loading"
      :readonly="readonly"
      @download="handleDownload"
      @delete="handleDelete"
      @preview="handlePreview"
      @selection-change="handleSelectionChange"
    />
    
    <!-- 预览对话框 -->
    <AttachmentPreview
      v-model:visible="previewVisible"
      :attachment="currentPreviewAttachment"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Attachment, AttachmentProps } from './types';
import { useAttachment } from './composables/useAttachment';
import AttachmentUploader from './components/AttachmentUploader.vue';
import AttachmentToolbar from './components/AttachmentToolbar.vue';
import AttachmentList from './components/AttachmentList.vue';
import AttachmentPreview from './components/AttachmentPreview.vue';

const props = withDefaults(defineProps<AttachmentProps>(), {
  readonly: false,
  maxFileSize: 10,
  allowedTypes: () => ['*'],
  maxFileCount: 50,
  showCategory: true,
  showDescription: true,
  layout: 'card'
});

const emit = defineEmits<{
  (e: 'update-count', count: number): void;
  (e: 'attachment-change', attachments: Attachment[]): void;
}>();

const {
  attachments,
  loading,
  currentLayout,
  currentCategory,
  categories,
  totalSize,
  filteredAttachments,
  previewVisible,
  currentPreviewAttachment,
  selectedAttachments,
  loadAttachments,
  handleUploadSuccess,
  handleUploadError,
  handleDownload,
  handleDelete,
  handleBatchDelete,
  handlePreview,
  handleSelectionChange
} = useAttachment(props, emit);

// 初始化加载
loadAttachments();
</script>
```

### Composables设计
```typescript
// composables/useAttachment.ts
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { Attachment, AttachmentProps } from '../types';
import { attachmentApi } from '@/api/attachment';

export function useAttachment(props: AttachmentProps, emit: any) {
  const attachments = ref<Attachment[]>([]);
  const loading = ref(false);
  const currentLayout = ref(props.layout);
  const currentCategory = ref<string>('all');
  const previewVisible = ref(false);
  const currentPreviewAttachment = ref<Attachment | null>(null);
  const selectedAttachments = ref<Attachment[]>([]);
  
  // 计算属性
  const categories = computed(() => {
    const categorySet = new Set(attachments.value.map(item => item.category));
    return [
      { label: '全部', value: 'all' },
      ...Array.from(categorySet).map(category => ({
        label: getCategoryLabel(category),
        value: category
      }))
    ];
  });
  
  const totalSize = computed(() => {
    return attachments.value.reduce((total, item) => total + item.fileSize, 0);
  });
  
  const filteredAttachments = computed(() => {
    if (currentCategory.value === 'all') {
      return attachments.value;
    }
    return attachments.value.filter(item => item.category === currentCategory.value);
  });
  
  // 加载附件列表
  const loadAttachments = async () => {
    if (!props.entityId) return;
    
    try {
      loading.value = true;
      const response = await attachmentApi.getAttachments(props.entityType, props.entityId);
      attachments.value = response.data || [];
      
      // 通知父组件更新计数
      emit('update-count', attachments.value.length);
      emit('attachment-change', attachments.value);
    } catch (error) {
      console.error('加载附件列表失败:', error);
      ElMessage.error('加载附件列表失败');
    } finally {
      loading.value = false;
    }
  };
  
  // 上传成功处理
  const handleUploadSuccess = (newAttachment: Attachment) => {
    attachments.value.unshift(newAttachment);
    ElMessage.success('上传成功');
    emit('update-count', attachments.value.length);
    emit('attachment-change', attachments.value);
  };
  
  // 下载处理
  const handleDownload = async (attachment: Attachment) => {
    try {
      await attachmentApi.downloadAttachment(attachment.id);
    } catch (error) {
      console.error('下载失败:', error);
      ElMessage.error('下载失败');
    }
  };
  
  // 删除处理
  const handleDelete = async (attachment: Attachment) => {
    try {
      await ElMessageBox.confirm('确定要删除这个附件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      await attachmentApi.deleteAttachment(attachment.id);
      
      const index = attachments.value.findIndex(item => item.id === attachment.id);
      if (index > -1) {
        attachments.value.splice(index, 1);
      }
      
      ElMessage.success('删除成功');
      emit('update-count', attachments.value.length);
      emit('attachment-change', attachments.value);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    }
  };
  
  // 监听实体ID变化
  watch(
    () => props.entityId,
    (newId) => {
      if (newId) {
        loadAttachments();
      }
    },
    { immediate: true }
  );
  
  return {
    attachments,
    loading,
    currentLayout,
    currentCategory,
    categories,
    totalSize,
    filteredAttachments,
    previewVisible,
    currentPreviewAttachment,
    selectedAttachments,
    loadAttachments,
    handleUploadSuccess,
    handleUploadError,
    handleDownload,
    handleDelete,
    handleBatchDelete,
    handlePreview,
    handleSelectionChange
  };
}
```

### 类型定义
```typescript
// types/attachment.ts
export interface Attachment {
  id: number;
  entityType: string;
  entityId: number;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  fileExtension: string;
  category: string;
  description?: string;
  uploadBy: string;
  uploadTime: string;
  downloadCount: number;
  isPublic: boolean;
  sortOrder: number;
}

export interface AttachmentProps {
  entityType: string;
  entityId: number;
  readonly?: boolean;
  maxFileSize?: number; // MB
  allowedTypes?: string[];
  maxFileCount?: number;
  showCategory?: boolean;
  showDescription?: boolean;
  layout?: 'card' | 'list' | 'compact';
}

export interface AttachmentUploadOptions {
  category?: string;
  description?: string;
  isPublic?: boolean;
}

export interface AttachmentStats {
  totalCount: number;
  totalSize: number;
  categoryStats: Array<{
    category: string;
    count: number;
    size: number;
  }>;
}
```

## 🔒 安全设计

### 文件上传安全
1. **文件类型验证**：检查MIME类型和文件扩展名
2. **文件大小限制**：防止上传过大文件
3. **文件名安全**：生成唯一文件名，防止路径遍历
4. **病毒扫描**：集成防病毒引擎（可选）

### 访问权限控制
1. **用户权限验证**：检查用户是否有权限访问对应实体
2. **文件访问控制**：通过应用层控制文件访问，不直接暴露文件路径
3. **下载权限**：记录下载日志，支持权限审核

### 数据安全
1. **软删除设计**：保证数据可恢复
2. **操作日志**：记录所有文件操作
3. **备份策略**：定期备份文件和数据库

## 📊 性能优化

### 文件存储优化
1. **分层存储**：按日期分层存储，避免单目录文件过多
2. **CDN加速**：静态文件通过CDN分发
3. **缩略图生成**：图片文件自动生成缩略图

### 数据库优化
1. **索引优化**：合理设计索引，优化查询性能
2. **分页查询**：大量数据分页加载
3. **缓存策略**：热点数据缓存

### 前端优化
1. **懒加载**：大量附件时懒加载
2. **虚拟滚动**：长列表虚拟滚动
3. **图片预览**：支持图片预览，减少下载

---

**文档版本**：v1.0  
**创建时间**：2025-07-01  
**审核状态**：待审核
