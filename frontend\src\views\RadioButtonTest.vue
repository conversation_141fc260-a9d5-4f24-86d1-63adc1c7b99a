<template>
  <div class="radio-button-test">
    <h2>Radio Button 测试页面</h2>
    
    <div class="test-section">
      <h3>基础用法</h3>
      <el-radio-group v-model="viewMode1" size="small" class="fixed-radio-group">
        <el-radio-button value="list">列表</el-radio-button>
        <el-radio-button value="grid">网格</el-radio-button>
      </el-radio-group>
      <p>当前选择: {{ viewMode1 }}</p>
    </div>

    <div class="test-section">
      <h3>三个选项</h3>
      <el-radio-group v-model="viewMode2" size="small">
        <el-radio-button value="table">表格</el-radio-button>
        <el-radio-button value="card">卡片</el-radio-button>
        <el-radio-button value="timeline">时间线</el-radio-button>
      </el-radio-group>
      <p>当前选择: {{ viewMode2 }}</p>
    </div>

    <div class="test-section">
      <h3>不同尺寸</h3>
      <div class="size-test">
        <div>
          <label>Large:</label>
          <el-radio-group v-model="viewMode3" size="large">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <label>Default:</label>
          <el-radio-group v-model="viewMode3">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <label>Small:</label>
          <el-radio-group v-model="viewMode3" size="small">
            <el-radio-button value="list">列表</el-radio-button>
            <el-radio-button value="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>禁用状态</h3>
      <el-radio-group v-model="viewMode4" size="small">
        <el-radio-button value="list">列表</el-radio-button>
        <el-radio-button value="grid" disabled>网格(禁用)</el-radio-button>
        <el-radio-button value="card">卡片</el-radio-button>
      </el-radio-group>
    </div>

    <div class="test-section">
      <h3>实际使用场景 - 团队成员视图</h3>
      <el-card class="members-card">
        <template #header>
          <div class="card-header">
            <span>团队成员 (3人)</span>
            <div class="header-actions">
              <el-radio-group v-model="teamViewMode" size="small">
                <el-radio-button value="list">列表</el-radio-button>
                <el-radio-button value="grid">网格</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="demo-content">
          <p>当前视图模式: {{ teamViewMode }}</p>
        </div>
      </el-card>
    </div>

    <div class="test-section">
      <h3>调试信息</h3>
      <div class="debug-info">
        <p><strong>Element Plus 命名空间:</strong> ep</p>
        <p><strong>预期CSS类名:</strong> .ep-radio-button</p>
        <p><strong>当前样式状态:</strong> 检查浏览器开发者工具中的元素样式</p>
        <div class="debug-buttons">
          <button @click="inspectStyles" class="debug-btn">检查样式</button>
          <button @click="logElements" class="debug-btn">输出元素信息</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const viewMode1 = ref('list')
const viewMode2 = ref('table')
const viewMode3 = ref('list')
const viewMode4 = ref('list')
const teamViewMode = ref('list')

// 调试函数
const inspectStyles = () => {
  const radioButtons = document.querySelectorAll('.ep-radio-button')
  console.log('找到的 radio-button 元素:', radioButtons)
  radioButtons.forEach((btn, index) => {
    console.log(`按钮 ${index}:`, {
      element: btn,
      classes: btn.className,
      computedStyle: window.getComputedStyle(btn)
    })
  })
}

const logElements = () => {
  const radioGroups = document.querySelectorAll('.ep-radio-group')
  console.log('找到的 radio-group 元素:', radioGroups)
  radioGroups.forEach((group, index) => {
    console.log(`组 ${index}:`, {
      element: group,
      classes: group.className,
      children: group.children
    })
  })
}
</script>

<style scoped>
.radio-button-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.test-section h3 {
  margin-top: 0;
  color: #303133;
}

.size-test > div {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.size-test label {
  width: 80px;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-content {
  padding: 20px;
  text-align: center;
  color: #666;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.debug-info p {
  margin: 8px 0;
}

.debug-buttons {
  margin-top: 15px;
}

.debug-btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.debug-btn:hover {
  background: #337ecc;
}

/* 强制修复样式 */
.fixed-radio-group {
  display: inline-flex !important;
}

.fixed-radio-group .ep-radio-button {
  margin: 0 !important;
  border-radius: 0 !important;
  border: 1px solid #dcdfe6 !important;
  background: #fff !important;
  color: #606266 !important;
}

.fixed-radio-group .ep-radio-button:not(:first-child) {
  margin-left: -1px !important;
}

.fixed-radio-group .ep-radio-button:first-child {
  border-radius: 4px 0 0 4px !important;
}

.fixed-radio-group .ep-radio-button:last-child {
  border-radius: 0 4px 4px 0 !important;
}

.fixed-radio-group .ep-radio-button.is-active {
  background: #409eff !important;
  color: #fff !important;
  border-color: #409eff !important;
  z-index: 1 !important;
}
</style>
