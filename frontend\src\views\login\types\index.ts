// 登录表单数据类型
export interface LoginForm {
  username: string;
  password: string;
  code: string;
  uuid: string;
  rememberMe: boolean;
}

// 验证码响应数据类型
export interface CodeImgResponse {
  msg: string;
  img: string;
  code: number;
  captchaEnabled: boolean;
  uuid: string;
}

// 企业微信二维码URL响应类型
export interface WecomQrCodeResponse {
  code: number;
  msg: string;
  data: string; // 二维码URL
}

// API 通用响应类型
export interface ApiResponse<T> {
  code: number;
  msg?: string;
  data?: T;
} 