<template>
  <el-container class="team-management">
    <!-- 主内容区域容器 -->
    <el-container class="main-container">
      <!-- 页面头部，包含标题和操作按钮 -->
      <el-header class="header">
        <h1>团队管理</h1>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            @click="openTeamDialog(null)"
            class="action-btn primary-btn"
          >
            <el-icon><Plus /></el-icon>
            新建团队
          </el-button>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <!-- 搜索区域 -->
        <el-card class="search-card">
          <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
            <el-form-item label="团队名称" prop="teamName">
              <el-input
                v-model="queryParams.teamName"
                placeholder="请输入团队名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="负责人" prop="leaderName">
              <el-input
                v-model="queryParams.leaderName"
                placeholder="请输入负责人姓名"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 团队列表 -->
        <el-card class="list-card">
          <el-table
            v-loading="loading"
            :data="teamList"
            border
            style="width: 100%"
            @row-click="handleRowClick"
          >
            <el-table-column type="index" width="50" />
            <el-table-column prop="teamName" label="团队名称" min-width="150" show-overflow-tooltip />
            <el-table-column prop="leaderName" label="负责人" width="120" show-overflow-tooltip />
            <el-table-column prop="description" label="团队描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="createTime" label="创建时间" width="180" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <el-button link size="small" @click.stop="openTeamDialog(row)">编辑</el-button>
                <el-button link size="small" @click.stop="handleViewMembers(row)">查看成员</el-button>
                <el-button link size="small" @click.stop="handleViewBusinessObjects(row)">关联对象</el-button>
                <el-button link size="small" style="color: #f56c6c;" @click.stop="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <el-pagination
            v-if="total > 0"
            class="pagination"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-card>
      </el-main>
    </el-container>

    <!-- 团队表单对话框 -->
    <el-dialog
      :title="teamFormTitle"
      v-model="teamDialogVisible"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="teamFormRef"
        :model="teamForm"
        :rules="teamFormRules"
        label-width="100px"
      >
        <el-form-item label="团队名称" prop="teamName">
          <el-input v-model="teamForm.teamName" placeholder="请输入团队名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leaderId">
          <el-select
            v-model="teamForm.leaderId"
            placeholder="请选择负责人"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.userId"
              :label="`${user.nickName}(${user.userName})`"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="teamForm.description"
            type="textarea"
            placeholder="请输入团队描述"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="teamDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTeamForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 团队成员管理对话框 -->
    <el-dialog
      title="团队成员管理"
      v-model="teamMemberDialogVisible"
      width="800px"
      append-to-body
      destroy-on-close
    >
      <unified-team-management
        v-if="teamMemberDialogVisible"
        :team-id="selectedTeamId"
      />
    </el-dialog>

    <!-- 团队关联业务对象对话框 -->
    <el-dialog
      title="团队关联的业务对象"
      v-model="businessObjectsDialogVisible"
      width="90%"
      append-to-body
      destroy-on-close
      top="5vh"
    >
      <TeamBusinessObjects
        v-if="businessObjectsDialogVisible"
        :team-id="selectedTeamId"
      />
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { addTeam, deleteTeam, getAvailableUsers, getTeam, listTeam, updateTeam } from '@/api/crm/team';
import TeamBusinessObjects from '@/components/TeamBusinessObjects.vue';
import UnifiedTeamManagement from '@/components/UnifiedTeamManagement.vue';
import { CrmTeam, UserOption } from '@/types/crm-team';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  teamName: '',
  leaderName: ''
});

// 团队列表数据
const loading = ref(false);
const teamList = ref<CrmTeam[]>([]);
const total = ref(0);

// 团队表单相关
const teamDialogVisible = ref(false);
const teamFormRef = ref<FormInstance>();
const teamForm = reactive<CrmTeam>({
  teamName: '',
  leaderId: undefined,
  description: ''
});

// 团队表单校验规则
const teamFormRules = reactive<FormRules>({
  teamName: [
    { required: true, message: '团队名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '团队名称长度必须在2到50个字符之间', trigger: 'blur' }
  ],
  leaderId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
});

// 用户选项
const userOptions = ref<UserOption[]>([]);

// 团队成员管理相关
const teamMemberDialogVisible = ref(false);
const selectedTeamId = ref<number>(0);

// 团队关联业务对象相关
const businessObjectsDialogVisible = ref(false);

// 计算属性
const teamFormTitle = computed(() => {
  return teamForm.teamId ? '编辑团队' : '新建团队';
});

// 方法
const getList = async () => {
  loading.value = true;
  try {
    const res = await listTeam(queryParams);
    // 后端列表接口返回的字段是 id，前端其他地方期望的是 teamId，在此做兼容处理
    const processedRows = (res.rows || []).map(team => ({
      ...team,
      teamId: team.id
    }));
    teamList.value = processedRows;
    total.value = res.total || 0;
  } catch (error) {
    console.error('获取团队列表失败:', error);
    ElMessage.error('获取团队列表失败');
  } finally {
    loading.value = false;
  }
};

const loadUserOptions = async () => {
  try {
    const res: any = await getAvailableUsers();
    userOptions.value = res.rows?.map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      nickName: user.nickName,
      deptName: user.dept?.deptName
    })) || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败');
  }
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.teamName = '';
  queryParams.leaderName = '';
  handleQuery();
};

const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getList();
};

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  getList();
};

const handleRowClick = (row: CrmTeam) => {
  // 点击行查看详情或执行其他操作
  console.log('查看团队详情:', row);
};

const openTeamDialog = async (team: CrmTeam | null) => {
  resetTeamForm();
  
  if (team) {
    // 编辑模式，加载团队详情
    try {
      const res = await getTeam(team.id || team.teamId!);
      Object.assign(teamForm, res.data);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      ElMessage.error('获取团队详情失败');
      return;
    }
  }
  
  // 加载用户选项
  await loadUserOptions();
  teamDialogVisible.value = true;
};

const resetTeamForm = () => {
  teamForm.teamId = undefined;
  teamForm.teamName = '';
  teamForm.leaderId = undefined;
  teamForm.description = '';
};

const submitTeamForm = async () => {
  if (!teamFormRef.value) return;
  
  await teamFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (teamForm.teamId) {
          // 更新团队
          await updateTeam(teamForm);
          ElMessage.success('更新团队成功');
        } else {
          // 新增团队
          await addTeam(teamForm);
          ElMessage.success('新增团队成功');
        }
        teamDialogVisible.value = false;
        getList();
      } catch (error) {
        console.error('保存团队失败:', error);
        ElMessage.error('保存团队失败');
      }
    }
  });
};

const handleViewMembers = (row: CrmTeam) => {
  // 使用 id 或 teamId，优先使用 id（后端返回的主键）
  selectedTeamId.value = row.id || row.teamId!;
  console.log('打开团队成员管理，teamId:', selectedTeamId.value, 'row:', row);
  teamMemberDialogVisible.value = true;
};

const handleViewBusinessObjects = (row: CrmTeam) => {
  // 使用 id 或 teamId，优先使用 id（后端返回的主键）
  selectedTeamId.value = row.id || row.teamId!;
  console.log('打开团队业务对象管理，teamId:', selectedTeamId.value, 'row:', row);
  businessObjectsDialogVisible.value = true;
};

const handleDelete = async (row: CrmTeam) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除团队「${row.teamName}」吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteTeam(row.id || row.teamId!);
    ElMessage.success('删除团队成功');
    getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除团队失败:', error);
      ElMessage.error('删除团队失败');
    }
  }
};

const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 生命周期钩子
onMounted(() => {
  getList();
});
</script>

<style scoped>
.team-management {
  height: 100%;
  width: 100%;
}

.main-container {
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.primary-btn {
  background-color: var(--el-color-primary);
  color: white;
}

.main-content {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  text-align: right;
}
</style>
