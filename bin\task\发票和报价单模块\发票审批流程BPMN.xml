<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.ruoyi.com/invoice">

  <process id="invoice-approval" name="发票审批流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <startEvent id="startEvent" name="提交审批">
      <extensionElements>
        <activiti:executionListener event="start" delegateExpression="${invoiceStatusListener}">
          <activiti:field name="eventType" stringValue="start"/>
        </activiti:executionListener>
      </extensionElements>
    </startEvent>
    
    <!-- 财务经理审批 -->
    <userTask id="financeManagerApproval" name="财务经理审批" 
              activiti:candidateGroups="finance_manager">
      <documentation>财务经理审核发票信息、合同关联和金额</documentation>
      <extensionElements>
        <activiti:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <activiti:value id="approved" name="同意"/>
          <activiti:value id="rejected" name="驳回"/>
          <activiti:value id="needModify" name="需要修改"/>
        </activiti:formProperty>
        <activiti:formProperty id="approvalComments" name="审批意见" type="string"/>
        <activiti:formProperty id="contractVerified" name="合同验证" type="boolean" default="false"/>
        <activiti:formProperty id="attachmentVerified" name="附件验证" type="boolean" default="false"/>
        
        <activiti:taskListener event="create" delegateExpression="${invoiceTaskAssignmentListener}"/>
        <activiti:taskListener event="complete" delegateExpression="${invoiceStatusListener}">
          <activiti:field name="eventType" stringValue="financeApproval"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 审批结果判断网关 -->
    <exclusiveGateway id="approvalGateway" name="审批结果判断"/>
    
    <!-- 金额判断网关 -->
    <exclusiveGateway id="amountGateway" name="金额判断"/>
    
    <!-- 总经理审批 -->
    <userTask id="ceoApproval" name="总经理审批" 
              activiti:candidateGroups="ceo">
      <documentation>总经理审批大额发票（>20万元）</documentation>
      <extensionElements>
        <activiti:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <activiti:value id="approved" name="同意"/>
          <activiti:value id="rejected" name="驳回"/>
          <activiti:value id="returnToFinance" name="退回财务"/>
        </activiti:formProperty>
        <activiti:formProperty id="approvalComments" name="审批意见" type="string"/>
        
        <activiti:taskListener event="create" delegateExpression="${invoiceTaskAssignmentListener}"/>
        <activiti:taskListener event="complete" delegateExpression="${invoiceStatusListener}">
          <activiti:field name="eventType" stringValue="ceoApproval"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 修改任务 -->
    <userTask id="modifyInvoice" name="修改发票" 
              activiti:assignee="${initiator}">
      <documentation>申请人修改发票信息</documentation>
      <extensionElements>
        <activiti:formProperty id="modifyResult" name="修改结果" type="enum" required="true">
          <activiti:value id="resubmit" name="重新提交"/>
          <activiti:value id="cancel" name="取消申请"/>
        </activiti:formProperty>
        <activiti:formProperty id="modifyComments" name="修改说明" type="string"/>
        
        <activiti:taskListener event="complete" delegateExpression="${invoiceStatusListener}">
          <activiti:field name="eventType" stringValue="modify"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 状态同步服务任务 -->
    <serviceTask id="syncApprovedStatus" name="同步审批通过状态"
                 activiti:delegateExpression="${invoiceStatusListener}">
      <extensionElements>
        <activiti:field name="eventType" stringValue="approved"/>
      </extensionElements>
    </serviceTask>
    
    <serviceTask id="syncRejectedStatus" name="同步审批驳回状态"
                 activiti:delegateExpression="${invoiceStatusListener}">
      <extensionElements>
        <activiti:field name="eventType" stringValue="rejected"/>
      </extensionElements>
    </serviceTask>
    
    <!-- 结束事件 -->
    <endEvent id="approvedEndEvent" name="审批通过"/>
    <endEvent id="rejectedEndEvent" name="审批驳回"/>
    <endEvent id="cancelledEndEvent" name="申请取消"/>
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="financeManagerApproval"/>
    <sequenceFlow id="flow2" sourceRef="financeManagerApproval" targetRef="approvalGateway"/>
    
    <!-- 财务经理审批结果分支 -->
    <sequenceFlow id="financeApproved" sourceRef="approvalGateway" targetRef="amountGateway">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="financeRejected" sourceRef="approvalGateway" targetRef="syncRejectedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="financeNeedModify" sourceRef="approvalGateway" targetRef="modifyInvoice">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'needModify'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 金额判断分支 -->
    <sequenceFlow id="smallAmount" sourceRef="amountGateway" targetRef="syncApprovedStatus">
      <conditionExpression xsi:type="tFormalExpression">${totalAmount <= 200000}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="largeAmount" sourceRef="amountGateway" targetRef="ceoApproval">
      <conditionExpression xsi:type="tFormalExpression">${totalAmount > 200000}</conditionExpression>
    </sequenceFlow>
    
    <!-- 总经理审批结果分支 -->
    <sequenceFlow id="ceoApproved" sourceRef="ceoApproval" targetRef="syncApprovedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="ceoRejected" sourceRef="ceoApproval" targetRef="syncRejectedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="ceoReturnToFinance" sourceRef="ceoApproval" targetRef="financeManagerApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'returnToFinance'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 修改结果分支 -->
    <sequenceFlow id="resubmit" sourceRef="modifyInvoice" targetRef="financeManagerApproval">
      <conditionExpression xsi:type="tFormalExpression">${modifyResult == 'resubmit'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="cancelApplication" sourceRef="modifyInvoice" targetRef="cancelledEndEvent">
      <conditionExpression xsi:type="tFormalExpression">${modifyResult == 'cancel'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 结束连线 -->
    <sequenceFlow id="flowToApprovedEnd" sourceRef="syncApprovedStatus" targetRef="approvedEndEvent"/>
    <sequenceFlow id="flowToRejectedEnd" sourceRef="syncRejectedStatus" targetRef="rejectedEndEvent"/>
    
  </process>

  <!-- BPMN图形信息（可选，用于流程图显示） -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_invoice-approval">
    <bpmndi:BPMNPlane bpmnElement="invoice-approval" id="BPMNPlane_invoice-approval">
      <!-- 这里可以添加图形布局信息，用于流程设计器显示 -->
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>

</definitions>
