package com.ruoyi.crm.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.mapper.CrmPaymentPlanMapper;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 回款计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@Service
public class CrmPaymentPlanServiceImpl implements ICrmPaymentPlanService {
    @Autowired
    private CrmPaymentPlanMapper paymentPlanMapper;

    /**
     * 查询回款计划列表
     *
     * @param plan 回款计划信息
     * @return 回款计划集合
     */
    @Override
    public List<CrmPaymentPlan> selectPaymentPlanList(CrmPaymentPlan plan) {
        return paymentPlanMapper.selectPaymentPlanList(plan);
    }

    /**
     * 查询回款计划详细信息
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    @Override
    public CrmPaymentPlan selectPaymentPlanById(Long id) {
        return paymentPlanMapper.selectPaymentPlanById(id);
    }

    /**
     * 根据合同ID查询回款计划列表
     *
     * @param contractId 合同ID
     * @return 回款计划集合
     */
    @Override
    public List<CrmPaymentPlan> selectPaymentPlanByContractId(Long contractId) {
        return paymentPlanMapper.selectPaymentPlanByContractId(contractId);
    }

    /**
     * 新增回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPaymentPlan(CrmPaymentPlan plan) {
        plan.setCreateTime(DateUtils.getNowDate());
        plan.setCreateBy(SecurityUtils.getUsername());
        return paymentPlanMapper.insertPaymentPlan(plan);
    }

    /**
     * 修改回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePaymentPlan(CrmPaymentPlan plan) {
        plan.setUpdateTime(DateUtils.getNowDate());
        plan.setUpdateBy(SecurityUtils.getUsername());
        return paymentPlanMapper.updatePaymentPlan(plan);
    }

    /**
     * 删除回款计划信息
     *
     * @param id 回款计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePaymentPlanById(Long id) {
        return paymentPlanMapper.deletePaymentPlanById(id);
    }

    /**
     * 批量删除回款计划信息
     *
     * @param ids 需要删除的回款计划ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePaymentPlanByIds(Long[] ids) {
        return paymentPlanMapper.deletePaymentPlanByIds(ids);
    }
} 