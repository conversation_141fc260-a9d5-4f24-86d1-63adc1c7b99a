package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;

/**
 * 客户跟进记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface ICrmCustomerFollowupRecordService {
    
    /**
     * 查询客户跟进记录
     * 
     * @param id 客户跟进记录主键
     * @return 客户跟进记录
     */
    CrmCustomerFollowupRecord selectCrmCustomerFollowupRecordById(Long id);

    /**
     * 查询客户跟进记录列表
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 客户跟进记录集合
     */
    List<CrmCustomerFollowupRecord> selectCrmCustomerFollowupRecordList(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 根据客户ID查询跟进记录列表
     * 
     * @param customerId 客户ID
     * @return 跟进记录集合
     */
    List<CrmCustomerFollowupRecord> selectFollowupRecordsByCustomerId(Long customerId);

    /**
     * 新增客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    int insertCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 修改客户跟进记录
     * 
     * @param crmCustomerFollowupRecord 客户跟进记录
     * @return 结果
     */
    int updateCrmCustomerFollowupRecord(CrmCustomerFollowupRecord crmCustomerFollowupRecord);

    /**
     * 批量删除客户跟进记录
     * 
     * @param ids 需要删除的客户跟进记录主键集合
     * @return 结果
     */
    int deleteCrmCustomerFollowupRecordByIds(Long[] ids);

    /**
     * 删除客户跟进记录信息
     * 
     * @param id 客户跟进记录主键
     * @return 结果
     */
    int deleteCrmCustomerFollowupRecordById(Long id);

    /**
     * 统计客户的跟进记录数量
     * 
     * @param customerId 客户ID
     * @return 跟进记录数量
     */
    int countFollowupRecordsByCustomerId(Long customerId);

    /**
     * 查询最近的跟进记录
     * 
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 最近的跟进记录
     */
    List<CrmCustomerFollowupRecord> selectRecentFollowupRecords(Long customerId, Integer limit);

    /**
     * 查询需要跟进的记录（根据下次跟进时间）
     * 
     * @param creatorId 创建者ID
     * @return 需要跟进的记录
     */
    List<CrmCustomerFollowupRecord> selectPendingFollowupRecords(Long creatorId);

    /**
     * 创建跟进记录
     * 
     * @param customerId 客户ID
     * @param followupType 跟进方式
     * @param content 跟进内容
     * @param result 跟进结果
     * @param nextFollowupTime 下次跟进时间
     * @param isImportant 是否重要
     * @return 结果
     */
    int createFollowupRecord(Long customerId, String followupType, String content, 
                           String result, java.util.Date nextFollowupTime, Boolean isImportant);
}