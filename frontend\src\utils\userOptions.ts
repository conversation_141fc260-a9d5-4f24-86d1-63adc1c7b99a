import { listUser } from '@/api/system/user';
import { UserOption } from '@/types/user';
import type { SimpleApiResponse } from '~/types/api/responseTypes';
// 默认用户数据，在API调用失败时使用
const defaultUsers: UserOption[] = [
    { label: '系统管理员', value: 1 },
    { label: '销售经理', value: 2 },
    { label: '销售主管', value: 3 },
    { label: '销售顾问', value: 4 }
];

/**
 * 获取用户选项列表
 * 用于表单、下拉菜单等地方展示用户列表
 * @returns Promise 返回用户选项列表
 */
export async function getUserOptions(): Promise<SimpleApiResponse<UserOption[]>> {
    try {
        const response = await listUser({}) as any;
        
        if (response && typeof response === 'object' && 'rows' in response && Array.isArray(response.rows)) {
            const options = response.rows.map((user: any) => ({
                label: user.userName || user.nickName || user.name || '未命名用户',
                value: user.userId || user.id || 0,
                nickName: user.nickName || user.name || '未命名用户'
            }));
            
            return {
                code: 200,
                data: options,
                msg: '获取用户列表成功'
            };
        }
        
        // 如果响应格式不符合预期，返回默认用户数据
        return { code: 200, data: defaultUsers, msg: '使用默认用户列表' };
    } catch (error) {
        console.error('获取用户列表失败:', error);
        return { code: 500, msg: '获取用户列表失败', data: defaultUsers };
    }
}