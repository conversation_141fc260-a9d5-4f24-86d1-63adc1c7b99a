<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    class="opportunity-form"
  >
    <el-form-item label="商机名称" prop="name">
      <el-input
        v-model="form.name"
        placeholder="请输入商机名称"
        :maxlength="50"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="所属客户" prop="customerId">
      <el-select
        v-model="form.customerId"
        placeholder="请选择客户"
        filterable
        remote
        :remote-method="handleCustomerSearch"
        :loading="customerLoading"
      >
        <el-option
          v-for="item in customerOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="商机阶段" prop="stage">
      <el-select v-model="form.stage" placeholder="请选择商机阶段">
        <el-option
          v-for="item in stageOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <el-tag :type="getStageTagType(item.value)" size="small">
            {{ item.label }}
          </el-tag>
        </el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="预计金额" prop="expectedAmount">
      <el-input-number
        v-model="form.expectedAmount"
        :precision="2"
        :step="1000"
        :min="0"
        controls-position="right"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="成交概率" prop="probability">
      <el-slider
        v-model="form.probability"
        :step="5"
        :marks="{
          0: '0%',
          25: '25%',
          50: '50%',
          75: '75%',
          100: '100%'
        }"
      />
    </el-form-item>

    <el-form-item label="预计成交日期" prop="expectedClosingDate">
      <el-date-picker
        v-model="form.expectedClosingDate"
        type="date"
        placeholder="选择预计成交日期"
        value-format="YYYY-MM-DD"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="备注" prop="remarks">
      <el-input
        v-model="form.remarks"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
        :maxlength="500"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { searchCustomers } from '@/api/customer'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const formRef = ref<FormInstance>()
const customerLoading = ref(false)
const customerOptions = ref([])

const stageOptions = [
  { label: '初步接触', value: 'initial_contact' },
  { label: '需求分析', value: 'needs_analysis' },
  { label: '方案/报价', value: 'proposal' },
  { label: '谈判/复审', value: 'negotiation' },
  { label: '赢单', value: 'closed_won' },
  { label: '输单', value: 'closed_lost' }
]

const form = reactive({
  name: '',
  customerId: '',
  stage: 'initial_contact',
  expectedAmount: 0,
  probability: 20,
  expectedClosingDate: '',
  remarks: '',
  ...props.initialData
})

const rules = {
  name: [
    { required: true, message: '请输入商机名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  customerId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  stage: [
    { required: true, message: '请选择商机阶段', trigger: 'change' }
  ],
  expectedAmount: [
    { required: true, message: '请输入预计金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额必须大于等于0', trigger: 'blur' }
  ],
  probability: [
    { required: true, message: '请选择成交概率', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '概率范围为0-100', trigger: 'blur' }
  ],
  expectedClosingDate: [
    { required: true, message: '请选择预计成交日期', trigger: 'change' }
  ]
}

const getStageTagType = (stage: string) => {
  const stageMap: Record<string, string> = {
    'initial_contact': 'info',
    'needs_analysis': 'warning',
    'proposal': 'success',
    'negotiation': 'danger',
    'closed_won': 'success',
    'closed_lost': 'info'
  }
  return stageMap[stage] || 'info'
}

const handleCustomerSearch = async (query: string) => {
  if (query) {
    customerLoading.value = true
    try {
      const res = await searchCustomers({ keyword: query })
      customerOptions.value = res.data
    } catch (error) {
      ElMessage.error('获取客户列表失败')
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

const validate = async () => {
  if (!formRef.value) return false
  return await formRef.value.validate()
}

const getFormData = () => {
  return { ...form }
}

defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.opportunity-form {
  padding: 20px;
}
</style> 