package com.ruoyi.crm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 回款计划Controller
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@RestController
@RequestMapping("/crm/payment/plan")
public class CrmPaymentPlanController extends BaseController {
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    /**
     * 查询回款计划列表
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmPaymentPlan plan) {
        startPage();
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanList(plan);
        return getDataTable(list);
    }

    /**
     * 导出回款计划列表
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:export')")
    @Log(title = "回款计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CrmPaymentPlan plan) {
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanList(plan);
        ExcelUtil<CrmPaymentPlan> util = new ExcelUtil<CrmPaymentPlan>(CrmPaymentPlan.class);
        return util.exportExcel(list, "payment_plan");
    }

    /**
     * 获取回款计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(paymentPlanService.selectPaymentPlanById(id));
    }

    /**
     * 根据合同ID查询回款计划列表
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:list')")
    @GetMapping("/contract/{contractId}")
    public AjaxResult listByContractId(@PathVariable("contractId") Long contractId) {
        List<CrmPaymentPlan> list = paymentPlanService.selectPaymentPlanByContractId(contractId);
        return success(list);
    }

    /**
     * 新增回款计划
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:add')")
    @Log(title = "回款计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmPaymentPlan plan) {
        return toAjax(paymentPlanService.insertPaymentPlan(plan));
    }

    /**
     * 修改回款计划
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:edit')")
    @Log(title = "回款计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmPaymentPlan plan) {
        return toAjax(paymentPlanService.updatePaymentPlan(plan));
    }

    /**
     * 删除回款计划
     */
    @PreAuthorize("@ss.hasPermi('crm:payment:plan:remove')")
    @Log(title = "回款计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(paymentPlanService.deletePaymentPlanByIds(ids));
    }
} 