<template>
    <div class="dashboard-container">
        <!-- 顶部标题栏 -->
        <div class="dashboard-header">
            <div class="header-left">
                <h1 class="dashboard-title">
                    <el-icon class="title-icon"><DataAnalysis /></el-icon>
                    仪表盘
                </h1>
                <div class="header-controls">
                    <el-button type="primary" size="small" :icon="Plus">
                        <span>AI助手</span>
                    </el-button>
                    <el-button size="small" :icon="Setting">新建仪表盘</el-button>
                </div>
            </div>
            <div class="header-right">
                <el-select v-model="selectedPeriod" size="small" class="period-select">
                    <el-option label="本人及下属" value="self"></el-option>
                    <el-option label="本人" value="personal"></el-option>
                </el-select>
                <el-select v-model="selectedMonth" size="small" class="month-select">
                    <el-option label="本月" value="thisMonth"></el-option>
                    <el-option label="上月" value="lastMonth"></el-option>
                    <el-option label="本季度" value="thisQuarter"></el-option>
                </el-select>
                <!-- TODO: 三个点按钮暂时隐藏 -->
                <!-- <el-button size="small" :icon="More" circle></el-button> -->
            </div>
        </div>

        <!-- Tab 导航 -->
        <div class="dashboard-tabs">
            <el-tabs v-model="activeName" class="custom-tabs">
                <el-tab-pane label="仪表盘" name="dashboard">
                    <div class="dashboard-content">
                        <!-- 销售简报卡片 -->
                        <div class="sales-report-card">
                            <div class="card-header">
                                <div class="header-title">
                                    <el-icon><TrendCharts /></el-icon>
                                    <span>销售简报</span>
                                </div>
                                <div class="header-info">
                                    <span class="period-info">本人及下属 | 本月</span>
                                </div>
                            </div>
                            <SalesMetricsGrid />
                        </div>

                        <!-- 主要内容区域 -->
                        <div class="main-content">
                            <!-- 左侧内容 -->
                            <div class="left-section">
                                <!-- 合同金额目标及完成情况 -->
                                <div class="chart-card">
                                    <div class="card-header">
                                        <div class="header-title">
                                            <el-icon><PieChart /></el-icon>
                                            <span>合同金额目标及完成情况</span>
                                        </div>
                                                                                 <div class="card-actions">
                                             <el-button size="small" text :icon="TrendCharts">设置目标</el-button>
                                             <el-button size="small" text :icon="TrendCharts">图表</el-button>
                                         </div>
                                    </div>
                                    <ContractTargetChart />
                                </div>

                                <!-- 销售漏斗 -->
                                <div class="chart-card">
                                                                         <div class="card-header">
                                         <div class="header-title">
                                             <el-icon><TrendCharts /></el-icon>
                                             <span>销售漏斗</span>
                                         </div>
                                        <div class="card-actions">
                                            <el-select v-model="selectedGroup" size="small" placeholder="销售流程部">
                                                <el-option label="销售流程部" value="salesDept"></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <SalesFunnelChart />
                                </div>
                            </div>

                            <!-- 右侧内容 -->
                            <div class="right-section">
                                <!-- 数据汇总 -->
                                <div class="summary-card">
                                    <div class="card-header">
                                        <div class="header-title">
                                            <el-icon><DataBoard /></el-icon>
                                            <span>数据汇总</span>
                                        </div>
                                        <div class="period-info">本人及下属 | 本月</div>
                                    </div>
                                    <DataSummaryGrid />
                                </div>

                                <!-- 排行榜 -->
                                <div class="ranking-card">
                                    <div class="card-header">
                                        <div class="header-title">
                                            <el-icon><Trophy /></el-icon>
                                            <span>排行榜</span>
                                        </div>
                                        <div class="period-info">本人及下属 | 本月</div>
                                    </div>
                                    <RankingList />
                                </div>

                                <!-- 客户遗忘提醒 -->
                                <div class="reminder-card">
                                    <div class="card-header">
                                        <div class="header-title">
                                            <el-icon><Clock /></el-icon>
                                            <span>客户遗忘提醒</span>
                                        </div>
                                        <div class="period-info">本人及下属 | 今天</div>
                                    </div>
                                    <CustomerReminderList />
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="员工客户分析" name="employee">
                    <EmployeeAnalysis />
                </el-tab-pane>

                <el-tab-pane label="销售漏斗分析" name="funnel">
                    <FunnelAnalysis />
                </el-tab-pane>

                <el-tab-pane label="员工业绩分析" name="performance">
                    <PerformanceAnalysis />
                </el-tab-pane>

                <el-tab-pane label="客户画像分析" name="customer">
                    <CustomerAnalysis />
                </el-tab-pane>

                <el-tab-pane label="产品分析" name="product">
                    <ProductAnalysis />
                </el-tab-pane>

                <el-tab-pane label="排行榜" name="ranking">
                    <RankingAnalysis />
                </el-tab-pane>

                <el-tab-pane label="业务目标完成情况" name="target">
                    <TargetAnalysis />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { 
  DataAnalysis, Plus, Setting, /* More, */ TrendCharts, PieChart, 
  DataBoard, Trophy, Clock 
} from '@element-plus/icons-vue'
// TODO: More图标暂时不使用，已注释

// 导入组件
import SalesMetricsGrid from './components/SalesMetricsGrid.vue'
import ContractTargetChart from './components/ContractTargetChart.vue'
import SalesFunnelChart from './components/SalesFunnelChart.vue'
import DataSummaryGrid from './components/DataSummaryGrid.vue'
import RankingList from './components/RankingList.vue'
import CustomerReminderList from './components/CustomerReminderList.vue'
import EmployeeAnalysis from './components/EmployeeAnalysis.vue'
import FunnelAnalysis from './components/FunnelAnalysis.vue'
import PerformanceAnalysis from './components/PerformanceAnalysis.vue'
import CustomerAnalysis from './components/CustomerAnalysis.vue'
import ProductAnalysis from './components/ProductAnalysis.vue'
import RankingAnalysis from './components/RankingAnalysis.vue'
import TargetAnalysis from './components/TargetAnalysis.vue'

// 响应式数据
const activeName = ref('dashboard')
const selectedPeriod = ref('self')
const selectedMonth = ref('thisMonth')
const selectedGroup = ref('salesDept')
</script>

<style lang="scss" scoped>
.dashboard-container {
    padding: 16px 24px;
    background-color: #f5f7fa;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 顶部标题栏
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
        display: flex;
        align-items: center;
        gap: 24px;
        
        .dashboard-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
            font-size: 24px;
            font-weight: 500;
            color: #1f2329;
            
            .title-icon {
                font-size: 28px;
                color: #1677ff;
            }
        }
        
        .header-controls {
            display: flex;
            gap: 12px;
        }
    }
    
    .header-right {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .period-select,
        .month-select {
            width: 120px;
        }
    }
}

// Tab 样式
.dashboard-tabs {
    .custom-tabs {
        :deep(.el-tabs__header) {
            margin: 0 0 24px 0;
            border-bottom: 1px solid #e4e7ed;
            
            .el-tabs__nav-wrap {
                padding: 0;
            }
            
            .el-tabs__item {
                padding: 0 24px;
                height: 48px;
                line-height: 48px;
                font-size: 14px;
                font-weight: 500;
                color: #646a73;
                border-bottom: 2px solid transparent;
                
                &.is-active {
                    color: #1677ff;
                    border-bottom-color: #1677ff;
                }
                
                &:hover {
                    color: #1677ff;
                }
            }
        }
        
        :deep(.el-tabs__content) {
            padding: 0;
        }
    }
}

// 仪表盘内容
.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

// 销售简报卡片
.sales-report-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #1f2329;
            
            .el-icon {
                font-size: 18px;
                color: #1677ff;
            }
        }
        
        .header-info {
            .period-info {
                font-size: 12px;
                color: #86909c;
            }
        }
    }
}

// 主要内容区域
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 24px;
    
    @media (max-width: 1400px) {
        grid-template-columns: 1fr;
    }
}

// 左侧内容
.left-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

// 右侧内容
.right-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

// 通用卡片样式
.chart-card,
.summary-card,
.ranking-card,
.reminder-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #1f2329;
            
            .el-icon {
                font-size: 18px;
                color: #1677ff;
            }
        }
        
        .card-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .period-info {
            font-size: 12px;
            color: #86909c;
        }
    }
}

// 图表卡片特殊样式
.chart-card {
    min-height: 400px;
}

// 数据汇总卡片
.summary-card {
    min-height: 300px;
}

// 排行榜卡片
.ranking-card {
    min-height: 350px;
}

// 客户提醒卡片
.reminder-card {
    min-height: 250px;
}

// 响应式设计
@media (max-width: 768px) {
    .dashboard-container {
        padding: 12px 16px;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }
        
        .header-right {
            align-self: stretch;
            justify-content: flex-end;
        }
    }
    
    .main-content {
        grid-template-columns: 1fr;
    }
}
</style>
