<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmLeadPoolMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmLeadPool" id="CrmLeadPoolResult">
        <result property="id" column="id" />
        <result property="leadId" column="lead_id" />
        <result property="poolStatus" column="pool_status" />
        <result property="qualityLevel" column="quality_level" />
        <result property="priority" column="priority" />
        <result property="sourceType" column="source_type" />
        <result property="enterPoolTime" column="enter_pool_time" />
        <result property="lastAssignTime" column="last_assign_time" />
        <result property="assignCount" column="assign_count" />
        <result property="region" column="region" />
        <result property="industry" column="industry" />
        <result property="estimatedValue" column="estimated_value" />
        <result property="remarks" column="remarks" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <!-- 扩展字段 -->
        <result property="leadName" column="lead_name" />
        <result property="customerName" column="customer_name" />
        <result property="mobile" column="mobile" />
        <result property="email" column="email" />
        <result property="leadSource" column="lead_source" />
    </resultMap>

    <sql id="selectCrmLeadPoolVo">
        select p.id, p.lead_id, p.pool_status, p.quality_level, p.priority, p.source_type,
               p.enter_pool_time, p.last_assign_time, p.assign_count, p.region, p.industry,
               p.estimated_value, p.remarks, p.create_by, p.create_time, p.update_by, p.update_time, p.del_flag,
               l.lead_name, l.customer_name, l.mobile, l.email, l.lead_source
        from crm_lead_pool p
        left join crm_business_leads l on p.lead_id = l.id
    </sql>

    <select id="selectCrmLeadPoolList" parameterType="com.ruoyi.common.domain.entity.CrmLeadPool" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        <where>  
            p.del_flag = '0'
            <if test="leadId != null "> and p.lead_id = #{leadId}</if>
            <if test="poolStatus != null  and poolStatus != ''"> and p.pool_status = #{poolStatus}</if>
            <if test="qualityLevel != null  and qualityLevel != ''"> and p.quality_level = #{qualityLevel}</if>
            <if test="priority != null "> and p.priority = #{priority}</if>
            <if test="sourceType != null  and sourceType != ''"> and p.source_type = #{sourceType}</if>
            <if test="region != null  and region != ''"> and p.region like concat('%', #{region}, '%')</if>
            <if test="industry != null  and industry != ''"> and p.industry like concat('%', #{industry}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(p.enter_pool_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(p.enter_pool_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by p.priority desc, p.enter_pool_time desc
    </select>
    
    <select id="selectAvailableLeadPoolList" parameterType="com.ruoyi.common.domain.entity.CrmLeadPool" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        <where>  
            p.del_flag = '0' and p.pool_status = 'available'
            <if test="qualityLevel != null  and qualityLevel != ''"> and p.quality_level = #{qualityLevel}</if>
            <if test="region != null  and region != ''"> and p.region like concat('%', #{region}, '%')</if>
            <if test="industry != null  and industry != ''"> and p.industry like concat('%', #{industry}, '%')</if>
        </where>
        order by p.priority desc, p.enter_pool_time asc
    </select>
    
    <select id="selectCrmLeadPoolById" parameterType="Long" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        where p.id = #{id} and p.del_flag = '0'
    </select>

    <select id="selectCrmLeadPoolByLeadId" parameterType="Long" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        where p.lead_id = #{leadId} and p.del_flag = '0'
    </select>

    <select id="selectLeadPoolByQualityLevel" parameterType="String" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        where p.quality_level = #{qualityLevel} and p.pool_status = 'available' and p.del_flag = '0'
        order by p.priority desc, p.enter_pool_time asc
    </select>

    <select id="selectLeadPoolByRegion" parameterType="String" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        where p.region = #{region} and p.pool_status = 'available' and p.del_flag = '0'
        order by p.priority desc, p.enter_pool_time asc
    </select>

    <select id="selectLeadPoolByIndustry" parameterType="String" resultMap="CrmLeadPoolResult">
        <include refid="selectCrmLeadPoolVo"/>
        where p.industry = #{industry} and p.pool_status = 'available' and p.del_flag = '0'
        order by p.priority desc, p.enter_pool_time asc
    </select>

    <insert id="insertCrmLeadPool" parameterType="com.ruoyi.common.domain.entity.CrmLeadPool" useGeneratedKeys="true" keyProperty="id">
        insert into crm_lead_pool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leadId != null">lead_id,</if>
            <if test="poolStatus != null and poolStatus != ''">pool_status,</if>
            <if test="qualityLevel != null and qualityLevel != ''">quality_level,</if>
            <if test="priority != null">priority,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="enterPoolTime != null">enter_pool_time,</if>
            <if test="lastAssignTime != null">last_assign_time,</if>
            <if test="assignCount != null">assign_count,</if>
            <if test="region != null">region,</if>
            <if test="industry != null">industry,</if>
            <if test="estimatedValue != null">estimated_value,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leadId != null">#{leadId},</if>
            <if test="poolStatus != null and poolStatus != ''">#{poolStatus},</if>
            <if test="qualityLevel != null and qualityLevel != ''">#{qualityLevel},</if>
            <if test="priority != null">#{priority},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="enterPoolTime != null">#{enterPoolTime},</if>
            <if test="lastAssignTime != null">#{lastAssignTime},</if>
            <if test="assignCount != null">#{assignCount},</if>
            <if test="region != null">#{region},</if>
            <if test="industry != null">#{industry},</if>
            <if test="estimatedValue != null">#{estimatedValue},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateCrmLeadPool" parameterType="com.ruoyi.common.domain.entity.CrmLeadPool">
        update crm_lead_pool
        <trim prefix="SET" suffixOverrides=",">
            <if test="leadId != null">lead_id = #{leadId},</if>
            <if test="poolStatus != null and poolStatus != ''">pool_status = #{poolStatus},</if>
            <if test="qualityLevel != null and qualityLevel != ''">quality_level = #{qualityLevel},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="enterPoolTime != null">enter_pool_time = #{enterPoolTime},</if>
            <if test="lastAssignTime != null">last_assign_time = #{lastAssignTime},</if>
            <if test="assignCount != null">assign_count = #{assignCount},</if>
            <if test="region != null">region = #{region},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="estimatedValue != null">estimated_value = #{estimatedValue},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateLeadPoolStatus">
        update crm_lead_pool 
        set pool_status = #{poolStatus}, update_by = #{updateBy}, update_time = sysdate()
        where id = #{id}
    </update>

    <update id="updateLeadPoolStatusBatch">
        update crm_lead_pool 
        set pool_status = #{poolStatus}, update_by = #{updateBy}, update_time = sysdate()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteCrmLeadPoolById" parameterType="Long">
        update crm_lead_pool set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmLeadPoolByIds" parameterType="String">
        update crm_lead_pool set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计相关查询 -->
    <select id="countLeadPool" parameterType="com.ruoyi.common.domain.entity.CrmLeadPool" resultType="int">
        select count(*) from crm_lead_pool p
        <where>
            p.del_flag = '0'
            <if test="poolStatus != null and poolStatus != ''"> and p.pool_status = #{poolStatus}</if>
            <if test="qualityLevel != null and qualityLevel != ''"> and p.quality_level = #{qualityLevel}</if>
        </where>
    </select>

    <select id="countLeadPoolByStatus" resultMap="CrmLeadPoolResult">
        select pool_status, count(*) as assign_count
        from crm_lead_pool 
        where del_flag = '0'
        group by pool_status
    </select>

    <select id="countLeadPoolByQualityLevel" resultMap="CrmLeadPoolResult">
        select quality_level, count(*) as assign_count
        from crm_lead_pool 
        where del_flag = '0'
        group by quality_level
    </select>

    <select id="countLeadPoolByRegion" resultMap="CrmLeadPoolResult">
        select region, count(*) as assign_count
        from crm_lead_pool 
        where del_flag = '0' and region is not null and region != ''
        group by region
    </select>

    <select id="countLeadPoolByIndustry" resultMap="CrmLeadPoolResult">
        select industry, count(*) as assign_count
        from crm_lead_pool
        where del_flag = '0' and industry is not null and industry != ''
        group by industry
        order by assign_count desc
    </select>

</mapper>
