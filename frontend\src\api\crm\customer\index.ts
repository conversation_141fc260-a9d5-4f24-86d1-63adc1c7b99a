import request from '@/utils/request';
import type { CustomerData, CustomerSearchParams, AssignCustomerData, BatchAssignCustomerData, FollowupRecord, CreateFollowupRecordDTO, FollowupRecordQueryParams, OperationLog, OperationLogQueryParams, OperationTypeStatistics } from './types';

// 搜索客户
export function searchCustomers(params: CustomerSearchParams) {
    return request({
        url: '/front/crm/customer/search',
        method: 'get',
        params
    });
}

// 获取客户列表
export function listCustomers(params: any) {
    return request({
        url: '/front/crm/customer/list',
        method: 'get',
        params
    });
}

// 获取客户详情
export function getCustomer(id: number) {
    return request<CustomerData>({
        url: `/front/crm/customer/${id}`,
        method: 'get'
    });
}

// 创建客户
export function createCustomer(data: Partial<CustomerData>) {
    return request({
        url: '/front/crm/customer',
        method: 'post',
        data
    });
}

// 更新客户
export function updateCustomer(data: Partial<CustomerData>) {
    return request({
        url: '/front/crm/customer',
        method: 'put',
        data
    });
}

// 删除客户
export function deleteCustomer(ids: string) {
    return request({
        url: `/front/crm/customer/${ids}`,
        method: 'delete'
    });
}

// 批量删除客户
export function batchDeleteCustomers(ids: number[]) {
    return deleteCustomer(ids.join(','));
}

// 导出客户数据
export function exportCustomers(params: any) {
    return request({
        url: '/front/crm/customer/export',
        method: 'post',
        data: params,
        responseType: 'blob'
    });
}

// 关注客户
export function followCustomer(customerId: number) {
    return request({
        url: `/front/crm/customer/follow/${customerId}`,
        method: 'post'
    });
}

// 取消关注客户
export function unfollowCustomer(customerId: number) {
    return request({
        url: `/front/crm/customer/follow/${customerId}`,
        method: 'delete'
    });
}

// 查询关注状态
export function getFollowStatus(customerId: number) {
    return request({
        url: `/front/crm/customer/follow/status/${customerId}`,
        method: 'get'
    });
}

// 批量关注客户
export function batchFollowCustomers(customerIds: number[]) {
    return request({
        url: '/front/crm/customer/follow/batch',
        method: 'post',
        data: customerIds
    });
}

// 批量取消关注客户
export function batchUnfollowCustomers(customerIds: number[]) {
    return request({
        url: '/front/crm/customer/follow/batch',
        method: 'delete',
        data: customerIds
    });
}

// 分配客户给指定用户
export function assignCustomer(assignData: AssignCustomerData) {
    return request({
        url: '/front/crm/customer/assign',
        method: 'post',
        data: assignData
    });
}

// 批量分配客户
export function batchAssignCustomers(batchAssignData: BatchAssignCustomerData) {
    return request({
        url: '/front/crm/customer/assign/batch',
        method: 'post',
        data: batchAssignData
    });
}

// ==== 跟进记录相关API ====

// 查询跟进记录列表
export function listFollowupRecords(params: FollowupRecordQueryParams) {
    return request({
        url: '/front/crm/customer/followup/list',
        method: 'get',
        params
    });
}

// 根据客户ID查询跟进记录
export function getFollowupRecordsByCustomerId(customerId: number) {
    return request<FollowupRecord[]>({
        url: `/front/crm/customer/followup/customer/${customerId}`,
        method: 'get'
    });
}

// 获取跟进记录详情
export function getFollowupRecord(id: number) {
    return request<FollowupRecord>({
        url: `/front/crm/customer/followup/${id}`,
        method: 'get'
    });
}

// 创建跟进记录
export function createFollowupRecord(data: CreateFollowupRecordDTO) {
    return request({
        url: '/front/crm/customer/followup',
        method: 'post',
        data
    });
}

// 更新跟进记录
export function updateFollowupRecord(data: FollowupRecord) {
    return request({
        url: '/front/crm/customer/followup',
        method: 'put',
        data
    });
}

// 删除跟进记录
export function deleteFollowupRecords(ids: number[]) {
    return request({
        url: `/front/crm/customer/followup/${ids.join(',')}`,
        method: 'delete'
    });
}

// 统计客户跟进记录数量
export function countFollowupRecords(customerId: number) {
    return request({
        url: `/front/crm/customer/followup/count/${customerId}`,
        method: 'get'
    });
}

// 查询最近跟进记录
export function getRecentFollowupRecords(customerId: number, limit = 5) {
    return request<FollowupRecord[]>({
        url: `/front/crm/customer/followup/recent/${customerId}`,
        method: 'get',
        params: { limit }
    });
}

// 查询待跟进记录
export function getPendingFollowupRecords() {
    return request<FollowupRecord[]>({
        url: '/front/crm/customer/followup/pending',
        method: 'get'
    });
}

// 快速创建跟进记录
export function quickCreateFollowupRecord(params: {
    customerId: number;
    followupType: string;
    content: string;
    result?: string;
    nextFollowupTime?: string;
    isImportant?: boolean;
}) {
    return request({
        url: '/front/crm/customer/followup/quick',
        method: 'post',
        params
    });
}

// ==== 操作日志相关API ====

// 查询操作日志列表
export function listOperationLogs(params: OperationLogQueryParams) {
    return request({
        url: '/front/crm/customer/operationlog/list',
        method: 'get',
        params
    });
}

// 根据客户ID查询操作日志
export function getOperationLogsByCustomerId(customerId: number) {
    return request<OperationLog[]>({
        url: `/front/crm/customer/operationlog/customer/${customerId}`,
        method: 'get'
    });
}

// 获取操作日志详情
export function getOperationLog(id: number) {
    return request<OperationLog>({
        url: `/front/crm/customer/operationlog/${id}`,
        method: 'get'
    });
}

// 创建操作日志
export function createOperationLog(data: OperationLog) {
    return request({
        url: '/front/crm/customer/operationlog',
        method: 'post',
        data
    });
}

// 更新操作日志
export function updateOperationLog(data: OperationLog) {
    return request({
        url: '/front/crm/customer/operationlog',
        method: 'put',
        data
    });
}

// 删除操作日志
export function deleteOperationLogs(ids: number[]) {
    return request({
        url: `/front/crm/customer/operationlog/${ids.join(',')}`,
        method: 'delete'
    });
}

// 查询时间段内的操作日志
export function getOperationLogsByTimeRange(startTime: string, endTime: string) {
    return request<OperationLog[]>({
        url: '/front/crm/customer/operationlog/timerange',
        method: 'get',
        params: { startTime, endTime }
    });
}

// 获取操作类型统计
export function getOperationTypeStatistics(params?: {
    customerId?: number;
    startTime?: string;
    endTime?: string;
}) {
    return request<OperationTypeStatistics>({
        url: '/front/crm/customer/operationlog/statistics/operation-types',
        method: 'get',
        params
    });
}

// 获取最近的操作日志
export function getRecentOperationLogs(params?: {
    limit?: number;
    customerId?: number;
}) {
    return request<OperationLog[]>({
        url: '/front/crm/customer/operationlog/recent',
        method: 'get',
        params
    });
} 