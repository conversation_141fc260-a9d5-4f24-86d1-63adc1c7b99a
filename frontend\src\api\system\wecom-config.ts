import request from '@/utils/request'

// 查询企业微信配置详细
export function getWecomConfig(id: number) {
  return request({
    url: `/wecom/config/${id}`,
    method: 'get'
  })
}

// 查询启用的企业微信配置
export function getEnabledWecomConfig() {
  return request({
    url: '/wecom/config/enabled',
    method: 'get'
  })
}

// 新增企业微信配置
export function addWecomConfig(data: any) {
  return request({
    url: '/wecom/config',
    method: 'post',
    data: data
  })
}

// 修改企业微信配置
export function updateWecomConfig(data: any) {
  return request({
    url: '/wecom/config',
    method: 'put',
    data: data
  })
}

// 删除企业微信配置
export function delWecomConfig(id: number) {
  return request({
    url: `/wecom/config/${id}`,
    method: 'delete'
  })
} 