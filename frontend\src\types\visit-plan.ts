// 拜访计划实体接口
export interface VisitPlan {
  id?: number;
  visitPlanName: string;
  visitTime: string | Date;
  customerId: number;
  customerName?: string;
  contactId?: number;
  contactName?: string;
  opportunityId?: number;
  opportunityName?: string;
  visitPurpose: string;
  remindTime: number;
  remark?: string;
  postponeReason?: string;
  postponeRemark?: string;
  cancelReason?: string;
  cancelRemark?: string;
  followupContent?: string;
  ownerId: number;
  ownerName?: string;
  status: VisitPlanStatus;
  deptId?: number;
  deptName?: string;
  actualVisitTime?: string | Date;
  completeTime?: string | Date;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

// 拜访计划状态类型
export type VisitPlanStatus = 'planned' | 'ongoing' | 'completed' | 'postponed' | 'cancelled';

// 拜访计划统计信息
export interface VisitPlanStatistics {
  total: number;
  planned: number;
  completed: number;
  postponed: number;
  cancelled: number;
  completionRate: number;
  onTimeRate: number;
}

// 状态映射
export const VISIT_STATUS_MAP: Record<VisitPlanStatus, { label: string; color: 'primary' | 'success' | 'warning' | 'info' | 'danger' }> = {
  planned: { label: '计划中', color: 'primary' },
  ongoing: { label: '进行中', color: 'warning' },
  completed: { label: '已完成', color: 'success' },
  postponed: { label: '已延期', color: 'info' },
  cancelled: { label: '已取消', color: 'danger' }
};

// 提醒时间选项
export const REMIND_TIME_OPTIONS = [
  { label: '提前15分钟', value: 15 },
  { label: '提前30分钟', value: 30 },
  { label: '提前1小时', value: 60 },
  { label: '提前2小时', value: 120 },
  { label: '提前1天', value: 1440 },
];

// 默认拜访计划
export const DEFAULT_VISIT_PLAN: Partial<VisitPlan> = {
  visitPlanName: '',
  visitTime: '',
  visitPurpose: '',
  remindTime: 30,
  remark: '',
  status: 'planned'
};

// 延期请求参数
export interface PostponeRequest {
  reason: string;
  remark?: string;
  newVisitTime: string;
}

// 取消请求参数
export interface CancelRequest {
  reason: string;
  remark?: string;
}

// 完成请求参数
export interface CompleteRequest {
  followupContent: string;
}
