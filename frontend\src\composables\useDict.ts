import { ref, reactive, onMounted } from 'vue'
import { getDicts } from '@/api/system/dict/data'

interface DictData {
  label: string
  value: string
  raw: any
}

interface DictType {
  [key: string]: DictData[]
}

/**
 * 字典数据 composable
 * @param dictTypes 字典类型数组
 * @returns 字典数据对象
 */
export function useDict(...dictTypes: string[]) {
  const dict = reactive<{ type: DictType }>({
    type: {}
  })

  const loading = ref(false)

  // 加载字典数据
  const loadDict = async (dictType: string) => {
    try {
      const response = await getDicts(dictType)
      if (response.data && Array.isArray(response.data)) {
        dict.type[dictType] = response.data.map((item: any) => ({
          label: item.dictLabel,
          value: item.dictValue,
          raw: item
        }))
      } else {
        dict.type[dictType] = []
      }
    } catch (error) {
      console.error(`加载字典 ${dictType} 失败:`, error)
      dict.type[dictType] = []
    }
  }

  // 加载所有字典
  const loadAllDicts = async () => {
    if (dictTypes.length === 0) return
    
    loading.value = true
    try {
      await Promise.all(dictTypes.map(dictType => loadDict(dictType)))
    } finally {
      loading.value = false
    }
  }

  // 组件挂载时加载字典
  onMounted(() => {
    loadAllDicts()
  })

  return {
    dict,
    loading,
    loadDict,
    loadAllDicts
  }
}

// 默认导出
export default useDict