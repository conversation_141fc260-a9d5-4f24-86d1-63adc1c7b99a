<template>
    <div class="lead-details-container">
        <base-details-tab
            :entity-data="entityData"
            entity-type="线索"
            :basic-fields="basicFields"
            :system-fields="systemFields"
            basic-title="基本信息"
            system-title="系统信息"
            @update:entity="$emit('update:entity', $event)"
            class="custom-details-tab"
        />
    </div>
</template>

<script>
import BaseDetailsTab from '@/components/CommonDrawer/BaseDetailsTab.vue';

export default {
    name: 'LeadDetailsTab',
    components: {
        BaseDetailsTab
    },
    props: {
        entityData: {
            type: Object,
            required: true
        }
    },
    emits: ['update:entity'],
    data() {
        return {
            // 基本信息字段配置
            basicFields: [
                { label: '线索名称', field: 'leadName' },
                { label: '客户名称', field: 'customerName' },
                { label: '线索来源', field: 'leadSource', component: 'el-select', props: {
                    options: [
                        { label: '广告', value: '广告' },
                        { label: '推荐', value: '推荐' },
                        { label: '搜索引擎', value: '搜索引擎' },
                        { label: '其他', value: '其他' }
                    ]
                }},
                { label: '手机号码', field: 'mobile' },
                { label: '电话', field: 'phone' },
                { label: '邮箱', field: 'email' },
                { label: '地址', field: 'address' },
                { label: '详细地址', field: 'detailedAddress' },
                { label: '客户行业', field: 'customerIndustry', component: 'el-select', props: {
                    options: [
                        { label: 'IT', value: 'IT' },
                        { label: '金融', value: '金融' },
                        { label: '教育', value: '教育' },
                        { label: '其他', value: '其他' }
                    ]
                }},
                { label: '客户级别', field: 'customerLevel', component: 'el-select', props: {
                    options: [
                        { label: 'A', value: 'A' },
                        { label: 'B', value: 'B' },
                        { label: 'C', value: 'C' },
                        { label: 'D', value: 'D' }
                    ]
                }},
                { label: '下次联系时间', field: 'nextContactTime', component: 'el-date-picker', props: {
                    type: 'datetime',
                    placeholder: '选择下次联系时间'
                }},
                { label: '备注', field: 'remarks', component: 'el-input', props: {
                    type: 'textarea',
                    rows: 3
                }}
            ],
            // 系统信息字段配置
            systemFields: [
                { label: '负责人', field: 'responsiblePersonId' },
                { label: '创建时间', field: 'createTime' },
                { label: '状态', field: 'status', component: 'el-select', props: {
                    options: [
                        { label: '未分配', value: '0' },
                        { label: '已分配', value: '1' },
                        { label: '跟进中', value: '2' },
                        { label: '已转化', value: '3' },
                        { label: '已失效', value: '4' }
                    ]
                }},
                { label: '删除标记', field: 'delFlag' }
            ]
        };
    }
};
</script>

<style scoped>
.lead-details-container {
    padding: 20px;
}

:deep(.el-card) {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

:deep(.el-card:hover) {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

:deep(.el-card__header) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(247, 248, 250, 0.6);
    backdrop-filter: blur(5px);
    padding: 16px 20px;
}

:deep(.el-form-item) {
    backdrop-filter: blur(5px);
    border-radius: 8px;
    transition: all 0.3s ease;
}

:deep(.el-form-item:hover) {
    background: rgba(247, 248, 250, 0.3);
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-textarea__wrapper) {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: none;
    transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover),
:deep(.el-textarea__wrapper:hover) {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--el-color-primary-light-5);
}

:deep(.el-input__wrapper:focus-within),
:deep(.el-select .el-input__wrapper:focus-within),
:deep(.el-textarea__wrapper:focus-within) {
    background: #ffffff;
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-5);
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
}

:deep(.el-card__body) {
    padding: 24px;
}

:deep(.section-title) {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        display: inline-block;
    }
}
</style> 