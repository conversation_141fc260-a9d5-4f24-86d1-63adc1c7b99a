package com.ruoyi.crm.service;

import java.util.List;
import java.util.Map;
import java.util.Date;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;

/**
 * 线索分配记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface ICrmLeadAssignmentRecordService {
    
    /**
     * 查询线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 线索分配记录
     */
    CrmLeadAssignmentRecord selectCrmLeadAssignmentRecordById(Long id);

    /**
     * 查询线索分配记录列表
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 线索分配记录集合
     */
    List<CrmLeadAssignmentRecord> selectCrmLeadAssignmentRecordList(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 新增线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    int insertCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 修改线索分配记录
     * 
     * @param crmLeadAssignmentRecord 线索分配记录
     * @return 结果
     */
    int updateCrmLeadAssignmentRecord(CrmLeadAssignmentRecord crmLeadAssignmentRecord);

    /**
     * 批量删除线索分配记录
     * 
     * @param ids 需要删除的线索分配记录主键集合
     * @return 结果
     */
    int deleteCrmLeadAssignmentRecordByIds(Long[] ids);

    /**
     * 删除线索分配记录信息
     * 
     * @param id 线索分配记录主键
     * @return 结果
     */
    int deleteCrmLeadAssignmentRecordById(Long id);

    /**
     * 根据线索ID查询分配记录列表
     * 
     * @param leadId 线索ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByLeadId(Long leadId);

    /**
     * 根据用户ID查询分配记录列表（作为分配对象）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByToUserId(Long userId);

    /**
     * 根据用户ID查询分配记录列表（作为原负责人）
     * 
     * @param userId 用户ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByFromUserId(Long userId);

    /**
     * 根据操作人ID查询分配记录列表
     * 
     * @param operatorId 操作人ID
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByOperatorId(Long operatorId);

    /**
     * 根据分配类型查询分配记录列表
     * 
     * @param assignmentType 分配类型
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByAssignmentType(String assignmentType);

    /**
     * 根据时间范围查询分配记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配记录集合
     */
    List<CrmLeadAssignmentRecord> getRecordsByTimeRange(Date startTime, Date endTime);

    /**
     * 创建手动分配记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param toUserId 分配给的用户ID
     * @param reason 分配原因
     * @return 结果
     */
    int createManualAssignmentRecord(Long leadId, Long poolId, Long toUserId, String reason);

    /**
     * 创建抢单记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param toUserId 抢单用户ID
     * @param reason 抢单原因
     * @return 结果
     */
    int createGrabAssignmentRecord(Long leadId, Long poolId, Long toUserId, String reason);

    /**
     * 创建回收记录
     * 
     * @param leadId 线索ID
     * @param poolId 线索池ID
     * @param fromUserId 原负责人ID
     * @param reason 回收原因
     * @return 结果
     */
    int createRecycleRecord(Long leadId, Long poolId, Long fromUserId, String reason);

    /**
     * 获取分配记录统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getAssignmentRecordStats();

    /**
     * 统计各分配类型的记录数量
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByAssignmentType();

    /**
     * 统计各用户的分配数量（作为分配对象）
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByToUser();

    /**
     * 统计各操作人的操作数量
     * 
     * @return 统计结果
     */
    List<CrmLeadAssignmentRecord> countRecordsByOperator();

    /**
     * 统计指定时间范围内的分配数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分配数量
     */
    int countRecordsByTimeRange(Date startTime, Date endTime);

    /**
     * 获取最近的分配记录
     * 
     * @param leadId 线索ID
     * @return 最近的分配记录
     */
    CrmLeadAssignmentRecord getLatestRecordByLeadId(Long leadId);
}
