<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmContactFollowupRecordsMapper">
    
    <resultMap type="CrmContactFollowupRecords" id="CrmContactFollowupRecordsResult">
        <result property="id"                    column="id"                    />
        <result property="contactId"             column="contact_id"            />
        <result property="followUpContent"       column="follow_up_content"     />
        <result property="nextContactMethod"     column="next_contact_method"   />
        <result property="followUpMethod"        column="follow_up_method"      />
        <result property="nextContactTime"       column="next_contact_time"     />
        <result property="communicationResult"   column="communication_result"  />
        <result property="meetingSummary"        column="meeting_summary"       />
        <result property="contactQuality"        column="contact_quality"       />
        <result property="relatedFiles"          column="related_files"        />
        <result property="creatorId"             column="creator_id"           />
        <result property="createdAt"             column="created_at"           />
        <result property="updatedAt"             column="updated_at"           />
    </resultMap>

    <sql id="selectCrmContactFollowupRecordsVo">
        select id, contact_id, follow_up_content, next_contact_method, follow_up_method, 
               next_contact_time, communication_result, meeting_summary, contact_quality, 
               related_files, creator_id, created_at, updated_at
        from crm_contact_followup_records
    </sql>

    <select id="selectCrmContactFollowupRecordsList" parameterType="CrmContactFollowupRecords" resultMap="CrmContactFollowupRecordsResult">
        <include refid="selectCrmContactFollowupRecordsVo"/>
        <where>  
            <if test="contactId != null "> and contact_id = #{contactId}</if>
            <if test="followUpContent != null  and followUpContent != ''"> and follow_up_content like concat('%', #{followUpContent}, '%')</if>
            <if test="nextContactMethod != null  and nextContactMethod != ''"> and next_contact_method = #{nextContactMethod}</if>
            <if test="followUpMethod != null  and followUpMethod != ''"> and follow_up_method = #{followUpMethod}</if>
            <if test="nextContactTime != null "> and next_contact_time = #{nextContactTime}</if>
            <if test="communicationResult != null  and communicationResult != ''"> and communication_result like concat('%', #{communicationResult}, '%')</if>
            <if test="contactQuality != null  and contactQuality != ''"> and contact_quality = #{contactQuality}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(created_at,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(created_at,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectCrmContactFollowupRecordsById" parameterType="Long" resultMap="CrmContactFollowupRecordsResult">
        <include refid="selectCrmContactFollowupRecordsVo"/>
        where id = #{id}
    </select>

    <select id="selectByContactId" parameterType="Long" resultMap="CrmContactFollowupRecordsResult">
        <include refid="selectCrmContactFollowupRecordsVo"/>
        where contact_id = #{contactId}
        order by created_at desc
    </select>

    <select id="countByContactId" parameterType="Long" resultType="int">
        select count(*) from crm_contact_followup_records where contact_id = #{contactId}
    </select>

    <select id="selectLatestByContactId" parameterType="Long" resultMap="CrmContactFollowupRecordsResult">
        <include refid="selectCrmContactFollowupRecordsVo"/>
        where contact_id = #{contactId}
        order by created_at desc
        limit 1
    </select>
        
    <insert id="insertCrmContactFollowupRecords" parameterType="CrmContactFollowupRecords" useGeneratedKeys="true" keyProperty="id">
        insert into crm_contact_followup_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactId != null">contact_id,</if>
            <if test="followUpContent != null and followUpContent != ''">follow_up_content,</if>
            <if test="nextContactMethod != null and nextContactMethod != ''">next_contact_method,</if>
            <if test="followUpMethod != null and followUpMethod != ''">follow_up_method,</if>
            <if test="nextContactTime != null">next_contact_time,</if>
            <if test="communicationResult != null and communicationResult != ''">communication_result,</if>
            <if test="meetingSummary != null and meetingSummary != ''">meeting_summary,</if>
            <if test="contactQuality != null and contactQuality != ''">contact_quality,</if>
            <if test="relatedFiles != null and relatedFiles != ''">related_files,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactId != null">#{contactId},</if>
            <if test="followUpContent != null and followUpContent != ''">#{followUpContent},</if>
            <if test="nextContactMethod != null and nextContactMethod != ''">#{nextContactMethod},</if>
            <if test="followUpMethod != null and followUpMethod != ''">#{followUpMethod},</if>
            <if test="nextContactTime != null">#{nextContactTime},</if>
            <if test="communicationResult != null and communicationResult != ''">#{communicationResult},</if>
            <if test="meetingSummary != null and meetingSummary != ''">#{meetingSummary},</if>
            <if test="contactQuality != null and contactQuality != ''">#{contactQuality},</if>
            <if test="relatedFiles != null and relatedFiles != ''">#{relatedFiles},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmContactFollowupRecords" parameterType="CrmContactFollowupRecords">
        update crm_contact_followup_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="followUpContent != null and followUpContent != ''">follow_up_content = #{followUpContent},</if>
            <if test="nextContactMethod != null">next_contact_method = #{nextContactMethod},</if>
            <if test="followUpMethod != null">follow_up_method = #{followUpMethod},</if>
            <if test="nextContactTime != null">next_contact_time = #{nextContactTime},</if>
            <if test="communicationResult != null">communication_result = #{communicationResult},</if>
            <if test="meetingSummary != null">meeting_summary = #{meetingSummary},</if>
            <if test="contactQuality != null">contact_quality = #{contactQuality},</if>
            <if test="relatedFiles != null">related_files = #{relatedFiles},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmContactFollowupRecordsById" parameterType="Long">
        delete from crm_contact_followup_records where id = #{id}
    </delete>

    <delete id="deleteCrmContactFollowupRecordsByIds" parameterType="String">
        delete from crm_contact_followup_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
