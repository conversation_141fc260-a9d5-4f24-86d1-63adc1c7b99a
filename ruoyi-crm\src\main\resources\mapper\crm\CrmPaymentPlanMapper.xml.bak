<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPaymentPlanMapper">
    
    <resultMap type="CrmPaymentPlan" id="PaymentPlanResult">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="planName" column="plan_name"/>
        <result property="planAmount" column="plan_amount"/>
        <result property="planDate" column="plan_date"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPaymentPlanVo">
        select id, contract_id, customer_id, plan_name, plan_amount, plan_date,
        status, remark, create_by, create_time, update_by, update_time
        from crm_business_payment_plans
    </sql>

    <select id="selectPaymentPlanList" parameterType="CrmPaymentPlan" resultMap="PaymentPlanResult">
        <include refid="selectPaymentPlanVo"/>
        <where>
            <if test="contractId != null">
                AND contract_id = #{contractId}
            </if>
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="planName != null and planName != ''">
                AND plan_name like concat('%', #{planName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="planDate != null">
                AND plan_date = #{planDate}
            </if>
        </where>
    </select>
    
    <select id="selectPaymentPlanById" parameterType="Long" resultMap="PaymentPlanResult">
        <include refid="selectPaymentPlanVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentPlanByContractId" parameterType="Long" resultMap="PaymentPlanResult">
        <include refid="selectPaymentPlanVo"/>
        where contract_id = #{contractId}
    </select>
        
    <insert id="insertPaymentPlan" parameterType="CrmPaymentPlan" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_payment_plans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractId != null">contract_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planAmount != null">plan_amount,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractId != null">#{contractId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planAmount != null">#{planAmount},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePaymentPlan" parameterType="CrmPaymentPlan">
        update crm_business_payment_plans
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planAmount != null">plan_amount = #{planAmount},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentPlanById" parameterType="Long">
        delete from crm_business_payment_plans where id = #{id}
    </delete>

    <delete id="deletePaymentPlanByIds" parameterType="String">
        delete from crm_business_payment_plans where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 