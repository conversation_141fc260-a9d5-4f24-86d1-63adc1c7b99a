<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM系统无密码用户注册登录完整设计方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 25px;
        }
        .mermaid {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .scenario {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        .issue {
            background-color: #ffeaa7;
            border-left: 4px solid #fdcb6e;
            padding: 15px;
            margin: 15px 0;
        }
        .passwordless {
            background-color: #e8f4fd;
            border: 2px solid #3498db;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        ul, ol {
            padding-left: 30px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet" />
    <script>
        mermaid.initialize({startOnLoad:true});
    </script>
</head>
<body>
    <div class="container">
        <h1>CRM系统无密码用户注册登录完整设计方案</h1>
        
        <div class="passwordless">
            <h2>💡 核心设计理念：无密码认证系统</h2>
            <p>本系统采用现代化的无密码认证机制，用户无需记住任何密码，只需通过安全便捷的认证方式即可访问系统：</p>
            <ul>
                <li>🔐 <strong>企业微信OAuth认证</strong> - 内部员工通过企业微信身份验证</li>
                <li>📱 <strong>手机短信验证</strong> - 客户通过手机验证码验证身份</li>
                <li>🚫 <strong>完全无密码</strong> - 系统不涉及任何密码设置和管理</li>
            </ul>
        </div>
        
        <h2>1. 系统概述</h2>
        <p>CRM系统采用无密码认证架构，支持多种用户注册和登录方式，包括内部工作人员（销售、财务等）和外部客户用户。系统设计基于现代用户体验理念：<strong>没有人愿意记住密码</strong>。</p>
        
        <div class="highlight">
            <h3>设计目标</h3>
            <ul>
                <li><strong>内部员工：</strong>通过企业微信扫码登录/注册，绑定手机号，完全无密码</li>
                <li><strong>外部客户：</strong>通过手机短信验证码"润物无声"式注册，无需任何密码</li>
                <li><strong>统一管理：</strong>所有用户都以手机号作为最终唯一标识</li>
                <li><strong>安全便捷：</strong>基于OAuth和短信验证的双重安全保障</li>
            </ul>
        </div>

        <h2>2. 现有表结构分析</h2>
        <table>
            <tr>
                <th>表名</th>
                <th>用途</th>
                <th>关键字段</th>
                <th>密码相关字段</th>
            </tr>
            <tr>
                <td>sys_user</td>
                <td>系统用户基础信息</td>
                <td>user_id, user_name, phonenumber, email</td>
                <td>password（内部员工保留，客户为空）</td>
            </tr>
            <tr>
                <td>crm_thirdparty_wechat</td>
                <td>企业微信第三方登录关联</td>
                <td>user_id, wecom_user_id</td>
                <td>无</td>
            </tr>
            <tr>
                <td>crm_wecom_config</td>
                <td>企业微信配置</td>
                <td>corp_id, corp_secret, agent_id</td>
                <td>无</td>
            </tr>
        </table>

        <h2>3. 新增表设计方案</h2>
        
        <h3>3.1 用户注册表 (crm_user_registration)</h3>
        <pre><code class="language-sql">CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码（加密存储）',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `passwordless_token` varchar(255) DEFAULT NULL COMMENT '无密码登录令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表（无密码设计）';</code></pre>

        <h3>3.2 用户认证方式表 (crm_user_auth_methods)</h3>
        <pre><code class="language-sql">CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `auth_token` varchar(500) DEFAULT NULL COMMENT '认证令牌（无密码登录）',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`)
) ENGINE=InnoDB COMMENT='用户认证方式表（支持无密码认证）';</code></pre>

        <h3>3.3 客户线索表增强 (crm_customer_leads)</h3>
        <pre><code class="language-sql">CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `registration_method` varchar(20) DEFAULT 'sms' COMMENT '注册方式：sms-短信无密码注册',
  `access_token` varchar(255) DEFAULT NULL COMMENT '客户专用访问令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表（无密码注册）';</code></pre>

        <h3>3.4 线索文件表 (crm_lead_files)</h3>
        <pre><code class="language-sql">CREATE TABLE `crm_lead_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_url` varchar(500) NOT NULL COMMENT '文件访问URL',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `upload_status` varchar(20) DEFAULT 'success' COMMENT '上传状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`)
) ENGINE=InnoDB COMMENT='线索文件表';</code></pre>

        <h3>3.5 沟通记录表 (crm_lead_communications)</h3>
        <pre><code class="language-sql">CREATE TABLE `crm_lead_communications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '沟通记录ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `sales_user_id` bigint(20) NOT NULL COMMENT '销售人员ID',
  `communication_type` varchar(50) NOT NULL COMMENT '沟通类型：phone-电话,email-邮件,message-留言',
  `content` text NOT NULL COMMENT '沟通内容',
  `communication_time` datetime NOT NULL COMMENT '沟通时间',
  `status` varchar(20) DEFAULT 'completed' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_sales_user_id` (`sales_user_id`),
  KEY `idx_communication_time` (`communication_time`)
) ENGINE=InnoDB COMMENT='沟通记录表';</code></pre>

        <h2>4. 完整业务流程设计</h2>

        <h3>4.1 企业微信员工无密码注册登录流程</h3>
        <div class="mermaid">
flowchart TD
    A[员工打开CRM系统] --> B[点击企业微信登录]
    B --> C[跳转到企业微信授权页面]
    C --> D[用户确认授权]
    D --> E[获取企业微信用户信息]
    E --> F{是否已注册?}
    
    F -->|是| G[检查手机号绑定状态]
    G --> H{手机号已绑定?}
    H -->|是| I[直接无密码登录成功]
    H -->|否| J[提示绑定手机号]
    
    F -->|否| K[创建注册记录]
    K --> J
    
    J --> L[用户输入手机号]
    L --> M[发送短信验证码]
    M --> N[用户输入验证码]
    N --> O{验证码正确?}
    
    O -->|否| P[提示错误，重新输入]
    P --> N
    
    O -->|是| Q[创建用户账户（无密码）]
    Q --> R[绑定企业微信和手机号]
    R --> S[生成无密码登录令牌]
    S --> T[注册完成，自动登录]
    
    I --> U[进入系统首页]
    T --> U
    
    style Q fill:#e8f4fd
    style S fill:#e8f4fd
    style T fill:#e8f4fd
</div>

        <h3>4.2 客户用户"润物无声"无密码注册完整流程</h3>
        <div class="mermaid">
flowchart TD
    A[客户访问前端上传页面] --> B[查看页面介绍和演示]
    B --> C[选择3D文件上传]
    C --> D[填写基本信息]
    D --> E[输入手机号码]
    E --> F[点击获取验证码]
    F --> G[系统发送短信验证码]
    G --> H[系统自动创建注册记录]
    H --> I[用户输入验证码]
    I --> J{验证码正确?}
    
    J -->|否| K[提示错误]
    K --> I
    
    J -->|是| L[系统自动创建用户账户（完全无密码）]
    L --> M[生成无密码访问令牌]
    M --> N[创建客户线索记录]
    N --> O[文件上传成功]
    O --> P[显示上传成功页面]
    P --> Q[提供客户专用访问链接]
    Q --> R[发送确认短信（含访问链接）]
    
    R --> S[后台销售人员收到线索通知]
    S --> T[系统自动分配给销售人员]
    T --> U[销售人员查看线索详情]
    U --> V[销售人员进行评估]
    V --> W[通过注册手机号联系客户]
    W --> X[客户通过专用链接查看进度]
    
    style L fill:#e8f4fd
    style M fill:#e8f4fd
    style Q fill:#e8f4fd
    style R fill:#e8f4fd
    style X fill:#e8f5e8
</div>

        <h3>4.3 客户后续访问和跟进流程</h3>
        <div class="mermaid">
flowchart TD
    A[客户收到短信通知] --> B[点击专用访问链接]
    B --> C[系统验证访问令牌]
    C --> D{令牌有效?}
    
    D -->|否| E[提示重新验证手机号]
    E --> F[发送新的验证码]
    F --> G[客户输入验证码]
    G --> H[重新生成访问令牌]
    H --> I[进入客户专区]
    
    D -->|是| I[进入客户专区]
    I --> J[查看项目进度]
    J --> K[查看评估结果]
    K --> L[查看销售人员联系方式]
    L --> M[在线留言或反馈]
    M --> N[系统通知销售人员]
    
    N --> O[销售人员回复客户]
    O --> P[客户收到回复通知]
    P --> Q[客户再次访问专区]
    Q --> R[查看最新回复]
    
    style I fill:#e8f5e8
    style J fill:#e8f5e8
    style K fill:#e8f5e8
    style L fill:#e8f5e8
    style M fill:#e8f5e8
    style Q fill:#e8f5e8
    style R fill:#e8f5e8
    style N fill:#fff3cd
    style O fill:#fff3cd
</div>

        <h3>4.4 销售人员处理客户线索完整流程</h3>
        <div class="mermaid">
flowchart TD
    A[系统接收到客户线索] --> B[线索智能分配算法]
    B --> C[根据地区/产品类型分配]
    C --> D[销售人员收到通知]
    D --> E[销售人员企业微信登录系统]
    E --> F[查看待处理线索列表]
    F --> G[点击查看线索详情]
    G --> H[查看客户基本信息]
    H --> I[下载客户上传的3D文件]
    I --> J[进行技术可行性评估]
    J --> K[计算成本和报价]
    K --> L[填写评估报告]
    L --> M{是否有合作机会?}
    
    M -->|否| N[标记线索为已关闭]
    N --> O[填写关闭原因]
    O --> P[系统记录并归档]
    
    M -->|是| Q[通过手机号联系客户]
    Q --> R[电话沟通详细需求]
    R --> S[记录沟通内容]
    S --> T[为客户提供专用访问链接]
    T --> U[在客户专区上传详细方案]
    U --> V[安排后续跟进计划]
    V --> W[设置提醒任务]
    W --> X[线索转化为商机]
    
    P --> Y[线索处理完成]
    X --> Z[进入商机管理流程]
    
    style E fill:#fff3cd
    style F fill:#fff3cd
    style G fill:#fff3cd
    style H fill:#fff3cd
    style I fill:#fff3cd
    style J fill:#fff3cd
    style K fill:#fff3cd
    style L fill:#fff3cd
    style Q fill:#fff3cd
    style R fill:#fff3cd
    style S fill:#fff3cd
    style T fill:#fff3cd
    style U fill:#fff3cd
    style V fill:#fff3cd
    style W fill:#fff3cd
    style A fill:#f8f9fa
    style B fill:#f8f9fa
    style C fill:#f8f9fa
    style D fill:#f8f9fa
    style P fill:#f8f9fa
    style X fill:#f8f9fa
    style Z fill:#f8f9fa
</div>

        <h2>5. 完整时序图设计</h2>

        <h3>5.1 企业微信无密码登录详细时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端页面
    participant B as 后端服务
    participant W as 企业微信API
    participant D as 数据库
    participant SMS as 短信服务
    
    Note over U,SMS: 企业微信无密码认证完整流程
    
    U->>F: 点击企业微信登录
    F->>W: 跳转到企业微信授权页面
    W->>U: 显示授权确认页面
    U->>W: 确认授权（无需密码）
    W->>F: 返回授权码
    F->>B: 发送授权码
    B->>W: 使用授权码获取用户信息
    W->>B: 返回用户信息(unionid, name等)
    B->>D: 查询crm_thirdparty_wechat表
    
    alt 用户已存在且手机号已绑定
        D->>B: 返回完整用户信息
        B->>D: 更新最后登录时间
        B->>B: 生成无密码JWT token
        B->>F: 登录成功，返回JWT token
        F->>U: 跳转到系统首页
    else 用户已存在但手机号未绑定
        D->>B: 返回用户信息（手机号为空）
        B->>F: 返回需要绑定手机号提示
        F->>U: 显示手机号绑定页面
        Note over U,SMS: 手机号绑定流程
        U->>F: 输入手机号（无需设置密码）
        F->>B: 提交手机号
        B->>SMS: 发送短信验证码
        B->>D: 更新注册记录
        B->>F: 返回验证码已发送
        F->>U: 显示验证码输入框
        U->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验
        B->>D: 更新用户手机号
        B->>D: 创建crm_user_auth_methods记录
        B->>B: 生成无密码JWT token
        B->>F: 绑定成功，返回JWT token
        F->>U: 跳转到系统首页
    else 用户不存在
        B->>D: 创建注册记录到crm_user_registration
        B->>F: 返回需要完成注册提示
        F->>U: 显示注册页面
        Note over U,SMS: 新用户注册流程
        U->>F: 输入手机号和基本信息
        F->>B: 提交注册信息
        B->>SMS: 发送短信验证码
        B->>D: 更新注册记录状态
        B->>F: 返回验证码已发送
        F->>U: 显示验证码输入框
        U->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验
        B->>D: 创建sys_user记录（password字段为空）
        B->>D: 创建crm_thirdparty_wechat关联
        B->>D: 创建crm_user_auth_methods记录
        B->>D: 更新注册记录状态为completed
        B->>B: 生成无密码JWT token
        B->>F: 注册成功，返回JWT token
        F->>U: 跳转到系统首页
    end
</div>

        <h3>5.2 客户无密码注册详细时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant C as 客户
    participant F as 前端页面
    participant B as 后端服务
    participant S as 短信服务
    participant D as 数据库
    participant File as 文件存储
    participant Sales as 销售系统
    participant Email as 邮件服务
    
    Note over C,Email: 客户完全无密码注册流程
    
    C->>F: 访问文件上传页面
    F->>C: 显示上传表单和说明
    C->>F: 阅读服务说明
    C->>F: 选择3D文件
    F->>C: 显示文件预览
    C->>F: 填写项目基本信息
    C->>F: 输入手机号（无需设置密码）
    F->>B: 提交手机号请求验证码
    
    B->>D: 检查手机号是否已注册
    alt 手机号已存在
        D->>B: 返回已存在用户信息
        B->>S: 发送登录验证码
        B->>F: 返回"验证码已发送到已注册手机"
    else 手机号不存在
        B->>S: 发送注册验证码
        B->>D: 创建注册记录(pending状态)
        B->>F: 返回"验证码已发送"
    end
    
    F->>C: 显示验证码输入框
    C->>F: 输入验证码
    F->>B: 提交验证码和文件
    B->>B: 验证码校验
    
    alt 验证码正确
        alt 新用户注册
            B->>D: 创建sys_user记录（password字段为NULL）
            B->>D: 创建crm_user_auth_methods记录（无密码认证）
            B->>D: 更新注册记录状态为completed
        else 已有用户登录
            B->>D: 获取用户信息
        end
        
        B->>File: 上传3D文件到存储系统
        File->>B: 返回文件访问URL
        B->>D: 创建crm_customer_leads记录
        B->>D: 创建crm_lead_files记录
        B->>B: 生成客户专用无密码访问令牌
        B->>D: 保存访问令牌到leads表
        B->>F: 上传成功，返回专用访问链接
        F->>C: 显示成功页面和专用访问链接
        
        Note over B,Email: 后台处理流程
        B->>Sales: 发送新线索通知
        B->>S: 发送确认短信给客户（含访问链接）
        B->>Email: 发送详细信息邮件给客户（可选）
        
        Sales->>Sales: 智能分配给合适的销售人员
        Sales->>D: 更新线索分配信息
        Sales->>B: 发送分配通知给销售人员
        
    else 验证码错误
        B->>F: 返回验证码错误
        F->>C: 提示重新输入
    end
</div>

        <h3>5.3 客户访问专区时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant C as 客户
    participant F as 前端页面
    participant B as 后端服务
    participant D as 数据库
    participant S as 短信服务
    
    Note over C,S: 客户专区无密码访问流程
    
    C->>F: 点击短信中的专用链接
    F->>B: 发送访问令牌
    B->>D: 验证访问令牌
    
    alt 令牌有效
        D->>B: 返回客户和线索信息
        B->>F: 返回客户专区数据
        F->>C: 显示客户专区首页
        
        C->>F: 查看项目进度
        F->>B: 请求项目详情
        B->>D: 查询线索处理状态
        D->>B: 返回最新进度信息
        B->>F: 返回进度数据
        F->>C: 显示项目进度页面
        
        C->>F: 查看评估结果
        F->>B: 请求评估报告
        B->>D: 查询评估结果
        D->>B: 返回评估数据
        B->>F: 返回评估结果
        F->>C: 显示评估报告页面
        
        C->>F: 在线留言
        F->>B: 提交留言内容
        B->>D: 保存客户留言到crm_lead_communications
        B->>B: 通知分配的销售人员
        B->>F: 返回留言成功
        F->>C: 显示留言提交成功
        
    else 令牌过期或无效
        B->>F: 返回需要重新验证
        F->>C: 显示重新验证页面
        C->>F: 输入手机号
        F->>B: 请求新的验证码
        B->>S: 发送验证码
        B->>F: 返回验证码已发送
        F->>C: 显示验证码输入框
        C->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验成功
        B->>B: 生成新的访问令牌
        B->>D: 更新令牌信息
        B->>F: 返回新的访问令牌
        F->>C: 自动跳转到客户专区
    end
</div>

        <h3>5.4 销售人员处理线索详细时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant S as 销售人员
    participant CRM as CRM系统
    participant D as 数据库
    participant File as 文件存储
    participant Phone as 电话系统
    participant C as 客户
    participant SMS as 短信服务
    
    Note over S,SMS: 销售人员处理客户线索完整流程
    
    CRM->>S: 推送新线索通知
    S->>CRM: 企业微信扫码登录
    CRM->>D: 验证销售人员身份
    D->>CRM: 返回销售人员信息
    CRM->>S: 显示系统首页
    
    S->>CRM: 点击线索管理
    CRM->>D: 查询分配给该销售的线索
    D->>CRM: 返回线索列表
    CRM->>S: 显示线索列表页面
    
    S->>CRM: 点击具体线索
    CRM->>D: 查询线索详细信息
    D->>CRM: 返回客户信息、文件信息等
    CRM->>S: 显示线索详情页面
    
    S->>CRM: 下载客户3D文件
    CRM->>D: 查询crm_lead_files表
    D->>CRM: 返回文件信息
    CRM->>File: 请求文件下载
    File->>CRM: 返回文件流
    CRM->>S: 下载文件到本地
    
    S->>S: 线下分析3D文件
    S->>S: 评估技术可行性
    S->>S: 计算成本和报价
    
    S->>CRM: 填写评估报告
    CRM->>D: 保存评估结果到crm_customer_leads
    D->>CRM: 确认保存成功
    CRM->>S: 显示保存成功提示
    
    alt 有合作机会
        S->>Phone: 拨打客户电话
        Phone->>C: 电话接通
        S->>C: 介绍自己和公司
        S->>C: 说明项目评估结果
        C->>S: 询问详细方案和报价
        S->>C: 详细沟通技术方案
        C->>S: 表达合作意向
        
        S->>CRM: 记录沟通内容
        CRM->>D: 保存沟通记录到crm_lead_communications
        
        S->>CRM: 上传详细方案到客户专区
        CRM->>File: 上传方案文件
        File->>CRM: 返回文件URL
        CRM->>D: 更新客户专区内容
        
        S->>CRM: 发送方案通知给客户
        CRM->>SMS: 发送短信通知客户查看
        SMS->>C: 发送短信（含专区链接）
        
        S->>CRM: 设置跟进提醒
        CRM->>D: 创建跟进任务
        
        S->>CRM: 将线索转化为商机
        CRM->>D: 创建商机记录
        CRM->>D: 更新线索状态为已转化
        
    else 无合作机会
        S->>CRM: 标记线索为已关闭
        S->>CRM: 填写关闭原因
        CRM->>D: 更新线索状态为已关闭
        CRM->>D: 保存关闭原因
        
        opt 礼貌告知客户
            S->>Phone: 拨打客户电话
            S->>C: 礼貌说明无法合作的原因
            S->>C: 感谢客户的信任
            S->>CRM: 记录告知情况
            CRM->>D: 保存沟通记录
        end
    end
</div>

        <h2>6. 无密码表关系设计</h2>
        <div class="mermaid">
erDiagram
    sys_user {
        bigint user_id PK
        varchar user_name
        varchar nick_name
        varchar phonenumber
        varchar email
        varchar password "内部员工保留，客户为NULL"
        char status
        datetime create_time
        datetime update_time
    }
    
    crm_user_registration {
        bigint id PK
        varchar phone_number
        varchar registration_type
        varchar wechat_union_id
        varchar verification_code "加密存储"
        datetime verification_expire_time
        tinyint is_verified
        bigint user_id FK
        varchar status
        varchar user_type
        varchar passwordless_token "无密码登录令牌"
        json source_data
        datetime create_time
        datetime update_time
    }
    
    crm_user_auth_methods {
        bigint id PK
        bigint user_id FK
        varchar auth_type
        varchar auth_identifier
        tinyint is_primary
        tinyint is_active
        datetime bind_time
        datetime last_used_time
        varchar auth_token "无密码认证令牌"
        datetime token_expire_time
        json metadata
        datetime create_time
        datetime update_time
    }
    
    crm_thirdparty_wechat {
        bigint id PK
        bigint user_id FK
        varchar wecom_user_id
        datetime create_time
        datetime update_time
    }
    
    crm_customer_leads {
        bigint id PK
        bigint user_id FK
        varchar phone_number
        varchar source_type
        json lead_data
        varchar status
        bigint assigned_to FK
        text evaluation_result
        text contact_notes
        varchar registration_method "无密码注册方式"
        varchar access_token "客户专用访问令牌"
        datetime create_time
        datetime update_time
    }
    
    crm_lead_files {
        bigint id PK
        bigint lead_id FK
        varchar file_name
        varchar file_url
        varchar file_type
        bigint file_size
        varchar upload_status
        datetime create_time
    }
    
    crm_lead_communications {
        bigint id PK
        bigint lead_id FK
        bigint sales_user_id FK
        varchar communication_type
        text content
        datetime communication_time
        varchar status
    }
    
    sys_user ||--o{ crm_user_registration : "一个用户可能有多个注册记录"
    sys_user ||--o{ crm_user_auth_methods : "一个用户可以有多种认证方式"
    sys_user ||--o| crm_thirdparty_wechat : "一对一企业微信绑定"
    sys_user ||--o{ crm_customer_leads : "一个用户可能对应多个线索"
    crm_user_registration ||--|| sys_user : "注册记录关联用户"
    crm_customer_leads ||--o{ crm_lead_files : "一个线索包含多个文件"
    crm_customer_leads ||--o{ crm_lead_communications : "一个线索有多次沟通记录"
    sys_user ||--o{ crm_lead_communications : "销售人员参与沟通"
</div>

        <h2>7. 无密码关键技术点</h2>

        <div class="passwordless">
            <h3>7.1 企业微信无密码集成</h3>
            <ul>
                <li><strong>OAuth2.0授权流程：</strong>使用企业微信提供的OAuth接口，完全不涉及密码</li>
                <li><strong>用户信息获取：</strong>通过企业微信API获取用户的unionid、用户名等基本信息</li>
                <li><strong>唯一标识：</strong>使用企业微信的unionid作为用户在系统中的唯一标识</li>
                <li><strong>令牌管理：</strong>生成JWT token进行会话管理，无需密码验证</li>
                <li><strong>自动绑定：</strong>首次登录时自动提示绑定手机号，完善用户信息</li>
            </ul>
        </div>

        <div class="passwordless">
            <h3>7.2 短信验证码无密码机制</h3>
            <ul>
                <li><strong>验证码生成：</strong>6位数字随机验证码，有效期5分钟</li>
                <li><strong>频率限制：</strong>同一手机号1分钟内只能发送一条，每天最多10条</li>
                <li><strong>安全策略：</strong>验证码错误超过5次锁定账户10分钟</li>
                <li><strong>无密码存储：</strong>验证成功后直接生成访问令牌，不设置密码</li>
                <li><strong>令牌持久化：</strong>为客户生成长期有效的专用访问令牌</li>
            </ul>
        </div>

        <div class="passwordless">
            <h3>7.3 润物无声无密码注册策略</h3>
            <ul>
                <li><strong>预注册：</strong>用户输入手机号发送验证码时就创建注册记录</li>
                <li><strong>完全无感知：</strong>用户只需要验证手机号，系统自动完成用户创建，无需任何密码</li>
                <li><strong>令牌访问：</strong>注册成功后生成专用访问令牌，用户可通过链接直接访问</li>
                <li><strong>线索转化：</strong>注册成功自动创建客户线索，分配给销售人员</li>
                <li><strong>专区管理：</strong>为每个客户创建专属的项目进度查看专区</li>
            </ul>
        </div>

        <div class="passwordless">
            <h3>7.4 客户专区无密码访问</h3>
            <ul>
                <li><strong>专用链接：</strong>每个客户获得唯一的专用访问链接</li>
                <li><strong>令牌验证：</strong>基于JWT令牌的安全访问机制</li>
                <li><strong>自动续期：</strong>令牌临近过期时自动通过短信验证续期</li>
                <li><strong>权限控制：</strong>客户只能访问自己的项目信息</li>
                <li><strong>实时更新：</strong>销售人员更新信息后客户可实时查看</li>
            </ul>
        </div>

        <h2>8. 无密码安全考虑</h2>

        <div class="issue">
            <h3>8.1 无密码数据安全</h3>
            <ul>
                <li><strong>手机号加密：</strong>存储时对手机号进行加密处理</li>
                <li><strong>验证码加密：</strong>验证码在数据库中加密存储</li>
                <li><strong>令牌安全：</strong>JWT token设置合理有效期，支持刷新机制</li>
                <li><strong>敏感信息脱敏：</strong>日志中的手机号等敏感信息进行脱敏处理</li>
                <li><strong>访问日志：</strong>记录所有无密码访问行为，便于安全审计</li>
            </ul>
        </div>

        <div class="issue">
            <h3>8.2 无密码访问控制</h3>
            <ul>
                <li><strong>角色权限：</strong>区分员工和客户角色，设置不同的系统访问权限</li>
                <li><strong>API权限：</strong>不同类型的API接口设置不同的访问权限</li>
                <li><strong>会话管理：</strong>基于令牌的会话管理，无需密码验证</li>
                <li><strong>设备绑定：</strong>可选的设备绑定机制，增强安全性</li>
                <li><strong>地理位置：</strong>可选的地理位置验证，防止异地恶意访问</li>
            </ul>
        </div>

        <div class="issue">
            <h3>8.3 防止滥用机制</h3>
            <ul>
                <li><strong>验证码限制：</strong>严格的发送频率和次数限制</li>
                <li><strong>IP黑名单：</strong>恶意请求的IP地址自动加入黑名单</li>
                <li><strong>行为分析：</strong>异常访问行为自动检测和预警</li>
                <li><strong>令牌撤销：</strong>支持手动撤销和批量撤销访问令牌</li>
            </ul>
        </div>

        <h2>9. 实施计划</h2>

        <div class="scenario">
            <h3>阶段一：数据库表设计和创建</h3>
            <ul>
                <li>创建新增的五个表：用户注册表、认证方式表、客户线索表、线索文件表、沟通记录表</li>
                <li>建立表之间的外键关系和索引</li>
                <li>清理现有sys_user表中客户用户的密码字段</li>
                <li>迁移现有数据到新表结构</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段二：无密码后端API开发</h3>
            <ul>
                <li>企业微信OAuth无密码登录接口</li>
                <li>短信验证码发送和无密码验证接口</li>
                <li>用户无密码注册和认证接口</li>
                <li>客户线索管理接口</li>
                <li>无密码令牌管理服务</li>
                <li>客户专区访问接口</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段三：前端界面开发</h3>
            <ul>
                <li>企业微信无密码登录页面</li>
                <li>手机号绑定页面（无密码设置）</li>
                <li>客户文件上传页面（无密码注册）</li>
                <li>销售线索管理页面</li>
                <li>客户专用访问页面（无密码访问）</li>
                <li>移动端适配</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段四：集成测试和部署</h3>
            <ul>
                <li>无密码认证流程测试</li>
                <li>安全测试和性能测试</li>
                <li>用户体验测试</li>
                <li>跨浏览器兼容性测试</li>
                <li>生产环境部署</li>
                <li>用户培训和系统上线</li>
            </ul>
        </div>

        <h2>10. 监控和维护</h2>

        <table>
            <tr>
                <th>监控项目</th>
                <th>监控指标</th>
                <th>报警阈值</th>
                <th>处理措施</th>
            </tr>
            <tr>
                <td>短信发送</td>
                <td>发送成功率</td>
                <td>< 95%</td>
                <td>检查短信服务商状态</td>
            </tr>
            <tr>
                <td>企业微信登录</td>
                <td>无密码登录成功率</td>
                <td>< 98%</td>
                <td>检查企业微信API状态</td>
            </tr>
            <tr>
                <td>无密码注册转化</td>
                <td>注册完成率</td>
                <td>< 80%</td>
                <td>优化无密码注册流程</td>
            </tr>
            <tr>
                <td>令牌安全</td>
                <td>令牌滥用率</td>
                <td>> 1%</td>
                <td>加强令牌安全策略</td>
            </tr>
            <tr>
                <td>线索处理</td>
                <td>线索处理时效</td>
                <td>> 24小时</td>
                <td>提醒销售人员处理</td>
            </tr>
            <tr>
                <td>客户专区访问</td>
                <td>访问成功率</td>
                <td>< 95%</td>
                <td>检查专区服务状态</td>
            </tr>
            <tr>
                <td>文件上传</td>
                <td>上传成功率</td>
                <td>< 90%</td>
                <td>检查文件存储服务</td>
            </tr>
        </table>

        <h2>11. 总结</h2>
        <p>本设计方案提供了一个完整的无密码双轨用户注册登录系统，完全摒弃了传统的密码认证机制，既满足了内部员工通过企业微信便捷登录的需求，又实现了外部客户的完全无感知注册体验。</p>
        
        <div class="passwordless">
            <p><strong>核心优势：</strong></p>
            <ul>
                <li>🚫 <strong>完全无密码</strong> - 系统不涉及任何密码设置和管理</li>
                <li>🔄 <strong>双轨认证机制</strong> - 满足不同用户群体需求</li>
                <li>📱 <strong>现代化用户体验</strong> - 基于手机和企业微信的便捷认证</li>
                <li>🔐 <strong>高安全性</strong> - OAuth和短信验证的双重安全保障</li>
                <li>💡 <strong>润物无声注册</strong> - 客户无感知的账户创建过程</li>
                <li>📊 <strong>完整线索管理</strong> - 支持业务发展的线索转化流程</li>
                <li>🏠 <strong>客户专区</strong> - 为每个客户提供专属的项目跟踪空间</li>
                <li>📞 <strong>完整沟通链路</strong> - 从线索到成交的全流程管理</li>
            </ul>
        </div>
        
        <div class="highlight">
            <p><strong>设计理念：</strong>在现代互联网时代，没有人愿意记住密码。我们的系统完全基于这一理念设计，为用户提供最便捷、最安全的无密码认证体验，同时确保业务流程的完整性和高效性。</p>
        </div>
    </div>
</body>
</html>
