import { createApp } from "vue";
import DebugApp from "./DebugApp.vue";

// 导入完整的 Element Plus
import * as Icons from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import "element-plus/theme-chalk/src/message-box.scss"; // 添加MessageBox样式
import 'uno.css';

// 导入路由（简化版）
import { createRouter, createWebHistory } from 'vue-router';

// 创建简化路由
const routes = [
  {
    path: '/',
    redirect: '/debug-messagebox'
  },
  {
    path: '/debug-messagebox',
    name: 'MessageBoxDebug',
    component: () => import('./views/MessageBoxDebug.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

const app = createApp(DebugApp);

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(Icons)) {
  app.component(key, component);
}

// 使用 Element Plus 完整版
app.use(ElementPlus);
app.use(router);

// 全局配置
app.config.globalProperties.$ELEMENT = {
  zIndex: 3000,
  size: 'default'
};

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('应用错误:', err, info);
};

app.mount("#app"); 