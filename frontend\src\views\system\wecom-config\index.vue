<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>企业微信配置</span>
          <el-button type="primary" @click="handleSubmit">保存配置</el-button>
        </div>
      </template>
      
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="企业ID" prop="corpId">
          <el-input v-model="form.corpId" placeholder="请输入企业ID" />
        </el-form-item>
        
        <el-form-item label="应用密钥" prop="corpSecret">
          <el-input v-model="form.corpSecret" placeholder="请输入应用密钥" show-password />
        </el-form-item>
        
        <el-form-item label="应用ID" prop="agentId">
          <el-input v-model="form.agentId" placeholder="请输入应用ID" />
        </el-form-item>
        
        <el-form-item label="API基础URL" prop="apiBaseUrl">
          <el-input v-model="form.apiBaseUrl" placeholder="请输入API基础URL" />
        </el-form-item>
        
        <el-form-item label="回调URI" prop="redirectUri">
          <el-input v-model="form.redirectUri" placeholder="请输入回调URI" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="tips">
        <h4>配置说明：</h4>
        <p>1. 企业ID：登录企业微信后台，在"我的企业"页面查看</p>
        <p>2. 应用密钥：在"应用管理"页面，点击应用，查看Secret</p>
        <p>3. 应用ID：在"应用管理"页面，点击应用，查看AgentId</p>
        <p>4. API基础URL：企业微信API接口地址，默认为https://qyapi.weixin.qq.com</p>
        <p>5. 回调URI：应用的回调地址，需要在企业微信后台配置</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { addWecomConfig, getEnabledWecomConfig, updateWecomConfig } from '@/api/system/wecom-config'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'

const formRef = ref()
const form = ref({
  id: undefined,
  corpId: '',
  corpSecret: '',
  agentId: '',
  apiBaseUrl: 'https://qyapi.weixin.qq.com',
  redirectUri: '',
  status: '0'
})

const rules = {
  corpId: [{ required: true, message: '请输入企业ID', trigger: 'blur' }],
  corpSecret: [{ required: true, message: '请输入应用密钥', trigger: 'blur' }],
  agentId: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
  apiBaseUrl: [{ required: true, message: '请输入API基础URL', trigger: 'blur' }],
  redirectUri: [{ required: true, message: '请输入回调URI', trigger: 'blur' }]
}

// 获取配置
const getConfig = async () => {
  try {
    const res = await getEnabledWecomConfig()
    if (res.data) {
      form.value = res.data
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (form.value.id) {
          await updateWecomConfig(form.value)
        } else {
          await addWecomConfig(form.value)
        }
        ElMessage.success('保存成功')
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tips {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.tips h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.tips p {
  margin: 5px 0;
  color: #909399;
  font-size: 14px;
}
</style> 