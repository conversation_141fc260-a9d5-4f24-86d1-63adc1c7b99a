package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 回款计划实体类
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmPaymentPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 计划ID */
    private Long id;
    
    /** 回款计划编号 */
    @Excel(name = "回款计划编号")
    private String planNumber;
    
    /** 客户ID */
    @Excel(name = "客户ID")
    private Long customerId;
    
    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;
    
    /** 负责人ID */
    @Excel(name = "负责人ID")
    private Long responsibleUserId;
    
    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    private String responsibleUserName;
    
    /** 总回款金额 */
    @Excel(name = "总回款金额")
    private BigDecimal totalAmount;
    
    /** 已回款金额 */
    @Excel(name = "已回款金额")
    private BigDecimal receivedAmount;
    
    /** 剩余回款金额 */
    @Excel(name = "剩余回款金额")
    private BigDecimal remainingAmount;
    
    /** 关联商机ID */
    @Excel(name = "关联商机ID")
    private Long opportunityId;
    
    /** 关联联系人ID */
    @Excel(name = "关联联系人ID")
    private Long contactId;
    
    /** 合同编号 */
    @Excel(name = "合同编号")
    private String contractNumber;
    
    /** 回款方式 */
    @Excel(name = "回款方式")
    private String paymentMethod;
    
    /** 计划状态 */
    @Excel(name = "计划状态")
    private String planStatus;
    
    /** 审批状态 */
    @Excel(name = "审批状态")
    private String approvalStatus;
    
    /** 币种 */
    @Excel(name = "币种")
    private String currency;
    
    /** 汇率 */
    @Excel(name = "汇率")
    private BigDecimal exchangeRate;
    
    /** 父计划ID */
    private Long parentPlanId;
    
    /** 计划类型 */
    @Excel(name = "计划类型")
    private String planType;
    
    /** 风险等级 */
    @Excel(name = "风险等级")
    private String riskLevel;
    
    /** 催收负责人ID */
    private Long collectionUserId;
    
    /** 删除标志（0存在 2删除） */
    private String delFlag;
    
    /** 分期列表 */
    private List<CrmPaymentInstallment> installments;
    
    /** 审批列表 */
    private List<CrmPaymentApproval> approvals;
    
    // --- Activiti工作流字段 ---
    /** 流程实例ID */
    private String processInstanceId;
    
    /** 流程定义Key */
    private String processDefinitionKey;
    
    /** 流程状态 */
    private String processStatus;
    
    /** 当前任务ID */
    private String currentTaskId;
    
    /** 当前任务名称 */
    private String currentTaskName;
    
    /** 当前处理人 */
    private String currentAssignee;
    
    // 兼容旧字段
    /** 合同ID */
    @Deprecated
    private Long contractId;
    
    /** 计划名称 */
    @Deprecated
    private String planName;
    
    /** 计划金额 */
    @Deprecated
    private BigDecimal planAmount;
    
    /** 计划回款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Deprecated
    private Date planDate;
    
    /** 状态 */
    @Deprecated
    private String status;
    
    /** 备注 */
    @Deprecated
    private String remarks;
} 