package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.common.domain.entity.CrmPaymentApproval;

/**
 * 回款计划Service接口
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
public interface ICrmPaymentPlanService {
    /**
     * 查询回款计划列表
     *
     * @param plan 回款计划信息
     * @return 回款计划集合
     */
    List<CrmPaymentPlan> selectPaymentPlanList(CrmPaymentPlan plan);

    /**
     * 查询回款计划详细信息
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    CrmPaymentPlan selectPaymentPlanById(Long id);

    /**
     * 查询包含分期和审批信息的回款计划
     *
     * @param id 回款计划ID
     * @return 回款计划信息
     */
    CrmPaymentPlan selectPaymentPlanWithDetailsById(Long id);

    /**
     * 根据合同ID查询回款计划列表
     *
     * @param contractId 合同ID
     * @return 回款计划集合
     */
    List<CrmPaymentPlan> selectPaymentPlanByContractId(Long contractId);

    /**
     * 新增回款计划（包含分期信息）
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    int insertPaymentPlan(CrmPaymentPlan plan);

    /**
     * 修改回款计划
     *
     * @param plan 回款计划信息
     * @return 结果
     */
    int updatePaymentPlan(CrmPaymentPlan plan);

    /**
     * 删除回款计划信息
     *
     * @param id 回款计划ID
     * @return 结果
     */
    int deletePaymentPlanById(Long id);

    /**
     * 批量删除回款计划信息
     *
     * @param ids 需要删除的回款计划ID数组
     * @return 结果
     */
    int deletePaymentPlanByIds(Long[] ids);

    /**
     * 提交审批
     *
     * @param planId 计划ID
     * @return 结果
     */
    int submitForApproval(Long planId);

    /**
     * 审批处理
     *
     * @param approvalId 审批ID
     * @param approved 是否通过
     * @param comments 审批意见
     * @return 结果
     */
    int processApproval(Long approvalId, boolean approved, String comments);

    /**
     * 生成回款计划编号
     *
     * @return 编号
     */
    String generatePlanNumber();

    /**
     * 更新回款金额统计
     *
     * @param planId 计划ID
     * @return 结果
     */
    int updatePaymentAmounts(Long planId);

    /**
     * 获取客户的回款计划统计
     *
     * @param customerId 客户ID
     * @return 统计信息
     */
    int getPaymentPlanCount(Long customerId);
} 