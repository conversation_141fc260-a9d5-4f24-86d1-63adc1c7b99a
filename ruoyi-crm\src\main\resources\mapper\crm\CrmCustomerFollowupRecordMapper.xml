<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerFollowupRecordMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord" id="CrmCustomerFollowupRecordResult">
        <id     property="id"                    column="id"                    />
        <result property="customerId"            column="customer_id"           />
        <result property="creatorId"            column="creator_id"           />
        <result property="followupType"          column="follow_up_type"         />
        <result property="followupContent"       column="follow_up_content"      />
        <result property="createTime"            column="created_at"            />
        <result property="updateTime"            column="updated_at"            />
        <!-- 关联信息 -->
        <result property="customerName"          column="customer_name"         />
        <result property="userName"              column="user_name"             />
        <result property="userNickName"          column="user_nick_name"        />
    </resultMap>

    <sql id="selectCrmCustomerFollowupRecordVo">
        select r.id, r.customer_id, r.creator_id, r.follow_up_type, r.follow_up_content, 
               r.created_at, r.updated_at,
               c.customer_name, u.user_name, u.nick_name as user_nick_name
        from crm_customer_followup_records r
        left join crm_business_customers c on r.customer_id = c.id
        left join sys_user u on r.creator_id = u.user_id
    </sql>

    <select id="selectCrmCustomerFollowupRecordList" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord" resultMap="CrmCustomerFollowupRecordResult">
        <include refid="selectCrmCustomerFollowupRecordVo"/>
        <where>
            <if test="customerId != null">
                AND r.customer_id = #{customerId}
            </if>
            <if test="creatorId != null">
                AND r.creator_id = #{creatorId}
            </if>
            <if test="followupType != null and followupType != ''">
                AND r.follow_up_type = #{followupType}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND r.created_at &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND r.created_at &lt;= #{params.endTime}
            </if>
        </where>
        ORDER BY r.created_at DESC
    </select>

    <select id="selectCrmCustomerFollowupRecordById" parameterType="Long" resultMap="CrmCustomerFollowupRecordResult">
        <include refid="selectCrmCustomerFollowupRecordVo"/>
        where r.id = #{id}
    </select>

    <select id="selectFollowupRecordsByCustomerId" parameterType="Long" resultMap="CrmCustomerFollowupRecordResult">
        <include refid="selectCrmCustomerFollowupRecordVo"/>
        where r.customer_id = #{customerId}
        ORDER BY r.created_at DESC
    </select>

    <select id="countFollowupRecordsByCustomerId" parameterType="Long" resultType="int">
        select count(*) from crm_customer_followup_records 
        where customer_id = #{customerId}
    </select>

    <select id="selectRecentFollowupRecords" resultMap="CrmCustomerFollowupRecordResult">
        <include refid="selectCrmCustomerFollowupRecordVo"/>
        where r.customer_id = #{customerId}
        ORDER BY r.created_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectPendingFollowupRecords" parameterType="Long" resultMap="CrmCustomerFollowupRecordResult">
        <include refid="selectCrmCustomerFollowupRecordVo"/>
        where r.creator_id = #{creatorId}
        ORDER BY r.created_at DESC
    </select>

    <insert id="insertCrmCustomerFollowupRecord" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord" useGeneratedKeys="true" keyProperty="id">
        insert into crm_customer_followup_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="followupType != null">follow_up_type,</if>
            <if test="followupContent != null">follow_up_content,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="followupType != null">#{followupType},</if>
            <if test="followupContent != null">#{followupContent},</if>
        </trim>
    </insert>

    <update id="updateCrmCustomerFollowupRecord" parameterType="com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord">
        update crm_customer_followup_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="followupType != null">follow_up_type = #{followupType},</if>
            <if test="followupContent != null">follow_up_content = #{followupContent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmCustomerFollowupRecordById" parameterType="Long">
        delete from crm_customer_followup_records where id = #{id}
    </delete>

    <delete id="deleteCrmCustomerFollowupRecordByIds" parameterType="Long">
        delete from crm_customer_followup_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>