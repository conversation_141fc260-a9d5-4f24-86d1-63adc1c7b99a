<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmAttachmentMapper">
    
    <resultMap type="CrmAttachment" id="CrmAttachmentResult">
        <result property="id"    column="id"    />
        <result property="entityType"    column="entity_type"    />
        <result property="entityId"    column="entity_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalName"    column="original_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileExtension"    column="file_extension"    />
        <result property="category"    column="category"    />
        <result property="description"    column="description"    />
        <result property="uploadBy"    column="upload_by"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="downloadCount"    column="download_count"    />
        <result property="isPublic"    column="is_public"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCrmAttachmentVo">
        select id, entity_type, entity_id, file_name, original_name, file_path, file_size, file_type, file_extension, category, description, upload_by, upload_time, download_count, is_public, sort_order, del_flag, create_by, create_time, update_by, update_time from crm_attachments
    </sql>

    <select id="selectCrmAttachmentList" parameterType="CrmAttachment" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        <where>  
            <if test="entityType != null  and entityType != ''"> and entity_type = #{entityType}</if>
            <if test="entityId != null "> and entity_id = #{entityId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalName != null  and originalName != ''"> and original_name like concat('%', #{originalName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="uploadBy != null  and uploadBy != ''"> and upload_by = #{uploadBy}</if>
            <if test="params.beginUploadTime != null and params.beginUploadTime != ''"><!-- 开始上传时间检索 -->
                and upload_time &gt;= #{params.beginUploadTime}
            </if>
            <if test="params.endUploadTime != null and params.endUploadTime != ''"><!-- 结束上传时间检索 -->
                and upload_time &lt;= #{params.endUploadTime}
            </if>
            <if test="isPublic != null "> and is_public = #{isPublic}</if>
            and del_flag = '0'
        </where>
        order by sort_order asc, upload_time desc
    </select>
    
    <select id="selectCrmAttachmentById" parameterType="Long" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectByEntityTypeAndId" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where entity_type = #{entityType} and entity_id = #{entityId} and del_flag = '0'
        order by sort_order asc, upload_time desc
    </select>

    <select id="countByEntityTypeAndId" resultType="int">
        select count(*) from crm_attachments
        where entity_type = #{entityType} and entity_id = #{entityId} and del_flag = '0'
    </select>

    <select id="selectByCategory" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where entity_type = #{entityType} and entity_id = #{entityId} and category = #{category} and del_flag = '0'
        order by sort_order asc, upload_time desc
    </select>

    <select id="selectExpiredAttachments" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where create_time &lt; #{expireDate} and del_flag = '0'
    </select>

    <select id="selectAttachmentStatistics" resultType="map">
        select 
            count(*) as totalCount,
            sum(file_size) as totalSize,
            count(case when category = 'contract' then 1 end) as contractCount,
            count(case when category = 'image' then 1 end) as imageCount,
            count(case when category = 'document' then 1 end) as documentCount,
            count(case when category = 'other' then 1 end) as otherCount,
            max(upload_time) as lastUploadTime
        from crm_attachments
        where entity_type = #{entityType} and entity_id = #{entityId} and del_flag = '0'
    </select>

    <select id="selectRecentAttachments" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where entity_type = #{entityType} and entity_id = #{entityId} and del_flag = '0'
        order by upload_time desc
        limit #{limit}
    </select>

    <select id="selectByFileName" resultMap="CrmAttachmentResult">
        <include refid="selectCrmAttachmentVo"/>
        where file_name = #{fileName} and del_flag = '0'
    </select>
        
    <insert id="insertCrmAttachment" parameterType="CrmAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into crm_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityType != null and entityType != ''">entity_type,</if>
            <if test="entityId != null">entity_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalName != null and originalName != ''">original_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileExtension != null">file_extension,</if>
            <if test="category != null">category,</if>
            <if test="description != null">description,</if>
            <if test="uploadBy != null">upload_by,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityType != null and entityType != ''">#{entityType},</if>
            <if test="entityId != null">#{entityId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalName != null and originalName != ''">#{originalName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileExtension != null">#{fileExtension},</if>
            <if test="category != null">#{category},</if>
            <if test="description != null">#{description},</if>
            <if test="uploadBy != null">#{uploadBy},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertCrmAttachment" parameterType="list">
        insert into crm_attachments(entity_type, entity_id, file_name, original_name, file_path, file_size, file_type, file_extension, category, description, upload_by, upload_time, download_count, is_public, sort_order, del_flag, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.entityType}, #{item.entityId}, #{item.fileName}, #{item.originalName}, #{item.filePath}, #{item.fileSize}, #{item.fileType}, #{item.fileExtension}, #{item.category}, #{item.description}, #{item.uploadBy}, #{item.uploadTime}, #{item.downloadCount}, #{item.isPublic}, #{item.sortOrder}, #{item.delFlag}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateCrmAttachment" parameterType="CrmAttachment">
        update crm_attachments
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityType != null and entityType != ''">entity_type = #{entityType},</if>
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalName != null and originalName != ''">original_name = #{originalName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileExtension != null">file_extension = #{fileExtension},</if>
            <if test="category != null">category = #{category},</if>
            <if test="description != null">description = #{description},</if>
            <if test="uploadBy != null">upload_by = #{uploadBy},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDownloadCount">
        update crm_attachments set download_count = download_count + 1 where id = #{id}
    </update>

    <delete id="deleteCrmAttachmentById" parameterType="Long">
        update crm_attachments set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmAttachmentByIds" parameterType="String">
        update crm_attachments set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
