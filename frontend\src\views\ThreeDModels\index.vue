<template>
  <div ref="threeContainer" class="three-container"></div>
  <div class="info">
    <p>相机位置: {{ cameraPosition }}</p>
    <p>目标位置: {{ targetPosition }}</p>0
    <p>缩放程度: {{ zoomLevel }}</p>
    <p>模型体积: {{ modelVolume }}</p>
  </div>
</template>

<script setup>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { onMounted, ref } from 'vue';
const threeContainer = ref(null);
const cameraPosition = ref({ x: 0, y: 0, z: 0 });
const targetPosition = ref({ x: 0, y: 0, z: 0 });
const zoomLevel = ref(1);
const modelVolume = ref(0);

// 计算几何体体积的函数
function calculateVolume(geometry) {
  let volume = 0;
  const position = geometry.attributes.position;
  const faces = position.count / 3;

  for (let i = 0; i < faces; i++) {
    const p1 = new THREE.Vector3().fromBufferAttribute(position, i * 3);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 1);
    const p3 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 2);

    volume += signedVolumeOfTriangle(p1, p2, p3);
  }

  return Math.abs(volume);
}

// 计算三角形的有符号体积
function signedVolumeOfTriangle(p1, p2, p3) {
  return p1.dot(p2.cross(p3)) / 6.0;
}

onMounted(() => {
  // 创建场景
  const scene = new THREE.Scene();
  
  // scene.background = new THREE.Color(0xffffff);
  // 创建相机
  const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  camera.position.z = 5;

  const target = { x: 118, y: -3.4221504288066125e-15, z: 51 };
  camera.lookAt(target);

  // 创建渲染器
  const renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  if (threeContainer.value) {
    threeContainer.value.appendChild(renderer.domElement);
  }

  
  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5); // 白色环境光，强度为0.5
  scene.add(ambientLight);

  // 添加点光源
  const pointLight = new THREE.PointLight(0xffffff, 1); // 白色点光源，强度为1
  pointLight.position.set(10, 10, 10); // 设置点光源位置
  scene.add(pointLight);


  // 添加坐标轴辅助工具
  const axesHelper = new THREE.AxesHelper(5);
  scene.add(axesHelper);
  // 创建一个立方体
  // const geometry = new THREE.BoxGeometry();
  // const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
  // const cube = new THREE.Mesh(geometry, material);
  // scene.add(cube);

  // 加载 STL 模型
  const loader = new STLLoader();
  loader.load('/models/1508523 v2.stl', (geometry) => {
    console.log(geometry);
    const material = new THREE.MeshNormalMaterial({ color: 0x0077ff });
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);
    
    // 调整模型位置
    mesh.position.set(0, 0, 0);

    
    // 计算模型体积
    modelVolume.value = calculateVolume(geometry);
    // 渲染场景
    const animate = function () {
      requestAnimationFrame(animate);
      controls.update(); // 更新控制器
      renderer.render(scene, camera);

      
      // 更新相机和目标位置
      cameraPosition.value = { x: camera.position.x, y: camera.position.y, z: camera.position.z };
      targetPosition.value = { x: controls.target.x, y: controls.target.y, z: controls.target.z };
      // zoomLevel.value = camera.zoom;
    };

    animate();
  });
  // 添加轨道控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true; // 启用阻尼效果
  controls.dampingFactor = 0.25; // 阻尼系数
  controls.screenSpacePanning = false; // 禁用屏幕空间平移
  controls.maxPolarAngle = Math.PI / 2; // 最大极角
  controls.addEventListener('change', () => {
    // 计算缩放倍数
    zoomLevel.value = camera.zoom;
  });

    // 设置默认位置  { "x": 130.26477735106727, "y": 189.32085921134802, "z": 153.25493895901292 }
    camera.position.set(130.26477735106727, 189.32085921134802, 153.25493895901292);
  // 设定目标位置 { "x": 126.85873476590898, "y": -3.4221504288066125e-15, "z": 76.29594371845312 }
  // 118.0108902386445, "y": -1.0876558003232934e-18, "z": 51.89443409078483 

  controls.target.set(118.0108902386445, -1.0876558003232934e-18, 51.89443409078483);

  // renderer.render(scene, camera);
  // 渲染场景
  // const animate = function () {
  //   requestAnimationFrame(animate);

  //   // cube.rotation.x += 0.01;
  //   // cube.rotation.y += 0.01;

  // };

  // animate();
});
</script>

<style scoped>
.three-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 5px;
}
</style>