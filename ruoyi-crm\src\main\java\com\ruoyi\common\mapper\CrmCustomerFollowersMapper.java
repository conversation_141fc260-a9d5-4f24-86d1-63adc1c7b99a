package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.entity.CrmCustomerFollowers;

/**
 * 客户关注Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Mapper
public interface CrmCustomerFollowersMapper {
    
    /**
     * 查询客户关注列表
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 客户关注集合
     */
    List<CrmCustomerFollowers> selectCrmCustomerFollowersList(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 新增客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    int insertCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 修改客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    int updateCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers);

    /**
     * 删除客户关注
     * 
     * @param id 客户关注主键
     * @return 结果
     */
    int deleteCrmCustomerFollowersById(Long id);

    /**
     * 关注客户（使用ON DUPLICATE KEY UPDATE避免重复关注）
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @param followerName 关注者用户名
     * @return 结果
     */
    int followCustomer(@Param("customerId") Long customerId, 
                      @Param("followerId") Long followerId, 
                      @Param("followerName") String followerName);

    /**
     * 取消关注客户（软删除）
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int unfollowCustomer(@Param("customerId") Long customerId, 
                        @Param("followerId") Long followerId);

    /**
     * 查询关注关系
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 关注关系
     */
    CrmCustomerFollowers selectFollowRelation(@Param("customerId") Long customerId, 
                                             @Param("followerId") Long followerId);

    /**
     * 批量关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @param followerName 关注者用户名
     * @return 结果
     */
    int batchFollowCustomers(@Param("customerIds") List<Long> customerIds, 
                           @Param("followerId") Long followerId, 
                           @Param("followerName") String followerName);

    /**
     * 批量取消关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @return 结果
     */
    int batchUnfollowCustomers(@Param("customerIds") List<Long> customerIds, 
                             @Param("followerId") Long followerId);

    /**
     * 统计用户关注的客户数量
     * 
     * @param followerId 关注者用户ID
     * @return 关注数量
     */
    int countFollowedCustomers(@Param("followerId") Long followerId);

    /**
     * 统计客户被关注的数量
     * 
     * @param customerId 客户ID
     * @return 被关注数量
     */
    int countCustomerFollowers(@Param("customerId") Long customerId);

    /**
     * 查询热门客户排行（按关注数量降序）
     * 
     * @param limit 限制数量
     * @return 热门客户列表
     */
    List<CrmCustomerFollowers> selectPopularCustomers(@Param("limit") Integer limit);
}