<template>
    <!-- 发票管理主容器 -->
    <el-container class="invoice-management">
        <!-- 主内容区域容器 -->
        <el-container class="main-container">
            <!-- 页面头部，包含标题和操作按钮 -->
            <el-header class="header">
                <h1>发票管理</h1>
                <div class="header-actions">
                    <el-button
                        type="primary"
                        size="small"
                        @click="handleAdd"
                        v-hasPermi="['crm:invoice:add']"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建发票
                    </el-button>
                </div>
            </el-header>

            <el-main class="main-content">
                <!-- 搜索和筛选区域 -->
                <div class="filter-section">
                    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="发票编号" prop="invoiceNo">
        <el-input
          v-model="queryParams.invoiceNo"
          placeholder="请输入发票编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票抬头" prop="invoiceTitle">
        <el-input
          v-model="queryParams.invoiceTitle"
          placeholder="请输入发票抬头"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票类型" prop="invoiceType">
        <el-select v-model="queryParams.invoiceType" placeholder="请选择发票类型" clearable>
          <el-option
            v-for="dict in dict.type.crm_invoice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.crm_invoice_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开票日期">
        <el-date-picker
          v-model="daterangeIssueDate"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 操作按钮区域 -->
                <div class="toolbar-section">
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button
                                type="success"
                                plain
                                icon="Edit"
                                :disabled="single"
                                @click="handleUpdate"
                                v-hasPermi="['crm:invoice:edit']"
                            >修改</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button
                                type="danger"
                                plain
                                icon="Delete"
                                :disabled="multiple"
                                @click="handleDelete"
                                v-hasPermi="['crm:invoice:remove']"
                            >删除</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button
                                type="warning"
                                plain
                                icon="Download"
                                @click="handleExport"
                                v-hasPermi="['crm:invoice:export']"
                            >导出</el-button>
                        </el-col>
                        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                    </el-row>
                </div>

                <!-- 发票数据表格 -->
                <div class="table-container">
                    <el-table 
                        v-loading="loading" 
                        :data="invoiceList" 
                        @selection-change="handleSelectionChange"
                        border 
                        sortable 
                        tooltip-effect="dark"
                        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                        :height="'100%'"
                        :max-height="'100%'"
                        class="invoice-table">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发票编号" align="center" prop="invoiceNo" />
      <el-table-column label="发票抬头" align="center" prop="invoiceTitle" />
      <el-table-column label="发票类型" align="center" prop="invoiceType">
        <template #default="scope">
          <dict-tag :options="dict.type.crm_invoice_type" :value="scope.row.invoiceType"/>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="联系人" align="center" prop="contactName" />
      <el-table-column label="含税金额" align="center" prop="amountIncludingTax">
        <template #default="scope">
          <span>{{ scope.row.currency }} {{ parseFloat(scope.row.amountIncludingTax || 0).toLocaleString() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="dict.type.crm_invoice_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="approvalStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.approvalStatus === 'pending'" type="warning">待审批</el-tag>
          <el-tag v-else-if="scope.row.approvalStatus === 'approved'" type="success">审批通过</el-tag>
          <el-tag v-else-if="scope.row.approvalStatus === 'rejected'" type="danger">审批驳回</el-tag>
          <el-tag v-else type="info">{{ scope.row.approvalStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开票日期" align="center" prop="issueDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['crm:invoice:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:invoice:edit']" v-if="scope.row.status === 'draft'">修改</el-button>
          <el-dropdown @command="handleCommand" trigger="click">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'submit', row: scope.row}" v-if="scope.row.status === 'draft'" v-hasPermi="['crm:invoice:approval']">
                  提交审批
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'issue', row: scope.row}" v-if="scope.row.status === 'approved'" v-hasPermi="['crm:invoice:issue']">
                  开票
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'cancel', row: scope.row}" v-if="scope.row.status === 'issued'" v-hasPermi="['crm:invoice:cancel']">
                  作废
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
                </div>
                
                <!-- 分页组件 -->
                <div class="pagination-section">
                    <pagination 
                        v-show="total>0" 
                        :total="total" 
                        v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize"
                        @pagination="getList" />
                </div>
            </el-main>
        </el-container>

    <!-- 添加或修改发票对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <el-form ref="invoiceRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="发票编号" prop="invoiceNo">
              <el-input v-model="form.invoiceNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票抬头" prop="invoiceTitle">
              <el-input v-model="form.invoiceTitle" placeholder="请输入发票抬头" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="发票类型" prop="invoiceType">
              <el-select v-model="form.invoiceType" placeholder="请选择发票类型">
                <el-option
                  v-for="dict in dict.type.crm_invoice_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="税率" prop="taxRate">
              <el-input-number v-model="form.taxRate" :precision="4" :min="0" :max="1" placeholder="税率" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="币种" prop="currency">
              <el-select v-model="form.currency" placeholder="请选择币种">
                <el-option label="人民币" value="CNY" />
                <el-option label="美元" value="USD" />
                <el-option label="欧元" value="EUR" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户" prop="customerId">
              <el-select v-model="form.customerId" placeholder="请选择客户" @change="handleCustomerChange" filterable>
                <el-option
                  v-for="customer in customerList"
                  :key="customer.id"
                  :label="customer.customerName"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactId">
              <el-select v-model="form.contactId" placeholder="请选择联系人" @change="handleContactChange" filterable>
                <el-option
                  v-for="contact in contactList"
                  :key="contact.id"
                  :label="contact.contactName"
                  :value="contact.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="纳税人识别号" prop="taxpayerId">
              <el-input v-model="form.taxpayerId" placeholder="请输入纳税人识别号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联报价单" prop="quotationId">
              <el-select v-model="form.quotationId" placeholder="请选择报价单" filterable clearable>
                <el-option
                  v-for="quotation in quotationList"
                  :key="quotation.id"
                  :label="quotation.quotationNo + ' - ' + quotation.quotationName"
                  :value="quotation.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bankName">
              <el-input v-model="form.bankName" placeholder="请输入开户银行" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="请输入银行账号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input v-model="form.companyAddress" placeholder="请输入公司地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司电话" prop="companyPhone">
              <el-input v-model="form.companyPhone" placeholder="请输入公司电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 发票明细 -->
        <el-divider content-position="left">发票明细</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" size="small" @click="handleAddItem">添加明细</el-button>
          </el-col>
        </el-row>
        <el-table :data="form.invoiceItems" border>
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="项目名称" width="150">
            <template #default="{ row }">
              <el-input v-model="row.itemName" placeholder="请输入项目名称" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="规格型号" width="120">
            <template #default="{ row }">
              <el-input v-model="row.specification" placeholder="规格型号" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="数量" width="100">
            <template #default="{ row }">
              <el-input-number v-model="row.quantity" :precision="2" :min="0" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="单位" width="80">
            <template #default="{ row }">
              <el-input v-model="row.unit" placeholder="单位" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row }">
              <el-input-number v-model="row.unitPrice" :precision="2" :min="0" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="金额" width="120">
            <template #default="{ row }">
              <span>{{ row.amount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税率%" width="80">
            <template #default="{ row }">
              <el-input-number v-model="row.taxRate" :precision="2" :min="0" :max="100" size="small" @change="calculateItemAmount(row)" />
            </template>
          </el-table-column>
          <el-table-column label="税额" width="100">
            <template #default="{ row }">
              <span>{{ row.taxAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="120">
            <template #default="{ row }">
              <el-input v-model="row.remarks" placeholder="备注" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" icon="Delete" size="small" @click="handleRemoveItem($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-row class="mt20">
          <el-col :span="24">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="不含税金额">
                <span style="font-weight: bold; color: #409EFF;">
                  {{ form.currency }} {{ parseFloat(form.amountExcludingTax || 0).toLocaleString() }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="税额">
                <span style="font-weight: bold; color: #E6A23C;">
                  {{ form.currency }} {{ parseFloat(form.taxAmount || 0).toLocaleString() }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="含税金额">
                <span style="font-size: 16px; font-weight: bold; color: #F56C6C;">
                  {{ form.currency }} {{ parseFloat(form.amountIncludingTax || 0).toLocaleString() }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
        </template>
      </el-dialog>
    </el-container>
</template>

<script setup name="Invoice">
import { getCurrentInstance, ref, reactive, onMounted, nextTick, toRefs } from 'vue';
import { listInvoice, getInvoice, delInvoice, addInvoice, updateInvoice, generateInvoiceNo, submitInvoiceApproval, issueInvoice, cancelInvoice } from "@/api/crm/invoice";
import { listCustomers } from "@/api/crm/customers";
import { listContacts } from "@/api/crm/contacts";
import { listQuotation } from "@/api/crm/quotation";
import { useDict } from '@/composables';
import { parseTime } from '@/utils/ruoyi';
import { Plus } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { dict } = useDict('crm_invoice_status', 'crm_invoice_type');

const invoiceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeIssueDate = ref([]);
const customerList = ref([]);
const contactList = ref([]);
const quotationList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    invoiceNo: null,
    invoiceTitle: null,
    invoiceType: null,
    customerName: null,
    status: null,
  },
  rules: {
    invoiceTitle: [
      { required: true, message: "发票抬头不能为空", trigger: "blur" }
    ],
    invoiceType: [
      { required: true, message: "发票类型不能为空", trigger: "change" }
    ],
    customerId: [
      { required: true, message: "客户不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询发票列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (daterangeIssueDate.value && daterangeIssueDate.value.length === 2) {
    queryParams.value.params["beginIssueDate"] = daterangeIssueDate.value[0];
    queryParams.value.params["endIssueDate"] = daterangeIssueDate.value[1];
  }
  listInvoice(queryParams.value).then(response => {
    invoiceList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    invoiceNo: null,
    invoiceTitle: null,
    invoiceType: "special",
    customerId: null,
    customerName: null,
    contactId: null,
    contactName: null,
    quotationId: null,
    contractId: null,
    contractNo: null,
    responsiblePersonId: null,
    taxpayerId: null,
    taxRate: 0.13,
    amountExcludingTax: 0,
    taxAmount: 0,
    amountIncludingTax: 0,
    bankName: null,
    bankAccount: null,
    companyAddress: null,
    companyPhone: null,
    status: "draft",
    approvalStatus: "pending",
    currency: "CNY",
    exchangeRate: 1,
    remarks: null,
    invoiceItems: []
  };
  proxy.resetForm("invoiceRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeIssueDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
async function handleAdd() {
  reset();
  
  // 生成发票编号
  try {
    const response = await generateInvoiceNo();
    form.value.invoiceNo = response.data;
  } catch (error) {
    console.error('生成发票编号失败:', error);
  }
  
  // 加载基础数据
  loadCustomerList();
  loadQuotationList();
  
  open.value = true;
  title.value = "添加发票";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getInvoice(id).then(response => {
    form.value = response.data;
    loadCustomerList();
    loadQuotationList();
    loadContactList(form.value.customerId);
    open.value = true;
    title.value = "修改发票";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["invoiceRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateInvoice(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addInvoice(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除发票编号为"' + _ids + '"的数据项？').then(function() {
    return delInvoice(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('crm/invoice/export', {
    ...queryParams.value
  }, `invoice_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  // TODO: 实现详情页面
  console.log('查看详情:', row);
}

/** 下拉菜单操作 */
function handleCommand(command) {
  const { action, row } = command;
  
  switch (action) {
    case 'submit':
      handleSubmitApproval(row);
      break;
    case 'issue':
      handleIssueInvoice(row);
      break;
    case 'cancel':
      handleCancelInvoice(row);
      break;
  }
}

/** 提交审批 */
function handleSubmitApproval(row) {
  proxy.$modal.confirm('确认提交发票"' + row.invoiceTitle + '"进行审批？').then(function() {
    return submitInvoiceApproval(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("提交审批成功");
  }).catch(() => {});
}

/** 开票 */
function handleIssueInvoice(row) {
  proxy.$modal.confirm('确认对发票"' + row.invoiceTitle + '"进行开票操作？').then(function() {
    return issueInvoice(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("开票成功");
  }).catch(() => {});
}

/** 作废发票 */
function handleCancelInvoice(row) {
  proxy.$prompt('请输入作废原因', '作废发票', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '作废原因不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    return cancelInvoice(row.id, { reason: value });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发票作废成功");
  }).catch(() => {});
}

/** 加载客户列表 */
function loadCustomerList() {
  listCustomers().then(response => {
    customerList.value = response.rows || [];
  });
}

/** 加载联系人列表 */
function loadContactList(customerId) {
  if (!customerId) {
    contactList.value = [];
    return;
  }
  
  listContacts({ customerId: customerId }).then(response => {
    contactList.value = response.rows || [];
  });
}

/** 加载报价单列表 */
function loadQuotationList() {
  listQuotation({ approvalStatus: 'approved' }).then(response => {
    quotationList.value = response.rows || [];
  });
}

/** 客户变更事件 */
function handleCustomerChange(customerId) {
  const customer = customerList.value.find(c => c.id === customerId);
  if (customer) {
    form.value.customerName = customer.customerName;
    form.value.invoiceTitle = customer.customerName;
  }
  
  // 清空联系人选择并重新加载
  form.value.contactId = null;
  form.value.contactName = null;
  loadContactList(customerId);
}

/** 联系人变更事件 */
function handleContactChange(contactId) {
  const contact = contactList.value.find(c => c.id === contactId);
  if (contact) {
    form.value.contactName = contact.contactName;
  }
}

/** 添加明细行 */
function handleAddItem() {
  if (!form.value.invoiceItems) {
    form.value.invoiceItems = [];
  }
  
  form.value.invoiceItems.push({
    itemName: '',
    itemCode: '',
    specification: '',
    unit: '',
    quantity: 1,
    unitPrice: 0,
    amount: 0,
    taxRate: form.value.taxRate || 0.13,
    taxAmount: 0,
    remarks: '',
    sortOrder: form.value.invoiceItems.length + 1
  });
}

/** 删除明细行 */
function handleRemoveItem(index) {
  form.value.invoiceItems.splice(index, 1);
  calculateTotalAmount();
}

/** 计算明细金额 */
function calculateItemAmount(item) {
  if (item.quantity && item.unitPrice) {
    item.amount = item.quantity * item.unitPrice;
    
    // 计算税额
    if (item.taxRate) {
      item.taxAmount = item.amount * item.taxRate;
    } else {
      item.taxAmount = 0;
    }
  } else {
    item.amount = 0;
    item.taxAmount = 0;
  }
  
  calculateTotalAmount();
}

/** 计算总金额 */
function calculateTotalAmount() {
  if (!form.value.invoiceItems || form.value.invoiceItems.length === 0) {
    form.value.amountExcludingTax = 0;
    form.value.taxAmount = 0;
    form.value.amountIncludingTax = 0;
    return;
  }
  
  let totalAmountExcludingTax = 0;
  let totalTaxAmount = 0;
  
  form.value.invoiceItems.forEach(item => {
    totalAmountExcludingTax += item.amount || 0;
    totalTaxAmount += item.taxAmount || 0;
  });
  
  form.value.amountExcludingTax = totalAmountExcludingTax;
  form.value.taxAmount = totalTaxAmount;
  form.value.amountIncludingTax = totalAmountExcludingTax + totalTaxAmount;
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 发票管理容器样式 */
.invoice-management {
    background-color: #fff;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主容器样式 */
.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 20px 16px;
    overflow: hidden;
    min-height: 0;
}

/* 筛选区域样式 */
.filter-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 工具栏区域样式 */
.toolbar-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 表格容器样式 */
.table-container {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    min-height: 0;
    flex: 1;
}

/* 表格样式 */
.invoice-table {
    flex: 1;
    border-radius: 8px;
}

/* 表格列对齐 */
:deep(.el-table .cell) {
    text-align: left;
}

/* 表格头部对齐 */
:deep(.el-table th .cell) {
    text-align: left;
}

/* 分页区域样式 */
.pagination-section {
    flex-shrink: 0;
    display: flex;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
}

/* 表格行高调整 */
:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-table th) {
    padding: 14px 0;
    background-color: #fafafa !important;
}

/* 表格操作按钮样式 */
:deep(.el-table .el-button) {
    font-size: var(--ep-font-size-base);
}

:deep(.el-table .el-button .el-icon) {
    font-size: var(--ep-font-size-base);
}

/* 表格边框优化 */
:deep(.el-table--border) {
    border: 1px solid #ebeef5;
}

:deep(.el-table--border td) {
    border-right: 1px solid #f0f2f5;
}

:deep(.el-table--border th) {
    border-right: 1px solid #f0f2f5;
}
</style>