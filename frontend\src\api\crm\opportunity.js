import request from '@/utils/request'

// 查询商机列表
export function listOpportunity(query) {
  return request({
    url: '/front/crm/opportunities/list',
    method: 'get',
    params: query
  })
}

// 查询商机详细
export function getOpportunity(id) {
  return request({
    url: '/front/crm/opportunities/' + id,
    method: 'get'
  })
}

// 新增商机
export function addOpportunity(data) {
  return request({
    url: '/front/crm/opportunities',
    method: 'post',
    data: data
  })
}

// 修改商机
export function updateOpportunity(data) {
  return request({
    url: '/front/crm/opportunities',
    method: 'put',
    data: data
  })
}

// 删除商机
export function delOpportunity(id) {
  return request({
    url: '/front/crm/opportunities/' + id,
    method: 'delete'
  })
}

// 根据客户ID查询商机
export function getOpportunityByCustomer(customerId) {
  return request({
    url: '/front/crm/opportunities/customer/' + customerId,
    method: 'get'
  })
}

// 搜索商机
export function searchOpportunity(keyword) {
  return request({
    url: '/front/crm/opportunities/search',
    method: 'get',
    params: { 
      keyword: keyword || '',
      pageNum: 1,
      pageSize: 100
    }
  })
}