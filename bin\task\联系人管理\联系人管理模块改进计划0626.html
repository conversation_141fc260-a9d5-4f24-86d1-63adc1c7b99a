<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人管理模块改进计划</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        /* 代码高亮样式 */
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .sql-keyword { color: #63b3ed; font-weight: bold; }
        .sql-string { color: #68d391; }
        .sql-comment { color: #a0aec0; font-style: italic; }
        .sql-number { color: #f6ad55; }
        .java-keyword { color: #63b3ed; font-weight: bold; }
        .java-annotation { color: #f6ad55; }
        .java-string { color: #68d391; }
        .java-comment { color: #a0aec0; font-style: italic; }
        .java-number { color: #f6ad55; }
        .java-type { color: #81c784; }
        .java-identifier { color: #e2e8f0; }
        .yaml-key { color: #63b3ed; font-weight: bold; }
        .yaml-value { color: #68d391; }
        .yaml-comment { color: #a0aec0; font-style: italic; }
        .bash-keyword { color: #63b3ed; font-weight: bold; }
        .bash-string { color: #68d391; }
        .bash-comment { color: #a0aec0; font-style: italic; }
        .bash-variable { color: #f6ad55; }
        /* Mermaid图表容器 */
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        .status-completed {
            color: #27ae60;
            font-weight: bold;
        }
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        .priority-high {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-medium {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-low {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .risk-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .phase-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .architecture-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        /* 任务清单样式 */
        .phase-box ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .phase-box ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        .phase-box ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        .phase-box ul li label:hover {
            color: #007bff;
        }
        
        /* 验收标准样式 */
        h3 + ul li {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        
        h3 + ul li input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        h3 + ul li label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            flex: 1;
        }
        
        h3 + ul li label:hover {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人管理模块改进计划</h1>
        
        <h2>项目背景</h2>
        <p>基于对现有联系人管理模块的深入分析，发现了数据设计、功能实现和用户体验方面的关键问题。本改进计划旨在通过系统性的优化，提升联系人管理的完整性和用户体验。</p>
        
        <h2>现状分析</h2>
        
        <h3>已完成功能</h3>
        <ul>
            <li><span class="status-completed">✅</span> 基础联系人CRUD操作</li>
            <li><span class="status-completed">✅</span> 联系人列表展示和分页</li>
            <li><span class="status-completed">✅</span> 基本的搜索和筛选</li>
            <li><span class="status-completed">✅</span> 联系人详情抽屉框架</li>
        </ul>
        
        <h3>发现的关键问题</h3>
        <ul>
            <li><span class="status-pending">❌</span> <strong>功能不完整</strong>："下属负责的联系人"筛选无法实现</li>
            <li><span class="status-pending">❌</span> <strong>关注功能缺失</strong>：缺少联系人关注表和相关功能</li>
            <li><span class="status-pending">❌</span> <strong>前后端不一致</strong>：前端传递filterType参数，后端未处理</li>
            <li><span class="status-pending">❌</span> <strong>查询逻辑不完整</strong>：CrmContactsMapper.xml缺少基于用户关系的筛选</li>
        </ul>
        
        <h2>改进目标</h2>
        
        <div class="success-box">
            <h3>核心目标</h3>
            <ol>
                <li><strong>完善数据架构</strong>：建立完整的用户关系和联系人关注体系</li>
                <li><strong>实现完整筛选</strong>：支持"我负责的"、"下属负责的"、"我关注的"等筛选功能</li>
                <li><strong>提升用户体验</strong>：优化界面交互和数据展示</li>
                <li><strong>增强系统扩展性</strong>：为未来功能扩展奠定基础</li>
            </ol>
        </div>
        
        <h2>技术架构设计</h2>
        
        <div class="architecture-box">
            <h3>数据库架构改进</h3>
            
            <div class="mermaid">
erDiagram
    crm_business_contacts {
        bigint id PK
        bigint responsible_person_id FK
        varchar name
        varchar mobile
        varchar phone
        varchar email
        varchar position
        tinyint is_key_decision_maker
        varchar direct_superior
        varchar address
        varchar detailed_address
        datetime next_contact_time
        date selected_date
        char gender
        text remarks
        char del_flag
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
    }
    
    crm_user_hierarchy {
        bigint id PK
        bigint user_id FK
        bigint superior_id FK
        int hierarchy_level
        datetime create_time
        datetime update_time
        varchar create_by
        varchar update_by
        char del_flag
    }
    
    crm_contact_followers {
        bigint id PK
        bigint contact_id FK
        bigint follower_id FK
        datetime follow_time
        tinyint is_active
        varchar create_by
        datetime create_time
    }
    
    sys_user {
        bigint user_id PK
        bigint dept_id FK
        varchar user_name
        varchar nick_name
        varchar email
        varchar phonenumber
        char sex
        varchar avatar
        varchar password
        char status
        char del_flag
        varchar login_ip
        datetime login_date
        varchar create_by
        datetime create_time
        varchar update_by
        datetime update_time
        varchar remark
    }
    
    crm_business_contacts ||--o{ crm_contact_followers : "被关注"
    sys_user ||--o{ crm_contact_followers : "关注者"
    sys_user ||--o{ crm_business_contacts : "负责人"
    sys_user ||--o{ crm_user_hierarchy : "用户"
    sys_user ||--o{ crm_user_hierarchy : "上级"
            </div>
            
            <h4>1. CRM用户层级关系表设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 新增CRM用户层级关系表（不侵入框架sys表）</span>
<span class="sql-keyword">CREATE TABLE</span> crm_user_hierarchy (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span>,
    user_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'用户ID'</span>,
    superior_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'上级用户ID'</span>,
    hierarchy_level <span class="sql-keyword">INT DEFAULT</span> <span class="sql-number">1</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'层级深度'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span>,
    update_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    update_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新者'</span>,
    del_flag <span class="sql-keyword">CHAR</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'0'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'删除标志（0代表存在 2代表删除）'</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">FOREIGN KEY</span> (superior_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">UNIQUE KEY</span> uk_user_superior (user_id, superior_id)
) <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM用户层级关系表'</span>;</code></pre>
            
            <h4>2. 联系人关注表设计</h4>
            <pre><code class="sql"><span class="sql-comment">-- 新增联系人关注表</span>
<span class="sql-keyword">CREATE TABLE</span> crm_contact_followers (
    id <span class="sql-keyword">BIGINT PRIMARY KEY AUTO_INCREMENT</span>,
    contact_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系人ID'</span>,
    follower_id <span class="sql-keyword">BIGINT NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注者用户ID'</span>,
    follow_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注时间'</span>,
    is_active <span class="sql-keyword">TINYINT</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-number">1</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'是否有效关注'</span>,
    create_by <span class="sql-keyword">VARCHAR</span>(<span class="sql-number">64</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">''</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
    create_time <span class="sql-keyword">DATETIME DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (contact_id) <span class="sql-keyword">REFERENCES</span> crm_business_contacts(id),
    <span class="sql-keyword">FOREIGN KEY</span> (follower_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
    <span class="sql-keyword">UNIQUE KEY</span> uk_contact_follower (contact_id, follower_id)
) <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'联系人关注表'</span>;</code></pre>
            
            <h4>3. 数据初始化脚本</h4>
            <pre><code class="sql"><span class="sql-comment">-- 基于部门关系初始化用户层级关系（示例）</span>
<span class="sql-keyword">INSERT INTO</span> crm_user_hierarchy (user_id, superior_id, hierarchy_level, create_by)
<span class="sql-keyword">SELECT</span> 
    u1.user_id,
    u2.user_id <span class="sql-keyword">AS</span> superior_id,
    <span class="sql-number">1</span> <span class="sql-keyword">AS</span> hierarchy_level,
    <span class="sql-string">'system'</span> <span class="sql-keyword">AS</span> create_by
<span class="sql-keyword">FROM</span> sys_user u1
<span class="sql-keyword">JOIN</span> sys_dept d1 <span class="sql-keyword">ON</span> u1.dept_id = d1.dept_id
<span class="sql-keyword">JOIN</span> sys_dept d2 <span class="sql-keyword">ON</span> d1.parent_id = d2.dept_id
<span class="sql-keyword">JOIN</span> sys_user u2 <span class="sql-keyword">ON</span> u2.dept_id = d2.dept_id
<span class="sql-keyword">WHERE</span> u1.del_flag = <span class="sql-string">'0'</span> <span class="sql-keyword">AND</span> u2.del_flag = <span class="sql-string">'0'</span>
<span class="sql-keyword">AND</span> u1.user_id != u2.user_id;</code></pre>
        </div>
        
        <h2>实施计划</h2>
        
        <div class="phase-box">
            <h3>第一阶段：数据库结构改进 <span class="priority-high">优先级：高</span></h3>
            
            <h4>1.1 创建用户关系表</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>执行用户层级关系表创建SQL</li>
                <li>初始化现有用户的层级关系数据</li>
                <li>创建用户关系管理的基础API</li>
            </ol>
            
            <h4>1.2 创建联系人关注表</h4>
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>执行联系人关注表创建SQL</li>
                <li>设计关注/取消关注的业务逻辑</li>
                <li>创建关注管理的API接口</li>
            </ol>
                    
            <p><strong>预估时间：</strong> 2天</p>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：后端API增强 <span class="priority-high">优先级：高</span></h3>
            
            <div class="mermaid">
flowchart TD
    A[前端筛选请求] --> B{筛选类型判断}
    B -->|mine| C[查询我负责的联系人]
    B -->|subordinate| D[查询下属负责的联系人]
    B -->|following| E[查询我关注的联系人]
    B -->|all| F[查询全部联系人]
    
    D --> G[CrmUserHierarchyService]
    G --> H[获取下属用户ID列表]
    
    E --> I[CrmContactFollowService]
    I --> J[获取关注的联系人ID列表]
    
    C --> K[CrmContactsService]
    H --> K
    J --> K
    F --> K
    
    K --> L[CrmContactsMapper]
    L --> M[数据库查询]
    M --> N[返回联系人列表]
            </div>
            
            <h4>2.1 增强CrmContactsController</h4>
            <pre><code class="java"><span class="java-comment">// 修改联系人列表查询方法</span>
<span class="java-annotation">@GetMapping</span>(<span class="java-string">"/list"</span>)
<span class="java-keyword">public</span> TableDataInfo getContactsList(CrmContacts crmContacts, 
                                   <span class="java-annotation">@RequestParam</span>(required = <span class="java-keyword">false</span>) String filterType) {
    startPage();
    <span class="java-comment">// 获取当前登录用户</span>
    Long currentUserId = SecurityUtils.getUserId();
    
    <span class="java-comment">// 根据筛选类型设置查询条件</span>
    <span class="java-keyword">if</span> (<span class="java-string">"mine"</span>.equals(filterType)) {
        crmContacts.setResponsiblePersonId(currentUserId);
    } <span class="java-keyword">else if</span> (<span class="java-string">"subordinate"</span>.equals(filterType)) {
        <span class="java-comment">// 查询下属负责的联系人</span>
        List&lt;Long&gt; subordinateIds = crmUserHierarchyService.getSubordinateIds(currentUserId);
        crmContacts.setSubordinateIds(subordinateIds);
    } <span class="java-keyword">else if</span> (<span class="java-string">"following"</span>.equals(filterType)) {
        <span class="java-comment">// 查询关注的联系人</span>
        crmContacts.setFollowerId(currentUserId);
    }
    
    List&lt;CrmContacts&gt; list = crmContactsService.selectCrmContactsList(crmContacts);
    <span class="java-keyword">return</span> getDataTable(list);
}</code></pre>
            
            <h4>2.2 创建CRM用户层级服务</h4>
            <pre><code class="java"><span class="java-comment">// CrmUserHierarchyService接口</span>
<span class="java-keyword">public interface</span> ICrmUserHierarchyService {
    <span class="java-comment">/**
     * 获取用户的直接下属ID列表
     */</span>
    List&lt;Long&gt; getSubordinateIds(Long userId);
    
    <span class="java-comment">/**
     * 获取用户的所有下属ID列表（多级）
     */</span>
    List&lt;Long&gt; getAllSubordinateIds(Long userId, <span class="java-keyword">int</span> maxLevel);
    
    <span class="java-comment">/**
     * 添加用户层级关系
     */</span>
    <span class="java-keyword">int</span> insertUserHierarchy(CrmUserHierarchy hierarchy);
    
    <span class="java-comment">/**
     * 删除用户层级关系
     */</span>
    <span class="java-keyword">int</span> deleteUserHierarchy(Long userId, Long superiorId);
}

<span class="java-comment">// CrmUserHierarchyServiceImpl实现类</span>
<span class="java-annotation">@Service</span>
<span class="java-keyword">public class</span> CrmUserHierarchyServiceImpl <span class="java-keyword">implements</span> ICrmUserHierarchyService {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> CrmUserHierarchyMapper crmUserHierarchyMapper;
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> List&lt;Long&gt; getSubordinateIds(Long userId) {
        <span class="java-keyword">return</span> crmUserHierarchyMapper.selectSubordinateIds(userId);
    }
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> List&lt;Long&gt; getAllSubordinateIds(Long userId, <span class="java-keyword">int</span> maxLevel) {
        <span class="java-keyword">return</span> crmUserHierarchyMapper.selectAllSubordinateIds(userId, maxLevel);
    }
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> <span class="java-keyword">int</span> insertUserHierarchy(CrmUserHierarchy hierarchy) {
        hierarchy.setCreateTime(DateUtils.getNowDate());
        <span class="java-keyword">return</span> crmUserHierarchyMapper.insertCrmUserHierarchy(hierarchy);
    }
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> <span class="java-keyword">int</span> deleteUserHierarchy(Long userId, Long superiorId) {
        <span class="java-keyword">return</span> crmUserHierarchyMapper.deleteCrmUserHierarchy(userId, superiorId);
    }
}</code></pre>
            
            <h4>2.3 创建联系人关注服务</h4>
            <pre><code class="java"><span class="java-comment">// CrmContactFollowController控制器</span>
<span class="java-annotation">@RestController</span>
<span class="java-annotation">@RequestMapping</span>(<span class="java-string">"/crm/contact/follow"</span>)
<span class="java-keyword">public class</span> CrmContactFollowController <span class="java-keyword">extends</span> BaseController {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> ICrmContactFollowService crmContactFollowService;
    
    <span class="java-comment">/**
     * 关注联系人
     */</span>
    <span class="java-annotation">@PostMapping</span>(<span class="java-string">"/add/{contactId}"</span>)
    <span class="java-keyword">public</span> AjaxResult followContact(<span class="java-annotation">@PathVariable</span> Long contactId) {
        Long userId = SecurityUtils.getUserId();
        <span class="java-keyword">return</span> toAjax(crmContactFollowService.followContact(contactId, userId));
    }
    
    <span class="java-comment">/**
     * 取消关注联系人
     */</span>
    <span class="java-annotation">@DeleteMapping</span>(<span class="java-string">"/remove/{contactId}"</span>)
    <span class="java-keyword">public</span> AjaxResult unfollowContact(<span class="java-annotation">@PathVariable</span> Long contactId) {
        Long userId = SecurityUtils.getUserId();
        <span class="java-keyword">return</span> toAjax(crmContactFollowService.unfollowContact(contactId, userId));
    }
    
    <span class="java-comment">/**
     * 获取联系人关注状态
     */</span>
    <span class="java-annotation">@GetMapping</span>(<span class="java-string">"/status/{contactId}"</span>)
    <span class="java-keyword">public</span> AjaxResult getFollowStatus(<span class="java-annotation">@PathVariable</span> Long contactId) {
        Long userId = SecurityUtils.getUserId();
        <span class="java-keyword">boolean</span> isFollowing = crmContactFollowService.isFollowing(contactId, userId);
        <span class="java-keyword">return</span> AjaxResult.success(isFollowing);
    }
}

<span class="java-comment">// CrmContactFollowService服务实现</span>
<span class="java-annotation">@Service</span>
<span class="java-keyword">public class</span> CrmContactFollowServiceImpl <span class="java-keyword">implements</span> ICrmContactFollowService {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> CrmContactFollowersMapper crmContactFollowersMapper;
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> <span class="java-keyword">int</span> followContact(Long contactId, Long followerId) {
        CrmContactFollowers follower = <span class="java-keyword">new</span> CrmContactFollowers();
        follower.setContactId(contactId);
        follower.setFollowerId(followerId);
        follower.setFollowTime(DateUtils.getNowDate());
        follower.setIsActive(<span class="java-number">1</span>);
        follower.setCreateBy(SecurityUtils.getUsername());
        <span class="java-keyword">return</span> crmContactFollowersMapper.insertCrmContactFollowers(follower);
    }
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> <span class="java-keyword">int</span> unfollowContact(Long contactId, Long followerId) {
        <span class="java-keyword">return</span> crmContactFollowersMapper.deleteCrmContactFollowers(contactId, followerId);
    }
    
    <span class="java-annotation">@Override</span>
    <span class="java-keyword">public</span> <span class="java-keyword">boolean</span> isFollowing(Long contactId, Long followerId) {
        CrmContactFollowers follower = crmContactFollowersMapper.selectByContactAndFollower(contactId, followerId);
        <span class="java-keyword">return</span> follower != <span class="java-keyword">null</span> && follower.getIsActive() == <span class="java-number">1</span>;
    }
}</code></pre>
            
            <p><strong>预估时间：</strong> 3天</p>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：数据访问层优化 <span class="priority-medium">优先级：中</span></h3>
            
            <h4>3.1 增强CrmContactsMapper.xml</h4>
            <div class="code-block">
<!-- 增强联系人列表查询 -->
<select id="selectCrmContactsList" parameterType="CrmContacts" resultMap="CrmContactsResult">
    SELECT 
        c.id, c.responsible_person_id, c.name, c.mobile, c.phone, c.email,
        c.position, c.is_key_decision_maker, c.direct_superior, c.address,
        c.detailed_address, c.next_contact_time, c.selected_date, c.gender,
        c.remarks, c.del_flag, c.create_by, c.create_time, c.update_by, c.update_time,
        u.nick_name as responsible_person_name,
        CASE WHEN cf.id IS NOT NULL THEN 1 ELSE 0 END as is_following
    FROM crm_business_contacts c
    LEFT JOIN sys_user u ON c.responsible_person_id = u.user_id
    LEFT JOIN crm_contact_followers cf ON c.id = cf.contact_id AND cf.follower_id = #{followerId}
    <where>
        c.del_flag = '0'
        <if test="responsiblePersonId != null">
            AND c.responsible_person_id = #{responsiblePersonId}
        </if>
        <if test="subordinateIds != null and subordinateIds.size() > 0">
            AND c.responsible_person_id IN
            <foreach collection="subordinateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="followerId != null">
            AND cf.follower_id = #{followerId} AND cf.is_active = 1
        </if>
        <!-- 其他现有查询条件 -->
        <if test="name != null and name != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="mobile != null and mobile != ''">
            AND c.mobile LIKE CONCAT('%', #{mobile}, '%')
        </if>
    </where>
    ORDER BY c.create_time DESC
</select>
            </div>
            
            <h4>3.2 创建UserHierarchyMapper</h4>
            <div class="code-block">
<!-- UserHierarchyMapper.xml -->
<select id="selectSubordinateIds" parameterType="Long" resultType="Long">
    SELECT user_id FROM sys_user_hierarchy WHERE superior_id = #{userId}
</select>

<select id="selectAllSubordinateIds" resultType="Long">
    WITH RECURSIVE subordinate_tree AS (
        SELECT user_id, superior_id, 1 as level
        FROM sys_user_hierarchy 
        WHERE superior_id = #{userId}
        
        UNION ALL
        
        SELECT h.user_id, h.superior_id, st.level + 1
        FROM sys_user_hierarchy h
        INNER JOIN subordinate_tree st ON h.superior_id = st.user_id
        WHERE st.level < #{maxLevel}
    )
    SELECT user_id FROM subordinate_tree
</select>
            </div>
            
            <p><strong>预估时间：</strong> 2天</p>
        </div>
        
        <div class="phase-box">
            <h3>第四阶段：前端功能增强 <span class="priority-medium">优先级：中</span></h3>
            
            <div class="mermaid">
flowchart TD
    A[联系人管理页面] --> B[筛选组件]
    B --> C{用户选择筛选类型}
    C -->|全部联系人| D[发送all请求]
    C -->|我负责的| E[发送mine请求]
    C -->|下属负责的| F[发送subordinate请求]
    C -->|我关注的| G[发送following请求]
    
    D --> H[后端API处理]
    E --> H
    F --> H
    G --> H
    
    H --> I[返回联系人列表]
    I --> J[更新前端表格]
    
    K[联系人列表] --> L[关注按钮]
    L --> M{当前关注状态}
    M -->|已关注| N[取消关注API]
    M -->|未关注| O[添加关注API]
    
    N --> P[更新关注状态]
    O --> P
    P --> Q[刷新列表]
            </div>
            
            <h4>4.1 数据库索引优化</h4>
            <pre><code class="sql"><span class="sql-comment">-- 为联系人表添加复合索引</span>
<span class="sql-keyword">CREATE INDEX</span> idx_contacts_responsible_del <span class="sql-keyword">ON</span> crm_business_contacts(responsible_person_id, del_flag);
<span class="sql-keyword">CREATE INDEX</span> idx_contacts_name_mobile <span class="sql-keyword">ON</span> crm_business_contacts(name, mobile);
<span class="sql-keyword">CREATE INDEX</span> idx_contacts_create_time <span class="sql-keyword">ON</span> crm_business_contacts(create_time);

<span class="sql-comment">-- 为用户层级表添加索引</span>
<span class="sql-keyword">CREATE INDEX</span> idx_hierarchy_user_superior <span class="sql-keyword">ON</span> crm_user_hierarchy(user_id, superior_id);
<span class="sql-keyword">CREATE INDEX</span> idx_hierarchy_superior <span class="sql-keyword">ON</span> crm_user_hierarchy(superior_id);

<span class="sql-comment">-- 为关注表添加索引</span>
<span class="sql-keyword">CREATE INDEX</span> idx_followers_contact_follower <span class="sql-keyword">ON</span> crm_contact_followers(contact_id, follower_id);
<span class="sql-keyword">CREATE INDEX</span> idx_followers_follower_active <span class="sql-keyword">ON</span> crm_contact_followers(follower_id, is_active);</code></pre>
            
            <h4>4.2 查询优化</h4>
            <pre><code class="xml"><span class="xml-comment">&lt;!-- 优化CrmContactsMapper.xml中的查询 --&gt;</span>
<span class="xml-tag">&lt;select</span> <span class="xml-attr">id=</span><span class="xml-value">"selectCrmContactsList"</span> <span class="xml-attr">resultMap=</span><span class="xml-value">"CrmContactsResult"</span><span class="xml-tag">&gt;</span>
    <span class="xml-keyword">SELECT</span> c.*, 
           <span class="xml-keyword">CASE WHEN</span> cf.follower_id <span class="xml-keyword">IS NOT NULL THEN</span> <span class="xml-number">1</span> <span class="xml-keyword">ELSE</span> <span class="xml-number">0</span> <span class="xml-keyword">END</span> <span class="xml-keyword">as</span> is_following
    <span class="xml-keyword">FROM</span> crm_business_contacts c
    <span class="xml-tag">&lt;if</span> <span class="xml-attr">test=</span><span class="xml-value">"followerId != null"</span><span class="xml-tag">&gt;</span>
        <span class="xml-keyword">INNER JOIN</span> crm_contact_followers cf 
        <span class="xml-keyword">ON</span> c.id = cf.contact_id 
        <span class="xml-keyword">AND</span> cf.follower_id = #{followerId} 
        <span class="xml-keyword">AND</span> cf.is_active = <span class="xml-number">1</span>
    <span class="xml-tag">&lt;/if&gt;</span>
    <span class="xml-tag">&lt;if</span> <span class="xml-attr">test=</span><span class="xml-value">"followerId == null"</span><span class="xml-tag">&gt;</span>
        <span class="xml-keyword">LEFT JOIN</span> crm_contact_followers cf 
        <span class="xml-keyword">ON</span> c.id = cf.contact_id 
        <span class="xml-keyword">AND</span> cf.follower_id = #{currentUserId} 
        <span class="xml-keyword">AND</span> cf.is_active = <span class="xml-number">1</span>
    <span class="xml-tag">&lt;/if&gt;</span>
    <span class="xml-tag">&lt;where&gt;</span>
        c.del_flag = <span class="xml-string">'0'</span>
        <span class="xml-tag">&lt;if</span> <span class="xml-attr">test=</span><span class="xml-value">"responsiblePersonId != null"</span><span class="xml-tag">&gt;</span>
            <span class="xml-keyword">AND</span> c.responsible_person_id = #{responsiblePersonId}
        <span class="xml-tag">&lt;/if&gt;</span>
        <span class="xml-tag">&lt;if</span> <span class="xml-attr">test=</span><span class="xml-value">"subordinateIds != null and subordinateIds.size() &gt; 0"</span><span class="xml-tag">&gt;</span>
            <span class="xml-keyword">AND</span> c.responsible_person_id <span class="xml-keyword">IN</span>
            <span class="xml-tag">&lt;foreach</span> <span class="xml-attr">item=</span><span class="xml-value">"id"</span> <span class="xml-attr">collection=</span><span class="xml-value">"subordinateIds"</span> <span class="xml-attr">open=</span><span class="xml-value">"("</span> <span class="xml-attr">separator=</span><span class="xml-value">","</span> <span class="xml-attr">close=</span><span class="xml-value">")"</span><span class="xml-tag">&gt;</span>
                #{id}
            <span class="xml-tag">&lt;/foreach&gt;</span>
        <span class="xml-tag">&lt;/if&gt;</span>
    <span class="xml-tag">&lt;/where&gt;</span>
    <span class="xml-keyword">ORDER BY</span> c.create_time <span class="xml-keyword">DESC</span>
<span class="xml-tag">&lt;/select&gt;</span></code></pre>
            
            <h4>4.3 缓存策略</h4>
            <pre><code class="java"><span class="java-comment">// 添加Redis缓存支持</span>
<span class="java-annotation">@Service</span>
<span class="java-keyword">public class</span> CrmContactsCacheService {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> RedisTemplate&lt;String, Object&gt; redisTemplate;
    
    <span class="java-keyword">private static final</span> String CONTACT_CACHE_KEY = <span class="java-string">"crm:contacts:"</span>;
    <span class="java-keyword">private static final</span> String USER_HIERARCHY_CACHE_KEY = <span class="java-string">"crm:hierarchy:"</span>;
    <span class="java-keyword">private static final</span> <span class="java-keyword">int</span> CACHE_EXPIRE_MINUTES = <span class="java-number">30</span>;
    
    <span class="java-comment">/**
     * 缓存用户下属关系
     */</span>
    <span class="java-keyword">public</span> <span class="java-keyword">void</span> cacheUserSubordinates(Long userId, List&lt;Long&gt; subordinateIds) {
        String key = USER_HIERARCHY_CACHE_KEY + userId;
        redisTemplate.opsForValue().set(key, subordinateIds, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }
    
    <span class="java-comment">/**
     * 获取缓存的用户下属关系
     */</span>
    <span class="java-annotation">@SuppressWarnings</span>(<span class="java-string">"unchecked"</span>)
    <span class="java-keyword">public</span> List&lt;Long&gt; getCachedUserSubordinates(Long userId) {
        String key = USER_HIERARCHY_CACHE_KEY + userId;
        <span class="java-keyword">return</span> (List&lt;Long&gt;) redisTemplate.opsForValue().get(key);
    }
    
    <span class="java-comment">/**
     * 清除用户层级缓存
     */</span>
    <span class="java-keyword">public</span> <span class="java-keyword">void</span> clearUserHierarchyCache(Long userId) {
        String pattern = USER_HIERARCHY_CACHE_KEY + <span class="java-string">"*"</span>;
        Set&lt;String&gt; keys = redisTemplate.keys(pattern);
        <span class="java-keyword">if</span> (keys != <span class="java-keyword">null</span> && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }
}</code></pre>
            
            <p><strong>预估时间：</strong> 2天</p>
        </div>
        
        <div class="phase-box">
            <h3>第五阶段：测试与优化 <span class="priority-low">优先级：低</span></h3>
            
            <div class="mermaid">
flowchart TD
    A[测试阶段] --> B[单元测试]
    A --> C[集成测试]
    A --> D[功能测试]
    A --> E[性能测试]
    
    B --> F[Service层测试]
    B --> G[Controller层测试]
    B --> H[Mapper层测试]
    
    C --> I[API接口测试]
    C --> J[数据库集成测试]
    C --> K[缓存集成测试]
    
    D --> L[筛选功能测试]
    D --> M[关注功能测试]
    D --> N[用户权限测试]
    
    E --> O[查询性能测试]
    E --> P[并发测试]
    E --> Q[压力测试]
    
    R[部署阶段] --> S[数据库迁移]
    R --> T[配置更新]
    R --> U[代码部署]
    R --> V[验证测试]
            </div>
            
            <h4>5.1 单元测试</h4>
            <pre><code class="java"><span class="java-comment">// CrmUserHierarchyServiceTest.java</span>
<span class="java-annotation">@SpringBootTest</span>
<span class="java-keyword">class</span> CrmUserHierarchyServiceTest {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> ICrmUserHierarchyService crmUserHierarchyService;
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testGetSubordinateIds() {
        <span class="java-comment">// 测试获取下属ID列表</span>
        Long userId = <span class="java-number">1L</span>;
        List&lt;Long&gt; subordinateIds = crmUserHierarchyService.getSubordinateIds(userId);
        assertThat(subordinateIds).isNotNull();
        assertThat(subordinateIds).isNotEmpty();
    }
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testInsertUserHierarchy() {
        <span class="java-comment">// 测试插入用户层级关系</span>
        CrmUserHierarchy hierarchy = <span class="java-keyword">new</span> CrmUserHierarchy();
        hierarchy.setUserId(<span class="java-number">2L</span>);
        hierarchy.setSuperiorId(<span class="java-number">1L</span>);
        hierarchy.setLevel(<span class="java-number">1</span>);
        
        <span class="java-keyword">int</span> result = crmUserHierarchyService.insertUserHierarchy(hierarchy);
        assertThat(result).isEqualTo(<span class="java-number">1</span>);
    }
}

<span class="java-comment">// CrmContactFollowServiceTest.java</span>
<span class="java-annotation">@SpringBootTest</span>
<span class="java-keyword">class</span> CrmContactFollowServiceTest {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> ICrmContactFollowService crmContactFollowService;
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testFollowContact() {
        <span class="java-comment">// 测试关注联系人</span>
        Long contactId = <span class="java-number">1L</span>;
        Long followerId = <span class="java-number">1L</span>;
        
        <span class="java-keyword">int</span> result = crmContactFollowService.followContact(contactId, followerId);
        assertThat(result).isEqualTo(<span class="java-number">1</span>);
        
        <span class="java-comment">// 验证关注状态</span>
        <span class="java-keyword">boolean</span> isFollowing = crmContactFollowService.isFollowing(contactId, followerId);
        assertThat(isFollowing).isTrue();
    }
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testUnfollowContact() {
        <span class="java-comment">// 测试取消关注</span>
        Long contactId = <span class="java-number">1L</span>;
        Long followerId = <span class="java-number">1L</span>;
        
        <span class="java-keyword">int</span> result = crmContactFollowService.unfollowContact(contactId, followerId);
        assertThat(result).isEqualTo(<span class="java-number">1</span>);
        
        <span class="java-comment">// 验证取消关注状态</span>
        <span class="java-keyword">boolean</span> isFollowing = crmContactFollowService.isFollowing(contactId, followerId);
        assertThat(isFollowing).isFalse();
    }
}</code></pre>
            
            <h4>5.2 集成测试</h4>
            <pre><code class="java"><span class="java-comment">// CrmContactsControllerIntegrationTest.java</span>
<span class="java-annotation">@SpringBootTest</span>
<span class="java-annotation">@AutoConfigureTestDatabase</span>(replace = AutoConfigureTestDatabase.Replace.NONE)
<span class="java-keyword">class</span> CrmContactsControllerIntegrationTest {
    
    <span class="java-annotation">@Autowired</span>
    <span class="java-keyword">private</span> TestRestTemplate restTemplate;
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testGetContactsListWithFilter() {
        <span class="java-comment">// 测试筛选功能</span>
        String url = <span class="java-string">"/crm/contacts/list?filterType=mine&pageNum=1&pageSize=10"</span>;
        
        ResponseEntity&lt;String&gt; response = restTemplate.getForEntity(url, String.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        <span class="java-comment">// 验证返回数据格式</span>
        String responseBody = response.getBody();
        assertThat(responseBody).contains(<span class="java-string">"rows"</span>);
        assertThat(responseBody).contains(<span class="java-string">"total"</span>);
    }
    
    <span class="java-annotation">@Test</span>
    <span class="java-keyword">void</span> testFollowContactAPI() {
        <span class="java-comment">// 测试关注API</span>
        Long contactId = <span class="java-number">1L</span>;
        String url = <span class="java-string">"/crm/contact/follow/add/"</span> + contactId;
        
        ResponseEntity&lt;String&gt; response = restTemplate.postForEntity(url, <span class="java-keyword">null</span>, String.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        <span class="java-comment">// 验证关注状态</span>
        String statusUrl = <span class="java-string">"/crm/contact/follow/status/"</span> + contactId;
        ResponseEntity&lt;String&gt; statusResponse = restTemplate.getForEntity(statusUrl, String.class);
        assertThat(statusResponse.getBody()).contains(<span class="java-string">"true"</span>);
    }
}</code></pre>
            
            <h4>5.3 性能测试</h4>
            <pre><code class="java"><span class="java-comment">// 性能测试脚本</span>
<span class="java-annotation">@Test</span>
<span class="java-keyword">void</span> testQueryPerformance() {
    <span class="java-comment">// 测试查询性能</span>
    <span class="java-keyword">long</span> startTime = System.currentTimeMillis();
    
    <span class="java-keyword">for</span> (<span class="java-keyword">int</span> i = <span class="java-number">0</span>; i &lt; <span class="java-number">1000</span>; i++) {
        CrmContacts queryParams = <span class="java-keyword">new</span> CrmContacts();
        queryParams.setResponsiblePersonId(<span class="java-number">1L</span>);
        crmContactsService.selectCrmContactsList(queryParams);
    }
    
    <span class="java-keyword">long</span> endTime = System.currentTimeMillis();
    <span class="java-keyword">long</span> duration = endTime - startTime;
    
    <span class="java-comment">// 验证查询时间在可接受范围内（例如：1000次查询在5秒内完成）</span>
    assertThat(duration).isLessThan(<span class="java-number">5000</span>);
    System.out.println(<span class="java-string">"1000次查询耗时: "</span> + duration + <span class="java-string">"ms"</span>);
}

<span class="java-annotation">@Test</span>
<span class="java-keyword">void</span> testConcurrentAccess() {
    <span class="java-comment">// 并发测试</span>
    <span class="java-keyword">int</span> threadCount = <span class="java-number">10</span>;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch latch = <span class="java-keyword">new</span> CountDownLatch(threadCount);
    
    <span class="java-keyword">for</span> (<span class="java-keyword">int</span> i = <span class="java-number">0</span>; i &lt; threadCount; i++) {
        executor.submit(() -&gt; {
            <span class="java-keyword">try</span> {
                <span class="java-comment">// 模拟并发查询</span>
                CrmContacts queryParams = <span class="java-keyword">new</span> CrmContacts();
                crmContactsService.selectCrmContactsList(queryParams);
            } <span class="java-keyword">finally</span> {
                latch.countDown();
            }
        });
    }
    
    <span class="java-keyword">try</span> {
        latch.await(<span class="java-number">10</span>, TimeUnit.SECONDS);
    } <span class="java-keyword">catch</span> (InterruptedException e) {
        fail(<span class="java-string">"并发测试超时"</span>);
    }
    
    executor.shutdown();
}</code></pre>
            
            <p><strong>预估时间：</strong> 2天</p>
        </div>
        
        <h2>实施时间表</h2>
        
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>任务</th>
                    <th>预估时间</th>
                    <th>负责人</th>
                    <th>依赖关系</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="3">第一阶段</td>
                    <td>创建用户关系表</td>
                    <td>0.5天</td>
                    <td>后端开发</td>
                    <td>无</td>
                </tr>
                <tr>
                    <td>创建联系人关注表</td>
                    <td>0.5天</td>
                    <td>后端开发</td>
                    <td>无</td>
                </tr>
                <tr>
                    <td>优化sys_user表</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>数据迁移方案</td>
                </tr>
                <tr>
                    <td rowspan="3">第二阶段</td>
                    <td>增强Controller</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>第一阶段完成</td>
                </tr>
                <tr>
                    <td>用户层级服务</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>用户关系表</td>
                </tr>
                <tr>
                    <td>联系人关注服务</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>关注表</td>
                </tr>
                <tr>
                    <td rowspan="2">第三阶段</td>
                    <td>增强Mapper</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>第二阶段完成</td>
                </tr>
                <tr>
                    <td>创建新Mapper</td>
                    <td>1天</td>
                    <td>后端开发</td>
                    <td>服务层完成</td>
                </tr>
                <tr>
                    <td rowspan="3">第四阶段</td>
                    <td>前端筛选优化</td>
                    <td>0.5天</td>
                    <td>前端开发</td>
                    <td>后端API完成</td>
                </tr>
                <tr>
                    <td>API调用增强</td>
                    <td>0.5天</td>
                    <td>前端开发</td>
                    <td>后端API完成</td>
                </tr>
                <tr>
                    <td>关注功能组件</td>
                    <td>1天</td>
                    <td>前端开发</td>
                    <td>关注API完成</td>
                </tr>
                <tr>
                    <td>第五阶段</td>
                    <td>测试与优化</td>
                    <td>2天</td>
                    <td>全栈开发</td>
                    <td>前四阶段完成</td>
                </tr>
                <tr style="background-color: #e8f4fd; font-weight: bold;">
                    <td colspan="2"><strong>总计</strong></td>
                    <td><strong>11天</strong></td>
                    <td colspan="2">包含缓冲时间</td>
                </tr>
            </tbody>
        </table>
        
        <h2>技术要点</h2>
        
        <h3>1. 数据库设计原则</h3>
        <ul>
            <li><strong>规范化设计</strong>：避免数据冗余，保持数据一致性</li>
            <li><strong>性能考虑</strong>：合理设置索引，优化查询性能</li>
            <li><strong>扩展性</strong>：预留扩展字段，支持未来功能增强</li>
        </ul>
        
        <h3>2. API设计原则</h3>
        <ul>
            <li><strong>RESTful风格</strong>：遵循REST API设计规范</li>
            <li><strong>参数验证</strong>：严格的输入参数验证和错误处理</li>
            <li><strong>权限控制</strong>：基于角色的访问控制</li>
        </ul>
        
        <h3>3. 前端优化策略</h3>
        <ul>
            <li><strong>组件复用</strong>：最大化复用现有组件</li>
            <li><strong>状态管理</strong>：合理使用Vuex管理应用状态</li>
            <li><strong>用户体验</strong>：加载状态、错误提示、操作反馈</li>
        </ul>
        
        <h2>风险评估与应对</h2>
        
        <div class="risk-box">
            <h3>技术风险</h3>
            <ul>
                <li><strong>高风险</strong>：数据迁移可能影响现有数据</li>
                <li><strong>中风险</strong>：复杂查询可能影响性能</li>
                <li><strong>低风险</strong>：前端组件兼容性问题</li>
            </ul>
            
            <h3>业务风险</h3>
            <ul>
                <li><strong>中风险</strong>：用户层级关系初始化可能不准确</li>
                <li><strong>低风险</strong>：新功能学习成本</li>
            </ul>
            
            <h3>应对策略</h3>
            <ul>
                <li><strong>数据备份</strong>：实施前完整备份数据库</li>
                <li><strong>分步实施</strong>：采用渐进式部署，降低风险</li>
                <li><strong>回滚方案</strong>：准备完整的回滚脚本</li>
                <li><strong>性能监控</strong>：实时监控系统性能指标</li>
                <li><strong>用户培训</strong>：提供详细的功能使用说明</li>
            </ul>
        </div>
        
        <h2>任务分解清单</h2>
        
        <div class="phase-box">
            <h3>第一阶段：数据库结构改进任务</h3>
            <ul>
                <li><input type="checkbox" id="task1-1" checked> <label for="task1-1">创建crm_user_hierarchy表SQL脚本</label></li>
                <li><input type="checkbox" id="task1-2" checked> <label for="task1-2">创建crm_contact_followers表SQL脚本</label></li>
                <li><input type="checkbox" id="task1-3" checked> <label for="task1-3">执行数据库表创建脚本</label></li>
                <li><input type="checkbox" id="task1-4" checked> <label for="task1-4">编写用户层级关系数据初始化脚本</label></li>
                <li><input type="checkbox" id="task1-5" checked> <label for="task1-5">执行数据初始化脚本</label></li>
                <li><input type="checkbox" id="task1-6" checked> <label for="task1-6">验证数据库表结构和数据完整性</label></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：后端API增强任务</h3>
            <ul>
                <li><input type="checkbox" id="task2-1" checked> <label for="task2-1">创建CrmUserHierarchy实体类</label></li>
                <li><input type="checkbox" id="task2-2" checked> <label for="task2-2">创建CrmContactFollowers实体类</label></li>
                <li><input type="checkbox" id="task2-3" checked> <label for="task2-3">创建ICrmUserHierarchyService接口</label></li>
                <li><input type="checkbox" id="task2-4" checked> <label for="task2-4">实现CrmUserHierarchyServiceImpl服务类</label></li>
                <li><input type="checkbox" id="task2-5" checked> <label for="task2-5">创建ICrmContactFollowService接口</label></li>
                <li><input type="checkbox" id="task2-6" checked> <label for="task2-6">实现CrmContactFollowServiceImpl服务类</label></li>
                <li><input type="checkbox" id="task2-7" checked> <label for="task2-7">创建CrmContactFollowController控制器</label></li>
                <li><input type="checkbox" id="task2-8" checked> <label for="task2-8">修改CrmContactsController增加filterType参数处理</label></li>
                <li><input type="checkbox" id="task2-9" checked> <label for="task2-9">在CrmContacts实体类中添加subordinateIds和followerId字段</label></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：数据访问层优化任务</h3>
            <ul>
                <li><input type="checkbox" id="task3-1" checked> <label for="task3-1">创建CrmUserHierarchyMapper接口</label></li>
                <li><input type="checkbox" id="task3-2" checked> <label for="task3-2">创建CrmUserHierarchyMapper.xml映射文件</label></li>
                <li><input type="checkbox" id="task3-3" checked> <label for="task3-3">创建CrmContactFollowersMapper接口</label></li>
                <li><input type="checkbox" id="task3-4" checked> <label for="task3-4">创建CrmContactFollowersMapper.xml映射文件</label></li>
                <li><input type="checkbox" id="task3-5"> <label for="task3-5">修改CrmContactsMapper.xml增强查询逻辑</label></li>
                <li><input type="checkbox" id="task3-6"> <label for="task3-6">添加支持下属ID列表查询的SQL</label></li>
                <li><input type="checkbox" id="task3-7"> <label for="task3-7">添加支持关注状态查询的SQL</label></li>
                <li><input type="checkbox" id="task3-8"> <label for="task3-8">优化查询性能，添加必要的JOIN条件</label></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第四阶段：数据库优化与性能提升任务</h3>
            <ul>
                <li><input type="checkbox" id="task4-1"> <label for="task4-1">为crm_business_contacts表添加复合索引</label></li>
                <li><input type="checkbox" id="task4-2"> <label for="task4-2">为crm_user_hierarchy表添加索引</label></li>
                <li><input type="checkbox" id="task4-3"> <label for="task4-3">为crm_contact_followers表添加索引</label></li>
                <li><input type="checkbox" id="task4-4"> <label for="task4-4">创建CrmContactsCacheService缓存服务</label></li>
                <li><input type="checkbox" id="task4-5"> <label for="task4-5">实现用户下属关系缓存逻辑</label></li>
                <li><input type="checkbox" id="task4-6"> <label for="task4-6">实现关注状态缓存逻辑</label></li>
                <li><input type="checkbox" id="task4-7"> <label for="task4-7">配置Redis缓存参数</label></li>
                <li><input type="checkbox" id="task4-8"> <label for="task4-8">优化SQL查询语句性能</label></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第五阶段：测试与优化任务</h3>
            <ul>
                <li><input type="checkbox" id="task5-1"> <label for="task5-1">编写CrmUserHierarchyService单元测试</label></li>
                <li><input type="checkbox" id="task5-2"> <label for="task5-2">编写CrmContactFollowService单元测试</label></li>
                <li><input type="checkbox" id="task5-3"> <label for="task5-3">编写CrmContactsController集成测试</label></li>
                <li><input type="checkbox" id="task5-4"> <label for="task5-4">编写CrmContactFollowController集成测试</label></li>
                <li><input type="checkbox" id="task5-5"> <label for="task5-5">编写Mapper层数据访问测试</label></li>
                <li><input type="checkbox" id="task5-6"> <label for="task5-6">执行查询性能测试</label></li>
                <li><input type="checkbox" id="task5-7"> <label for="task5-7">执行并发访问测试</label></li>
                <li><input type="checkbox" id="task5-8"> <label for="task5-8">执行功能完整性测试</label></li>
                <li><input type="checkbox" id="task5-9"> <label for="task5-9">修复测试中发现的问题</label></li>
                <li><input type="checkbox" id="task5-10"> <label for="task5-10">性能优化和代码重构</label></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第六阶段：部署与上线任务</h3>
            <ul>
                <li><input type="checkbox" id="task6-1"> <label for="task6-1">准备生产环境数据库迁移脚本</label></li>
                <li><input type="checkbox" id="task6-2"> <label for="task6-2">更新生产环境配置文件</label></li>
                <li><input type="checkbox" id="task6-3"> <label for="task6-3">编写自动化部署脚本</label></li>
                <li><input type="checkbox" id="task6-4"> <label for="task6-4">执行生产环境数据库备份</label></li>
                <li><input type="checkbox" id="task6-5"> <label for="task6-5">执行数据库迁移脚本</label></li>
                <li><input type="checkbox" id="task6-6"> <label for="task6-6">部署后端应用代码</label></li>
                <li><input type="checkbox" id="task6-7"> <label for="task6-7">部署前端静态资源</label></li>
                <li><input type="checkbox" id="task6-8"> <label for="task6-8">执行部署后验证测试</label></li>
                <li><input type="checkbox" id="task6-9"> <label for="task6-9">监控系统运行状态</label></li>
                <li><input type="checkbox" id="task6-10"> <label for="task6-10">准备回滚方案（如需要）</label></li>
            </ul>
        </div>
        
        <h2>验收标准</h2>
        
        <h3>功能验收</h3>
        <ul>
            <li><input type="checkbox" id="accept1-1"> <label for="accept1-1">"我负责的"筛选功能正常工作</label></li>
            <li><input type="checkbox" id="accept1-2"> <label for="accept1-2">"下属负责的"筛选功能正常工作</label></li>
            <li><input type="checkbox" id="accept1-3"> <label for="accept1-3">"我关注的联系人"筛选功能正常工作</label></li>
            <li><input type="checkbox" id="accept1-4"> <label for="accept1-4">联系人关注/取消关注功能正常</label></li>
            <li><input type="checkbox" id="accept1-5"> <label for="accept1-5">用户层级关系管理功能正常</label></li>
        </ul>
        
        <h3>性能验收</h3>
        <ul>
            <li><input type="checkbox" id="accept2-1"> <label for="accept2-1">联系人列表查询响应时间 < 2秒</label></li>
            <li><input type="checkbox" id="accept2-2"> <label for="accept2-2">筛选操作响应时间 < 1秒</label></li>
            <li><input type="checkbox" id="accept2-3"> <label for="accept2-3">关注操作响应时间 < 500ms</label></li>
        </ul>
        
        <h3>数据完整性验收</h3>
        <ul>
            <li><input type="checkbox" id="accept3-1"> <label for="accept3-1">现有联系人数据完整性保持</label></li>
            <li><input type="checkbox" id="accept3-2"> <label for="accept3-2">用户关系数据准确性验证</label></li>
            <li><input type="checkbox" id="accept3-3"> <label for="accept3-3">关注关系数据一致性验证</label></li>
        </ul>
        
        <h2>后续优化方向</h2>
        
        <div class="highlight">
            <h3>短期优化（1-2个月）</h3>
            <ul>
                <li>增加联系人标签功能</li>
                <li>实现联系人批量操作</li>
                <li>优化移动端体验</li>
            </ul>
            
            <h3>中期优化（3-6个月）</h3>
            <ul>
                <li>集成客户关系图谱</li>
                <li>实现智能推荐功能</li>
                <li>增加数据分析报表</li>
            </ul>
            
            <h3>长期规划（6个月以上）</h3>
            <ul>
                <li>AI辅助联系人管理</li>
                <li>与外部系统集成</li>
                <li>高级权限管理</li>
            </ul>
        </div>
        
        <hr>
        <p style="text-align: center; color: #7f8c8d; margin-top: 30px;">
            <em>文档生成时间：2024年12月</em><br>
            <em>项目：CRM系统 - 联系人管理模块改进计划</em><br>
            <em>版本：v1.0</em>
        </p>
        
        <div class="phase-box">
            <h3>第六阶段：部署与上线 <span class="priority-medium">优先级：中</span></h3>
            
            <h4>6.1 数据库迁移</h4>
            <pre><code class="sql"><span class="sql-comment">-- 生产环境数据库迁移脚本</span>
<span class="sql-comment">-- migration_v1.0.sql</span>

<span class="sql-comment">-- 1. 创建用户层级关系表</span>
<span class="sql-keyword">CREATE TABLE</span> <span class="sql-identifier">crm_user_hierarchy</span> (
    <span class="sql-identifier">id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
    <span class="sql-identifier">user_id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'用户ID'</span>,
    <span class="sql-identifier">superior_id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'上级用户ID'</span>,
    <span class="sql-identifier">level</span> <span class="sql-type">int</span> <span class="sql-keyword">DEFAULT</span> <span class="sql-number">0</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'层级深度'</span>,
    <span class="sql-identifier">create_time</span> <span class="sql-type">datetime</span> <span class="sql-keyword">DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
    <span class="sql-identifier">update_time</span> <span class="sql-type">datetime</span> <span class="sql-keyword">DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
    <span class="sql-keyword">PRIMARY KEY</span> (<span class="sql-identifier">id</span>),
    <span class="sql-keyword">UNIQUE KEY</span> <span class="sql-identifier">uk_user_superior</span> (<span class="sql-identifier">user_id</span>, <span class="sql-identifier">superior_id</span>),
    <span class="sql-keyword">KEY</span> <span class="sql-identifier">idx_user_id</span> (<span class="sql-identifier">user_id</span>),
    <span class="sql-keyword">KEY</span> <span class="sql-identifier">idx_superior_id</span> (<span class="sql-identifier">superior_id</span>)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-identifier">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-identifier">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM用户层级关系表'</span>;

<span class="sql-comment">-- 2. 创建联系人关注表</span>
<span class="sql-keyword">CREATE TABLE</span> <span class="sql-identifier">crm_contact_followers</span> (
    <span class="sql-identifier">id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
    <span class="sql-identifier">contact_id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系人ID'</span>,
    <span class="sql-identifier">follower_id</span> <span class="sql-type">bigint</span> <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注者用户ID'</span>,
    <span class="sql-identifier">follow_time</span> <span class="sql-type">datetime</span> <span class="sql-keyword">DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关注时间'</span>,
    <span class="sql-keyword">PRIMARY KEY</span> (<span class="sql-identifier">id</span>),
    <span class="sql-keyword">UNIQUE KEY</span> <span class="sql-identifier">uk_contact_follower</span> (<span class="sql-identifier">contact_id</span>, <span class="sql-identifier">follower_id</span>),
    <span class="sql-keyword">KEY</span> <span class="sql-identifier">idx_contact_id</span> (<span class="sql-identifier">contact_id</span>),
    <span class="sql-keyword">KEY</span> <span class="sql-identifier">idx_follower_id</span> (<span class="sql-identifier">follower_id</span>)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-identifier">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-identifier">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'CRM联系人关注表'</span>;

<span class="sql-comment">-- 3. 为现有联系人表添加索引优化</span>
<span class="sql-keyword">ALTER TABLE</span> <span class="sql-identifier">crm_business_contacts</span> 
<span class="sql-keyword">ADD INDEX</span> <span class="sql-identifier">idx_responsible_person</span> (<span class="sql-identifier">responsible_person_id</span>),
<span class="sql-keyword">ADD INDEX</span> <span class="sql-identifier">idx_create_time</span> (<span class="sql-identifier">create_time</span>),
<span class="sql-keyword">ADD INDEX</span> <span class="sql-identifier">idx_update_time</span> (<span class="sql-identifier">update_time</span>);</code></pre>
            
            <h4>6.2 配置更新</h4>
            <pre><code class="yaml"><span class="yaml-comment"># application-prod.yml 生产环境配置更新</span>
<span class="yaml-key">spring</span>:
  <span class="yaml-key">redis</span>:
    <span class="yaml-key">host</span>: <span class="yaml-value">redis-cluster.company.com</span>
    <span class="yaml-key">port</span>: <span class="yaml-value">6379</span>
    <span class="yaml-key">password</span>: <span class="yaml-value">${REDIS_PASSWORD}</span>
    <span class="yaml-key">timeout</span>: <span class="yaml-value">3000ms</span>
    <span class="yaml-key">lettuce</span>:
      <span class="yaml-key">pool</span>:
        <span class="yaml-key">max-active</span>: <span class="yaml-value">20</span>
        <span class="yaml-key">max-idle</span>: <span class="yaml-value">10</span>
        <span class="yaml-key">min-idle</span>: <span class="yaml-value">5</span>

<span class="yaml-comment"># CRM模块缓存配置</span>
<span class="yaml-key">crm</span>:
  <span class="yaml-key">cache</span>:
    <span class="yaml-key">user-hierarchy</span>:
      <span class="yaml-key">ttl</span>: <span class="yaml-value">3600</span> <span class="yaml-comment"># 用户层级关系缓存1小时</span>
    <span class="yaml-key">contact-follow</span>:
      <span class="yaml-key">ttl</span>: <span class="yaml-value">1800</span> <span class="yaml-comment"># 关注状态缓存30分钟</span></code></pre>
            
            <h4>6.3 部署脚本</h4>
            <pre><code class="bash"><span class="bash-comment">#!/bin/bash</span>
<span class="bash-comment"># deploy.sh - 联系人管理模块部署脚本</span>

<span class="bash-keyword">echo</span> <span class="bash-string">"开始部署联系人管理模块改进..."</span>

<span class="bash-comment"># 1. 备份数据库</span>
<span class="bash-keyword">echo</span> <span class="bash-string">"备份数据库..."</span>
mysqldump -h <span class="bash-variable">$DB_HOST</span> -u <span class="bash-variable">$DB_USER</span> -p<span class="bash-variable">$DB_PASSWORD</span> <span class="bash-variable">$DB_NAME</span> &gt; backup_<span class="bash-variable">$(date +%Y%m%d_%H%M%S)</span>.sql

<span class="bash-comment"># 2. 执行数据库迁移</span>
<span class="bash-keyword">echo</span> <span class="bash-string">"执行数据库迁移..."</span>
mysql -h <span class="bash-variable">$DB_HOST</span> -u <span class="bash-variable">$DB_USER</span> -p<span class="bash-variable">$DB_PASSWORD</span> <span class="bash-variable">$DB_NAME</span> &lt; migration_v1.0.sql

<span class="bash-comment"># 3. 部署应用代码</span>
<span class="bash-keyword">echo</span> <span class="bash-string">"部署应用代码..."</span>
systemctl stop crm-application
cp -r ./target/crm-*.jar /opt/crm-app/
cp -r ./frontend/dist/* /opt/crm-app/static/
systemctl start crm-application

<span class="bash-comment"># 4. 验证部署结果</span>
<span class="bash-keyword">echo</span> <span class="bash-string">"验证部署结果..."</span>
sleep 30
response=<span class="bash-variable">$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health)</span>
<span class="bash-keyword">if</span> [ <span class="bash-variable">$response</span> -eq 200 ]; <span class="bash-keyword">then</span>
    <span class="bash-keyword">echo</span> <span class="bash-string">"部署成功！"</span>
<span class="bash-keyword">else</span>
    <span class="bash-keyword">echo</span> <span class="bash-string">"部署失败！"</span>
    <span class="bash-keyword">exit</span> 1
<span class="bash-keyword">fi</span></code></pre>
            
            <p><strong>预估时间：</strong> 1天</p>
        </div>
        
    </div>
</body>
</html>