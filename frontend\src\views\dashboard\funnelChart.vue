<template>
    <div class="chart-container">
        <!-- 销售漏斗图 -->
        <div ref="funnelChart" class="funnel-chart"></div>

        <!-- 数据表格 -->
        <el-table :data="tableData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="stage" label="阶段"></el-table-column>
            <el-table-column prop="amount" label="金额"></el-table-column>
            <el-table-column prop="opportunity" label="商机"></el-table-column>
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            chart: null,
            tableData: [
                { stage: '线索', amount: 30000, opportunity: 'A公司' },
                { stage: '洽谈', amount: 20000, opportunity: 'B公司' },
                { stage: '报价', amount: 15000, opportunity: 'C公司' },
                { stage: '成交', amount: 10000, opportunity: 'D公司' }
            ]
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initFunnelChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initFunnelChart() {
            if (this.chart) {
                this.chart.dispose();
            }
            
            const chartDom = this.$refs.funnelChart;
            if (!chartDom) return;
            
            this.chart = echarts.init(chartDom);
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c} ({d}%)'
                },
                series: [
                    {
                        name: '销售漏斗',
                        type: 'funnel',
                        left: '10%',
                        top: 20,
                        bottom: 20,
                        width: '80%',
                        min: 0,
                        max: 30000,
                        minSize: '0%',
                        maxSize: '100%',
                        sort: 'descending',
                        gap: 2,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                        labelLine: {
                            length: 10,
                            lineStyle: {
                                width: 1,
                                type: 'solid'
                            }
                        },
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        emphasis: {
                            label: {
                                fontSize: 20
                            }
                        },
                        data: [
                            { value: 30000, name: '线索' },
                            { value: 20000, name: '洽谈' },
                            { value: 15000, name: '报价' },
                            { value: 10000, name: '成交' }
                        ]
                    }
                ]
            };
            this.chart.setOption(option);
        }
    }
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.funnel-chart {
    width: 100%;
    height: 400px;
}
</style>