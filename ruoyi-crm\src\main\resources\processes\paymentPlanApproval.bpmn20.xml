<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:activiti="http://activiti.org/bpmn"
             targetNamespace="http://activiti.org/bpmn"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://www.omg.org/spec/BPMN/20100524/BPMN20.xsd">

  <process id="paymentPlanApproval" name="回款计划审批流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <startEvent id="start" name="开始审批" />
    
    <!-- 部门主管审批 -->
    <userTask id="departmentApproval" name="部门主管审批" 
              activiti:assignee="${submitterId}" 
              activiti:candidateGroups="department_manager">
      <documentation>部门主管对回款计划进行初步审批</documentation>
    </userTask>
    
    <!-- 部门审批结果判断 -->
    <exclusiveGateway id="departmentDecision" name="部门审批结果" />
    
    <!-- 财务审批 (金额>=50000) -->
    <userTask id="financeApproval" name="财务审批"
              activiti:candidateGroups="finance_manager">
      <documentation>财务部门对回款计划进行审批</documentation>
    </userTask>
    
    <!-- 财务审批结果判断 -->
    <exclusiveGateway id="financeDecision" name="财务审批结果" />
    
    <!-- 总经理审批 (金额>=100000) -->
    <userTask id="generalManagerApproval" name="总经理审批"
              activiti:candidateGroups="general_manager">
      <documentation>总经理对大额回款计划进行最终审批</documentation>
    </userTask>
    
    <!-- 总经理审批结果判断 -->
    <exclusiveGateway id="generalManagerDecision" name="总经理审批结果" />
    
    <!-- 审批通过 -->
    <serviceTask id="approvalSuccess" name="审批通过处理"
                 activiti:class="com.ruoyi.activiti.service.ApprovalSuccessService">
      <documentation>处理审批通过后的业务逻辑</documentation>
    </serviceTask>
    
    <!-- 审批拒绝 -->
    <serviceTask id="approvalReject" name="审批拒绝处理"
                 activiti:class="com.ruoyi.activiti.service.ApprovalRejectService">
      <documentation>处理审批拒绝后的业务逻辑</documentation>
    </serviceTask>
    
    <!-- 结束事件 -->
    <endEvent id="endSuccess" name="审批完成" />
    <endEvent id="endReject" name="审批拒绝" />
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="departmentApproval" />
    
    <sequenceFlow id="flow2" sourceRef="departmentApproval" targetRef="departmentDecision" />
    
    <!-- 部门审批通过，判断是否需要财务审批 -->
    <sequenceFlow id="flow3" sourceRef="departmentDecision" targetRef="financeApproval" name="通过且需要财务审批">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == true && requiresFinanceApproval == true}
      </conditionExpression>
    </sequenceFlow>
    
    <!-- 部门审批通过，不需要财务审批，直接成功 -->
    <sequenceFlow id="flow4" sourceRef="departmentDecision" targetRef="approvalSuccess" name="通过且无需财务审批">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == true && requiresFinanceApproval == false}
      </conditionExpression>
    </sequenceFlow>
    
    <!-- 部门审批拒绝 -->
    <sequenceFlow id="flow5" sourceRef="departmentDecision" targetRef="approvalReject" name="部门审批拒绝">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == false}
      </conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="flow6" sourceRef="financeApproval" targetRef="financeDecision" />
    
    <!-- 财务审批通过，判断是否需要总经理审批 -->
    <sequenceFlow id="flow7" sourceRef="financeDecision" targetRef="generalManagerApproval" name="通过且需要总经理审批">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == true && requiresGeneralManagerApproval == true}
      </conditionExpression>
    </sequenceFlow>
    
    <!-- 财务审批通过，不需要总经理审批，直接成功 -->
    <sequenceFlow id="flow8" sourceRef="financeDecision" targetRef="approvalSuccess" name="通过且无需总经理审批">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == true && requiresGeneralManagerApproval == false}
      </conditionExpression>
    </sequenceFlow>
    
    <!-- 财务审批拒绝 -->
    <sequenceFlow id="flow9" sourceRef="financeDecision" targetRef="approvalReject" name="财务审批拒绝">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == false}
      </conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="flow10" sourceRef="generalManagerApproval" targetRef="generalManagerDecision" />
    
    <!-- 总经理审批通过 -->
    <sequenceFlow id="flow11" sourceRef="generalManagerDecision" targetRef="approvalSuccess" name="总经理审批通过">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == true}
      </conditionExpression>
    </sequenceFlow>
    
    <!-- 总经理审批拒绝 -->
    <sequenceFlow id="flow12" sourceRef="generalManagerDecision" targetRef="approvalReject" name="总经理审批拒绝">
      <conditionExpression xsi:type="tFormalExpression">
        ${approved == false}
      </conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="flow13" sourceRef="approvalSuccess" targetRef="endSuccess" />
    <sequenceFlow id="flow14" sourceRef="approvalReject" targetRef="endReject" />
    
  </process>
  
</definitions>