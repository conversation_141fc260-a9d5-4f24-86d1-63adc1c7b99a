<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MessageBox 独立测试</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 50px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .test-button {
            padding: 12px 24px;
            font-size: 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: #337ab7;
        }
        .info {
            margin-top: 20px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 4px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>MessageBox 独立测试</h1>
        <p>这是一个完全独立的测试页面，用于排除项目代码干扰</p>
        
        <button class="test-button" onclick="testBasicAlert()">基础 Alert</button>
        <button class="test-button" onclick="testConfirm()">确认对话框</button>
        <button class="test-button" onclick="testWithOptions()">完整选项测试</button>
        
        <div class="info">
            <h3>测试指引：</h3>
            <ul>
                <li>点击按钮观察 MessageBox 是否正常显示</li>
                <li>检查遮罩层是否出现（黑色半透明背景）</li>
                <li>检查对话框是否居中显示</li>
                <li>检查按钮是否可以正常点击</li>
                <li>打开浏览器开发者工具查看控制台输出</li>
            </ul>
            <p><strong>状态：</strong> <span id="status">准备测试...</span></p>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 创建 Vue 应用
        const app = createApp({
            data() {
                return {
                    status: '准备测试...'
                }
            }
        });

        // 使用 Element Plus
        app.use(ElementPlus);
        
        // 挂载应用
        app.mount('#app-placeholder');

        // 更新状态显示
        function updateStatus(msg) {
            document.getElementById('status').textContent = msg;
            console.log('状态更新:', msg);
        }

        // 基础 Alert 测试
        async function testBasicAlert() {
            updateStatus('测试基础 Alert...');
            console.log('=== 开始基础 Alert 测试 ===');
            
            try {
                console.log('ElMessageBox:', ElMessageBox);
                await ElMessageBox.alert('这是一个基础的消息提示', '提示', {
                    confirmButtonText: '确定'
                });
                updateStatus('基础 Alert 测试成功');
                ElMessage.success('测试成功！');
            } catch (error) {
                console.error('基础 Alert 测试失败:', error);
                updateStatus('基础 Alert 测试失败: ' + error.message);
                ElMessage.error('测试失败');
            }
        }

        // 确认对话框测试
        async function testConfirm() {
            updateStatus('测试确认对话框...');
            console.log('=== 开始确认对话框测试 ===');
            
            try {
                await ElMessageBox.confirm('这是一个确认对话框', '确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                updateStatus('确认对话框测试成功');
                ElMessage.success('确认成功！');
            } catch (error) {
                if (error === 'cancel') {
                    updateStatus('用户取消了操作');
                    ElMessage.info('已取消');
                } else {
                    console.error('确认对话框测试失败:', error);
                    updateStatus('确认对话框测试失败: ' + error.message);
                    ElMessage.error('测试失败');
                }
            }
        }

        // 完整选项测试
        async function testWithOptions() {
            updateStatus('测试完整选项...');
            console.log('=== 开始完整选项测试 ===');
            
            try {
                await ElMessageBox.confirm('这是一个带完整选项的确认对话框', '完整测试', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true,
                    closeOnClickModal: false,
                    closeOnPressEscape: true,
                    showClose: true,
                    lockScroll: true
                });
                updateStatus('完整选项测试成功');
                ElMessage.success('完整测试成功！');
            } catch (error) {
                if (error === 'cancel') {
                    updateStatus('用户取消了完整测试');
                    ElMessage.info('已取消完整测试');
                } else {
                    console.error('完整选项测试失败:', error);
                    updateStatus('完整选项测试失败: ' + error.message);
                    ElMessage.error('完整测试失败');
                }
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            updateStatus('页面加载完成，Element Plus 已就绪');
            console.log('Element Plus 版本:', ElementPlus.version);
            console.log('ElMessageBox:', ElMessageBox);
        });
    </script>

    <!-- 隐藏的 Vue 挂载点 -->
    <div id="app-placeholder" style="display: none;"></div>
</body>
</html> 