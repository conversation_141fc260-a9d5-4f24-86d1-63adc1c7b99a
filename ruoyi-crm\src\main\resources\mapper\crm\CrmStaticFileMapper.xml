<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmStaticFileMapper">
    
    <resultMap type="CrmStaticFile" id="CrmStaticFileResult">
        <result property="id" column="id"/>
        <result property="moduleType" column="module_type"/>
        <result property="moduleId" column="module_id"/>
        <result property="storageType" column="storage_type"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="fileExtension" column="file_extension"/>
        <result property="md5Hash" column="md5_hash"/>
        <result property="metadata" column="metadata"/>
        <result property="description" column="description"/>
        <result property="tags" column="tags"/>
        <result property="isPublic" column="is_public"/>
        <result property="downloadCount" column="download_count"/>
        <result property="viewCount" column="view_count"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectCrmStaticFileVo">
        select id, module_type, module_id, storage_type, file_url, file_name, file_size, file_type, file_extension, md5_hash, metadata, description, tags, is_public, download_count, view_count, del_flag, create_by, create_time, update_by, update_time, remark from crm_static_files
    </sql>

    <select id="selectCrmStaticFileList" parameterType="CrmStaticFile" resultMap="CrmStaticFileResult">
        <include refid="selectCrmStaticFileVo"/>
        <where>  
            <if test="moduleType != null  and moduleType != ''"> and module_type = #{moduleType}</if>
            <if test="moduleId != null"> and module_id = #{moduleId}</if>
            <if test="storageType != null  and storageType != ''"> and storage_type = #{storageType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileExtension != null  and fileExtension != ''"> and file_extension = #{fileExtension}</if>
            <if test="md5Hash != null  and md5Hash != ''"> and md5_hash = #{md5Hash}</if>
            <if test="metadata != null  and metadata != ''"> and metadata = #{metadata}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="tags != null  and tags != ''"> and tags = #{tags}</if>
            <if test="isPublic != null "> and is_public = #{isPublic}</if>
            <if test="downloadCount != null "> and download_count = #{downloadCount}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
        </where>
    </select>
    
    <select id="selectCrmStaticFileById" parameterType="Long" resultMap="CrmStaticFileResult">
        <include refid="selectCrmStaticFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCrmStaticFile" parameterType="CrmStaticFile" useGeneratedKeys="true" keyProperty="id">
        insert into crm_static_files
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleType != null">module_type,</if>
            <if test="moduleId != null">module_id,</if>
            <if test="storageType != null">storage_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileExtension != null">file_extension,</if>
            <if test="md5Hash != null">md5_hash,</if>
            <if test="metadata != null">metadata,</if>
            <if test="description != null">description,</if>
            <if test="tags != null">tags,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="moduleType != null">#{moduleType},</if>
            <if test="moduleId != null">#{moduleId},</if>
            <if test="storageType != null">#{storageType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileExtension != null">#{fileExtension},</if>
            <if test="md5Hash != null">#{md5Hash},</if>
            <if test="metadata != null">#{metadata},</if>
            <if test="description != null">#{description},</if>
            <if test="tags != null">#{tags},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCrmStaticFile" parameterType="CrmStaticFile">
        update crm_static_files
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleType != null">module_type = #{moduleType},</if>
            <if test="moduleId != null">module_id = #{moduleId},</if>
            <if test="storageType != null">storage_type = #{storageType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileExtension != null">file_extension = #{fileExtension},</if>
            <if test="md5Hash != null">md5_hash = #{md5Hash},</if>
            <if test="metadata != null">metadata = #{metadata},</if>
            <if test="description != null">description = #{description},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmStaticFileById" parameterType="Long">
        delete from crm_static_files where id = #{id}
    </delete>

    <delete id="deleteCrmStaticFileByIds" parameterType="String">
        delete from crm_static_files where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateDownloadCount" parameterType="Long">
        update crm_static_files set download_count = download_count + 1 where id = #{id}
    </update>

    <update id="updateViewCount" parameterType="Long">
        update crm_static_files set view_count = view_count + 1 where id = #{id}
    </update>
</mapper> 