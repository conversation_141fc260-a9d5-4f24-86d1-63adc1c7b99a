# 分表配置激活说明

## 📋 `application-sharding.yml` 生效条件

### 1. **配置文件激活方式**

`application-sharding.yml` 是一个Spring Boot Profile配置文件，需要被明确激活才能生效。

#### 方式一：通过主配置文件引入（推荐）

在 `application.yml` 中添加：

```yaml
spring:
  profiles:
    include: sharding  # 引入application-sharding.yml配置
```

**说明**：
- `sharding` 对应文件名 `application-sharding.yml` 中的 `sharding` 部分
- 这种方式会在所有环境下都激活分表配置

#### 方式二：通过启动参数激活

```bash
# 方式2.1：命令行参数
java -jar ruoyi-crm.jar --spring.profiles.active=sharding

# 方式2.2：JVM参数
java -Dspring.profiles.active=sharding -jar ruoyi-crm.jar

# 方式2.3：环境变量
export SPRING_PROFILES_ACTIVE=sharding
java -jar ruoyi-crm.jar
```

#### 方式三：在IDE中激活

在IDE的运行配置中设置：
- **Program arguments**: `--spring.profiles.active=sharding`
- **VM options**: `-Dspring.profiles.active=sharding`
- **Environment variables**: `SPRING_PROFILES_ACTIVE=sharding`

### 2. **配置生效验证**

#### 验证方式一：查看启动日志

启动应用时，如果配置生效，会看到类似日志：

```
2024-12-22 23:20:00.123  INFO --- [main] o.s.core.env.ConfigurableEnvironment : 
The following profiles are active: sharding

2024-12-22 23:20:01.456  INFO --- [main] c.r.c.config.ShardingConfiguration : 
分表配置已加载: enabled=true, dataRetentionMonths=36
```

#### 验证方式二：通过API检查

启动后调用配置查询接口：

```bash
curl http://localhost:8080/crm/table-sharding/config
```

如果返回配置信息，说明配置已生效：

```json
{
  "enabled": true,
  "dataRetentionMonths": 36,
  "preCreateMonths": 3,
  "cleanupCron": "0 0 2 1 * ?",
  "strategies": {
    "crm_business_leads": {
      "type": "MONTH",
      "enabled": true
    }
  }
}
```

#### 验证方式三：检查Bean注册

通过Spring Boot Actuator查看Bean注册情况：

```bash
curl http://localhost:8080/actuator/beans | grep -i sharding
```

### 3. **不同环境的配置策略**

#### 开发环境配置

**文件**: `application-dev.yml`
```yaml
spring:
  profiles:
    include: sharding

crm:
  sharding:
    enabled: true  # 开发环境启用分表测试
```

#### 测试环境配置

**文件**: `application-test.yml`
```yaml
spring:
  profiles:
    include: sharding

crm:
  sharding:
    enabled: true
    data-retention-months: 12  # 测试环境保留时间短一些
```

#### 生产环境配置

**文件**: `application-prod.yml`
```yaml
spring:
  profiles:
    include: sharding

crm:
  sharding:
    enabled: true
    data-retention-months: 36
    cleanup-cron: "0 0 2 1 * ?"  # 生产环境定时清理
```

### 4. **配置优先级**

Spring Boot配置的加载优先级（从高到低）：

1. **命令行参数** (`--spring.profiles.active=sharding`)
2. **JVM系统属性** (`-Dspring.profiles.active=sharding`)
3. **环境变量** (`SPRING_PROFILES_ACTIVE=sharding`)
4. **application.yml中的spring.profiles.include**
5. **默认配置**

### 5. **配置覆盖规则**

#### 示例：多配置文件覆盖

如果同时存在多个配置文件：

```
application.yml              # 基础配置
application-sharding.yml     # 分表配置
application-dev.yml          # 开发环境配置
```

激活 `dev` 和 `sharding` profile：

```bash
java -jar app.jar --spring.profiles.active=dev,sharding
```

**配置合并顺序**：
1. `application.yml` (基础)
2. `application-sharding.yml` (分表配置)
3. `application-dev.yml` (环境配置，优先级最高)

### 6. **条件化配置**

#### 使用 @ConditionalOnProperty

```java
@Configuration
@ConditionalOnProperty(prefix = "crm.sharding", name = "enabled", havingValue = "true")
public class ShardingAutoConfiguration {
    // 只有当 crm.sharding.enabled=true 时才会创建这些Bean
}
```

#### 使用 @Profile

```java
@Configuration
@Profile("sharding")
public class ShardingConfiguration {
    // 只有当 sharding profile 激活时才会加载
}
```

### 7. **常见问题排查**

#### 问题1：配置文件没有生效

**检查清单**：
- [ ] 文件名是否正确：`application-sharding.yml`
- [ ] 文件位置是否正确：`src/main/resources/`
- [ ] Profile是否正确激活
- [ ] YAML语法是否正确

**解决方案**：
```bash
# 检查当前激活的Profile
curl http://localhost:8080/actuator/env | grep "spring.profiles.active"

# 检查配置是否加载
curl http://localhost:8080/actuator/configprops | grep -i sharding
```

#### 问题2：配置部分生效

**可能原因**：
- YAML缩进错误
- 配置被其他文件覆盖
- Bean创建失败

**调试方法**：
```java
@Component
public class ConfigDebugger {
    
    @Value("${crm.sharding.enabled:false}")
    private boolean shardingEnabled;
    
    @PostConstruct
    public void debugConfig() {
        log.info("分表配置状态: enabled={}", shardingEnabled);
    }
}
```

#### 问题3：动态切换配置

**运行时禁用分表**：
```bash
# 通过管理接口动态修改（需要实现）
curl -X POST http://localhost:8080/crm/table-sharding/config \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'
```

### 8. **最佳实践**

#### 推荐的配置激活方式

1. **开发阶段**：在 `application.yml` 中使用 `spring.profiles.include`
2. **部署阶段**：通过启动参数控制
3. **容器化部署**：通过环境变量控制

#### 配置文件组织

```
src/main/resources/
├── application.yml              # 基础配置
├── application-sharding.yml     # 分表配置
├── application-dev.yml          # 开发环境
├── application-test.yml         # 测试环境
└── application-prod.yml         # 生产环境
```

#### Docker部署示例

```dockerfile
# Dockerfile
ENV SPRING_PROFILES_ACTIVE=prod,sharding
CMD ["java", "-jar", "ruoyi-crm.jar"]
```

```yaml
# docker-compose.yml
version: '3'
services:
  crm-app:
    image: ruoyi-crm:latest
    environment:
      - SPRING_PROFILES_ACTIVE=prod,sharding
      - CRM_SHARDING_ENABLED=true
```

### 9. **配置监控**

#### 添加配置变更监控

```java
@Component
public class ShardingConfigMonitor {
    
    @EventListener
    public void handleConfigChange(EnvironmentChangeEvent event) {
        if (event.getKeys().stream().anyMatch(key -> key.startsWith("crm.sharding"))) {
            log.info("分表配置发生变更: {}", event.getKeys());
            // 重新初始化分表管理器
        }
    }
}
```

---

## 总结

`application-sharding.yml` 的生效需要：

1. ✅ **正确的文件命名和位置**
2. ✅ **激活对应的Spring Profile**
3. ✅ **正确的YAML语法**
4. ✅ **相关Bean的正确创建**

最简单的激活方式是在 `application.yml` 中添加：
```yaml
spring:
  profiles:
    include: sharding
```

这样可以确保分表配置在所有环境下都能正确加载和生效。 