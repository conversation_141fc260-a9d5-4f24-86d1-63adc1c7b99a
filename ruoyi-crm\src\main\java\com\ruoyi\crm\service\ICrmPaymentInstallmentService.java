package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;

/**
 * 回款分期Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface ICrmPaymentInstallmentService 
{
    /**
     * 查询回款分期
     * 
     * @param id 回款分期主键
     * @return 回款分期
     */
    public CrmPaymentInstallment selectCrmPaymentInstallmentById(Long id);

    /**
     * 查询回款分期列表
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 回款分期集合
     */
    public List<CrmPaymentInstallment> selectCrmPaymentInstallmentList(CrmPaymentInstallment crmPaymentInstallment);

    /**
     * 根据计划ID查询分期列表
     * 
     * @param planId 计划ID
     * @return 回款分期集合
     */
    public List<CrmPaymentInstallment> selectInstallmentsByPlanId(Long planId);

    /**
     * 新增回款分期
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 结果
     */
    public int insertCrmPaymentInstallment(CrmPaymentInstallment crmPaymentInstallment);

    /**
     * 批量新增回款分期
     * 
     * @param installments 回款分期列表
     * @return 结果
     */
    public int batchInsertInstallments(List<CrmPaymentInstallment> installments);

    /**
     * 修改回款分期
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 结果
     */
    public int updateCrmPaymentInstallment(CrmPaymentInstallment crmPaymentInstallment);

    /**
     * 批量删除回款分期
     * 
     * @param ids 需要删除的回款分期主键集合
     * @return 结果
     */
    public int deleteCrmPaymentInstallmentByIds(Long[] ids);

    /**
     * 删除回款分期信息
     * 
     * @param id 回款分期主键
     * @return 结果
     */
    public int deleteCrmPaymentInstallmentById(Long id);

    /**
     * 根据计划ID删除分期
     * 
     * @param planId 计划ID
     * @return 结果
     */
    public int deleteInstallmentsByPlanId(Long planId);

    /**
     * 更新分期状态
     * 
     * @param id 分期ID
     * @param status 状态
     * @return 结果
     */
    public int updateInstallmentStatus(Long id, String status);

    /**
     * 更新逾期天数
     * 
     * @return 结果
     */
    public int updateOverdueDays();

    /**
     * 记录实际回款
     * 
     * @param installmentId 分期ID
     * @param actualAmount 实际回款金额
     * @param paymentVoucher 付款凭证
     * @return 结果
     */
    public int recordActualPayment(Long installmentId, java.math.BigDecimal actualAmount, String paymentVoucher);
}