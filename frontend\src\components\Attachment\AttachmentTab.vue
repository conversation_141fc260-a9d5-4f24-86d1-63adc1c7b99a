<template>
  <div class="attachment-tab">
    <!-- 上传区域 -->
    <div class="upload-section" v-if="!readonly">
      <el-upload
        ref="uploadRef"
        class="upload-dragger"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :multiple="true"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        drag
      >
        <div class="upload-content">
          <i class="el-icon-upload upload-icon"></i>
          <div class="upload-text">将文件拖拽到此处，或点击上传</div>
          <div class="upload-hint">支持 PDF、图片、Word、Excel 等格式，单个文件不超过 10MB</div>
        </div>
      </el-upload>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar-section" v-if="attachments.length > 0">
      <div class="toolbar-left">
        <span class="attachment-count">共 {{ attachments.length }} 个附件</span>
        <span class="total-size" v-if="statistics.totalSize">
          总大小: {{ formatFileSize(statistics.totalSize) }}
        </span>
      </div>
      <div class="toolbar-right">
        <el-button
          v-if="!readonly && selectedAttachments.length > 0"
          type="danger"
          size="mini"
          @click="handleBatchDelete"
        >
          批量删除 ({{ selectedAttachments.length }})
        </el-button>
        <el-button-group>
          <el-button
            :class="{ active: viewMode === 'list' }"
            size="mini"
            @click="viewMode = 'list'"
          >
            <i class="el-icon-menu"></i>
          </el-button>
          <el-button
            :class="{ active: viewMode === 'grid' }"
            size="mini"
            @click="viewMode = 'grid'"
          >
            <i class="el-icon-s-grid"></i>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 附件列表 - 列表布局 -->
    <div v-if="viewMode === 'list'" class="list-layout">
      <div v-if="attachments.length === 0" class="empty-state">
        <i class="el-icon-document-copy empty-icon"></i>
        <p class="empty-text">暂无附件</p>
      </div>
      
      <div v-else class="attachment-list">
        <div
          v-for="attachment in attachments"
          :key="attachment.id"
          class="list-item"
          :class="{ selected: isSelected(attachment.id) }"
          @click="toggleSelect(attachment.id)"
        >
          <!-- 文件图标 -->
          <div class="list-icon" :class="getIconClass(attachment.category)">
            {{ getCategoryIcon(attachment.category) }}
          </div>
          
          <!-- 文件信息 -->
          <div class="list-info">
            <h4 class="file-name" :title="attachment.originalName">
              {{ attachment.originalName }}
            </h4>
            <div class="list-meta">
              <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
              <span class="upload-time">{{ formatTime(attachment.uploadTime) }}</span>
              <span class="upload-by">{{ attachment.uploadBy }}</span>
              <span v-if="attachment.downloadCount > 0" class="download-count">
                下载 {{ attachment.downloadCount }} 次
              </span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="list-actions" @click.stop>
            <el-button
              link
              size="mini"
              @click="handlePreview(attachment)"
              v-if="canPreview(attachment)"
            >
              预览
            </el-button>
            <el-button
              link
              size="mini"
              @click="handleDownload(attachment)"
            >
              下载
            </el-button>
            <el-button
              v-if="!readonly"
              link
              size="mini"
              style="color: #f56c6c"
              @click="handleDelete(attachment)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件列表 - 网格布局 -->
    <div v-else class="grid-layout">
      <div v-if="attachments.length === 0" class="empty-state">
        <i class="el-icon-document-copy empty-icon"></i>
        <p class="empty-text">暂无附件</p>
      </div>
      
      <div v-else class="attachment-grid">
        <div
          v-for="attachment in attachments"
          :key="attachment.id"
          class="grid-item"
          :class="{ selected: isSelected(attachment.id) }"
          @click="toggleSelect(attachment.id)"
        >
          <div class="grid-icon" :class="getIconClass(attachment.category)">
            {{ getCategoryIcon(attachment.category) }}
          </div>
          <div class="grid-info">
            <h4 class="file-name" :title="attachment.originalName">
              {{ attachment.originalName }}
            </h4>
            <div class="grid-meta">
              <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
              <span class="upload-time">{{ formatTime(attachment.uploadTime) }}</span>
            </div>
          </div>
          <div class="grid-actions" @click.stop>
            <el-button
              link
              size="mini"
              @click="handlePreview(attachment)"
              v-if="canPreview(attachment)"
            >
              预览
            </el-button>
            <el-button
              link
              size="mini"
              @click="handleDownload(attachment)"
            >
              下载
            </el-button>
            <el-button
              v-if="!readonly"
              link
              size="mini"
              style="color: #f56c6c"
              @click="handleDelete(attachment)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewVisible"
      width="80%"
      :before-close="handleClosePreview"
    >
      <div class="preview-content">
        <div v-if="previewData.previewType === 'image'" class="image-preview">
          <img :src="previewData.previewUrl" alt="图片预览" style="max-width: 100%;" />
        </div>
        <div v-else-if="previewData.previewType === 'pdf'" class="pdf-preview">
          <iframe
            :src="previewData.previewUrl"
            width="100%"
            height="600px"
            frameborder="0"
          ></iframe>
        </div>
        <div v-else class="file-info-preview">
          <div class="preview-icon" :class="getIconClass(previewData.category)">
            {{ getCategoryIcon(previewData.category || 'other') }}
          </div>
          <h3>{{ previewData.originalName }}</h3>
          <p>文件大小: {{ previewData.fileSizeFormatted }}</p>
          <p>文件类型: {{ previewData.fileType }}</p>
          <el-button type="primary" @click="handleDownload(previewData)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
    batchDeleteAttachments,
    downloadAttachment,
    getAttachmentsByEntity,
    getAttachmentStats,
    previewAttachment
} from '@/api/attachment'
import { getToken } from '@/utils/auth'

export default {
  name: 'AttachmentTab',
  props: {
    entityType: {
      type: String,
      required: true
    },
    entityId: {
      type: [String, Number],
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      attachments: [],
      statistics: {},
      selectedAttachments: [],
      viewMode: 'list', // list | grid
      previewVisible: false,
      previewData: {},
      loading: false
    }
  },
  computed: {
    uploadUrl() {
      return `${process.env.VUE_APP_BASE_API}/front/crm/attachments/${this.entityType}/${this.entityId}`
    },
    uploadHeaders() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    },
    uploadData() {
      return {
        entityType: this.entityType,
        entityId: this.entityId
      }
    }
  },
  mounted() {
    this.loadAttachments()
    this.loadStatistics()
  },
  methods: {
    // 加载附件列表
    async loadAttachments() {
      try {
        this.loading = true
        const response = await getAttachmentsByEntity(this.entityType, this.entityId)
        this.attachments = response.data || []
        this.$emit('update-count', this.attachments.length)
      } catch (error) {
        this.$message.error('加载附件列表失败')
        console.error('Load attachments error:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载统计信息
    async loadStatistics() {
      try {
        const response = await getAttachmentStats(this.entityType, this.entityId)
        this.statistics = response.data || {}
      } catch (error) {
        console.error('Load statistics error:', error)
      }
    },

    // 上传前验证
    beforeUpload(file) {
      const isValidSize = file.size <= 10 * 1024 * 1024 // 10MB
      if (!isValidSize) {
        this.$message.error('文件大小不能超过 10MB')
        return false
      }

      const validTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain'
      ]

      if (!validTypes.includes(file.type)) {
        this.$message.error('不支持的文件类型')
        return false
      }

      return true
    },

    // 上传成功
    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        this.$message.success('上传成功')
        this.loadAttachments()
        this.loadStatistics()
        this.$emit('attachment-change', this.attachments)
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(error) {
      this.$message.error('上传失败')
      console.error('Upload error:', error)
    },

    // 预览附件
    async handlePreview(attachment) {
      try {
        const response = await previewAttachment(attachment.id)
        if (response.code === 200) {
          this.previewData = response.data
          this.previewVisible = true
        } else {
          this.$message.error('获取预览信息失败')
        }
      } catch (error) {
        this.$message.error('预览失败')
        console.error('Preview error:', error)
      }
    },

    // 下载附件
    async handleDownload(attachment) {
      try {
        const response = await downloadAttachment(attachment.id)
        
        // 创建下载链接
        const blob = new Blob([response])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = attachment.originalName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('下载成功')
      } catch (error) {
        this.$message.error('下载失败')
        console.error('Download error:', error)
      }
    },

    // 删除附件
    handleDelete(attachment) {
      this.$confirm(`确定要删除附件 "${attachment.originalName}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAttachment(attachment.id)
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedAttachments.length === 0) {
        this.$message.warning('请选择要删除的附件')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedAttachments.length} 个附件吗？`, '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchDelete()
      })
    },

    // 执行删除
    async deleteAttachment(id) {
      try {
        const response = await batchDeleteAttachments([id])
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadAttachments()
          this.loadStatistics()
          this.$emit('attachment-change', this.attachments)
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
        console.error('Delete error:', error)
      }
    },

    // 执行批量删除
    async batchDelete() {
      try {
        const response = await batchDeleteAttachments(this.selectedAttachments)
        if (response.code === 200) {
          this.$message.success('批量删除成功')
          this.selectedAttachments = []
          this.loadAttachments()
          this.loadStatistics()
          this.$emit('attachment-change', this.attachments)
        } else {
          this.$message.error(response.msg || '批量删除失败')
        }
      } catch (error) {
        this.$message.error('批量删除失败')
        console.error('Batch delete error:', error)
      }
    },

    // 选择/取消选择
    toggleSelect(id) {
      if (this.readonly) return
      
      const index = this.selectedAttachments.indexOf(id)
      if (index > -1) {
        this.selectedAttachments.splice(index, 1)
      } else {
        this.selectedAttachments.push(id)
      }
    },

    // 是否已选择
    isSelected(id) {
      return this.selectedAttachments.includes(id)
    },

    // 是否可预览
    canPreview(attachment) {
      const previewTypes = ['image/', 'application/pdf', 'text/']
      return previewTypes.some(type => attachment.fileType && attachment.fileType.startsWith(type))
    },

    // 获取分类图标
    getCategoryIcon(category) {
      const icons = {
        contract: '📄',
        image: '🖼️',
        document: '📝',
        spreadsheet: '📊',
        presentation: '📽️',
        video: '🎥',
        audio: '🎵',
        other: '📁'
      }
      return icons[category] || icons.other
    },

    // 获取图标样式类
    getIconClass(category) {
      return `icon-${category || 'other'}`
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      let fileSize = size
      
      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }
      
      return Math.round(fileSize * 100) / 100 + ' ' + units[index]
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      
      const date = new Date(time)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diffDays < 7) {
        return diffDays + '天前'
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    // 关闭预览
    handleClosePreview() {
      this.previewVisible = false
      this.previewData = {}
    }
  }
}
</script>

<style scoped>
.attachment-tab {
  padding: 16px;
}

/* 上传区域样式 */
.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #bdc3c7;
  border-radius: 12px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.upload-dragger .el-upload-dragger:hover {
  border-color: #3498db;
  background: #ebf3fd;
}

.upload-content {
  text-align: center;
  padding: 20px;
}

.upload-icon {
  font-size: 36px;
  color: #95a5a6;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 6px;
}

.upload-hint {
  font-size: 12px;
  color: #7f8c8d;
}

/* 工具栏样式 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.attachment-count {
  font-weight: 600;
  color: #2c3e50;
}

.total-size {
  font-size: 12px;
  color: #7f8c8d;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.el-button-group .el-button.active {
  background: #409eff;
  color: white;
}

/* 列表布局样式 */
.list-layout {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e8ed;
}

.attachment-list {
  max-height: 500px;
  overflow-y: auto;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s;
  cursor: pointer;
}

.list-item:hover {
  background: #f8f9fa;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item.selected {
  background: #e8f4fd;
  border-left: 3px solid #409eff;
}

.list-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

/* 文件图标颜色 */
.icon-contract { background: #e74c3c; }
.icon-image { background: #2ecc71; }
.icon-document { background: #3498db; }
.icon-spreadsheet { background: #27ae60; }
.icon-presentation { background: #f39c12; }
.icon-video { background: #9b59b6; }
.icon-audio { background: #e67e22; }
.icon-other { background: #95a5a6; }

.list-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-meta {
  font-size: 12px;
  color: #7f8c8d;
  display: flex;
  gap: 16px;
}

.list-actions {
  display: flex;
  gap: 8px;
}

/* 网格布局样式 */
.grid-layout {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.attachment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.grid-item {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 16px;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.grid-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.grid-item.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.grid-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  font-size: 24px;
  color: white;
}

.grid-info {
  text-align: center;
  margin-bottom: 12px;
}

.grid-info .file-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 6px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-meta {
  font-size: 12px;
  color: #7f8c8d;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.grid-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #95a5a6;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin: 0;
}

/* 预览对话框样式 */
.preview-content {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 600px;
  border-radius: 8px;
}

.file-info-preview {
  padding: 40px;
}

.preview-icon {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 40px;
  color: white;
}

.file-info-preview h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 12px;
}

.file-info-preview p {
  color: #7f8c8d;
  margin-bottom: 8px;
}
</style>
