import request from '@/utils/request'
import { ApiResponse, ApiResponseOnlyDataArray } from '~/types'

export interface StaticFileRecord {
  id: number
  fileName: string
  filePath: string
  moduleType: string
  moduleId: number
  createBy: string
  createTime: string
}

export interface ListQueryParams {
  pageNum?: number
  pageSize?: number
  moduleType?: string
  moduleId?: number
}

// 查询静态文件记录列表
export function listStaticfile(query: ListQueryParams): Promise<ApiResponse<{ rows: StaticFileRecord[], total: number }>> {
  return request({
    url: '/crm/staticfile/list',
    method: 'get',
    params: query
  })
}

// 查询静态文件记录详细
export function getStaticfile(id: number): Promise<ApiResponse<StaticFileRecord>> {
  return request({
    url: '/crm/staticfile/' + id,
    method: 'get'
  })
}

// 新增静态文件记录
export function addStaticfile(data: Omit<StaticFileRecord, 'id'>): Promise<ApiResponse<void>> {
  return request({
    url: '/crm/staticfile',
    method: 'post',
    data
  })
}

// 修改静态文件记录
export function updateStaticfile(data: StaticFileRecord): Promise<ApiResponse<void>> {
  return request({
    url: '/crm/staticfile',
    method: 'put',
    data
  })
}

// 删除静态文件记录
export function delStaticfile(id: number): Promise<ApiResponse<void>> {
  return request({
    url: '/crm/staticfile/' + id,
    method: 'delete'
  })
}

// 导出静态文件记录
export function exportStaticfile(query: ListQueryParams): Promise<ApiResponse<void>> {
  return request({
    url: '/crm/staticfile/export',
    method: 'get',
    params: query
  })
}

// 查询模块的静态文件列表
export function listModuleFiles(moduleType: string, moduleId: number): Promise<ApiResponseOnlyDataArray<StaticFileRecord>> {
  return request({
    url: '/crm/staticfile/list',
    method: 'get',
    params: { moduleType, moduleId }
  })
}

interface UploadFileParams {
  file: File
  moduleType: string
  moduleId: number
}

// 上传文件
export function uploadFile(data: UploadFileParams): Promise<ApiResponse<{ fileId: number }>> {
  const formData = new FormData()
  formData.append('file', data.file)
  formData.append('moduleType', data.moduleType)
  formData.append('moduleId', data.moduleId.toString())

  return request({
    url: '/crm/staticfile/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载文件
export function downloadFile(id: number): Promise<Blob> {
  return request({
    url: `/crm/staticfile/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 预览文件
export function previewFile(id: number): Promise<ApiResponse<string>> {
  return request({
    url: '/crm/staticfile/preview/' + id,
    method: 'get'
  })
}

// 删除文件
export function deleteFile(id: number): Promise<ApiResponse<void>> {
  return request({
    url: `/crm/staticfile/${id}`,
    method: 'delete'
  })
}

// 更新文件信息
export function updateFile(data: Partial<StaticFileRecord>): Promise<ApiResponse<void>> {
  return request({
    url: '/crm/staticfile',
    method: 'put',
    data
  })
}
