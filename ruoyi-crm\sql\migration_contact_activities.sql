-- ===================================================================
-- 联系人活动记录数据迁移脚本
-- 作者: 系统重构
-- 日期: 2025-06-27
-- 描述: 将联系人相关的跟进记录从 crm_business_follow_up_records 迁移到 crm_contact_followup_records
-- ===================================================================

-- 1. 数据迁移前的准备工作
-- 1.1 检查源表和目标表是否存在
SELECT 'Source table exists' AS status, COUNT(*) AS record_count 
FROM crm_business_follow_up_records 
WHERE module_type = 'contact' OR related_contact_id IS NOT NULL;

SELECT 'Target table exists' AS status, COUNT(*) AS record_count 
FROM crm_contact_followup_records;

-- 1.2 备份现有数据（可选，建议执行）
-- CREATE TABLE crm_contact_followup_records_backup AS SELECT * FROM crm_contact_followup_records;

-- 2. 数据迁移主脚本
-- 2.1 从旧表迁移联系人相关的跟进记录
INSERT INTO crm_contact_followup_records (
    contact_id,
    follow_up_content,
    next_contact_method,
    follow_up_method,
    next_contact_time,
    communication_result,
    meeting_summary,
    contact_quality,
    related_files,
    creator_id,
    created_at,
    updated_at
)
SELECT 
    related_contact_id,                    -- 联系人ID
    follow_up_content,                     -- 跟进内容
    next_contact_method,                   -- 下次联系方式
    follow_up_method,                      -- 跟进方式
    next_follow_time,                      -- 下次联系时间（字段名映射）
    contact_result,                        -- 沟通结果（字段名映射）
    content,                               -- 会议纪要（使用content字段）
    'normal',                              -- 联系质量评级（默认值，可根据需要调整）
    related_files,                         -- 相关文件
    CAST(creator_id AS UNSIGNED),          -- 创建人ID（转换数据类型）
    created_at,                           -- 创建时间
    updated_at                            -- 更新时间
FROM crm_business_follow_up_records 
WHERE (module_type = 'contact' OR related_contact_id IS NOT NULL)
  AND related_contact_id IS NOT NULL;

-- 3. 数据完整性验证
-- 3.1 检查迁移后的记录数量
SELECT 'Migration result' AS status, 
       COUNT(*) AS migrated_records,
       MIN(created_at) AS earliest_record,
       MAX(created_at) AS latest_record
FROM crm_contact_followup_records;

-- 3.2 检查是否有空的联系人ID
SELECT 'Data integrity check' AS status, 
       COUNT(*) AS records_with_null_contact_id
FROM crm_contact_followup_records 
WHERE contact_id IS NULL;

-- 3.3 验证创建人ID是否正确转换
SELECT 'Creator ID check' AS status,
       COUNT(*) AS total_records,
       COUNT(CASE WHEN creator_id IS NOT NULL THEN 1 END) AS records_with_creator,
       COUNT(CASE WHEN creator_id IS NULL THEN 1 END) AS records_without_creator
FROM crm_contact_followup_records;

-- 3.4 检查日期字段
SELECT 'Date fields check' AS status,
       COUNT(*) AS total_records,
       COUNT(CASE WHEN created_at IS NOT NULL THEN 1 END) AS records_with_created_at,
       COUNT(CASE WHEN updated_at IS NOT NULL THEN 1 END) AS records_with_updated_at,
       COUNT(CASE WHEN next_contact_time IS NOT NULL THEN 1 END) AS records_with_next_contact_time
FROM crm_contact_followup_records;

-- 4. 数据清理（可选 - 在确认迁移成功后执行）
-- 4.1 清理旧表中已迁移的联系人记录（慎重执行）
/*
DELETE FROM crm_business_follow_up_records 
WHERE (module_type = 'contact' OR related_contact_id IS NOT NULL)
  AND related_contact_id IS NOT NULL;
*/

-- 5. 索引优化检查
-- 5.1 检查新表的索引使用情况
EXPLAIN SELECT * FROM crm_contact_followup_records WHERE contact_id = 1;
EXPLAIN SELECT * FROM crm_contact_followup_records WHERE creator_id = 1 AND created_at > '2025-01-01';
EXPLAIN SELECT * FROM crm_contact_followup_records WHERE next_contact_time > NOW();

-- 6. 统计信息更新
ANALYZE TABLE crm_contact_followup_records;

-- ===================================================================
-- 迁移脚本执行说明:
-- 1. 建议在非生产环境先执行测试
-- 2. 执行前请备份相关数据表
-- 3. 字段映射说明:
--    - next_follow_time -> next_contact_time
--    - contact_result -> communication_result  
--    - content -> meeting_summary
--    - creator_id: 从字符串转换为数字类型
-- 4. 默认值说明:
--    - contact_quality 设置为 'normal'，可根据业务需求调整
-- 5. 执行后请验证数据完整性和业务逻辑
-- ===================================================================
