import type { WecomResponse } from '@/types';
import request from '@/utils/request';
import type { CodeImgResponse } from '@/views/login/types';

export interface LoginResult {
  msg: string;
  code: number;
  token: string;
}

interface UserDept {
  deptId: number;
  parentId: number;
  ancestors: string;
  deptName: string;
  orderNum: number;
  leader: string;
  status: string;
  children: UserDept[];
}

interface UserRole {
  roleId: number;
  roleName: string;
  roleKey: string;
  roleSort: number;
  dataScope: string;
  menuCheckStrictly: boolean;
  deptCheckStrictly: boolean;
  status: string;
  admin: boolean;
}

interface UserDetail {
  userId: number;
  deptId: number;
  userName: string;
  nickName: string;
  email: string;
  phonenumber: string;
  sex: string;
  avatar: string;
  status: string;
  delFlag: string;
  loginIp: string;
  loginDate: string;
  dept: UserDept;
  roles: UserRole[];
  admin: boolean;
}

export interface GetInfoResult {
  msg: string;
  code: number;
  permissions: string[];
  roles: string[];
  user: UserDetail;
}

// 登录方法
export function login(username: string, password: string, code: string, uuid: string): Promise<LoginResult> {
  const data = {
    username,
    password,
    code,
    uuid
  };
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  });
}

// 获取用户详细信息
export function getInfo(): Promise<GetInfoResult> {
  return request({
    url: '/getInfo',
    method: 'get'
  });
}

// 退出方法
export function logout(): Promise<null> {
  return request({
    url: '/logout',
    method: 'post'
  });
}

// 获取验证码
export function getCodeImg(): Promise<CodeImgResponse> {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  });
}

// 获取企业微信登录二维码URL
export function getWecomQrCodeUrl(): Promise<WecomResponse<string>> {
  const url = '/wechatwork/login/qrcode';
  return request<WecomResponse<string>>({
    url: url,
    headers: {
      isToken: false
    },
    method: 'get',
    params: {
      state: new Date().getTime().toString() // 使用时间戳作为state参数，用于防止CSRF攻击
    }
  });
}