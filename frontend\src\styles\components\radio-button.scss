// 修复 radio-button 样式问题
.ep-radio-group {
  display: inline-flex;
  align-items: center;
  font-size: 0;

  .ep-radio-button {
    position: relative;
    display: inline-block;
    margin: 0;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 0;
    background: #fff;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;
    box-sizing: border-box;
    vertical-align: top;

    &:hover:not(.is-disabled) {
      color: #409eff;
      border-color: #409eff;
      z-index: 1;
    }

    &.is-active {
      color: #fff;
      background: #409eff;
      border-color: #409eff;
      z-index: 2;

      .ep-radio-button__inner {
        background: transparent;
        color: #fff;
      }
    }

    &.is-disabled {
      cursor: not-allowed;
      color: #c0c4cc;
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }

    &__original-radio {
      position: absolute;
      opacity: 0;
      outline: none;
      z-index: -1;
    }

    &__inner {
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      padding: 8px 16px;
      background: transparent;
      transition: all 0.3s;
      border: none;
      outline: none;
      font-size: 14px;
    }

    // 当有多个radio-button相邻时的样式
    &:not(:first-child) {
      margin-left: -1px;
    }

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &:first-child:last-child {
      border-radius: 4px;
    }
  }
}

// 针对小尺寸的特殊处理
.ep-radio-group.ep-radio-group--small {
  .ep-radio-button {
    height: 28px;
    font-size: 12px;

    .ep-radio-button__inner {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

// 强制覆盖可能的默认样式
.ep-radio-button {
  position: relative !important;
  display: inline-block !important;
  margin: 0 !important;
  border: 1px solid #dcdfe6 !important;
  border-radius: 0 !important;
  background: #fff !important;
  color: #606266 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  user-select: none !important;
  box-sizing: border-box !important;
  vertical-align: top !important;

  &:hover:not(.is-disabled) {
    color: #409eff !important;
    border-color: #409eff !important;
    z-index: 1 !important;
  }

  &.is-active {
    color: #fff !important;
    background: #409eff !important;
    border-color: #409eff !important;
    z-index: 2 !important;
  }

  &:not(:first-child) {
    margin-left: -1px !important;
  }

  &:first-child {
    border-radius: 4px 0 0 4px !important;
  }

  &:last-child {
    border-radius: 0 4px 4px 0 !important;
  }

  &:first-child:last-child {
    border-radius: 4px !important;
  }
}