# CRM系统无密码用户注册登录完整实施方案

## 📋 项目概述

基于现有CRM系统架构，实施无密码双轨用户认证机制：
- **内部员工：** 企业微信OAuth无密码登录
- **外部客户：** 手机短信验证码"润物无声"式注册
- **统一管理：** 手机号作为最终唯一标识
- **完全无密码：** 系统不涉及任何密码设置和管理

## 🔍 现状分析

### 当前数据库表结构
- ✅ `sys_user` - 用户基础信息表（已存在，需修改）
- ✅ `crm_thirdparty_wechat` - 企业微信关联表（已存在）
- ✅ `crm_wecom_config` - 企业微信配置表（已存在）
- ❌ **缺失无密码认证相关表** - 需新增

### 当前代码架构
- ✅ 若依框架基础架构完整
- ✅ CRM业务模块结构清晰
- ✅ SysUser实体类完整
- ❌ **缺失无密码认证模块** - 需新增

## 🚀 实施计划

## 阶段一：数据库设计和创建（1-2天）

### 1.1 新增无密码认证核心表

```sql
-- 用户注册表（无密码设计）
CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码（加密存储）',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `passwordless_token` varchar(255) DEFAULT NULL COMMENT '无密码登录令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表（无密码设计）';

-- 用户认证方式表（支持无密码认证）
CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `auth_token` varchar(500) DEFAULT NULL COMMENT '认证令牌（无密码登录）',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`)
) ENGINE=InnoDB COMMENT='用户认证方式表（支持无密码认证）';

-- 客户线索表增强（无密码注册）
CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `registration_method` varchar(20) DEFAULT 'sms' COMMENT '注册方式：sms-短信无密码注册',
  `access_token` varchar(255) DEFAULT NULL COMMENT '客户专用访问令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表（无密码注册）';

-- 线索文件表
CREATE TABLE `crm_lead_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小',
  `upload_status` varchar(20) DEFAULT 'uploaded' COMMENT '上传状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`)
) ENGINE=InnoDB COMMENT='线索文件表';

-- 线索沟通记录表
CREATE TABLE `crm_lead_communications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '沟通记录ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `sales_user_id` bigint(20) NOT NULL COMMENT '销售人员ID',
  `communication_type` varchar(50) NOT NULL COMMENT '沟通类型：phone-电话,email-邮件,meeting-会议,visit-拜访',
  `content` text COMMENT '沟通内容',
  `communication_time` datetime NOT NULL COMMENT '沟通时间',
  `status` varchar(20) DEFAULT 'completed' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_sales_user_id` (`sales_user_id`)
) ENGINE=InnoDB COMMENT='线索沟通记录表';
```

### 1.2 数据迁移脚本

```sql
-- 清理客户用户的密码字段
UPDATE sys_user 
SET password = NULL 
WHERE user_type = '01' -- 假设01为客户类型
   OR user_id IN (
       SELECT DISTINCT user_id 
       FROM crm_business_customers 
       WHERE responsible_person_id IS NOT NULL
   );

-- 为现有企业微信用户创建认证方式记录
INSERT INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    tw.user_id,
    'wechat' as auth_type,
    tw.wecom_user_id as auth_identifier,
    1 as is_primary,
    1 as is_active
FROM crm_thirdparty_wechat tw
INNER JOIN sys_user su ON tw.user_id = su.user_id
WHERE su.del_flag = '0';

-- 为现有用户创建手机号认证方式记录
INSERT INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    user_id,
    'phone' as auth_type,
    phonenumber as auth_identifier,
    CASE WHEN user_id NOT IN (SELECT user_id FROM crm_user_auth_methods WHERE auth_type = 'wechat') THEN 1 ELSE 0 END as is_primary,
    1 as is_active
FROM sys_user 
WHERE phonenumber IS NOT NULL 
  AND phonenumber != ''
  AND del_flag = '0';
```

## 阶段二：后端API开发（3-5天）

### 2.1 创建无密码认证实体类

#### CrmUserRegistration.java - 用户注册记录实体
```java
package com.ruoyi.crm.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_user_registration")
public class CrmUserRegistration extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("phone_number")
    private String phoneNumber;
    
    @TableField("registration_type")
    private String registrationType;
    
    @TableField("wechat_union_id")
    private String wechatUnionId;
    
    @TableField("verification_code")
    private String verificationCode;
    
    @TableField("verification_expire_time")
    private LocalDateTime verificationExpireTime;
    
    @TableField("is_verified")
    private Boolean isVerified;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("status")
    private String status;
    
    @TableField("user_type")
    private String userType;
    
    @TableField("source_data")
    private String sourceData;
    
    @TableField("passwordless_token")
    private String passwordlessToken;

    // 常量定义
    public static final String TYPE_WECHAT = "wechat";
    public static final String TYPE_SMS = "sms";
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_VERIFIED = "verified";
    public static final String STATUS_COMPLETED = "completed";
    public static final String USER_TYPE_EMPLOYEE = "employee";
    public static final String USER_TYPE_CUSTOMER = "customer";
}
```

#### CrmUserAuthMethods.java - 用户认证方式实体
```java
package com.ruoyi.crm.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_user_auth_methods")
public class CrmUserAuthMethods extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("auth_type")
    private String authType;
    
    @TableField("auth_identifier")
    private String authIdentifier;
    
    @TableField("is_primary")
    private Boolean isPrimary;
    
    @TableField("is_active")
    private Boolean isActive;
    
    @TableField("bind_time")
    private LocalDateTime bindTime;
    
    @TableField("last_used_time")
    private LocalDateTime lastUsedTime;
    
    @TableField("auth_token")
    private String authToken;
    
    @TableField("token_expire_time")
    private LocalDateTime tokenExpireTime;
    
    @TableField("metadata")
    private String metadata;

    // 认证类型常量
    public static final String AUTH_TYPE_WECHAT = "wechat";
    public static final String AUTH_TYPE_PHONE = "phone";
}
```

### 2.2 创建无密码认证服务接口

#### ICrmPasswordlessAuthService.java
```java
package com.ruoyi.crm.service;

import com.ruoyi.common.core.domain.AjaxResult;

public interface ICrmPasswordlessAuthService {
    /**
     * 企业微信无密码登录
     */
    AjaxResult wechatPasswordlessLogin(String code);
    
    /**
     * 发送短信验证码（无需密码）
     */
    AjaxResult sendPasswordlessCode(String phoneNumber, String userType);
    
    /**
     * 验证码登录/注册（完全无密码）
     */
    AjaxResult passwordlessVerify(String phoneNumber, String verificationCode);
    
    /**
     * 客户专用令牌访问
     */
    AjaxResult accessCustomerZone(String accessToken);
    
    /**
     * 生成无密码访问令牌
     */
    String generatePasswordlessToken(Long userId, String userType);
    
    /**
     * 验证无密码访问令牌
     */
    boolean validatePasswordlessToken(String token);
    
    /**
     * 刷新无密码访问令牌
     */
    String refreshPasswordlessToken(String oldToken);
}
```

### 2.3 无密码认证控制器

#### CrmPasswordlessAuthController.java
```java
package com.ruoyi.crm.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.crm.service.ICrmPasswordlessAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/crm/auth")
public class CrmPasswordlessAuthController extends BaseController {
    
    @Autowired
    private ICrmPasswordlessAuthService passwordlessAuthService;
    
    /**
     * 企业微信无密码登录
     */
    @Anonymous
    @PostMapping("/wechat/login")
    public AjaxResult wechatLogin(@RequestBody WechatLoginRequest request) {
        return passwordlessAuthService.wechatPasswordlessLogin(request.getCode());
    }
    
    /**
     * 发送短信验证码
     */
    @Anonymous
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestBody SmsRequest request) {
        return passwordlessAuthService.sendPasswordlessCode(
            request.getPhoneNumber(), 
            request.getUserType()
        );
    }
    
    /**
     * 验证码登录/注册
     */
    @Anonymous
    @PostMapping("/sms/verify")
    public AjaxResult verifySmsCode(@RequestBody VerifyRequest request) {
        return passwordlessAuthService.passwordlessVerify(
            request.getPhoneNumber(), 
            request.getVerificationCode()
        );
    }
    
    /**
     * 客户专区访问
     */
    @Anonymous
    @GetMapping("/customer/zone")
    public AjaxResult accessCustomerZone(@RequestParam String token) {
        return passwordlessAuthService.accessCustomerZone(token);
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/token/refresh")
    public AjaxResult refreshToken(@RequestParam String token) {
        String newToken = passwordlessAuthService.refreshPasswordlessToken(token);
        return AjaxResult.success("令牌刷新成功", newToken);
    }
}
```

### 2.4 无密码认证服务实现

#### CrmPasswordlessAuthServiceImpl.java（核心逻辑）
```java
package com.ruoyi.crm.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.crm.domain.CrmUserRegistration;
import com.ruoyi.crm.domain.CrmUserAuthMethods;
import com.ruoyi.crm.service.ICrmPasswordlessAuthService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Service
public class CrmPasswordlessAuthServiceImpl implements ICrmPasswordlessAuthService {
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public AjaxResult wechatPasswordlessLogin(String code) {
        try {
            // 1. 通过code获取企业微信用户信息
            WechatUserInfo wechatUser = getWechatUserInfo(code);
            
            // 2. 查找已有用户或创建注册记录
            CrmUserAuthMethods authMethod = findAuthMethodByWechat(wechatUser.getUnionId());
            
            if (authMethod != null && authMethod.getUserId() != null) {
                // 已有用户，检查手机号绑定
                SysUser user = userService.selectUserById(authMethod.getUserId());
                if (user != null && user.getPhonenumber() != null) {
                    // 直接登录
                    String token = generatePasswordlessToken(user.getUserId(), 
                        CrmUserRegistration.USER_TYPE_EMPLOYEE);
                    updateLastUsedTime(authMethod.getId());
                    return AjaxResult.success("登录成功", createLoginResponse(user, token));
                } else {
                    // 需要绑定手机号
                    return AjaxResult.error(1001, "请先绑定手机号");
                }
            } else {
                // 新用户，创建注册记录
                CrmUserRegistration registration = createWechatRegistration(wechatUser);
                return AjaxResult.error(1002, "请完成注册", registration.getId());
            }
        } catch (Exception e) {
            logger.error("企业微信登录失败", e);
            return AjaxResult.error("登录失败，请重试");
        }
    }
    
    @Override
    @Transactional
    public AjaxResult sendPasswordlessCode(String phoneNumber, String userType) {
        try {
            // 1. 校验手机号格式
            if (!isValidPhoneNumber(phoneNumber)) {
                return AjaxResult.error("手机号格式不正确");
            }
            
            // 2. 检查发送频率限制
            String rateLimitKey = "sms:rate:" + phoneNumber;
            if (redisTemplate.hasKey(rateLimitKey)) {
                return AjaxResult.error("发送过于频繁，请稍后再试");
            }
            
            // 3. 生成验证码
            String verificationCode = generateVerificationCode();
            
            // 4. 发送短信
            boolean sent = sendSmsVerificationCode(phoneNumber, verificationCode);
            if (!sent) {
                return AjaxResult.error("短信发送失败，请重试");
            }
            
            // 5. 创建或更新注册记录
            CrmUserRegistration registration = createOrUpdateSmsRegistration(
                phoneNumber, userType, verificationCode);
            
            // 6. 设置频率限制和验证码缓存
            redisTemplate.opsForValue().set(rateLimitKey, "1", 60, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set("sms:code:" + phoneNumber, 
                verificationCode, 5, TimeUnit.MINUTES);
            
            return AjaxResult.success("验证码已发送");
        } catch (Exception e) {
            logger.error("发送验证码失败", e);
            return AjaxResult.error("发送失败，请重试");
        }
    }
    
    @Override
    @Transactional
    public AjaxResult passwordlessVerify(String phoneNumber, String verificationCode) {
        try {
            // 1. 验证验证码
            String cachedCode = (String) redisTemplate.opsForValue()
                .get("sms:code:" + phoneNumber);
            if (cachedCode == null || !cachedCode.equals(verificationCode)) {
                return AjaxResult.error("验证码错误或已过期");
            }
            
            // 2. 查找注册记录
            CrmUserRegistration registration = findRegistrationByPhone(
                phoneNumber, CrmUserRegistration.TYPE_SMS);
            if (registration == null) {
                return AjaxResult.error("注册记录不存在");
            }
            
            // 3. 查找或创建用户
            SysUser user = findOrCreateUser(phoneNumber, registration.getUserType());
            
            // 4. 创建认证方式记录
            createOrUpdateAuthMethod(user.getUserId(), phoneNumber);
            
            // 5. 更新注册记录状态
            updateRegistrationStatus(registration.getId(), 
                CrmUserRegistration.STATUS_COMPLETED, user.getUserId());
            
            // 6. 生成访问令牌
            String token = generatePasswordlessToken(user.getUserId(), 
                registration.getUserType());
            
            // 7. 清除验证码缓存
            redisTemplate.delete("sms:code:" + phoneNumber);
            
            // 8. 如果是客户类型，创建线索记录
            if (CrmUserRegistration.USER_TYPE_CUSTOMER.equals(registration.getUserType())) {
                createCustomerLead(user.getUserId(), phoneNumber, token);
            }
            
            return AjaxResult.success("验证成功", createLoginResponse(user, token));
        } catch (Exception e) {
            logger.error("验证失败", e);
            return AjaxResult.error("验证失败，请重试");
        }
    }
    
    @Override
    public String generatePasswordlessToken(Long userId, String userType) {
        try {
            // 使用JWT生成无密码令牌
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", userId);
            claims.put("userType", userType);
            claims.put("loginType", "passwordless");
            claims.put("timestamp", System.currentTimeMillis());
            
            // 设置过期时间：员工30天，客户7天
            long expireTime = CrmUserRegistration.USER_TYPE_EMPLOYEE.equals(userType) 
                ? 30 * 24 * 60 * 60 * 1000L : 7 * 24 * 60 * 60 * 1000L;
            
            return JwtUtil.createToken(claims, expireTime);
        } catch (Exception e) {
            logger.error("生成令牌失败", e);
            return null;
        }
    }
    
    // 其他辅助方法...
    private WechatUserInfo getWechatUserInfo(String code) {
        // 调用企业微信API获取用户信息
        // 实现企业微信OAuth流程
    }
    
    private String generateVerificationCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }
    
    private boolean sendSmsVerificationCode(String phoneNumber, String code) {
        // 调用短信服务API发送验证码
        // 集成阿里云SMS、腾讯云SMS等
    }
}
```

## 阶段三：前端开发（2-3天）

### 3.1 前端API接口定义

#### passwordless-auth.ts
```typescript
// frontend/src/api/passwordless-auth.ts
import request from '@/utils/request'

// 请求类型定义
interface WechatLoginRequest {
  code: string
}

interface SmsRequest {
  phoneNumber: string
  userType: 'employee' | 'customer'
}

interface VerifyRequest {
  phoneNumber: string
  verificationCode: string
}

interface AuthResponse {
  token: string
  userInfo: {
    userId: number
    userName: string
    nickName: string
    userType: string
    phoneNumber: string
  }
}

// 无密码认证API
export const passwordlessAuthAPI = {
  // 企业微信无密码登录
  wechatLogin: (data: WechatLoginRequest): Promise<AuthResponse> => {
    return request({
      url: '/crm/auth/wechat/login',
      method: 'post',
      data
    })
  },

  // 发送短信验证码
  sendSmsCode: (data: SmsRequest): Promise<void> => {
    return request({
      url: '/crm/auth/sms/send',
      method: 'post',
      data
    })
  },

  // 验证码登录/注册
  verifySmsCode: (data: VerifyRequest): Promise<AuthResponse> => {
    return request({
      url: '/crm/auth/sms/verify',
      method: 'post',
      data
    })
  },

  // 客户专区访问
  accessCustomerZone: (token: string): Promise<any> => {
    return request({
      url: '/crm/auth/customer/zone',
      method: 'get',
      params: { token }
    })
  },

  // 刷新令牌
  refreshToken: (token: string): Promise<string> => {
    return request({
      url: '/crm/auth/token/refresh',
      method: 'post',
      params: { token }
    })
  }
}
```

### 3.2 无密码登录页面

#### PasswordlessLogin.vue
```vue
<template>
  <div class="passwordless-login-container">
    <div class="login-form">
      <div class="login-header">
        <h2>CRM系统登录</h2>
        <p>安全便捷，无需密码</p>
      </div>

      <!-- 企业微信登录区域 -->
      <div class="wechat-login-section">
        <el-button 
          type="primary" 
          size="large" 
          @click="wechatLogin"
          :loading="wechatLoading"
          class="wechat-login-btn"
        >
          <i class="icon-wechat"></i>
          企业微信登录
        </el-button>
        <p class="login-tip">内部员工请使用企业微信扫码登录</p>
      </div>

      <el-divider>或</el-divider>

      <!-- 手机号登录区域 -->
      <div class="phone-login-section">
        <el-form ref="phoneFormRef" :model="phoneForm" :rules="phoneRules">
          <el-form-item prop="phoneNumber">
            <el-input
              v-model="phoneForm.phoneNumber"
              placeholder="请输入手机号"
              size="large"
              prefix-icon="el-icon-phone"
              maxlength="11"
            />
          </el-form-item>
          
          <el-form-item prop="verificationCode">
            <div class="verification-input">
              <el-input
                v-model="phoneForm.verificationCode"
                placeholder="请输入验证码"
                size="large"
                prefix-icon="el-icon-message"
                maxlength="6"
              />
              <el-button
                @click="sendVerificationCode"
                :disabled="!canSendCode"
                :loading="sendingCode"
                class="send-code-btn"
              >
                {{ codeButtonText }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              @click="phoneLogin"
              :loading="phoneLoading"
              class="phone-login-btn"
            >
              立即登录
            </el-button>
          </el-form-item>
        </el-form>
        <p class="login-tip">
          首次登录将自动创建账户，无需设置密码
        </p>
      </div>
    </div>

    <!-- 企业微信二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="企业微信扫码登录"
      width="400px"
      center
    >
      <div class="qrcode-container">
        <div id="wechat-qrcode"></div>
        <p>请使用企业微信扫描二维码</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { passwordlessAuthAPI } from '@/api/passwordless-auth'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const qrCodeVisible = ref(false)
const wechatLoading = ref(false)
const phoneLoading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)

const phoneForm = reactive({
  phoneNumber: '',
  verificationCode: '',
  userType: 'employee'
})

// 表单验证规则
const phoneRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// 计算属性
const canSendCode = computed(() => {
  return phoneForm.phoneNumber && 
         /^1[3-9]\d{9}$/.test(phoneForm.phoneNumber) && 
         countdown.value === 0
})

const codeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s后重发` : '获取验证码'
})

// 企业微信登录
const wechatLogin = () => {
  wechatLoading.value = true
  qrCodeVisible.value = true
  
  // 初始化企业微信二维码
  initWechatQRCode()
}

const initWechatQRCode = () => {
  // 初始化企业微信扫码登录
  // 具体实现根据企业微信SDK文档
  const redirectUri = `${window.location.origin}/auth/wechat/callback`
  const qrCodeUrl = `https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=${appId}&agentid=${agentId}&redirect_uri=${encodeURIComponent(redirectUri)}&state=login`
  
  // 生成二维码
  new QRCode(document.getElementById('wechat-qrcode'), {
    text: qrCodeUrl,
    width: 200,
    height: 200
  })
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return
  
  try {
    sendingCode.value = true
    await passwordlessAuthAPI.sendSmsCode({
      phoneNumber: phoneForm.phoneNumber,
      userType: phoneForm.userType
    })
    
    ElMessage.success('验证码已发送')
    startCountdown()
  } catch (error) {
    ElMessage.error('验证码发送失败')
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 手机号登录
const phoneLogin = async () => {
  try {
    phoneLoading.value = true
    const response = await passwordlessAuthAPI.verifySmsCode({
      phoneNumber: phoneForm.phoneNumber,
      verificationCode: phoneForm.verificationCode
    })
    
    // 保存用户信息和token
    userStore.setToken(response.token)
    userStore.setUserInfo(response.userInfo)
    
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error) {
    ElMessage.error('登录失败，请检查验证码')
  } finally {
    phoneLoading.value = false
  }
}

// 处理企业微信登录回调
const handleWechatCallback = async (code: string) => {
  try {
    const response = await passwordlessAuthAPI.wechatLogin({ code })
    
    userStore.setToken(response.token)
    userStore.setUserInfo(response.userInfo)
    
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error: any) {
    if (error.code === 1001) {
      // 需要绑定手机号
      router.push('/auth/bind-phone')
    } else if (error.code === 1002) {
      // 需要完成注册
      router.push('/auth/register')
    } else {
      ElMessage.error('登录失败')
    }
  }
}

onMounted(() => {
  // 检查是否有企业微信回调参数
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  if (code) {
    handleWechatCallback(code)
  }
})
</script>

<style scoped>
.passwordless-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90vw;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.wechat-login-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  margin-bottom: 10px;
}

.verification-input {
  display: flex;
  gap: 10px;
}

.verification-input .el-input {
  flex: 1;
}

.send-code-btn {
  min-width: 100px;
}

.phone-login-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
}

.login-tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin: 10px 0;
}

.qrcode-container {
  text-align: center;
  padding: 20px;
}

.qrcode-container p {
  margin-top: 15px;
  color: #666;
}
</style>
```

### 3.3 客户专区页面

#### CustomerZone.vue
```vue
<template>
  <div class="customer-zone">
    <div class="zone-header">
      <h1>{{ customerInfo.name || '客户' }}，欢迎访问项目专区</h1>
      <p>您的项目编号：{{ leadInfo.id }}</p>
    </div>

    <div class="zone-content">
      <el-row :gutter="20">
        <!-- 项目进度 -->
        <el-col :span="12">
          <el-card title="项目进度">
            <div class="progress-container">
              <el-steps :active="currentStep" finish-status="success">
                <el-step title="需求提交" description="文件已上传"></el-step>
                <el-step title="技术评估" description="专业团队评估中"></el-step>
                <el-step title="方案设计" description="定制化方案"></el-step>
                <el-step title="项目实施" description="开始制作"></el-step>
                <el-step title="完成交付" description="项目完成"></el-step>
              </el-steps>
            </div>
            
            <div class="progress-details">
              <h4>当前状态：{{ statusText }}</h4>
              <p>{{ statusDescription }}</p>
              <el-tag :type="statusType">{{ leadInfo.status }}</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 评估结果 -->
        <el-col :span="12">
          <el-card title="评估结果">
            <div v-if="leadInfo.evaluationResult" class="evaluation-content">
              <div v-html="leadInfo.evaluationResult"></div>
              <el-divider></el-divider>
              <p class="eval-note">
                <i class="el-icon-info"></i>
                如有疑问，请联系您的专属客服
              </p>
            </div>
            <div v-else class="no-evaluation">
              <el-empty description="评估结果将在48小时内提供"></el-empty>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 上传的文件 -->
      <el-card title="项目文件" class="files-section">
        <el-table :data="leadFiles" stripe>
          <el-table-column prop="fileName" label="文件名"></el-table-column>
          <el-table-column prop="fileType" label="文件类型"></el-table-column>
          <el-table-column prop="fileSize" label="文件大小" :formatter="formatFileSize"></el-table-column>
          <el-table-column prop="createTime" label="上传时间"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 沟通记录 -->
      <el-card title="沟通记录" class="communication-section">
        <div class="communication-list">
          <div
            v-for="comm in communications"
            :key="comm.id"
            class="communication-item"
          >
            <div class="comm-header">
              <span class="comm-type">{{ getCommTypeText(comm.communicationType) }}</span>
              <span class="comm-time">{{ comm.communicationTime }}</span>
            </div>
            <div class="comm-content">{{ comm.content }}</div>
          </div>
        </div>

        <!-- 在线留言 -->
        <el-divider>在线留言</el-divider>
        <el-form @submit.prevent="submitMessage">
          <el-form-item>
            <el-input
              v-model="messageContent"
              type="textarea"
              :rows="4"
              placeholder="请输入您的问题或反馈..."
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitMessage" :loading="submitting">
              发送留言
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 联系信息 -->
      <el-card title="专属客服" class="contact-section">
        <div class="contact-info">
          <div class="sales-info">
            <el-avatar :size="60" :src="salesInfo.avatar"></el-avatar>
            <div class="sales-details">
              <h3>{{ salesInfo.name }}</h3>
              <p>您的专属客服</p>
              <p>
                <i class="el-icon-phone"></i>
                {{ salesInfo.phone }}
              </p>
              <p>
                <i class="el-icon-message"></i>
                {{ salesInfo.email }}
              </p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { passwordlessAuthAPI } from '@/api/passwordless-auth'

// 响应式数据
const customerInfo = reactive({
  name: '',
  phone: ''
})

const leadInfo = reactive({
  id: '',
  status: 'new',
  evaluationResult: '',
  contactNotes: ''
})

const leadFiles = ref([])
const communications = ref([])
const salesInfo = reactive({
  name: '',
  phone: '',
  email: '',
  avatar: ''
})

const messageContent = ref('')
const submitting = ref(false)

// 计算属性
const currentStep = computed(() => {
  const statusMap = {
    'new': 0,
    'contacted': 1,
    'evaluating': 2,
    'designing': 3,
    'implementing': 4,
    'completed': 5
  }
  return statusMap[leadInfo.status] || 0
})

const statusText = computed(() => {
  const textMap = {
    'new': '需求已提交',
    'contacted': '已分配专属客服',
    'evaluating': '技术评估中',
    'designing': '方案设计中',
    'implementing': '项目实施中',
    'completed': '项目已完成'
  }
  return textMap[leadInfo.status] || '处理中'
})

const statusDescription = computed(() => {
  const descMap = {
    'new': '您的需求已收到，我们将在24小时内联系您',
    'contacted': '专属客服将为您提供一对一服务',
    'evaluating': '技术团队正在对您的项目进行详细评估',
    'designing': '我们正在为您定制专属解决方案',
    'implementing': '项目正在按计划执行中',
    'completed': '感谢您选择我们的服务'
  }
  return descMap[leadInfo.status] || ''
})

const statusType = computed(() => {
  const typeMap = {
    'new': 'info',
    'contacted': 'warning',
    'evaluating': 'primary',
    'designing': 'success',
    'implementing': 'success',
    'completed': 'success'
  }
  return typeMap[leadInfo.status] || 'info'
})

// 方法
const loadCustomerZone = async () => {
  try {
    const token = getTokenFromUrl()
    const response = await passwordlessAuthAPI.accessCustomerZone(token)
    
    Object.assign(customerInfo, response.customerInfo)
    Object.assign(leadInfo, response.leadInfo)
    leadFiles.value = response.leadFiles || []
    communications.value = response.communications || []
    Object.assign(salesInfo, response.salesInfo)
  } catch (error) {
    ElMessage.error('加载失败，请检查访问链接')
  }
}

const getTokenFromUrl = () => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('token') || ''
}

const formatFileSize = (row: any) => {
  const size = row.fileSize
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / 1024 / 1024).toFixed(1) + ' MB'
}

const getCommTypeText = (type: string) => {
  const typeMap = {
    'phone': '电话沟通',
    'email': '邮件联系',
    'meeting': '会议讨论',
    'visit': '上门拜访'
  }
  return typeMap[type] || type
}

const downloadFile = (file: any) => {
  window.open(file.fileUrl, '_blank')
}

const submitMessage = async () => {
  if (!messageContent.value.trim()) {
    ElMessage.warning('请输入留言内容')
    return
  }

  try {
    submitting.value = true
    // 调用API提交留言
    await submitCustomerMessage({
      leadId: leadInfo.id,
      content: messageContent.value
    })
    
    ElMessage.success('留言已提交，我们会尽快回复')
    messageContent.value = ''
    
    // 重新加载沟通记录
    loadCustomerZone()
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  loadCustomerZone()
})
</script>

<style scoped>
.customer-zone {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.zone-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.zone-header h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-details h4 {
  margin: 15px 0 5px 0;
  color: #333;
}

.evaluation-content {
  line-height: 1.6;
}

.eval-note {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
}

.files-section,
.communication-section,
.contact-section {
  margin-top: 20px;
}

.communication-item {
  border-left: 3px solid #409eff;
  padding-left: 15px;
  margin-bottom: 15px;
}

.comm-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comm-type {
  font-weight: bold;
  color: #409eff;
}

.comm-time {
  color: #999;
  font-size: 12px;
}

.comm-content {
  color: #666;
  line-height: 1.5;
}

.contact-info {
  text-align: center;
}

.sales-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sales-details h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.sales-details p {
  margin: 5px 0;
  color: #666;
}

.sales-details i {
  margin-right: 5px;
  color: #409eff;
}
</style>
```

## 阶段四：集成测试和部署（2天）

### 4.1 数据库SQL脚本文件

#### passwordless_auth_tables.sql
```sql
-- CRM无密码认证系统数据库创建脚本
-- 执行前请备份现有数据库

-- 创建用户注册表
CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码（加密存储）',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `passwordless_token` varchar(255) DEFAULT NULL COMMENT '无密码登录令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表（无密码设计）';

-- 创建用户认证方式表
CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `auth_token` varchar(500) DEFAULT NULL COMMENT '认证令牌（无密码登录）',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`)
) ENGINE=InnoDB COMMENT='用户认证方式表（支持无密码认证）';

-- 创建客户线索表
CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `registration_method` varchar(20) DEFAULT 'sms' COMMENT '注册方式：sms-短信无密码注册',
  `access_token` varchar(255) DEFAULT NULL COMMENT '客户专用访问令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表（无密码注册）';

-- 添加外键约束
ALTER TABLE `crm_user_auth_methods` 
ADD CONSTRAINT `fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`);

ALTER TABLE `crm_customer_leads` 
ADD CONSTRAINT `fk_leads_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`),
ADD CONSTRAINT `fk_leads_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `sys_user` (`user_id`);

-- 数据迁移：清理客户用户的密码字段
UPDATE sys_user 
SET password = NULL 
WHERE user_type = '01' 
   AND del_flag = '0';

-- 为现有企业微信用户创建认证方式记录
INSERT INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    tw.user_id,
    'wechat' as auth_type,
    tw.wecom_user_id as auth_identifier,
    1 as is_primary,
    1 as is_active
FROM crm_thirdparty_wechat tw
INNER JOIN sys_user su ON tw.user_id = su.user_id
WHERE su.del_flag = '0'
  AND NOT EXISTS (
      SELECT 1 FROM crm_user_auth_methods 
      WHERE user_id = tw.user_id AND auth_type = 'wechat'
  );

-- 为现有用户创建手机号认证方式记录
INSERT INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    user_id,
    'phone' as auth_type,
    phonenumber as auth_identifier,
    CASE WHEN user_id NOT IN (SELECT user_id FROM crm_user_auth_methods WHERE auth_type = 'wechat') THEN 1 ELSE 0 END as is_primary,
    1 as is_active
FROM sys_user 
WHERE phonenumber IS NOT NULL 
  AND phonenumber != ''
  AND del_flag = '0'
  AND NOT EXISTS (
      SELECT 1 FROM crm_user_auth_methods 
      WHERE user_id = sys_user.user_id AND auth_type = 'phone'
  );
```

### 4.2 配置文件

#### application-passwordless.yml
```yaml
# 无密码认证配置
passwordless:
  auth:
    # JWT配置
    jwt:
      secret: "CRM_PASSWORDLESS_SECRET_KEY_2025"
      expire:
        employee: **********  # 员工30天(毫秒)
        customer: 604800000   # 客户7天(毫秒)
    
    # 短信配置
    sms:
      provider: "aliyun"  # 短信服务商：aliyun/tencent
      rate-limit:
        per-minute: 1     # 每分钟最多发送次数
        per-day: 10       # 每天最多发送次数
      code:
        length: 6         # 验证码长度
        expire: 300       # 过期时间(秒)
    
    # 企业微信配置
    wechat:
      corp-id: "${WECHAT_CORP_ID:your_corp_id}"
      agent-id: "${WECHAT_AGENT_ID:your_agent_id}"
      corp-secret: "${WECHAT_CORP_SECRET:your_corp_secret}"
      redirect-uri: "${WECHAT_REDIRECT_URI:http://localhost:8080/auth/wechat/callback}"
    
    # 安全配置
    security:
      max-login-attempts: 5     # 最大登录尝试次数
      lockout-duration: 600     # 锁定时间(秒)
      token-refresh-threshold: 86400  # 令牌刷新阈值(秒)

# Redis配置
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 短信服务配置
aliyun:
  sms:
    access-key-id: "${ALIYUN_ACCESS_KEY_ID:your_access_key}"
    access-key-secret: "${ALIYUN_ACCESS_KEY_SECRET:your_access_secret}"
    sign-name: "${ALIYUN_SMS_SIGN_NAME:CRM系统}"
    template-code: "${ALIYUN_SMS_TEMPLATE_CODE:SMS_123456789}"
```

### 4.3 测试用例

#### PasswordlessAuthTest.java
```java
package com.ruoyi.crm.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.crm.domain.CrmUserRegistration;
import com.ruoyi.crm.service.ICrmPasswordlessAuthService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class PasswordlessAuthTest {

    @Autowired
    private ICrmPasswordlessAuthService passwordlessAuthService;

    @Test
    public void testSendSmsCode() {
        // 测试发送短信验证码
        AjaxResult result = passwordlessAuthService.sendPasswordlessCode(
            "13800138000", 
            CrmUserRegistration.USER_TYPE_CUSTOMER
        );
        assertEquals(200, result.get("code"));
    }

    @Test
    public void testSmsVerification() {
        // 测试短信验证码验证
        String phoneNumber = "13800138001";
        String userType = CrmUserRegistration.USER_TYPE_CUSTOMER;
        
        // 首先发送验证码
        passwordlessAuthService.sendPasswordlessCode(phoneNumber, userType);
        
        // 然后验证（需要模拟验证码）
        AjaxResult result = passwordlessAuthService.passwordlessVerify(
            phoneNumber, 
            "123456"  // 测试验证码
        );
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.get("data"));
    }

    @Test
    public void testTokenGeneration() {
        // 测试令牌生成
        String token = passwordlessAuthService.generatePasswordlessToken(
            1L, 
            CrmUserRegistration.USER_TYPE_EMPLOYEE
        );
        
        assertNotNull(token);
        assertTrue(token.length() > 0);
        
        // 验证令牌
        boolean isValid = passwordlessAuthService.validatePasswordlessToken(token);
        assertTrue(isValid);
    }

    @Test
    public void testWechatLogin() {
        // 测试企业微信登录（需要模拟企业微信API）
        String mockCode = "mock_wechat_code";
        
        AjaxResult result = passwordlessAuthService.wechatPasswordlessLogin(mockCode);
        
        // 根据模拟数据验证结果
        assertNotNull(result);
    }

    @Test
    public void testCustomerZoneAccess() {
        // 测试客户专区访问
        String mockToken = "mock_customer_token";
        
        AjaxResult result = passwordlessAuthService.accessCustomerZone(mockToken);
        
        assertNotNull(result);
    }

    @Test
    public void testRateLimiting() {
        // 测试频率限制
        String phoneNumber = "13800138002";
        String userType = CrmUserRegistration.USER_TYPE_CUSTOMER;
        
        // 第一次发送应该成功
        AjaxResult result1 = passwordlessAuthService.sendPasswordlessCode(phoneNumber, userType);
        assertEquals(200, result1.get("code"));
        
        // 立即再次发送应该失败（频率限制）
        AjaxResult result2 = passwordlessAuthService.sendPasswordlessCode(phoneNumber, userType);
        assertNotEquals(200, result2.get("code"));
    }
}
```

### 4.4 部署脚本

#### deploy-passwordless.sh
```bash
#!/bin/bash
# CRM无密码认证系统部署脚本

set -e

echo "开始部署CRM无密码认证系统..."

# 配置变量
PROJECT_DIR="/app/crm"
BACKUP_DIR="/app/backup/$(date +%Y%m%d_%H%M%S)"
DB_HOST="localhost"
DB_NAME="crm41"
DB_USER="root"

# 创建备份目录
echo "创建备份目录..."
mkdir -p $BACKUP_DIR

# 备份数据库
echo "备份数据库..."
mysqldump -h$DB_HOST -u$DB_USER -p $DB_NAME > $BACKUP_DIR/crm_backup.sql

# 备份配置文件
echo "备份配置文件..."
cp -r $PROJECT_DIR/src/main/resources/application*.yml $BACKUP_DIR/

# 停止应用
echo "停止应用..."
sudo systemctl stop crm-backend || echo "应用未运行"

# 执行数据库迁移
echo "执行数据库迁移..."
mysql -h$DB_HOST -u$DB_USER -p $DB_NAME < sql/passwordless_auth_tables.sql

# 更新应用代码
echo "更新应用代码..."
cd $PROJECT_DIR
git pull origin main

# 编译应用
echo "编译应用..."
mvn clean package -DskipTests

# 更新配置文件
echo "更新配置文件..."
cp config/application-passwordless.yml src/main/resources/

# 启动应用
echo "启动应用..."
sudo systemctl start crm-backend

# 等待应用启动
echo "等待应用启动..."
sleep 30

# 健康检查
echo "执行健康检查..."
health_check() {
    curl -f http://localhost:8080/crm/auth/health > /dev/null 2>&1
    return $?
}

retry_count=0
max_retries=5

while [ $retry_count -lt $max_retries ]; do
    if health_check; then
        echo "应用启动成功！"
        break
    else
        echo "健康检查失败，重试 $((retry_count + 1))/$max_retries"
        retry_count=$((retry_count + 1))
        sleep 10
    fi
done

if [ $retry_count -eq $max_retries ]; then
    echo "应用启动失败，回滚..."
    sudo systemctl stop crm-backend
    
    # 恢复数据库
    mysql -h$DB_HOST -u$DB_USER -p $DB_NAME < $BACKUP_DIR/crm_backup.sql
    
    # 恢复配置
    cp $BACKUP_DIR/application*.yml $PROJECT_DIR/src/main/resources/
    
    echo "回滚完成，请检查日志"
    exit 1
fi

echo "部署完成！"

# 运行基本测试
echo "运行基本测试..."
curl -X POST http://localhost:8080/crm/auth/sms/send \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"13800138000","userType":"customer"}' || echo "测试请求失败"

echo "部署和测试完成！"
```

### 4.5 监控配置

#### passwordless-monitor.yml
```yaml
# Prometheus监控配置
management:
  endpoints:
    web:
      exposure:
        include: ["health", "metrics", "prometheus"]
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: "crm-passwordless-auth"
      environment: "${SPRING_PROFILES_ACTIVE:dev}"

# 自定义监控指标
passwordless:
  monitoring:
    enabled: true
    metrics:
      - sms_send_count
      - sms_send_success_rate
      - wechat_login_count
      - wechat_login_success_rate
      - passwordless_login_count
      - token_generation_count
      - customer_zone_access_count
      - registration_completion_rate
```

## 阶段五：上线运维（持续）

### 5.1 安全检查清单

#### 上线前安全检查
- [ ] **数据加密**：验证码、令牌等敏感数据已加密存储
- [ ] **频率限制**：短信发送、登录尝试等已设置合理限制
- [ ] **令牌安全**：JWT令牌签名密钥已配置且足够复杂
- [ ] **HTTPS部署**：生产环境强制使用HTTPS
- [ ] **日志脱敏**：敏感信息（手机号、验证码）已脱敏处理
- [ ] **权限校验**：API接口已正确配置访问权限
- [ ] **SQL注入防护**：数据库查询已使用参数化查询
- [ ] **XSS防护**：前端输入已进行XSS过滤
- [ ] **CSRF保护**：关键操作已添加CSRF保护

### 5.2 性能优化

#### 数据库优化
```sql
-- 创建索引优化查询性能
CREATE INDEX idx_registration_phone_type ON crm_user_registration(phone_number, registration_type);
CREATE INDEX idx_registration_status_time ON crm_user_registration(status, create_time);
CREATE INDEX idx_auth_methods_user_type ON crm_user_auth_methods(user_id, auth_type);
CREATE INDEX idx_auth_methods_identifier ON crm_user_auth_methods(auth_identifier);
CREATE INDEX idx_leads_phone_status ON crm_customer_leads(phone_number, status);
CREATE INDEX idx_leads_assigned_time ON crm_customer_leads(assigned_to, create_time);

-- 数据清理定时任务
-- 清理过期的注册记录（7天前）
DELETE FROM crm_user_registration 
WHERE status = 'pending' 
  AND create_time < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理过期的验证码记录（1小时前）
UPDATE crm_user_registration 
SET verification_code = NULL, verification_expire_time = NULL 
WHERE verification_expire_time < DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

#### Redis缓存策略
```yaml
# Redis缓存配置
spring:
  redis:
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

# 缓存key设计
cache:
  keys:
    sms_code: "sms:code:{phoneNumber}"
    sms_rate: "sms:rate:{phoneNumber}"
    user_token: "user:token:{userId}"
    wechat_user: "wechat:user:{unionId}"
    customer_zone: "customer:zone:{token}"
  
  expires:
    sms_code: 300      # 验证码5分钟
    sms_rate: 60       # 频率限制1分钟
    user_token: 1800   # 用户令牌30分钟
    wechat_user: 3600  # 微信用户信息1小时
    customer_zone: 600 # 客户专区10分钟
```

### 5.3 监控告警

#### 监控指标配置
```yaml
# application-monitoring.yml
monitoring:
  alerts:
    sms:
      send_failure_rate:
        threshold: 0.05  # 5%失败率
        window: "5m"
        action: "email,wechat"
      
      daily_limit_reached:
        threshold: 0.8   # 80%日限额
        action: "email"
    
    authentication:
      login_failure_rate:
        threshold: 0.1   # 10%失败率
        window: "5m"
        action: "email,wechat"
      
      suspicious_activity:
        threshold: 10    # 10次异常尝试
        window: "1m"
        action: "email,wechat,block_ip"
    
    performance:
      response_time:
        threshold: "2s"
        percentile: 95
        action: "email"
      
      error_rate:
        threshold: 0.01  # 1%错误率
        window: "5m"
        action: "email,wechat"

  dashboards:
    - name: "无密码认证概览"
      panels:
        - "每日注册用户数"
        - "短信发送成功率"
        - "企业微信登录成功率"
        - "客户专区访问量"
        - "API响应时间"
        - "错误率趋势"
```

### 5.4 运维手册

#### 常见问题处理

**问题1：短信发送失败**
```bash
# 检查短信服务状态
curl -X POST http://localhost:8080/crm/auth/sms/send \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"13800138000","userType":"customer"}'

# 检查短信服务配置
kubectl describe configmap sms-config

# 查看短信服务日志
kubectl logs -f deployment/crm-backend | grep "SMS"
```

**问题2：企业微信登录异常**
```bash
# 检查企业微信配置
curl http://localhost:8080/crm/auth/wechat/config

# 验证企业微信API连通性
curl -X GET "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${CORP_ID}&corpsecret=${CORP_SECRET}"

# 查看相关日志
kubectl logs -f deployment/crm-backend | grep "WeChat"
```

**问题3：数据库连接问题**
```bash
# 检查数据库连接
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASSWORD} -e "SELECT 1"

# 查看连接池状态
curl http://localhost:8080/actuator/metrics/hikaricp.connections.active

# 重启数据库连接
kubectl rollout restart deployment/crm-backend
```

### 5.5 版本升级计划

#### 渐进式部署策略
```yaml
# kubernetes部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crm-passwordless-auth
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    spec:
      containers:
      - name: crm-backend
        image: crm/backend:passwordless-v1.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production,passwordless"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

## 📊 总结

### 项目收益

#### 用户体验提升
- ✅ **完全无密码**：用户无需记住任何密码
- ✅ **注册简化**：客户"润物无声"式注册，3步完成
- ✅ **企业微信集成**：员工扫码即可登录，无需额外认证
- ✅ **专属客户专区**：每个客户获得专用访问链接

#### 安全性增强
- ✅ **双重认证**：企业微信OAuth + 短信验证码
- ✅ **令牌管理**：JWT无密码令牌，定期自动刷新
- ✅ **频率限制**：防止暴力破解和短信轰炸
- ✅ **访问控制**：细粒度权限管理，数据隔离

#### 业务价值
- ✅ **线索转化**：客户注册即生成线索，自动分配销售
- ✅ **跟进便利**：销售人员实时查看客户状态和进度
- ✅ **客户服务**：专属客服联系方式，沟通记录完整
- ✅ **数据分析**：完整的用户行为数据，支持精准营销

### 技术架构优势

#### 扩展性
- 🔧 **模块化设计**：认证模块独立，易于扩展其他认证方式
- 🔧 **微服务友好**：API设计符合微服务架构
- 🔧 **数据库分离**：认证数据与业务数据分离，便于维护

#### 可维护性
- 🔧 **标准化接口**：RESTful API设计，文档完整
- 🔧 **监控完善**：关键指标全覆盖，异常及时告警
- 🔧 **日志规范**：结构化日志，便于问题排查

#### 性能优化
- 🔧 **缓存策略**：Redis缓存热点数据，响应迅速
- 🔧 **数据库优化**：索引优化，查询性能提升
- 🔧 **异步处理**：短信发送异步化，用户体验流畅

这个完整的实施方案提供了从数据库设计到上线运维的全流程指导，确保CRM系统无密码认证功能的成功实施和稳定运行。

<write_to_file>
<path>ruoyi-crm/src/main/java/com/ruoyi/crm/domain/CrmUserRegistration.java</path>
<content>
package com.ruoyi.crm.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户注册记录对象 crm_user_registration
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("crm_user_registration")
public class CrmUserRegistration extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 注册记录ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 手机号码 */
    @TableField("phone_number")
    private String phoneNumber;

    /** 注册类型：wechat-企业微信,sms-短信验证 */
    @TableField("registration_type")
    private String registrationType;

    /** 企业微信唯一ID */
    @TableField("wechat_union_id")
    private String wechatUnionId;

    /** 验证码（加密存储） */
    @TableField("verification_code")
    private String verificationCode;

    /** 验证码过期时间 */
    @TableField("verification_expire_time")
    private LocalDateTime verificationExpireTime;

    /** 是否已验证 */
    @TableField("is_verified")
    private Boolean isVerified;

    /** 关联的用户ID */
    @TableField("user_id")
    private Long userId;

    /** 状态：pending-待处理,verified-已验证,completed-已完成 */
    @TableField("status")
    private String status;

    /** 用户类型：employee-员工,customer-客户 */
    @TableField("user_type")
    private String userType;

    /** 来源数据(如企业微信用户信息) */
    @TableField("source_data")
    private String sourceData;

    /** 无密码登录令牌 */
    @TableField("passwordless_token")
    private String passwordlessToken;

    // 注册类型常量
    public static final String TYPE_WECHAT = "wechat";
    public static final String TYPE_SMS = "sms";
    
    // 状态常量
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_VERIFIED = "verified";
    public static final String STATUS_COMPLETED = "completed";
    
    // 用户类型常量
    public static final String USER_TYPE_EMPLOYEE = "employee";
    public static final String USER_TYPE_CUSTOMER = "customer";
}
