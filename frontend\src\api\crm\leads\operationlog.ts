import request from '@/utils/request'

// 查询参数类型
export interface LeadOperationLogQuery {
  leadId?: string | number;
  operName?: string;
  operationType?: string;
  startTime?: string;
  endTime?: string;
  pageNum?: number;
  pageSize?: number;
}

// 操作日志详情类型
export interface OperationLogDetail {
  before?: string;
  after?: string;
}

// 操作日志数据类型
export interface LeadOperationLog {
  id: number;
  leadId: number;
  operName: string;
  operationType: string;
  operContent: string;
  createTime: string;
  details?: OperationLogDetail;
}

// 返回数据类型
export interface OperationLogResponse {
  total: number;
  rows: LeadOperationLog[];
  code: number;
  msg: string;
}

// 查询线索操作日志列表
export function listLeadOperationLog(query: LeadOperationLogQuery): Promise<OperationLogResponse> {
  return request({
    url: '/front/crm/leadOperationlog/list',
    method: 'get',
    params: query
  })
}
