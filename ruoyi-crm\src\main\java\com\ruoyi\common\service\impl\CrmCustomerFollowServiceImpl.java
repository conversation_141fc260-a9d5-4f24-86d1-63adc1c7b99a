package com.ruoyi.common.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmCustomerFollowers;
import com.ruoyi.common.mapper.CrmCustomerFollowersMapper;
import com.ruoyi.common.service.ICrmCustomerFollowService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 客户关注Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class CrmCustomerFollowServiceImpl implements ICrmCustomerFollowService {
    
    @Autowired
    private CrmCustomerFollowersMapper crmCustomerFollowersMapper;

    /**
     * 查询客户关注列表
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 客户关注
     */
    @Override
    public List<CrmCustomerFollowers> selectCrmCustomerFollowersList(CrmCustomerFollowers crmCustomerFollowers) {
        return crmCustomerFollowersMapper.selectCrmCustomerFollowersList(crmCustomerFollowers);
    }

    /**
     * 新增客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    @Override
    public int insertCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers) {
        return crmCustomerFollowersMapper.insertCrmCustomerFollowers(crmCustomerFollowers);
    }

    /**
     * 修改客户关注
     * 
     * @param crmCustomerFollowers 客户关注
     * @return 结果
     */
    @Override
    public int updateCrmCustomerFollowers(CrmCustomerFollowers crmCustomerFollowers) {
        return crmCustomerFollowersMapper.updateCrmCustomerFollowers(crmCustomerFollowers);
    }

    /**
     * 删除客户关注对象
     * 
     * @param id 客户关注主键
     * @return 结果
     */
    @Override
    public int deleteCrmCustomerFollowersById(Long id) {
        return crmCustomerFollowersMapper.deleteCrmCustomerFollowersById(id);
    }

    /**
     * 关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int followCustomer(Long customerId, Long followerId) {
        if (customerId == null || followerId == null) {
            return 0;
        }
        
        // 获取当前用户名作为操作者
        String followerName = SecurityUtils.getUsername();
        if (followerName == null) {
            followerName = "system";
        }
        
        // 使用专门的followCustomer方法，它有ON DUPLICATE KEY UPDATE逻辑
        return crmCustomerFollowersMapper.followCustomer(customerId, followerId, followerName);
    }

    /**
     * 取消关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int unfollowCustomer(Long customerId, Long followerId) {
        if (customerId == null || followerId == null) {
            return 0;
        }
        
        // 软删除：更新is_active状态而非物理删除
        return crmCustomerFollowersMapper.unfollowCustomer(customerId, followerId);
    }

    /**
     * 判断是否关注客户
     * 
     * @param customerId 客户ID
     * @param followerId 关注者用户ID
     * @return 是否关注
     */
    @Override
    public boolean isFollowing(Long customerId, Long followerId) {
        if (customerId == null || followerId == null) {
            return false;
        }
        
        CrmCustomerFollowers follower = crmCustomerFollowersMapper.selectFollowRelation(customerId, followerId);
        return follower != null && follower.isActiveFollow();
    }

    /**
     * 批量关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int batchFollowCustomers(List<Long> customerIds, Long followerId) {
        if (customerIds == null || customerIds.isEmpty() || followerId == null) {
            return 0;
        }
        
        // 获取当前用户名作为操作者
        String followerName = SecurityUtils.getUsername();
        if (followerName == null) {
            followerName = "system";
        }
        
        return crmCustomerFollowersMapper.batchFollowCustomers(customerIds, followerId, followerName);
    }

    /**
     * 批量取消关注客户
     * 
     * @param customerIds 客户ID列表
     * @param followerId 关注者用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUnfollowCustomers(List<Long> customerIds, Long followerId) {
        if (customerIds == null || customerIds.isEmpty() || followerId == null) {
            return 0;
        }
        
        return crmCustomerFollowersMapper.batchUnfollowCustomers(customerIds, followerId);
    }

    /**
     * 统计用户关注的客户数量
     * 
     * @param followerId 关注者用户ID
     * @return 关注数量
     */
    @Override
    public int countFollowedCustomers(Long followerId) {
        if (followerId == null) {
            return 0;
        }
        return crmCustomerFollowersMapper.countFollowedCustomers(followerId);
    }

    /**
     * 统计客户被关注的数量
     * 
     * @param customerId 客户ID
     * @return 被关注数量
     */
    @Override
    public int countCustomerFollowers(Long customerId) {
        if (customerId == null) {
            return 0;
        }
        return crmCustomerFollowersMapper.countCustomerFollowers(customerId);
    }

    /**
     * 查询热门客户排行
     * 
     * @param limit 限制数量
     * @return 热门客户列表
     */
    @Override
    public List<CrmCustomerFollowers> selectPopularCustomers(Integer limit) {
        return crmCustomerFollowersMapper.selectPopularCustomers(limit);
    }
}