package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.mapper.CrmPaymentInstallmentMapper;
import com.ruoyi.common.mapper.CrmPaymentPlanMapper;
import com.ruoyi.common.domain.entity.CrmPaymentInstallment;
import com.ruoyi.crm.service.ICrmPaymentInstallmentService;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;

/**
 * 回款分期Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class CrmPaymentInstallmentServiceImpl implements ICrmPaymentInstallmentService 
{
    @Autowired
    private CrmPaymentInstallmentMapper crmPaymentInstallmentMapper;
    
    @Autowired
    private CrmPaymentPlanMapper paymentPlanMapper;
    
    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    /**
     * 查询回款分期
     * 
     * @param id 回款分期主键
     * @return 回款分期
     */
    @Override
    public CrmPaymentInstallment selectCrmPaymentInstallmentById(Long id)
    {
        return crmPaymentInstallmentMapper.selectCrmPaymentInstallmentById(id);
    }

    /**
     * 查询回款分期列表
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 回款分期
     */
    @Override
    public List<CrmPaymentInstallment> selectCrmPaymentInstallmentList(CrmPaymentInstallment crmPaymentInstallment)
    {
        return crmPaymentInstallmentMapper.selectCrmPaymentInstallmentList(crmPaymentInstallment);
    }

    /**
     * 根据计划ID查询分期列表
     * 
     * @param planId 计划ID
     * @return 回款分期集合
     */
    @Override
    public List<CrmPaymentInstallment> selectInstallmentsByPlanId(Long planId)
    {
        return crmPaymentInstallmentMapper.selectInstallmentsByPlanId(planId);
    }

    /**
     * 新增回款分期
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 结果
     */
    @Override
    public int insertCrmPaymentInstallment(CrmPaymentInstallment crmPaymentInstallment)
    {
        crmPaymentInstallment.setCreateTime(DateUtils.getNowDate());
        crmPaymentInstallment.setCreateBy(SecurityUtils.getUsername());
        
        if (crmPaymentInstallment.getInstallmentStatus() == null) {
            crmPaymentInstallment.setInstallmentStatus("待回款");
        }
        if (crmPaymentInstallment.getActualAmount() == null) {
            crmPaymentInstallment.setActualAmount(BigDecimal.ZERO);
        }
        if (crmPaymentInstallment.getOverdueDays() == null) {
            crmPaymentInstallment.setOverdueDays(0);
        }
        if (crmPaymentInstallment.getPenaltyAmount() == null) {
            crmPaymentInstallment.setPenaltyAmount(BigDecimal.ZERO);
        }
        
        return crmPaymentInstallmentMapper.insertCrmPaymentInstallment(crmPaymentInstallment);
    }

    /**
     * 批量新增回款分期
     * 
     * @param installments 回款分期列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertInstallments(List<CrmPaymentInstallment> installments)
    {
        if (installments == null || installments.isEmpty()) {
            return 0;
        }
        
        for (CrmPaymentInstallment installment : installments) {
            installment.setCreateTime(DateUtils.getNowDate());
            installment.setCreateBy(SecurityUtils.getUsername());
            
            if (installment.getInstallmentStatus() == null) {
                installment.setInstallmentStatus("待回款");
            }
            if (installment.getActualAmount() == null) {
                installment.setActualAmount(BigDecimal.ZERO);
            }
            if (installment.getOverdueDays() == null) {
                installment.setOverdueDays(0);
            }
            if (installment.getPenaltyAmount() == null) {
                installment.setPenaltyAmount(BigDecimal.ZERO);
            }
        }
        
        return crmPaymentInstallmentMapper.batchInsertInstallments(installments);
    }

    /**
     * 修改回款分期
     * 
     * @param crmPaymentInstallment 回款分期
     * @return 结果
     */
    @Override
    public int updateCrmPaymentInstallment(CrmPaymentInstallment crmPaymentInstallment)
    {
        crmPaymentInstallment.setUpdateTime(DateUtils.getNowDate());
        crmPaymentInstallment.setUpdateBy(SecurityUtils.getUsername());
        
        int result = crmPaymentInstallmentMapper.updateCrmPaymentInstallment(crmPaymentInstallment);
        
        // 如果更新了实际回款金额，需要更新计划的统计信息
        if (result > 0 && crmPaymentInstallment.getActualAmount() != null) {
            paymentPlanService.updatePaymentAmounts(crmPaymentInstallment.getPlanId());
        }
        
        return result;
    }

    /**
     * 批量删除回款分期
     * 
     * @param ids 需要删除的回款分期主键集合
     * @return 结果
     */
    @Override
    public int deleteCrmPaymentInstallmentByIds(Long[] ids)
    {
        return crmPaymentInstallmentMapper.deleteCrmPaymentInstallmentByIds(ids);
    }

    /**
     * 删除回款分期信息
     * 
     * @param id 回款分期主键
     * @return 结果
     */
    @Override
    public int deleteCrmPaymentInstallmentById(Long id)
    {
        return crmPaymentInstallmentMapper.deleteCrmPaymentInstallmentById(id);
    }

    /**
     * 根据计划ID删除分期
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    public int deleteInstallmentsByPlanId(Long planId)
    {
        return crmPaymentInstallmentMapper.deleteInstallmentsByPlanId(planId);
    }

    /**
     * 更新分期状态
     * 
     * @param id 分期ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateInstallmentStatus(Long id, String status)
    {
        return crmPaymentInstallmentMapper.updateInstallmentStatus(id, status);
    }

    /**
     * 更新逾期天数
     * 
     * @return 结果
     */
    @Override
    public int updateOverdueDays()
    {
        return crmPaymentInstallmentMapper.updateOverdueDays();
    }

    /**
     * 记录实际回款
     * 
     * @param installmentId 分期ID
     * @param actualAmount 实际回款金额
     * @param paymentVoucher 付款凭证
     * @return 结果
     */
    @Override
    @Transactional
    public int recordActualPayment(Long installmentId, BigDecimal actualAmount, String paymentVoucher)
    {
        CrmPaymentInstallment installment = crmPaymentInstallmentMapper.selectCrmPaymentInstallmentById(installmentId);
        if (installment == null) {
            throw new ServiceException("分期记录不存在");
        }
        
        // 更新实际回款信息
        installment.setActualAmount(actualAmount);
        installment.setActualDate(new Date());
        installment.setPaymentVoucher(paymentVoucher);
        installment.setUpdateTime(DateUtils.getNowDate());
        installment.setUpdateBy(SecurityUtils.getUsername());
        
        // 根据实际回款金额更新状态
        if (actualAmount.compareTo(installment.getInstallmentAmount()) >= 0) {
            installment.setInstallmentStatus("已回款");
        } else if (actualAmount.compareTo(BigDecimal.ZERO) > 0) {
            installment.setInstallmentStatus("部分回款");
        }
        
        int result = crmPaymentInstallmentMapper.updateCrmPaymentInstallment(installment);
        
        // 更新计划的统计信息
        if (result > 0) {
            paymentPlanService.updatePaymentAmounts(installment.getPlanId());
        }
        
        return result;
    }
}